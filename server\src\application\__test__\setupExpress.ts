import { Config } from '../../config';
import createHealthCheckApp from '../health-check';
import createTrackerApp from '../tracker';
import createExpressApp from '../express';
import createCache from '../../cache';
import createGetCredentialApp from '../credential';
import createGQLApiWithoutToken from '../../gql';

const createConfig = async () => {
  const deps = {
    log: {
      error: (p1: string | Error, p2?: string | Error) =>
        console.error(p1, p2 ?? ''),
      info: (p1: string | Error, p2?: string | Error) =>
        console.log(p1, p2 ?? ''),
      debug: (p1: string | Error, p2?: string | Error) =>
        console.log(p1, p2 ?? ''),
    },
    cache: createCache(),
    gql: createGQLApiWithoutToken(),
  };
  const config: Config = {
    ...deps,
    healthCheckApp: createHealthCheckApp(deps),
    trackerApp: createTrackerApp(deps),
    getCredentialApp: createGetCredentialApp(deps),
  };

  return config;
};

const expressApp = async () => {
  const config = await createConfig();
  return createExpressApp({ config });
};
export default expressApp;
