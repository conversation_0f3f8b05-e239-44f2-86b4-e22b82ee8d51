import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';

export const ROUTE_CHANGED = 'ROUTE_CHANGED';

function useRouteChanged() {
  const { pathname, search} = useLocation();
  const dispatch = useDispatch();
  const currentPath = useRef<string>();

  useEffect(() => {
    if (currentPath.current !== pathname) {
      dispatch({ type: ROUTE_CHANGED, payload: { pathname, search } });
      currentPath.current = pathname;
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);
}

export default useRouteChanged;
