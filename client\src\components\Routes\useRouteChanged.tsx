import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { LocationState, onRouteChanged } from "@store/modules/location/slice";

function useRouteChanged() {
  const location = useLocation();
  const dispatch = useDispatch();
  const [history, setHistory] = useState<LocationState>();

  useEffect(() => {
    const { state, ...meta } = location;
    setHistory({ payload: { ...state }, ...meta });
  }, [location]);

  useEffect(() => {
    const { state, ...meta } = location;
    dispatch(onRouteChanged({ payload: { ...state }, ...meta, history }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location, dispatch]);
}

export default useRouteChanged;
