.home {
  --default-height-table: calc(100vh - 310px);
  --default-height-with-pending-files: calc(100vh - 420px);
  --default-height-search-table: calc(100vh - 366px);
  --default-height-search-table-with-pending-files: calc(100vh - 576px);

  position: relative;
  height: 100%;
  min-height: 620px;
  overflow: hidden;
  background-color: var(--home-background);

  .home__search-heading {
    padding: 0 30px;

    .home__search-heading-title {
      @include size-4;
    }

    .home__search-heading-items-returned {
      @include size-3;

      margin-top: 10px;
      color: var(--text-secondary);
    }
  }

  .home__main-content {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    height: 100%;
  }

  .home__tables {
    width: 50%;
    display: flex;
    flex-direction: column;

    .table__loading {
      max-height: var(--default-height-table);
    }

    .table__content {
      max-height: var(--default-height-table);
    }

    .table__with-pending-files {
      .table__loading {
        max-height: var(--default-height-search-table-with-pending-files);
      }
      .table__content {
        max-height: var(--default-height-search-table-with-pending-files);
      }
    }

    .table__search-result {
      .table__loading {
        max-height: var(--default-height-search-table);
      }
      .table__content {
        max-height: var(--default-height-search-table);
      }
    }

    .table__search-result-with-pending-files {
      .table__loading {
        max-height: var(--default-height-search-table-with-pending-files);
      }
      .table__content {
        max-height: var(--default-height-search-table-with-pending-files);
      }
    }
  }

  .MuiTabs-root {
    margin-left: 30px;
    margin-top: 22px;
  }

  .MuiTab-root {
    min-height: 0;
  }

  .home__tab-container {
    margin-top: 17px;
    padding: 0 0 0 30px;
    height: calc(100% - 100px);

    .home__create-pending-events {
      margin-bottom: 25px;
    }

    .home__upload-pending-files {
      max-height: 185px;
      overflow: hidden auto;   
      margin-bottom: 25px; 
    }
  }

  hr {
    margin-top: 0;
    border-top: var(--divider);
  }

  .home__detail {
    width: 50%;
    min-width: 580px;

    .home__detail-container {
      flex-shrink: 1;
      border: solid 1px var(--divider);
      border-radius: 8px;
      margin-right: 30px;
      margin-bottom: 50px;
      padding: 20px 25px 6px 26px;

      .home__detail-container-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        height: calc(100% - 30px);
        color: var(--disabled);
        padding-bottom: 14px;

        @include size-1;
      }

      .home__detail-name-textfield {
        padding: 0;
      }

      .home__detail-name-textfield-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;

        .home__detail-name-textfield {
          padding: 0;
        }
  
        .material-icons {
          font-size: 14px;
          margin-bottom: 3px;
          color: var(--icon-edit);
          cursor: pointer;
        }
      }

      .home__detail-name-container {
        display: flex;
        align-items: center;
        gap: 8px;

        .home__detail-name {
          line-height: 22px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .material-icons {
          font-size: 14px;
          margin-bottom: 3px;
          color: var(--icon-edit);
          cursor: pointer;
        }
      }

      .home__detail-date {
        @include size-1;

        color: var(--text-secondary);
        line-height: 16px;
        margin-top: 1.5px;
      }

      .home__detail-creator {
        @include size-0-bold;

        margin-top: 14px;
      }

      .home__detail-files-match-group-container {
        background-color: var(--row-active);
        padding: 18px 20px;
        margin-top: 11px;

        .home__detail-details {
          display: table;
          width: 130px;

          .home__detail-files,
          .home__detail-match-group {
            display: table-row;
            align-items: center;

            &-title {
              @include size-1;
            }

            > * {
              display: table-cell;
              vertical-align: middle;
              margin-right: 8px;
              height: 25px;
            }

            .material-icons {
              font-size: 18px;
              padding-left: 2px;
            }

            img {
              width: 20px;
              height: 20px;
            }
          }

          &.file {
            width: 100%;
            padding-right: 30px;
          }

          .home__detail-file-heading {
            color: var(--text-secondary);

            @include size-0;
          }

          .home__detail-file-info {
            display: flex;
            justify-content: space-between;
          }

          .home__detail-file {
            display: flex;
            align-items: center;
            color: var(--text-secondary);

            @include size-1;

            img {
              margin-right: 10px;
            }

            .home__detail-files-title {
              @include size-1;

              line-height: 18px;
              margin-left: 5px;
            }
          }

          .home__detail-file-size-container {
            display: flex;
            width: 45%;
            justify-content: space-between;

            .home__detail-file-size {
              color: var(--text-secondary);

              @include size-1;

              span {
                color: var(--text-primary);
                margin-left: 6px;
              }
            }

            .home__detail-file-more-info {
              cursor: pointer;
              user-select: none;
              display: none;

              .material-symbols-outlined {
                font-size: 16px;
                margin-right: 5px;
              }

              .home__detail-file-more-info-text {
                @include size-1;

                color: var(--link);
                line-height: 18px;
              }
            }
          }
        }
      }

      .home__detail-actions {
        display: flex;
        justify-content: space-evenly;
        margin-left: -26px;
        margin-top: 20px;
        width: calc(100% + 51px);
        border-top: solid 1px var(--divider);
        padding: 10px 0 0;

        span {
          color: var(--text-primary);
          text-transform: none;

          @include size-1;
        }

        img {
          width: 18px;
          margin-right: 10px;
        }
        .home__detail-disabled-button {
          cursor: default;

          span,
          img {
            opacity: 0.4;
          }
        }
      }

      .home__detail-hidden {
        visibility: hidden;
        height: 0;
      }

      .home__detail-display-none {
        display: none;
      }
    }
  }
}
