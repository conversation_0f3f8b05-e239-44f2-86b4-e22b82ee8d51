import { FormControl, MenuItem, Select, SelectChangeEvent, Skeleton } from '@mui/material';
import { onEnter } from '@utility/keypressHelpers';
import cn from 'classnames';
import './Pagination.scss';
import { range } from 'lodash';
import { I18nTranslate } from '@i18n';

interface Props {
  totalCount: number;
  pageSize: number;
  prevPage: () => void;
  nextPage: () => void;
  setPageSize: (size: number) => void;
  currentPage: number;
  totalPages: number;
  loading: boolean;
  setPage?: (page: number) => void;
}

export default function Pagination({ setPage, totalCount, prevPage, nextPage, currentPage, totalPages, loading, setPageSize, pageSize }: Props) {
  const currentPageIndex = currentPage - 1;

  return (
    <div className="pagination" data-testid="pagination">
      {loading &&
        <Skeleton
          className="pagination-page-size"
          variant="rectangular"
          width={135}
          height={22}
        />}
      {!loading && <>
        <div className="pagination-page-size-text">
          {I18nTranslate.TranslateMessage('resultsPerPage')}
        </div>
        <FormControl className="pagination-page-size" variant="standard" size="small">
          <Select
            className="pagination-page-size-select"
            value={pageSize}
            onChange={(event: SelectChangeEvent<number>) => setPageSize(Number(event.target.value))}
            inputProps={{ 'data-testid': 'table-pagination-page-size' }}
            disableUnderline
          >
            <MenuItem value="10" data-testid="table-pagination-page-size-menu-item-10">
              10
            </MenuItem>
            <MenuItem value="50" data-testid="table-pagination-page-size-menu-item-50">
              50
            </MenuItem>
            <MenuItem value="100" data-testid="table-pagination-page-size-menu-item-100">
              100
            </MenuItem>
          </Select>
        </FormControl>
        {setPage && <>
          <span className="pagination-page-select-drop-control-page">Page:</span>
          <FormControl variant="standard" size="small">
            <Select
              className="pagination-page-select-drop"
              value={currentPage}
              onChange={(event: SelectChangeEvent<number>) => setPage?.(Number(event.target.value))}
              disableUnderline
            >
              {range(1, totalPages + 1).map(p => <MenuItem key={`PageSelectDrop-${p}`} value={p}>
                {p}
              </MenuItem>)}
            </Select>
          </FormControl>
        </>}
      </>}
      {loading &&
        <Skeleton
          className="pagination-page-selector"
          variant="rectangular"
          width={135}
          height={22}
        />}
      {!loading && <div className="pagination-page-selector">
        <div className="pagination-page-selector-text" data-testid="table-pagination-page-selector-text">
          {`${totalCount === 0 ? 0 : currentPageIndex * pageSize + 1}-${(currentPageIndex + 1) * pageSize > totalCount ? totalCount : (currentPageIndex + 1) * pageSize} `}
          {`${I18nTranslate.Intl().formatMessage({ id: 'of' })} ${totalCount}`}
        </div>
        <div
          tabIndex={0}
          role="button"
          className={cn('pagination-page-selector-back material-icons', { enabled: currentPageIndex > 0 })}
          aria-label="Previous page"
          data-testid="table-pagination-page-selector-back"
          onClick={prevPage}
          onKeyUp={onEnter(prevPage)}
        >
          chevron_left
        </div>
        <div
          tabIndex={0}
          role="button"
          aria-label="Next page"
          className={cn('pagination-page-selector-next material-icons', { enabled: currentPage < totalPages })}
          data-testid="table-pagination-page-selector-next"
          onClick={nextPage}
          onKeyUp={onEnter(nextPage)}
        >
          chevron_right
        </div>
      </div>}
    </div>
  );
}
