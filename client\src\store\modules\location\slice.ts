import type { PayloadAction } from "@reduxjs/toolkit";
import { createAppSlice } from "../../createAppSlice";
import { omit } from "lodash";

export interface LocationState {
  pathname: string;
  search: string;
  hash: string;
  key: string;
  history?: {
    hash: string;
    key: string;
    pathname: string;
    search: string;
    payload?: object;
  };
  payload?: object;
}

const initialState: LocationState = {
  pathname: "",
  search: "",
  hash: "",
  key: "",
  history: undefined,
  payload: undefined,
};

export const locationSlice = createAppSlice({
  name: "location",
  initialState,
  reducers: (create) => ({
    onRouteChanged: create.reducer(
      (state, action: PayloadAction<LocationState>) => ({
        ...state,
        ...action.payload,
        payload: { ...action.payload.payload },
      })
    ),
  }),
  selectors: {
    selectLocation: (location) => location,
    selectCurrentLocation: (location) => omit(location, 'history'),
    selectPathname: (location) => location.pathname,
    selectPayload: (location) => location.payload,
    selectHistory: (location) => location.history,
  },
});

export const { onRouteChanged } = locationSlice.actions;

export const { selectLocation, selectPathname, selectPayload, selectHistory, selectCurrentLocation } = locationSlice.selectors;

export const { actions: locationActions, reducer: locationReducer } =
  locationSlice;
