import Redis from 'ioredis';
import { Tracklet, File, Event } from '../../types/tracker';

const ONE_WEEK_EXP = 60 * 60 * 24 * 7;

class RedisWrapper {
  private client: Redis;
  private log: Logger;

  constructor(client: Redis, log: Logger) {
    this.client = client;
    this.log = log;

    this.event = {
      set: this.setEvent.bind(this),
      get: this.getEvent.bind(this),
      del: this.delEvent.bind(this),
    };

    this.file = {
      set: this.setFile.bind(this),
      get: this.getFile.bind(this),
      del: this.delFile.bind(this),
    };

    this.tracklet = {
      set: this.setTracklet.bind(this),
      get: this.getTracklet.bind(this),
      del: this.delTracklet.bind(this),
    };
  }

  private getPrefixedKey(type: string, id: string, orgId?: string): string {
    return `${type}-${orgId ? `${orgId}-` : ''}${id}`;
  }

  private async get(key: string): Promise<string | null> {
    try {
      this.log.info(`RedisWrapper Getting data with key: ${key}`);
      return await this.client.get(key);
    } catch (error) {
      this.log.error(`Error getting key ${key} from Redis`, error);
      throw error;
    }
  }

  private async set(key: string, value: string): Promise<void> {
    try {
      this.log.info(
        `RedisWrapper Setting data with key: ${key} and value ${value}`
      );
      await this.client.set(key, value, 'EX', ONE_WEEK_EXP);
    } catch (error) {
      this.log.error(`Error setting key ${key} in Redis`, error);
      throw error;
    }
  }

  private async del(key: string): Promise<void> {
    try {
      this.log.info(`RedisWrapper Deleting data with key: ${key}`);
      await this.client.del(key);
    } catch (error) {
      this.log.error(`Error deleting key ${key} from Redis`, error);
      throw error;
    }
  }

  close(): void {
    this.client.quit();
  }

  // Methods for handling events
  private async getEvent(
    eventId: string,
    orgId: string
  ): Promise<Event | null> {
    const key = this.getPrefixedKey('event', eventId, orgId);
    const result = await this.get(key);
    return result ? JSON.parse(result) : null;
  }

  private async setEvent(
    eventId: string,
    orgId: string,
    data: Event
  ): Promise<void> {
    const key = this.getPrefixedKey('event', eventId, orgId);
    await this.set(key, JSON.stringify(data));
  }

  private async delEvent(eventId: string, orgId: string): Promise<void> {
    const key = this.getPrefixedKey('event', eventId, orgId);
    await this.del(key);
  }

  // Methods for handling files
  private async getFile(fileId: string): Promise<File | null> {
    const key = this.getPrefixedKey('file', fileId);
    const result = await this.get(key);
    return result ? JSON.parse(result) : null;
  }

  private async setFile(fileId: string, data: File): Promise<void> {
    const key = this.getPrefixedKey('file', fileId);
    await this.set(key, JSON.stringify(data));
  }

  private async delFile(fileId: string): Promise<void> {
    const key = this.getPrefixedKey('file', fileId);
    await this.del(key);
  }

  // Methods for handling tracklets
  private async getTracklet(trackletId: string): Promise<Tracklet | null> {
    const key = this.getPrefixedKey('tracklet', trackletId);
    const result = await this.get(key);
    return result ? JSON.parse(result) : null;
  }

  private async setTracklet(trackletId: string, data: Tracklet): Promise<void> {
    const key = this.getPrefixedKey('tracklet', trackletId);
    await this.set(key, JSON.stringify(data));
  }

  private async delTracklet(trackletId: string): Promise<void> {
    const key = this.getPrefixedKey('tracklet', trackletId);
    await this.del(key);
  }

  public event: {
    set: (eventId: string, orgId: string, data: Event) => Promise<void>;
    get: (eventId: string, orgId: string) => Promise<Event | null>;
    del: (eventId: string, orgId: string) => Promise<void>;
  };

  public file: {
    set: (fileId: string, data: File) => Promise<void>;
    get: (fileId: string) => Promise<File | null>;
    del: (fileId: string) => Promise<void>;
  };

  public tracklet: {
    set: (trackletId: string, data: Tracklet) => Promise<void>;
    get: (trackletId: string) => Promise<Tracklet | null>;
    del: (trackletId: string) => Promise<void>;
  };
}

export default RedisWrapper;
