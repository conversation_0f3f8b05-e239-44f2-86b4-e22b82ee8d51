import { Context, RequestHeader } from '@application/types';
import { queries } from '@tracker/graphQL';
import { FilenamesByTdoIds } from '@tracker/graphQL/responses';
import { callGQL } from '@util/api/graphQL/callGraphQL';

export async function getFileNameByTdos<ReqPayload, Data>({
  tdoIds,
  context,
  headers,
}: {
  tdoIds: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  // 100 is max limit for this query
  const limit = 100;
  let offset = 0;

  let result = await callGQL<FilenamesByTdoIds, ReqPayload, Data>(
    context,
    headers,
    queries.getFilesTemporalData,
    {
      tdoIds,
      limit,
      offset,
    }
  );

  // Count is NOT total count >:(
  let count = result.temporalDataObjects.count;
  let results = [...result.temporalDataObjects.records];

  while (count === limit) {
    offset += limit;
    // Offset 3000 is aiwares limit on this field
    if (offset > 3000) {
      context.log.error('ERROR: Hit limit on trying to get file names');
      break;
    }
    result = await callGQL<FilenamesByTdoIds, ReqPayload, Data>(
      context,
      headers,
      queries.getFilesTemporalData,
      {
        tdoIds,
        limit,
        offset,
      }
    );
    count = result.temporalDataObjects.count;
    results = [...results, ...result.temporalDataObjects.records];
  }

  return results;
}
