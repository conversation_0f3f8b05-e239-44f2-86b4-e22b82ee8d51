import GQLApi from '../../../../util/api/graphQL';
import env, { CloudType } from '../../../../env';
import {
  generateAzureReadOnlyCredential,
  generateAzureWritableCredential,
} from './azure';
import { ActionError, UnauthorizedError } from '@common/errors';
import {
  generateS3ReadOnlyCredential,
  generateS3WritableCredential,
} from './aws';
import NodeCache from 'node-cache';

export type PermissionType = 'writable' | 'readonly' | 'invalid';

export async function getCredential({
  gql,
  token,
  log,
  cloud,
  cache,
  noCache = false,
}: {
  gql: GQLApi;
  token: string;
  log: Logger;
  cloud: CloudType;
  cache?: NodeCache;
  noCache?: boolean;
}): Promise<{
  storage: string;
  storageUrl?: string;
  bucket?: string;
  region?: string;
  credential: {
    accessKeyId?: string;
    secretAccessKey?: string;
    sessionToken?: string;
    sasToken?: string;
  };
}> {
  const jwtResponse = await gql.verifyJWT(token);
  const jwt = jwtResponse.data?.verifyJWT;
  let tokenPermission: PermissionType = 'invalid';
  if (
    jwt?.jwtToken &&
    [
      env.trackerEngineId,
      ...(env.validWritableCredentialEngineIds || []),
    ].includes(jwt?.payload?.engineId)
  ) {
    log.info('valid jwt for the tracker engine');
    tokenPermission = 'writable';
  } else {
    const tokenResponse = await gql.validateToken(token);
    if (tokenResponse?.data?.validateToken?.token) {
      log.info('valid token for the user');
      tokenPermission = 'readonly';
    } else {
      log.error('jwtoken validation failed', jwtResponse.errors);
      log.error('token validation failed', tokenResponse.errors);
      throw new UnauthorizedError();
    }
  }
  if (cloud === 'azure') {
    if (tokenPermission === 'writable') {
      log.info('generate azure blob writable credential');
      const result = generateAzureWritableCredential(
        noCache ? undefined : cache
      );
      return result;
    } else if (tokenPermission === 'readonly') {
      log.info('generate azure blob readonly credential');
      const result = generateAzureReadOnlyCredential(
        noCache ? undefined : cache
      );
      return result;
    } else {
      log.error('token validation failed');
      throw new UnauthorizedError();
    }
  } else if (cloud === 'aws') {
    if (tokenPermission === 'writable') {
      log.info('generate aws s3 writable credential');
      const result = await generateS3WritableCredential(
        noCache ? undefined : cache
      );
      return result;
    } else if (tokenPermission === 'readonly') {
      log.info('generate aws s3 readonly credential');
      const result = await generateS3ReadOnlyCredential(
        noCache ? undefined : cache
      );
      return result;
    } else {
      log.error('token validation failed');
      throw new UnauthorizedError();
    }
  } else {
    log.error('unsupported cloud provider');
    throw new ActionError('unsupported cloud provider');
  }
}
