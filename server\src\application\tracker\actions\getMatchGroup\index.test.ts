import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { getSplicingTdoDetails } from '@tracker/actions/getSplicingTdoDetails';
import { createRequest, createResponse } from 'node-mocks-http';
import getMatchGroup from '.';
import { responses } from '@tracker/graphQL';
import { JobStatus, VideoSpliceDetails } from '../../../../../../types/tracker';

let cxt: Context<object, responses.getMatchGroup>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        temporalDataObject: {},
      })
  ),
}));

jest.mock('@tracker/actions/getSplicingTdoDetails', () => ({
  getSplicingTdoDetails: jest
    .fn()
    .mockImplementation((): { [key: string]: VideoSpliceDetails } => ({
      tdo1: {
        downloadUrl: 'mock-download-url-1',
        status: JobStatus.Complete,
      },
      tdo2: {
        downloadUrl: 'mock-download-url-1',
        status: JobStatus.Complete,
      },
    })),
}));

describe('Get Match Group Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        matchGroup: {
          id: 'id',
          name: 'name',
          eventId: 'eventId',
          modifiedDateTime: 'modifiedDateTime',
        },
        matchGroupId: '',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {
          matchGroupId: 'matchGroupId',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'matchGroupsSchemaId');
  });

  it('Successfully queries a matchgroup', async () => {
    const mockTimelineProject = {
      groups: [
        {
          id: 'group1',
          name: 'Group 1',
          tracklets: [
            {
              trackletId: 'tracklet1',
              tdoId: 'tdo1',
              startTimeMs: 1000,
              stopTimeMs: 2000,
            },
            {
              trackletId: 'tracklet2',
              tdoId: 'tdo2',
              startTimeMs: 3000,
              stopTimeMs: 4000,
            },
          ],
        },
        {
          id: 'group2',
          name: 'Group 2',
          tracklets: [
            {
              trackletId: 'tracklet3',
              tdoId: 'tdo3',
              startTimeMs: 5000,
              stopTimeMs: 6000,
            },
          ],
        },
      ],
      modifiedDateTime: '2024-05-03T21:57:48.320Z',
      modifiedUserId: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
    };
    const mockStructuredDataObject = {
      id: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
      data: {
        id: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
        name: 'Test Match Group',
        eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
        searches: [
          {
            id: '12345678-b1cb-4b93-bfea-0401861d8d40',
            searchName: 'testSearchName',
            referenceTrackletId: '7777',
          },
          {
            id: 'ABCDEFGH-b1cb-4b93-bfea-0401861d8d40',
            searchName: 'testSearchName',
            referenceTrackletId: '7777',
          },
        ],
        selectedTracklets: ['7777'],
        timelineProject: mockTimelineProject,
        generatedTimelines: [
          {
            id: 'timeline1',
            timeline: mockTimelineProject,
            name: 'Timeline Project 1',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user1',
            tdoId: 'tdo1',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
          {
            id: 'timeline2',
            timeline: mockTimelineProject,
            name: 'Timeline Project 2',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user2',
            tdoId: 'tdo2',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
        ],
      },
      createdDateTime: '2024-05-03T21:14:26.769Z',
      modifiedDateTime: '2024-05-03T21:57:48.320Z',
    };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        structuredDataObject: mockStructuredDataObject,
      })
    );

    const response = await getMatchGroup(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
      }
    );
    expect(response).not.toBeNull();
    expect(response.data.matchGroup).toStrictEqual({
      ...mockStructuredDataObject.data,
      id: mockStructuredDataObject.id,
    });
  });

  it('Does not attempt to query w/o fileId', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.req.params.matchGroupId = undefined;

    expect(async () => await getMatchGroup(cxt)).rejects.toThrow(
      'No matchGroupId provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Does not attempt to query w/o schemaId', async () => {
    cxt.req.params.matchGroupId = 'matchGroupId';
    cxt.cache.set('matchGroupsSchemaId', undefined);

    await expect(async () => await getMatchGroup(cxt)).rejects.toThrow(
      'schemaId not found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
