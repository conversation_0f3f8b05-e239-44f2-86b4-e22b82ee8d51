import React from 'react';
import { Skeleton } from '@mui/material';
import classNames from 'classnames';
import './Column.scss';

export const ASCENDING_SORT = 1;

export const NO_SORT = 0;

export const DESCENDING_SORT = -1;

export interface ColumnProps {
  Component?: React.ComponentType<ColumnProps>;
  index?: number;
  dataKey: string;
  minWidth?: number;
  width?: number;
  grow: number;
  title: string;
  sortOrder?: number;
  sortable?: boolean;
  onClick?: (event?: React.MouseEvent) => void;
  children?: React.ReactNode;
  setColRef?: (ref: HTMLDivElement) => void;
  loading?: boolean;
}

export const Column = (props: ColumnProps) => {
  const { dataKey, minWidth, width, children, loading, index, title, grow, sortOrder = 0, sortable, onClick = () => { }, setColRef, Component } = props;
  const sortOrderText = ASCENDING_SORT === sortOrder ? 'ascending' : DESCENDING_SORT === sortOrder ? 'descending' : 'none';
  const ariaLabel = `${title} column${sortable ? `, sorted in ${sortOrderText} order` : ''}`;

  return (
    <div
      className="column"
      role="columnheader"
      tabIndex={0}
      data-testid={`column-${index}`}
      key={dataKey}
      onClick={sortable ? onClick : undefined}
      onKeyUp={(e) => (e.keyCode === 13 && sortable ? onClick() : null)}
      aria-label={ariaLabel}
      aria-sort={sortOrderText}
      ref={setColRef}
      style={{
        width,
        minWidth,
        flexGrow: grow,
        cursor: sortable ? 'pointer' : 'initial',
      }}
    >
      {Component && <Component {...props} />}
      {!Component && (
        <>
          {loading && <Skeleton
            className="column__title"
            variant="rectangular"
            width={76}
            height={18}
          />}
          {!loading && <>
            <div className={classNames('column__title', { active: [ASCENDING_SORT, DESCENDING_SORT].includes(sortOrder) })} data-testid="column-title">
              {title || children}
            </div>
            <div data-testid="column-sort-container" className={classNames('column__sort', 'material-icons', { sortable })}>
              <div data-testid="column-up-arrow" className={classNames('column__sort-asc', 'material-icons', { active: sortOrder === ASCENDING_SORT })}>
                chevron_left
              </div>
              <div data-testid="column-down-arrow" className={classNames('column__sort-dsc', 'material-icons', { active: sortOrder === DESCENDING_SORT })}>
                chevron_right
              </div>
            </div>
          </>
          }
        </>
      )}
    </div>
  );
};

export default Column;
