import ls from 'localstorage-slim';
import { ReactNode, useEffect, useState } from 'react';
import { I18nProvider, LOCALES, LocaleTypes } from '@i18n';
import { AppContainer as LibAppContainer } from '@veritone/glc-react';

const OFFSET = 240;

const mapLanguage = (language: string) => ({
  en: 'en-US' as const,
  fr: 'fr-CA' as const,
})[language] || LOCALES.ENGLISH;

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const AppContainer = ({
  sideBarOffset,
  ...props
}: {
  readonly sideBarOffset?: boolean;
  readonly appBarOffset?: boolean;
  readonly topBarOffset?: boolean;
  readonly children?: ReactNode;
}) => {
  const [language, setLanguage] = useState<LocaleTypes>(
    mapLanguage(ls.get('language') ?? window.navigator.language)
  );

  useEffect(() => {
    /* Handle aiWARE preferredLanguage */
    const aiWARE = async () => {
      while (
        !window.aiware ||
        !window.aiware?.store.getState().auth?.user?.preferredLanguage
      ) {
        await delay(500);
      }

      const preferredLanguage = window.aiware.store.getState().auth?.user?.preferredLanguage;
      if (preferredLanguage) {
        setLanguage(mapLanguage(preferredLanguage));
      }

      window.aiware.on(
        'languageChange',
        function (
          error?: unknown,
          data?: {
            preferredLanguage: 'en' | 'fr';
          }
        ) {
          if (!error && data?.preferredLanguage) {
            const preferredLanguage = mapLanguage(data.preferredLanguage);
            setLanguage(preferredLanguage);
            ls.set('language', preferredLanguage);
          }
        }
      );
    };

    /* Run async function */
    aiWARE().then();
  }, []);

  return (
    <I18nProvider locale={language}>
      <LibAppContainer
        {...props}
        leftOffset={sideBarOffset ? OFFSET : 0}
      />
    </I18nProvider>
  );
};

export default AppContainer;
