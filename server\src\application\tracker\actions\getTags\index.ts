import { Context } from '../../../types';
import { Variables } from 'graphql-request';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { GraphQLError, ActionValidationError } from '@common/errors';

interface SearchVars extends Variables {
  search: {
    index: string[];
    type: string;
    limit: number;
    offset: number;
    sort?: { field: string; order: string }[];
    query: {
      operator: string;
      conditions: {
        operator: string;
        conditions: {
          operator: string;
          conditions: { field: string; operator: string; value: string }[];
        }[];
      }[];
    };
  };
}

const getTags = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.getTags>> | undefined
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const schemaId = cache.get<string>('eventsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    const searchVars: SearchVars = {
      search: {
        index: ['mine'],
        type: schemaId,
        limit: 1000,
        offset: 0,
        query: {
          operator: 'or',
          conditions: [],
        },
      },
    };

    const { searchMedia } = await callGQL<
      responses.searchMedia<{
        id: string;
        name: string;
        tags: string[];
        createdBy: string;
        createdByName: string;
        description: string;
        eventStartDate: string;
        eventEndDate: string;
        createdDateTime: string;
        modifiedDateTime: string;
      }>,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, searchVars);

    if (searchMedia) {
      const tagResults = Array.from(
        new Set(
          searchMedia.jsondata.results
            .flatMap((result) => result.tags || [])
            .filter(Boolean)
        )
      );

      const getTags = {
        tags: tagResults,
      };

      const new_data = Object.assign({}, data, {
        getTags: getTags,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e.message);
  }
};

export default getTags;
