import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { has } from 'lodash';

const mockStructuredDataObject = {
  id: 'a-matchgroup-id',
  data: {
    id: 'a-matchgroup-id',
    name: 'Test Match Group',
    eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
    selectedTracklets: ['trackletId1', 'trackletId2'],
  },
  createdDateTime: '2024-05-03T21:14:26.769Z',
  modifiedDateTime: '2024-05-03T21:57:48.320Z',
};

jest.mock('../../../application/credential/actions/util/azure', () => ({
  generateAzureReadOnlyCredential: jest.fn().mockImplementation(() => ({
    storage: 'blob',
    storageUrl: 'storageUrl',
    credential: { sasToken: 'readonlyCredential' },
  })),
}));

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }

      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'an-event-id',
            contentTemplates: [
              {
                id: 'id',
                sdo: {
                  id: 'sdoId',
                  data: {
                    id: 'eventId',
                    name: 'oldName',
                    tags: ['oldTag'],
                    createdBy: 'oldCreatedBy',
                    createdByName: 'oldCreatedByName',
                    description: 'oldDescription',
                    eventStartDate: 'oldEventStartDate',
                    eventEndDate: 'oldEventEndDate',
                  },
                },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getMatchGroup)) {
        let data = null;
        if (variables?.id === 'a-matchgroup-id') {
          data = mockStructuredDataObject;
        }
        if (variables?.id === 'a-empty-matchgroup-id') {
          data = {
            id: 'a-empty-matchgroup-id',
            data: {
              id: 'a-empty-matchgroup-id',
              name: 'no selected tracklets match group',
              eventId: 'eventId123',
            },
            createdDateTime: '2024-05-03T21:14:26.769Z',
            modifiedDateTime: '2024-05-03T21:57:48.320Z',
          };
        }
        return Promise.resolve({
          structuredDataObject: data,
        });
      }
      if (query.includes(queries.getTdoByFolder)) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            childTDOs: { records: [{ id: 'tdoId123' }] },
          },
        });
      }
      if (query.includes('temporalDataObject_3100000030_0')) {
        return Promise.resolve({
          '3100000030': {
            id: '3100000030',
            name: 'mock-file-name',
          },
        });
      }
      if (
        query.includes(queries.searchMedia) &&
        variables &&
        !has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [
                {
                  organizationId: '1',
                  referenceId: 'reference-tracklet-id-mock-1',
                  id: '3100000030',
                  score: 0.95,
                  label: 'person',
                  startTimeMs: 200,
                  stopTimeMs: 640,
                  tags: [
                    {
                      key: 'BodyAverage',
                    },
                    {
                      key: 'FaceFront',
                    },
                    {
                      key: 'FaceLeft',
                    },
                  ],
                },
                {
                  organizationId: '2',
                  referenceId: 'reference-tracklet-id-mock-2',
                  id: '3100000030',
                  score: 0.95,
                  label: 'person',
                  startTimeMs: 200,
                  stopTimeMs: 640,
                  tags: [
                    {
                      key: 'BodyAverage',
                    },
                    {
                      key: 'FaceFront',
                    },
                    {
                      key: 'FaceLeft',
                    },
                  ],
                },
                {
                  organizationId: '3',
                  referenceId: 'reference-tracklet-id-mock-3',
                  id: '3100000030',
                  score: 0.95,
                  label: 'person',
                  startTimeMs: 400,
                  stopTimeMs: 640,
                  tags: [
                    {
                      key: 'BodyAverage',
                    },
                    {
                      key: 'FaceFront',
                    },
                    {
                      key: 'FaceLeft',
                    },
                  ],
                },
              ],
              totalResults: 3,
              limit: 10,
              from: 0,
              to: 3,
            },
          },
        });
      }
      if (
        query.includes(queries.searchMedia) &&
        variables &&
        has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              aggregations: {
                recordingId: {
                  doc_count_error_upper_bound: 0,
                  sum_other_doc_count: 0,
                  buckets: [
                    {
                      key: '3100000030',
                      doc_count: 3,
                    },
                  ],
                },
              },
            },
          },
        });
      }
    }
  ),
}));

describe('get match group search', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('get match group selected tracklets', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/match-groups/a-matchgroup-id/selected-tracklets')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'a-matchgroup-id',
        schemaId: 'schemaId',
      }
    );
    // TODO: Add more fingerprintSearch tests
  });

  it('returns no results in response when no selected tracklets in match group', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/match-groups/a-empty-matchgroup-id/selected-tracklets')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);
    expect(resp.body).toEqual({
      matchGroupId: 'a-empty-matchgroup-id',
      matchGroupName: 'no selected tracklets match group',
      eventId: 'eventId123',
    });
  });

  it('gives 404 error when no match group found', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/match-groups/a-unknow-matchgroup-id/selected-tracklets')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
