import qs from 'qs';
import { createAppSlice } from '../../createAppSlice';
import { EventsState, EventState, FilesState, FileState } from './types';
import {
  GetEventResponse,
  SearchEventsResponse,
  SearchFilesResponse,
  UpdateEventResponse,
  UpdateFileResponse,
} from '@shared-types/responses';
import {
  GetEventsParams,
  GetFilesParams,
  SearchEventsQueryParams,
  SearchFilesQueryParams,
} from '@shared-types/requests';
import {
  updateFile as updateFileSearch,
  updateEvent as updateEventSearch,
  createEvent,
} from '@store/modules/upload/slice';
import type { Event, File } from '@shared-types/tracker';
import { PayloadAction } from '@reduxjs/toolkit';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import { AlertLevel, createSnackNotification } from '@components/common';
import { getLocalStorage, getUpdatedEventsLocalStorage, getUpdatedFilesLocalStorage, setLocalStorage, UpdatedEventLocalStorage, UpdatedFileLocalStorage } from '@utility/localStorage';
import { isNumber, isString } from 'lodash';
import ls from 'localstorage-slim';
import { UpdateEventParams } from '../event/types';


export interface HomeSliceState {
  events: EventsState;
  files: FilesState;
  eventSelected: EventState;
  fileSelected: FileState;
  sort?: { sort: string; dir: string };
}

const initialState: HomeSliceState = {
  events: {
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  files: {
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  eventSelected: {
    event: undefined,
    status: 'idle',
    error: '',
  },
  fileSelected: {
    file: undefined,
    status: 'idle',
    error: '',
  },
  sort: undefined,
};

export const homeSlice = createAppSlice({
  name: 'home',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();

    return {
      setSort: create.reducer(
        (
          state,
          action: PayloadAction<{ dir: string; sort: string } | undefined>
        ) => {
          state.sort = action.payload;
        }
      ),
      setEventPagination: create.reducer(
        (
          state,
          action: PayloadAction<{ currentPage: number; pageSize: number }>
        ) => {
          state.events.currentPage = action.payload.currentPage;
          state.events.pageSize = action.payload.pageSize;
        }
      ),
      getEvents: createHttpThunk(
        async (
          {
            currentPage,
            pageSize,
            dir,
            sort,
          }: GetEventsParams & { isPoll?: boolean },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const searchEventsQuery: SearchEventsQueryParams = {
            pageSize,
            currentPage,
            sortBy: sort ? sort : 'eventStartDate',
            sortDirection: dir ? dir : 'desc',
          };
          const response = await http.get<SearchEventsResponse>(signal)(
            `/events/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix getSTate type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.events.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const { results } = action.payload;
            const resultsIds = results.map((event) => event.id);
            const { value: localStoragePendingCreateEvents } = getLocalStorage(
              'pendingCreateEvents'
            ) || { value: [] };
            const remainPendingEvents = localStoragePendingCreateEvents.filter(
              (event: Event) => !resultsIds.includes(event.id)
            );
            setLocalStorage('pendingCreateEvents', remainPendingEvents, 1);
            const updatedEventsLocalStorage = getUpdatedEventsLocalStorage(action.payload.results);

            state.events = {
              ...action.payload,
              // Use the updated event name from local storage, if available, instead of querying Elasticsearch
              results: action.payload.results.map(
                (event) => {
                  const updatedEvent = updatedEventsLocalStorage?.find((e) => e.value.eventId === event.id);
                  if (updatedEvent) {
                    return { ...event, name: updatedEvent.value.eventName };
                  }
                  return event;
                }
              ),
              pageSize: action.payload.pageSize ?? 10,
              currentPage: action.payload.currentPage ?? 1,
              totalCount: action.payload.totalCount ?? 10,
              totalPages: action.payload.totalPages ?? 10,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (action.error.message === 'Aborted') {
              state.events.status = 'idle';
            } else {
              createSnackNotification(
                AlertLevel.Error,
                'Get events failed',
                action.error.message
              );
              state.events.status = 'failure';
            }
          },
        }
      ),
      getEventById: createHttpThunk(
        async (
          {
            eventId,
          }: {
            eventId: string;
            isPoll?: boolean;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetEventResponse>(signal)(
            `/event/${eventId}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.eventSelected.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            state.eventSelected = {
              event: action.payload.event,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              if (action.error.message !== 'Aborted') {
                createSnackNotification(
                  AlertLevel.Error,
                  'Get event failed',
                  action.error.message
                );
                state.eventSelected.status = 'failure';
              } else {
                state.eventSelected.status = 'idle';
              }
            }
          },
        }
      ),
      updateEvent: create.reducer((state, action: PayloadAction<Event>) => {
        for (let i = 0; i < state.events.results.length; i++) {
          if (state.events.results[i].id === action.payload.id) {
            state.events.results[i] = {
              ...state.events.results[i],
              ...action.payload,
            };
            break;
          }
        }
      }),
      updateEventById: createHttpThunk(
        async ({ eventId, ...data }: UpdateEventParams, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
            dispatch,
          } = thunkAPI;
          const response = await http.patch<UpdateEventResponse>(signal)(
            `/event/${eventId}`,
            data,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          if (response.status === 200) {
            dispatch(updateEventSearch(response.data.event));
          }
          return response.data.event;
        },
        {
          pending: (state) => {
            state.eventSelected.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.eventSelected.status = 'idle';
            state.eventSelected.event = action.payload;
            const index = state.events.results.findIndex(item => item.id === action.payload.id);
            if (index !== -1) {
              state.events.results[index] = {
                ...state.events.results[index],
                ...action.payload,
              };
            }

            createSnackNotification(
              AlertLevel.Success,
              'Success',
              'Updated event successfully'
            );

            // Store updated eventId and event name in localStorage
            const { eventId, name } = action.meta.arg;
            let updatedEvents: UpdatedEventLocalStorage[] | null = ls.get('updatedEvents');
            if (Array.isArray(updatedEvents)) {
              updatedEvents = updatedEvents.filter(
                (item) => isString(item.value.eventId) && isString(item.value.eventName) && isNumber(item.expiry)
              );
            } else {
              updatedEvents = [];
            }

            // Add the new updated event to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: {
                eventId,
                eventName: name || '',
              },
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };

            const existingUpdatedEvent = updatedEvents.findIndex((e) => e.value.eventId === item.value.eventId);
            if (existingUpdatedEvent > -1) {
              updatedEvents.splice(
                existingUpdatedEvent - 1,
                1
              );
            }

            updatedEvents.push(item);
            // Store the updated array back in localStorage
            ls.set('updatedEvents', updatedEvents);
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Update event failed',
                action.error.message
              );
              state.eventSelected.status = 'failure';
            } else {
              state.eventSelected.status = 'idle';
            }
          },
        }
      ),
      setFilesPagination: create.reducer(
        (
          state,
          action: PayloadAction<{ currentPage: number; pageSize: number }>
        ) => {
          state.files.currentPage = action.payload.currentPage;
          state.files.pageSize = action.payload.pageSize;
        }
      ),
      getFiles: createHttpThunk(
        async (
          {
            currentPage,
            pageSize,
            sort,
            dir,
          }: GetFilesParams & { isPoll?: boolean },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          let sortByColumn = 'createdTime';
          switch (sort) {
            case 'fileName':
              sortByColumn = 'veritone-file.filename';
              break;
            case 'uploadDate':
              sortByColumn = 'createdTime';
              break;
            default:
              sortByColumn = sort ? sort : 'createdTime';
          }

          const searchFilesQuery: SearchFilesQueryParams = {
            pageSize,
            currentPage,
            sortBy: sortByColumn,
            sortDirection: dir ? dir : 'desc',
          };
          const response = await http.get<SearchFilesResponse>(signal)(
            `/files/${qs.stringify(searchFilesQuery, {
              addQueryPrefix: true,
            })}`,
            // Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.files.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const updatedFilesLocalStorage = getUpdatedFilesLocalStorage(action.payload.results);

            state.files = {
              ...action.payload,
              // Use the updated fileName from local storage, if available, instead of querying Elasticsearch
              results: action.payload.results.map(
                (file) => {
                  const updatedFile = updatedFilesLocalStorage?.find((f) => f.value.fileId === file.id);
                  if (updatedFile) {
                    return { ...file, fileName: updatedFile.value.fileName };
                  }
                  return file;
                }
              ),
              pageSize: action.payload.pageSize,
              currentPage: action.payload.currentPage,
              totalCount: action.payload.totalCount,
              totalPages: action.payload.totalPages,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (action.error.message === 'Aborted') {
              state.files.status = 'idle';
            } else {
              createSnackNotification(
                AlertLevel.Error,
                'Get files failed',
                action.error.message
              );
              state.files.status = 'failure';
            }
          },
        }
      ),
      updateFileById: createHttpThunk(
        async (
          {
            fileId,
            fileName,
          }: {
            fileId: string;
            fileName: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
            dispatch,
          } = thunkAPI;
          const response = await http.patch<UpdateFileResponse>(signal)(`/file/${fileId}`,
            {
              name: fileName
            },
            {
            // Fix getState type
            Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
          });

          if (response.status === 200) {
            dispatch(updateFileSearch(response.data.file));
          }

          return response.data.file;
        },
        {
          pending: (state) => {
            state.fileSelected.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.fileSelected.status = 'idle';
            state.fileSelected.file = action.payload;
            const index = state.files.results.findIndex(item => item.id === action.payload.id);
            if (index !== -1) {
              state.files.results[index] = {
                ...state.files.results[index],
                ...action.payload,
              };
            }

            createSnackNotification(
              AlertLevel.Success,
              'Success',
              'Updated file name successfully'
            );

            // Store updated fileId and fileName in localStorage
            const { fileId, fileName } = action.meta.arg;
            let updatedFiles: UpdatedFileLocalStorage[] | null = ls.get('updatedFiles');
            if (Array.isArray(updatedFiles)) {
              updatedFiles = updatedFiles.filter(
                (item) => isString(item.value.fileId) && isString(item.value.fileName) && isNumber(item.expiry)
              );
            } else {
              updatedFiles = [];
            }

            // Add the new updated file to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: {
                fileId,
                fileName
              },
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };

            const existingUpdatedFile = updatedFiles.findIndex((f) => f.value.fileId === item.value.fileId);
            if (existingUpdatedFile > -1) {
              updatedFiles.splice(
                existingUpdatedFile - 1,
                1
              );
            }
            updatedFiles.push(item);
            // Store the updated array back in localStorage
            ls.set('updatedFiles', updatedFiles);
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Update file name failed',
                action.error.message
              );
              state.fileSelected.status = 'failure';
            } else {
              state.fileSelected.status = 'idle';
            }
          },
        }
      ),
      poll: createHttpThunk(
        async (
          {
            type,
            currentPage,
            pageSize,
            sort,
            dir,
          }: {
            type: 'events' | 'files';
            currentPage: string | number;
            dir?: string;
            sort?: string;
            pageSize: string | number;
          },
          thunkAPI
        ) => {
          const { signal, dispatch } = thunkAPI;
          let dispatchGetEvents: DispatchPromise;
          let dispatchGetFiles: DispatchPromise;
          const pollInterval = setInterval(() => {
            if (type === 'events') {
              dispatchGetEvents = dispatch(
                getEvents({ currentPage, pageSize, dir, sort, isPoll: true })
              );
            }
            if (type === 'files') {
              dispatchGetFiles = dispatch(
                getFiles({ currentPage, pageSize, dir, sort, isPoll: true })
              );
            }
          }, 7500);

          signal.addEventListener('abort', () => {
            clearInterval(pollInterval);
            dispatchGetEvents?.abort();
            dispatchGetFiles?.abort();
          });
        }
      ),
      updateEventSelected: create.reducer(
        (state, action: PayloadAction<Event | undefined>) => {
          state.eventSelected.event = action.payload;
        }
      ),
      updateFileSelected: create.reducer(
        (state, action: PayloadAction<File | undefined>) => {
          state.fileSelected.file = action.payload;
        }
      ),
    };
  },
  extraReducers: (builder) => {
    builder
      .addCase(createEvent.pending, (state) => {
        state.events.status = 'loading';
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        const event = action.payload.event;
        state.events.status = 'idle';
        if (event) {
          const { value: pendingCreateEvents } = getLocalStorage(
            'pendingCreateEvents'
          ) || { value: [] };
          pendingCreateEvents.push(event);
          setLocalStorage('pendingCreateEvents', pendingCreateEvents, 1);
        }
      });
  },
  selectors: {
    selectEvents: (state) => state.events,
    selectFiles: (state) => state.files,
    selectEventSelected: (state) => state.eventSelected,
    selectFileSelected: (state) => state.fileSelected,
    selectSort: (state) => state.sort,
  },
});

export const {
  setSort,
  setEventPagination,
  getEvents,
  getEventById,
  getFiles,
  updateEventById,
  updateFileById,
  setFilesPagination,
  updateEvent,
  poll,
  updateEventSelected,
  updateFileSelected,
} = homeSlice.actions;

export const {
  selectEvents,
  selectFiles,
  selectEventSelected,
  selectFileSelected,
  selectSort,
} = homeSlice.selectors;

export const { actions: homeActions, reducer: homeReducer } = homeSlice;
