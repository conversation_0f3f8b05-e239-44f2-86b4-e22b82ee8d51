import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import getSignedWritableUrl from '../getSignedWritableUrl';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import * as ResTypes from '../../../../../../types/responses';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'a-uuid-v4-string'),
}));

let cxt: Context<object, ResTypes.UploadFilePayloadResponse>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        getSignedWritableUrl: { url: 'url', unsignedUrl: 'unsignedUrl' },
      })
  ),
}));

describe('Get Signed Writable URL', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        fileName: 'fileName',
        currentTime: '',
        eventId: '',
        fileType: '',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully gets signed writable URL', async () => {
    const response = await getSignedWritableUrl(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { key: 'fileName-a-uuid-v4-string', expiresInSeconds: 43200 }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no fileName', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.fileName = undefined;

    expect(async () => await getSignedWritableUrl(cxt)).rejects.toThrowError(
      'No fileName provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
