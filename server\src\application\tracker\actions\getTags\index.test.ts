import NodeCache from 'node-cache';
import searchEvents from '../searchEvents';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import getTags from '.';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        searchMedia: {
          jsondata: {
            results: [
              {
                id: 'id',
                name: 'name',
                tags: ['tag1', 'tag2'],
                createdBy: 'createdBy',
                createdByName: 'createdByName',
                description: 'description',
                eventStartDate: 'eventStartDate',
                eventEndDate: 'eventEndDate',
                createdDateTime: 'createdDateTime',
                modifiedDateTime: 'modifiedDateTime',
              },
              {
                id: 'id2',
                name: 'name2',
                tags: ['tag3', 'tag4'],
                createdBy: 'createdBy2',
                createdByName: 'createdByName',
                description: 'description2',
                eventStartDate: 'eventStartDate2',
                eventEndDate: 'eventEndDate2',
                createdDateTime: 'createdDateTime2',
                modifiedDateTime: 'modifiedDateTime2',
              },
              {
                id: 'id3',
                name: 'name3',
                tags: ['tag3', 'tag5'],
                createdBy: 'createdBy3',
                createdByName: 'createdByName',
                description: 'description3',
                eventStartDate: 'eventStartDate3',
                eventEndDate: 'eventEndDate3',
                createdDateTime: 'createdDateTime3',
                modifiedDateTime: 'modifiedDateTime3',
              },
            ],
            totalResults: 0,
            limit: 10,
            from: 0,
            to: 10,
            timestamp: 1,
          },
        },
      })
  ),
}));

describe('Search Tags', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        query: {
          pageSize: 50,
          currentPage: 1,
          query: 'query',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('eventsSchemaId', 'eventsSchemaId');
  });

  it('Query all tags', async () => {
    const response = await getTags(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          limit: 1000,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [],
          },
          type: 'eventsSchemaId',
        },
      }
    );

    const expectedTagsResult = {
      // filters duplicates and adds tags from all events
      getTags: { tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5'] },
    };

    expect(response).not.toBeNull();
    expect(response?.data).toMatchObject(expectedTagsResult);
  });
});
