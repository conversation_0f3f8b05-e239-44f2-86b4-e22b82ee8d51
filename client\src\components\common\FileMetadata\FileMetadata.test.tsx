import { render, screen } from '@testing-library/react';
import FileMetadata from './FileMetadata';
import { I18nProvider, LOCALES } from '@i18n';

interface FileMetadataProps {
  fileName: string;
  uploadDate: string;
  location: string;
  fileType: string;
  fileSize: number;
  length: number;
}

const mockFile: FileMetadataProps = {
  fileName: 'test.mp4',
  uploadDate: '2024-06-24T19:30:01.787Z',
  location: 'Location A',
  fileType: 'video/mp4',
  fileSize: 1713050,
  length: 3600,
};

describe('FileMetadata Component', () => {
  test('renders FileMetadata component', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <FileMetadata {...mockFile} />
      </I18nProvider>
    );
    expect(screen.getByText('File Name:')).toBeInTheDocument();
    expect(screen.getByText('test.mp4')).toBeInTheDocument();
    expect(screen.getByText('Upload Date:')).toBeInTheDocument();
    expect(screen.getByText('File GPS Location:')).toBeInTheDocument();
    expect(screen.getByText('Location A')).toBeInTheDocument();
    expect(screen.getByText('File Type:')).toBeInTheDocument();
    expect(screen.getByText('video/mp4')).toBeInTheDocument();
    expect(screen.getByText('File Size:')).toBeInTheDocument();
    expect(screen.getByText('1.6 Mb')).toBeInTheDocument();
    expect(screen.getByText('Video Length:')).toBeInTheDocument();
    expect(screen.getByText('01:00:00')).toBeInTheDocument();
  });
});
