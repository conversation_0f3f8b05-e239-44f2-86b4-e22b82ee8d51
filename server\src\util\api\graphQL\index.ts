import { fetchGraph<PERSON><PERSON><PERSON> } from './callGraphQL';

class GQLApi {
  constructor(
    private readonly endpoint: string,
    private token: string | undefined,
    private readonly veritoneAppId: string
  ) {
    this.endpoint = endpoint;
    this.token = token;
    this.veritoneAppId = veritoneAppId;
  }

  setToken(token: string) {
    this.token = token;
  }

  async verifyJWT(token: string) {
    const query = `
      mutation verifyJWT($token: String!) {
        verifyJWT(jwtToken: $token) {
          jwtToken
          payload
        }
      }
    `;

    return await fetchGraph<PERSON><PERSON><PERSON><{
      verifyJWT: {
        jwtToken: string;
        payload: {
          contentOrganizationId: number;
          engineId: string;
        };
      } | null;
    }>({
      endpoint: this.endpoint,
      query,
      variables: { token },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
  }

  async validateToken(token: string) {
    const query = `
      mutation validateToken($token: String!) {
        validateToken(token: $token) {
          token
        }
      }
    `;

    return await fetchGrap<PERSON><PERSON><PERSON><PERSON><{
      validateToken: {
        token: string;
      } | null;
    }>({
      endpoint: this.endpoint,
      query,
      variables: { token },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
  }
}

export default GQLApi;
