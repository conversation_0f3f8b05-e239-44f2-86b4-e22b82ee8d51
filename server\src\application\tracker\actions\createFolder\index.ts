import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import * as ReqTypes from '../../../../../../types/requests';

const createFolder = async <
  ReqPayload,
  Data extends Partial<
    responses.getMe & responses.getParentFolderId & ReqTypes.CreateEventPayload
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          responses.getMe & responses.createFolder & ReqTypes.CreateEventPayload
        >
    >
  | undefined
> => {
  const { data, req, log, cache: _cache } = context;
  const headers = { Authorization: req.headers.authorization };

  const { name, userId, parentFolderId, description = '' } = data;

  if (!name || !userId || !parentFolderId) {
    throw new ActionError(
      `Missing required data: ${[!name ? 'name' : '', !userId ? 'userId' : '', !parentFolderId ? 'parentFolderId' : ''].filter(Boolean).join(', ')}`
    );
  }

  try {
    const { createFolder } = await callGQL<
      responses.createFolder,
      ReqPayload,
      Data
    >(context, headers, queries.createFolder, {
      name,
      description,
      userId,
      parentFolderId,
    });

    if (createFolder) {
      const new_data = Object.assign({}, data, { createFolder: createFolder });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createFolder;
