import { GetFileSearchResultsResponse } from '@shared-types/responses';
import { Event, File as TrackerFile, Attributes, TrackletBoundingBox } from '@shared-types/tracker';
import { ApiStatus } from '@store/types';

export interface TrackletsApi {
  data: {
    trackId: number;
    startMs: number;
    endMs: number;
    thumbnailPath: string;
  }[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}
export interface TrackletsState extends TrackletsApi {
  status: ApiStatus;
  error?: string;
}

export interface FileResultsState extends GetFileSearchResultsResponse {
  apiStatus: ApiStatus;
  error?: string;
}

export interface FileDeletionState {
  id: string;
  status: ApiStatus;
  message: string;
  error?: string;
}

export interface FileState extends TrackerFile {
  apiStatus: ApiStatus;
  error?: string;
}

export interface EventState {
  data?: Event;
  apiStatus: ApiStatus;
  error?: string;
}

export interface TrackletFileState {
  file?: TrackerFile;
  apiStatus: ApiStatus;
  error?: string;
}

export interface BoundingBoxesState {
  data: TrackletBoundingBox[];
  apiStatus: ApiStatus;
  error?: string;
}

export interface SelectedAttributes {
  person: Partial<{
    [attributeGroup in keyof Attributes]: {
      [attribute: string]: boolean;
    };
  }>;
  vehicle: Partial<{
    [attributeGroup in keyof Attributes]: {
      [attribute: string]: boolean;
    };
  }>;
}
export type CurrentTab = 'people' | 'vehicles';

export type TrackletType = 'person' | 'vehicle';
