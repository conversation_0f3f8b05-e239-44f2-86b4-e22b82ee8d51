import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { omit } from 'lodash';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'matchGroupId'),
}));

jest.useFakeTimers().setSystemTime(new Date('2024-04-01T00:00:00.000Z'));
jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.createStructuredData)) {
        return Promise.resolve({
          createStructuredData: {
            id: 'matchGroupId',
            data: {
              id: '20c8cda9-7a91-42ef-8598-235537ea40e5',
              eventId: '0eed0212-613e-4947-87e0-2712a1bbc4a1',
              name: 'Match Group',
              searches: [],
              selectedTracklets: [],
            },
            schemaId: 'schemaId',
            createdDateTime: 'created Datetime',
            modifiedDateTime: 'modified Datetime',
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: '69db4c93-453c-4b3e-ad65-ddb1df09a468',
            name: 'A Yu-5',
            description: null,
            treeObjectId: '4da6d497-bd19-4917-8a57-de293eba8a52',
            createdDateTime: '2024-11-06T08:20:33.638Z',
            modifiedDateTime: '2024-11-06T08:20:33.638Z',
            parent: { organization: { id: '7682' } },
            contentTemplates: [
              {
                id: 'ee870d09-c806-40ca-a6dd-7ad6fcac5cb4',
                sdo: {
                  id: 'c252eb1a-67d2-4ba8-97dc-9e08231bc276',
                  schemaId: 'ce8e2183-729d-48dd-9480-a17131b56935',
                  data: {},
                },
              },
            ],
          },
        });
      }
    }
  ),
}));

describe('patch event', () => {
  it('patch event', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const mockPostData: object = {
      eventId: '0eed0212-613e-4947-87e0-2712a1bbc4a1',
      name: 'Match Group',
      searches: [],
      selectedTracklets: [],
    };

    const resp = await supertest(expressApp)
      .post('/api/v1/match-groups')
      .set('Authorization', 'Bearer validToken')
      .send(mockPostData)
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMe
    );

    expect(callGQL).toHaveBeenLastCalledWith(
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'matchGroupId',
        schemaId: 'schemaId',
        data: {
          id: 'matchGroupId',
          ...mockPostData,
        },
      }
    );
  });

  it('patch event is not successful', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const mockPostData: object = {
      eventId: '0eed0212-613e-4947-87e0-2712a1bbc4a1',
      name: 'Match Group',
      searches: [],
      selectedTracklets: [],
    };

    const missingFields = ['eventId', 'name'];

    for (const missingField of missingFields) {
      const updatedPostData = omit(mockPostData, missingField);
      const resp = await supertest(expressApp)
        .post('/api/v1/match-groups')
        .set('Authorization', 'Bearer validToken')
        .send(updatedPostData)
        .expect(400);
    }
  });
});
