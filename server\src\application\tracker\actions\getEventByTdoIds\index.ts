import { Context } from '../../../types';
import { responses } from '../../graphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';
const batchQuerySize = 10;
const getEventByTdoIds = async <ReqPayload, Data extends { tdoIds: string[] }>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Partial<responses.FolderByTdoIds>>> => {
  const { data, req, log: _log } = context;
  const headers = { Authorization: req.headers.authorization };

  const tdoIds = data?.tdoIds ?? [];
  if (tdoIds.length === 0) {
    throw new ActionError('No tdoIds provided');
  }

  try {
    const validData = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];
      batch.forEach((tdoId, index) => {
        updateQuery.push(
          `temporalDataObject_${tdoId}_${index}:temporalDataObject(id: "${tdoId}") {
            id
            folders {
              id
              name
            }
          }`
        );
      });
      const query = `query { ${updateQuery.join('\n')} }`;
      // The forth parameter makes this request error tolerant - if a TDO does not exist, it will NOT throw, it will return the rest from the batch
      const temporalDataObject = await callGQL<
        responses.getFileTemporalData,
        ReqPayload,
        Data
      >(context, headers, query, {}, true);

      const result = Object.values(temporalDataObject);
      validData.push(...result.filter((res) => res?.id));
    }
    const newData: {
      [key: string]: { eventId: string; eventName: string };
    } = {};
    for (const data of validData) {
      if (!newData[data.id]) {
        newData[data.id] = {
          eventId: data?.folders?.[0]?.id,
          eventName: data?.folders?.[0]?.name,
        };
      }
    }

    return { ...context, data: newData };
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
};

export default getEventByTdoIds;
