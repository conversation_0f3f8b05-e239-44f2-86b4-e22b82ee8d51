import Redis from 'ioredis';
import { Request, Response } from 'express';
import { Schema } from 'joi';
import NodeCache from 'node-cache';
import GQLApi from '../util/api/graphQL';
import RedisWrapper from 'src/redisWrapper';

export type RequestWithMeta<Payload> = Request & {
  metadata: {
    correlationId: string;
  };
  body: Payload;
  headers: { authorization: string };
};

export type RequestHeader = Record<string, string>;

export interface ProvisionOrgRequest {
  orgName: string;
}

export type TableRow = Record<string, unknown>;

export interface Context<ReqPayload, Data> {
  req: RequestWithMeta<ReqPayload>;
  res: Response;
  log: Logger;
  validations?: { schema: Schema; validationData: unknown }[];
  cache: NodeCache;
  redis?: Redis;
  redisWrapper?: RedisWrapper;
  queries?: {
    [queryName: string]: (row?: TableRow) => Promise<TableRow>;
  };
  data: Data;
  returnCode?: number;
  gql?: GQLApi;
  requestIds?: string[];
}
