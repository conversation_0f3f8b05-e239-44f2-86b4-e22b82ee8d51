import { Context, RequestHeader } from '@application/types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { responses } from '../../graphQL';
const batchQuerySize = 10;

export async function getTdos<ReqPayload, Data>({
  tdoIds,
  context,
  headers,
}: {
  tdoIds: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  if (tdoIds.length === 0) {
    throw new ActionError('No tdoIds provided');
  }

  try {
    const validData = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];
      batch.forEach((tdoId, index) => {
        updateQuery.push(
          `temporalDataObject_${tdoId}_${index}:temporalDataObject(id: "${tdoId}"){
            id
            name
            assets {
              records {
                assetType
                signedUri
              }
            }
            folders {
              id
              name
            }
            thumbnailUrl
            primaryAsset(assetType: "media") {
              signedUri
            }
            thumbnailAssets: assets(assetType: "thumbnail-sprite") {
              records {
                id
                name
                contentType
                signedUri
                details
              }
            }
            streams {
              uri
              protocol
            }
            jobs {
              records {
                name
                status
                tasks {
                  records {
                    status
                    engine {
                      name
                    }
                  }
                }
              }
            }
            primaryAsset(assetType: "media") {
              signedUri
            }
            streams {
              uri
              protocol
            }
          }`
        );
      });

      const query = `query { ${updateQuery.join('\n')} }`;

      // The forth parameter makes this request error tolerant - if a TDO does not exist, it will NOT throw, it will return the rest from the batch
      const temporalDataObject = await callGQL<
        responses.getFileTemporalData,
        ReqPayload,
        Data
      >(context, headers, query, {}, true);

      const result = Object.values(temporalDataObject);
      validData.push(...result.filter((res) => res?.id));
    }
    const tdos: {
      [key: string]: {
        id: string;
        name: string;
        assets?: { records: { assetType: string; signedUri: string }[] };
        eventId: string;
        eventName: string;
        thumbnailUrl: string;
        thumbnailAssets?: {
          records: {
            id: string;
            name: string;
            contentType: string;
            signedUri: string;
            details: Record<string, unknown>;
          }[];
        };
        streams: {
          uri: string;
          protocol: string;
        }[];
        jobs: { records: { name: string; status: string }[] };
      };
    } = {};
    for (const data of validData) {
      if (!tdos[data.id]) {
        tdos[data.id] = {
          id: data?.id,
          name: data?.name,
          assets: data?.assets,
          eventId: data?.folders?.[0]?.id,
          eventName: data?.folders?.[0]?.name,
          thumbnailUrl: data?.thumbnailUrl,
          thumbnailAssets: data?.thumbnailAssets,
          streams: data?.streams,
          jobs: data?.jobs,
        };
      }
    }

    return tdos;
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
}
