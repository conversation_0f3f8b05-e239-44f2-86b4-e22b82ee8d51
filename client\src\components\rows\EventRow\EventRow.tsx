import './EventRow.scss';
import cn from 'classnames';
import { RowProps } from '@components/common/Table/Table';
import { Event } from '@shared-types/tracker';
import { toLocalTime } from '@utility/convert';
import { constant } from 'lodash';
import { AlertLevel, createSnackNotification } from '@components/common';
import { I18nTranslate } from '@i18n';

interface AdditionalProps {
  isPendingDeletion: (id: string) => boolean;
}

export function EventRow({
  colData,
  rowData,
  rowIndex,
  onRowClick,
  onDoubleRowClick,
  selectedId,
  additionalProps,
}: RowProps<Event, AdditionalProps>) {
  const { id } = rowData;
  const { isPendingDeletion } = additionalProps ?? {
    isPendingDeletion: constant(false),
  };

  const renderCell = ({ dataKey }: { dataKey: string }) => {
    switch (dataKey) {
      case 'name':
        return (
          <div 
            role="cell" 
            className="event-row__cell-name"
            data-testid="event-row-name"
          >
            {rowData[dataKey]}
          </div>
        );
      case 'eventStartDate':
        return (
          <div role="cell" className="event-row__cell-name">
            {I18nTranslate.TranslateDate(toLocalTime(rowData[dataKey]))}
          </div>
        );
    }
  };

  const eventRowOnClick = () => {
    if (isPendingDeletion(id)) {
      createSnackNotification(
        AlertLevel.Warning,
        'Warning',
        'This event is pending deletion and cannot be selected.'
      );
    } else {
      onRowClick?.(rowData);
    }
  };

  const eventRowOnDoubleClick = () => {
    if (isPendingDeletion(id)) {
      createSnackNotification(
        AlertLevel.Warning,
        'Warning',
        'This event is pending deletion and cannot be selected.'
      );
    } else {
      onDoubleRowClick?.(rowData);
    }
  };

  return (
    <div
      role="row"
      className={cn('event-row', { selected: selectedId === id })}
    >
      <div
        className={cn('event-row__row', { pending: isPendingDeletion(id) })}
        data-testid={`event-row-${rowIndex}-${rowData.id}`}
        onClick={eventRowOnClick}
        onDoubleClick={eventRowOnDoubleClick}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="event-row__cell"
            data-testid={`event-row-cell-${rowIndex}-${id}-${index}`}
            key={`EventRowCell-${id}-${rowIndex}-${index}-${dataKey}`}
            style={{
              flexGrow: grow,
              width,
              minWidth: width ?? minWidth,
            }}
          >
            {renderCell({ dataKey })}
          </div>
        ))}
      </div>
    </div>
  );
}

export default EventRow;
