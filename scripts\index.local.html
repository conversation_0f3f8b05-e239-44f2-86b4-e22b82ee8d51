<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link href='https://fonts.googleapis.com/css?family=Nunito Sans' rel='stylesheet'>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Veritone Track</title>
  <script>
    // if (!location.href.includes('local.veritone.com')) {
    window['config'] = {}
    // }
  </script>
  <script type="module">
    const { aiwareJSPath, aiwareJSVersion } = window.config;
    document
      .getElementsByTagName('head')[0]
      .insertAdjacentHTML(
        'afterbegin',
        `<link rel="stylesheet" type="text/css" href="${aiwareJSPath + aiwareJSVersion}/js-core.umd.min.css">`
      );
    const aiwareJSScript = document.createElement('script');
    aiwareJSScript.setAttribute(
      'src',
      `${aiwareJSPath + aiwareJSVersion}/js-core.esm.js`
    );
    aiwareJSScript.setAttribute('type', 'module');
    aiwareJSScript.setAttribute('nonce', 'NGINX_CSP_NONCE');
    document.head.appendChild(aiwareJSScript);

    import {
      initChatWithSupport,
      chatWithSupport,
    } from 'https://get.aiware.com/veritone-support/latest/2/index.js';

    initChatWithSupport().then(() => {
      window.chatWithSupport = chatWithSupport;
    });
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
  <!-- jss-insertion-point -->
</head>

<body class="dark">
  <div id="root"> </div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>