import {
  Event,
  File,
  GeneratedTimelineWithMatchGroup,
} from '@shared-types/tracker';
import {
  DeleteEventResponse,
  DeleteMatchGroupSearchResponse,
  GetMatchGroupsResponse,
  SearchFilesResponse,
  GetTagsResponse,
} from '@shared-types/responses';
import { ApiStatus } from '@store/types';

// Remove during api integration and replace with root type response
export interface FilesApi {
  data: File[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface FilesState extends SearchFilesResponse {
  status: ApiStatus;
  error?: string;
}

export interface FileState {
  file?: File;
  status: ApiStatus;
  error?: string;
}

// Remove during api integration and replace with root type response
export interface EventState extends Event {
  status: ApiStatus;
  error?: string;
  matchGroupsCount?: number;
  filesCount?: number;
}

export interface MatchGroupsState extends GetMatchGroupsResponse {
  status: ApiStatus;
  error?: string;
}

export interface EventDeletionState extends DeleteEventResponse {
  status: ApiStatus;
  error?: string;
}

export interface MatchGroupDeletionState
  extends DeleteMatchGroupSearchResponse {
  status: ApiStatus;
  error?: string;
}

export interface UpdateEventParams {
  eventId: string;
  name?: string;
  tags?: Array<string>;
  description?: string;
  eventStartDate?: string;
  eventEndDate?: string;
}

export interface GetFilesParams {
  eventId?: string;
  query?: string;
  sort?: string;
  dir?: string;
  page: number;
  limit: number;
}

export interface GetMatchGroupsParams {
  eventId: string;
  query?: string;
  sort?: string;
  dir?: string;
  page?: number;
  limit?: number;
}

export interface TagsState extends GetTagsResponse {
  status: ApiStatus;
  error?: string;
}

export interface GeneratedTimelineState {
  results: Array<GeneratedTimelineWithMatchGroup>;
  status: ApiStatus;
  error?: string;
}

export interface DeleteGeneratedTimelineState {
  id: string;
  status: ApiStatus;
  error?: string;
}
