import { Context } from '../../types';
import { queries, responses } from '../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { GraphQLError } from '@common/errors';

const getMe = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & responses.getMe>> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  try {
    const response = await callGQL<responses.me, ReqPayload, Data>(
      context,
      headers,
      queries.getMe
    );

    const new_data = Object.assign({}, data, {
      userId: response.me.id,
      userEmail: response.me.email,
      firstName: response.me.firstName,
      lastName: response.me.lastName,
      userOrganizationId: response.me.organizationId,
    });
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default getMe;
