import cn from 'classnames';
import { Chip } from '@mui/material';
import { AlertLevel, createSnackNotification } from '@components/common';
import { RowProps } from '@components/common/Table/Table';
import './PendingUploadFileRow.scss';
import { toLocalTime } from '@utility/convert';
import { PendingUploadFile } from '@store/modules/upload/slice';
import { I18nTranslate } from '@i18n';

export function PendingUploadFileRow({
  colData,
  rowData,
  rowIndex,
  selectedId,
}: RowProps<PendingUploadFile>) {
  const { id } = rowData;

  const renderCell = ({ dataKey }: { dataKey: string }) => {
    switch (dataKey) {
      case 'fileName':
        return (
          <div role="cell" className="pending-file-row__cell-fileName">
            {rowData[dataKey]}
          </div>
        );
      case 'status': {
        const status = `${rowData[dataKey]}`;
        return (
          <div role="cell" className="pending-file-row__cell-status">
            <Chip
              className={cn('status', rowData[dataKey])}
              label={status.toUpperCase()}
              variant="outlined"
            />
          </div>
        );
      }
      case 'uploadDate': {
        const uploadDate = `${rowData[dataKey]}`;
        return (
          <div role="cell" className="pending-file-row__cell-uploadDate">
            {I18nTranslate.TranslateDate(toLocalTime(uploadDate))}
          </div>
        );
      }
    }
  };

  const fileRowOnClick = () => {
    createSnackNotification(
      AlertLevel.Warning,
      'Warning',
      'File has not yet processed.'
    );
  };

  const fileRowOnDoubleClick = () => {
    createSnackNotification(
      AlertLevel.Warning,
      'Warning',
      'File has not yet processed.'
    );
  };

  return (
    <div role="row" data-testid="pending-file-row" className={cn('pending-file-row', { selected: selectedId === id })}>
      <div
        data-testid={`pending-file-row-${rowData.id}`}
        className={cn('pending-file-row__row')}
        onClick={fileRowOnClick}
        onDoubleClick={fileRowOnDoubleClick}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="pending-file-row__cell"
            data-testid={`pending-file-row-cell-${rowIndex}-${id}-${index}`}
            key={`FileRowCell-${id}-${rowIndex}-${id}-${index}`}
            style={{
              flexGrow: grow,
              width,
              minWidth: width ?? minWidth,
            }}
          >
            {renderCell({ dataKey })}
          </div>
        ))}
      </div>
    </div>
  );
}

export default PendingUploadFileRow;
