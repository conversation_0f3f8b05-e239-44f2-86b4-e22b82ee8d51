import { Box, Chip } from '@mui/material';
import './DetectedAttributes.scss';
import { Attributes } from '@shared-types/tracker';
import { addSpacesToCamelCase } from '@utility/convert';
import { getAttributeValue } from '@utility/getAttributeValue';

const DetectedAttributes = ({ showTitle, attributes }: { showTitle?: boolean; attributes?: Attributes }) => (
  <div className="detected-attributes__main-container">
    {showTitle && <div className="detected-attributes__header-text">
      Detected Attributes
    </div>}
    <div className="detected-attributes__scrollable-div main__scrollbar">
      {attributes ? Object.keys(attributes).map((attribute, index) => (
        <div className="detected-attributes__accordion-container" key={index}>
          <div className="detected-attributes__name">{addSpacesToCamelCase(attribute)}</div>
          <div className="detected-attributes__chips" data-testid={`detected-attributes__chips-${attribute}`}>
            {attributes?.[attribute]?.map((attributeRecord, index) => (
              <Chip
                data-testid="detected-attributes__chip"
                key={index}
                className="detected-attributes__chip"
                label={
                  <span
                    data-testid={`detected-attributes__chip-label-${attribute}-${attributeRecord.value}`}
                  >
                    {getAttributeValue(attributeRecord.value)}
                  </span>
                }
              />
            ))}
          </div>
        </div>
      )) : (
        <Box data-testid="detected-attributes-no-attributes">No attributes detected</Box>
      )}
    </div>
  </div>
);

export default DetectedAttributes;
