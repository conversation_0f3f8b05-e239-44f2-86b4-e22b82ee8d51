import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';

const createContentTemplate = async <
  ReqPayload,
  Data extends Partial<
    responses.createFolder & responses.createStructuredData
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          responses.createFolder &
            responses.createStructuredData &
            responses.createFolderContentTemplate
        >
    >
  | undefined
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { createFolder, createStructuredData } = data;
  if (!createFolder || !createStructuredData) {
    throw new ActionError('No folder or structured data provided');
  }

  try {
    const { createFolderContentTemplate } = await callGQL<
      responses.createFolderContentTemplate,
      ReqPayload,
      Data
    >(context, headers, queries.createFolderContentTemplate, {
      folderId: createFolder.id,
      sdoId: createStructuredData.id,
      schemaId: createStructuredData.schemaId,
    });

    if (createFolderContentTemplate) {
      const new_data = Object.assign({}, data, { createFolderContentTemplate });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createContentTemplate;
