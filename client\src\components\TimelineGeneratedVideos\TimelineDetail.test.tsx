import { Provider } from 'react-redux';
import TimelineDetail from './TimelineDetail';
import configureMockStore from 'redux-mock-store';
import { downloadFromUrl } from '@utility/downloadFromUrl';
import { fireEvent, render, screen } from '@testing-library/react';
import {
  GeneratedTimelineWithMatchGroup,
  JobStatus,
} from '@shared-types/tracker';
import { I18nProvider, LOCALES } from '@i18n';

const mockStore = configureMockStore();
const store = mockStore({});

const mockTimeline: GeneratedTimelineWithMatchGroup = {
  id: '1',
  name: 'Timeline 1',
  createdDateTime: '2022-01-01T00:00:00Z',
  createdUserId: 'user1',
  createdUserName: 'user name',
  tdoId: 'tdo1',
  resolution: '1920x1080',
  outputFormat: 'mp4',
  videoLengthMs: 543289,
  videoSizeBytes: 5000000,
  status: JobStatus.Complete,
  matchGroup: {
    id: 'matchGroup1',
    name: 'Match Group 1',
  },
};

jest.mock('@utility/downloadFromUrl', () => ({
  downloadFromUrl: jest.fn(),
}));

describe('TimelineDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders TimelineDetail component', () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineDetail
            timeline={mockTimeline}
            setSelectedTimeline={jest.fn()}
          />
        </I18nProvider>
      </Provider>
    );

    expect(
      screen.getByRole('heading', {
        name: mockTimeline.name,
      })
    ).toBeInTheDocument();
    expect(screen.getByText('COMPLETE')).toBeInTheDocument();
    expect(
      screen.getByText(`Timeline Video Created by: user name`)
    ).toBeInTheDocument();
    expect(screen.getByText(/file size:/i)).toBeInTheDocument();
    expect(screen.getByText(/4.8 Mb/i)).toBeInTheDocument();
    expect(screen.getByText(/video length:/i)).toBeInTheDocument();
    expect(screen.getByText(/00:09:03/i)).toBeInTheDocument();
    expect(screen.getByText(mockTimeline.matchGroup.name)).toBeInTheDocument();
  });

  Object.values(JobStatus).forEach((status) => {
    test(`Timeline Detail JobStatus ${status}`, () => {
      render(
        <Provider store={store}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <TimelineDetail
              timeline={{ ...mockTimeline, status }}
              setSelectedTimeline={jest.fn()}
            />
          </I18nProvider>
        </Provider>
      );
      expect(
        screen.getByRole('heading', {
          name: mockTimeline.name,
        })
      ).toBeInTheDocument();
      expect(screen.getByText(status.toUpperCase())).toBeInTheDocument();
      expect(screen.getByTestId('timeline-status')).toHaveClass(status);
    });
  });

  test('should call downloadFromUrl when the button is clicked and has downloadUrl', () => {
    // Render the component
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineDetail
            timeline={{ ...mockTimeline, downloadUrl: 'www.test.com' }}
            setSelectedTimeline={jest.fn()}
          />
        </I18nProvider>
      </Provider>
    );

    // Find the button by its test ID
    const downloadButton = screen.getByTestId('timeline-download-button');

    // Simulate a click event on the button
    fireEvent.click(downloadButton);

    // Assert that downloadFromUrl was called
    expect(downloadFromUrl).toHaveBeenCalled();
  });

  test('should call downloadFromUrl when the button is clicked and has downloadUrl', () => {
    // Render the component
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineDetail
            timeline={mockTimeline}
            setSelectedTimeline={jest.fn()}
          />
        </I18nProvider>
      </Provider>
    );

    // Find the button by its test ID
    const downloadButton = screen.getByTestId('timeline-download-button');

    // Simulate a click event on the button
    fireEvent.click(downloadButton);

    // Assert that downloadFromUrl was not called
    expect(downloadFromUrl).not.toHaveBeenCalled();
  });
});
