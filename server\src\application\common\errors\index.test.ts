import BaseError from './BaseError';
import ActionError from './ActionError';
import ApiError from './ApiError';
import DatabaseError from './DatabaseError';
import ForbiddenError from './ForbiddenError';
import GraphQLError from './GraphQLError';
import InternalServerError from './InternalServerError';
import NotFoundError from './NotFoundError';
import UnauthorizedError from './UnauthorizedError';
import ValidationError from './ValidationError';
import { AxiosError } from 'axios';

interface ErrorClassWithDefaultMessageInfo {
  class: typeof ForbiddenError | typeof UnauthorizedError;
  hasDefaultMessage: string;
  name: string;
}

interface ErrorClassWithoutDefaultMessageInfo {
  class:
    | typeof ActionError
    | typeof DatabaseError
    | typeof GraphQLError
    | typeof InternalServerError
    | typeof NotFoundError
    | typeof ValidationError;
  name: string;
}

type ErrorClassInfo =
  | ErrorClassWithDefaultMessageInfo
  | ErrorClassWithoutDefaultMessageInfo;

const errorClasses: ErrorClassInfo[] = [
  {
    class: ActionError,
    name: 'ActionError',
  },
  {
    class: DatabaseError,
    name: 'DatabaseError',
  },
  {
    class: ForbiddenError,
    hasDefaultMessage: 'Forbidden',
    name: 'ForbiddenError',
  },
  {
    class: GraphQLError,
    name: 'GraphQLError',
  },
  {
    class: InternalServerError,
    name: 'InternalServerError',
  },
  { class: NotFoundError, name: 'NotFoundError' },
  {
    class: UnauthorizedError,
    hasDefaultMessage: 'Unauthorized',
    name: 'UnauthorizedError',
  },
  {
    class: ValidationError,
    name: 'ValidationError',
  },
];

const defaultMessage = 'An unknown error has occurred';

describe('Error Classes', () => {
  errorClasses.forEach((errorClass) => {
    const { class: ErrorClass, name } = errorClass;
    describe(name, () => {
      it(`should set the message and name correctly when given a string`, () => {
        const error = new ErrorClass('Custom message');
        expect(error.message).toBe('Custom message');
        expect(error.name).toBe(name);
      });

      it(`should set the message and name correctly when given an Error object`, () => {
        const error = new ErrorClass(new Error('Custom message'));
        expect(error.message).toBe('Custom message');
        expect(error.name).toBe(name);
      });

      it('should handle null as error message', () => {
        const error = new ErrorClass(null as any);
        expect(error.message).toBe(defaultMessage);
        expect(error.name).toBe(name);
      });

      it('should set the default message when given an unexpected type', () => {
        const error = new ErrorClass(123 as any);
        expect(error.message).toBe(defaultMessage);
        expect(error.name).toBe(name);
      });

      it('should handle undefined as error message', () => {
        const error = new ErrorClass(undefined as any);
        if ('hasDefaultMessage' in errorClass) {
          expect(error.message).toBe(errorClass.hasDefaultMessage);
        } else {
          expect(error.message).toBe(defaultMessage);
        }
        expect(error.name).toBe(name);
      });

      it('should handle non-Error object as error message', () => {
        const error = new ErrorClass({ message: 'Object error' } as any);
        expect(error.message).toBe('Object error');
        expect(error.name).toBe(name);
      });

      if ('hasDefaultMessage' in errorClass) {
        it('should set the default message with class that allows defaultMessage', () => {
          const error = new errorClass.class();
          expect(error.message).toBe(errorClass.hasDefaultMessage);
          expect(error.name).toBe(name);
        });
      }
    });
  });
});

describe('BaseError', () => {
  it('should set the message and name correctly when given a string', () => {
    const error = new BaseError('Test error', 'BaseError');
    expect(error.message).toBe('Test error');
    expect(error.name).toBe('BaseError');
  });

  it('should set the message and name correctly when given an Error object', () => {
    const error = new BaseError(new Error('Test error'), 'BaseError');
    expect(error.message).toBe('Test error');
    expect(error.name).toBe('BaseError');
  });

  it('should set the default message when given an unexpected type', () => {
    const error = new BaseError(123 as any, 'BaseError');
    expect(error.message).toBe('An unknown error has occurred');
    expect(error.name).toBe('BaseError');
  });

  it('should capture the stack trace', () => {
    const error = new BaseError('Test error', 'BaseError');
    expect(error.stack).toBeDefined();
  });

  it('should handle empty Error object as error message', () => {
    const error = new BaseError(new Error(), 'BaseError');
    expect(error.message).toBe('');
    expect(error.name).toBe('BaseError');
  });

  it('should handle null as error message', () => {
    const error = new BaseError(null as any, 'BaseError');
    expect(error.message).toBe('An unknown error has occurred');
    expect(error.name).toBe('BaseError');
  });

  it('should handle undefined as error message', () => {
    const error = new BaseError(undefined as any, 'BaseError');
    expect(error.message).toBe('An unknown error has occurred');
    expect(error.name).toBe('BaseError');
  });

  it('should handle non-Error object as error message', () => {
    const error = new BaseError(
      { message: 'Object error' } as any,
      'BaseError'
    );
    expect(error.message).toBe('Object error');
    expect(error.name).toBe('BaseError');
  });
});

describe('ApiError', () => {
  const name = 'ApiError';

  it('should handle AxiosError with response data', () => {
    const axiosError = {
      isAxiosError: true,
      response: {
        data: { message: 'Axios error response message' },
      },
      message: 'Axios error message',
    } as AxiosError;

    const error = new ApiError(axiosError);
    expect(error.message).toBe(JSON.stringify(axiosError?.response?.data));
    expect(error.name).toBe(name);
  });

  it('should handle AxiosError without response data', () => {
    const axiosError = {
      isAxiosError: true,
      response: undefined,
      message: 'Axios error message',
    } as AxiosError;

    const error = new ApiError(axiosError);
    expect(error.message).toBe(axiosError.message);
    expect(error.name).toBe(name);
  });

  it('should handle generic Error', () => {
    const genericError = new Error('Generic error message');
    const error = new ApiError(genericError);
    expect(error.message).toBe(genericError.message);
    expect(error.name).toBe(name);
  });

  it('should handle null as error message', () => {
    const error = new ApiError(null as any);
    expect(error.message).toBe(defaultMessage);
    expect(error.name).toBe(name);
  });

  it('should handle undefined as error message', () => {
    const error = new ApiError(undefined as any);
    expect(error.message).toBe(defaultMessage);
    expect(error.name).toBe(name);
  });
});
