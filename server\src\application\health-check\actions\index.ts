import send from '../actions/send';
import sendError from '../actions/sendError';
import validateToken from '../actions/validateToken';
import validateRequest from '../actions/validateRequest';
import validateJwtToken from '../actions/validateJwtToken';
import actionWrapper from '@util/actionWrapper';

export default {
  send: actionWrapper(send),
  sendError: actionWrapper(sendError),
  validateToken: actionWrapper(validateToken),
  validateRequest: actionWrapper(validateRequest),
  validateJwtToken: actionWrapper(validateJwtToken),
};
