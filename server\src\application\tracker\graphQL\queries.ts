export const deleteFolderContentTemplate = `
  mutation deleteFolderContentTemplate($contentTemplateId: ID!) {
    deleteFolderContentTemplate(id: $contentTemplateId) {
      id
      message
    }
  }
`;

export const deleteStructuredData = `
  mutation deleteStructuredData($sdoId: ID!, $schemaId: ID!) {
    deleteStructuredData(input: { id: $sdoId, schemaId: $schemaId }) {
      id
      message
    }
  }
`;

export const deleteFolder = `
  mutation deleteFolder($folderId: ID!) {
    deleteFolder(input: { id: $folderId, orderIndex: 0 }) {
      id
      message
    }
  }
`;

export const deleteTemporalData = `
  mutation deleteTemporalData($tdoId: ID!) {
    deleteTDO(id: $tdoId) {
      id
      message
    }
  }
`;

export const deleteMatchGroupSearch = `
  mutation deleteMatchGroupSearch($tdoId: ID!) {
    deleteTDO(id: $tdoId) {
      id
      message
    }
  }
`;

export const getFolder = `
  query getFolder($folderId: ID!) {
    folder(id: $folderId) {
      id
      name
      description
      treeObjectId
      createdDateTime
      modifiedDateTime
      parent {
        organization {
          id
        }
      }
      contentTemplates {
        id
        sdo {
          id
          schemaId
          data
        }
      }
    }
  }
`;

export const getFileTemporalData = `
  query getFileTemporalData($tdoId: ID!) {
    temporalDataObject(id: $tdoId) {
      id
      name
      folders {
        id
        name
        parent {
          organizationId
        }
      }
      jobs {
        records {
          name
          status
        }
      }
      details
      description
      createdDateTime
      modifiedDateTime
      createdBy
      primaryAsset(assetType: "media") {
        signedUri
      }
      thumbnailAssets: assets(assetType: "thumbnail-sprite") {
        records {
          id
          name
          contentType
          signedUri
          details
        }
      }
      streams {
        uri
        protocol
      }
      assets {
        records {
          assetType
          signedUri
        }
      }
      thumbnailUrl
    }
  }
`;

export const getFilesTemporalData = `
query getFilesTemporalData($tdoIds: [ID!]!, $offset: Int, $limit: Int) {
    temporalDataObjects(ids: $tdoIds, offset: $offset, limit: $limit) {
      records {
        id
        name
      }
      count
      offset
      limit
    }
  }
`;

export const createFolder = `
  mutation createFolder(
    $name: String!
    $description: String!
    $userId: ID!
    $parentFolderId: ID!
  ) {
    createFolder(
      input: {
        name: $name
        description: $description
        userId: $userId
        parentId: $parentFolderId
    }
  ) {
      id
      name
      description
    }
  }
`;

export const createStructuredData = `
  mutation createStructuredData($id: ID!, $schemaId: ID!, $data: JSONData!) {
    createStructuredData(input: { id: $id, schemaId: $schemaId, data: $data }) {
      id
      data
      schemaId
      createdDateTime
      modifiedDateTime
    }
  }
`;

export const createFolderContentTemplate = `
   mutation createContentTemplate(
    $folderId: ID!,
    $sdoId: ID!,
    $schemaId: ID!,
  ) {
    createFolderContentTemplate(input: {
      folderId: $folderId
      sdoId: $sdoId
      schemaId: $schemaId
    }) {
      id
      createdDateTime
      modifiedDateTime
    }
  }
`;

export const getSignedWritableUrl = `
  query getSignedWritableUrl($key: String, $expiresInSeconds: Int) {
    getSignedWritableUrl(key: $key, expiresInSeconds: $expiresInSeconds) {
      url
      getUrl
      unsignedUrl
      key
      bucket
      expiresInSeconds
    }
  }
`;

export const createTemporalData = `
  mutation createTemporalData($input: CreateTDO!) {
    createTDO(input: $input) {
      id
      name
      status
      details
      createdDateTime
      modifiedDateTime
      createdBy
    }
  }
`;

export const searchMedia = `
  query searchMedia($search: JSONData!) {
    searchMedia(search: $search) {
      jsondata
    }
  }
`;

export const lookupLatestSchemaId = `
  query fetchLatestSchema($registryId: ID!) {
    dataRegistry(id: $registryId) {
      publishedSchema {
        id
      }
    }
  }
`;

export const getMe = `
  query me {
    me {
      id
      email
      firstName
      lastName
      organizationId
      permissions {
        records {
          description
          id
          name
        }
      }
      acls {
        objectId
        objectType
        access {
          owner
        }
      }
    }
  }
`;

export const createIngestJob = (useCluster: boolean) => `
  mutation (
    $tdoId: ID!
    ${useCluster ? '$clusterId: ID!' : ''}
    $glcIngestorEngineId: ID!
    $glcIngestorPayload: JSONData!
    $trackerEngineId: ID!
    $trackerEnginePayload: JSONData!
    $outputWriterEngineId: ID!
  ) {
    createJob(input: {
      targetId: $tdoId,
      ${useCluster ? 'clusterId: $clusterId,' : ''}
      name: "Track Ingest Job",
      tasks: [
        {
          engineId: $glcIngestorEngineId,
          payload: $glcIngestorPayload,
          executionPreferences: {
            priority: -20

          }
          ioFolders: [
            {
              referenceId: "ingestOutputFolder"
              mode: chunk
              type: output
            }
          ]
        },
        {
          engineId: $trackerEngineId,
          payload: $trackerEnginePayload,
          executionPreferences: {
            maxEngines: 20
            parentCompleteBeforeStarting: true
            priority: -5
          }
          ioFolders: [
            {
              referenceId: "trackInputFolder"
              mode: chunk
              type: input
            },
            {
              referenceId: "trackOutputFolder"
              mode: chunk
              type: output
            }
          ]
        },
        {
          engineId:  $outputWriterEngineId,
            executionPreferences: {
              parentCompleteBeforeStarting: true
              priority: -10
            }
            ioFolders: [
              {
                referenceId: "ow-input-track"
                mode: chunk
                type: input
              }
            ]
        },
      ],
      routes: [
        {  ## ingest --> Track V2
          parentIoFolderReferenceId: "ingestOutputFolder"
          childIoFolderReferenceId: "trackInputFolder"
          options: {}
        },
        {  ## Track V2 --> output writer
          parentIoFolderReferenceId: "trackOutputFolder"
          childIoFolderReferenceId: "ow-input-track"
          options: {}
        },
    ]
  }) {
      id
      targetId
      status
  }
}`;

export const searchStructuredDataObjects = (
  sdoSearchVariables?: Record<string, string>
) => `
      query searchStructuredDataObject(
        $schemaId: ID!
        $limit: Int
        $offset: Int
        $sort: OrderDirection!
        $sortType: StructuredDataOrderByField!
      ) {
        structuredDataObjects(
          schemaId: $schemaId
          limit: $limit
          offset: $offset
          orderBy: { field: $sortType, direction: $sort }
          ${
            sdoSearchVariables && Object.entries(sdoSearchVariables).length > 0
              ? `filter: { ${Object.entries(sdoSearchVariables).map(([key, value]) => `${key}: {eq: "${value}"}`)} }`
              : ''
          }
        ) {
          records {
            id
            data
            createdDateTime
            modifiedDateTime
          }
          count
          offset
          limit
          orderBy {
            field
            direction
          }
        }
      }
    `;

export const getMatchGroup = `
query GetMatchGroup(
  $id: ID!
  $schemaId: ID!
) {
  structuredDataObject(
    id: $id
    schemaId: $schemaId
  ) {
      id
      data
      createdDateTime
      modifiedDateTime
  }
}
`;

export const updateFolder = `
  mutation updateFolder($folderId: ID!, $name: String!) {
    updateFolder(input: {
      id: $folderId,
      name: $name,
    }) {
      id
      name
    }
  }
`;

export const updateTdo = `
 mutation updateNameTDO($tdoId: ID!, $details: JSONData, $name: String){
    updateTDO( input: {
      id: $tdoId,
      name: $name,
      details: $details
    })
    {
      id
      name
      details
    }
  }
`;

export const getTdoByFolder = `
  query folder(
    $id: ID!
    $limit: Int
    $offset: Int
  ) {
    folder(id: $id) {
        id
        childTDOs(
          limit: $limit
          offset: $offset
        ) {
          records {
            id
            name
          }
        }
    }
  }
`;

export const getRootFolder = `
  query getRootFolder {
    rootFolders(type: cms) {
      id
      name
      ownerId
    }
  }
`;

export const createRootFolder = `
  mutation {
    createRootFolders(rootFolderType: cms) {
      id
      name
      ownerId
    }
  }
`;

export const engineResults = `
  query engineResults($tdoId: ID, $engineIds: [ID!], $startOffsetMs: Int, $stopOffsetMs: Int) {
    engineResults(tdoId: $tdoId, engineIds:$engineIds, startOffsetMs: $startOffsetMs, stopOffsetMs: $stopOffsetMs){
      records{
        jsondata
      }
    }
  }
`;

export const createSpliceJob = (useCluster: boolean) => `
  mutation createSpliceJob($engineId: ID!, ${useCluster ? '$clusterId: ID!,' : ''} $payload: JSONData!) {
    createJob(input: {
      target: {}
      ${useCluster ? 'clusterId: $clusterId' : ''}
      tasks: [
        {
          engineId: $engineId,
          payload: $payload
        }
      ]
      ## Empty routes here is intentional
      ## Per Stefan Minkov "If that is not passed in, we treat it as legacy (non-v3) job"
      routes: []
    }) {
      targetId
      id
      status
    }
  }
`;
