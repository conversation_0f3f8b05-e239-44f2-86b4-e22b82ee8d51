import NodeCache from 'node-cache';
import Redis from 'ioredis';
import supertest from 'supertest';
import { has } from 'lodash';
import { Variables } from 'graphql-request';
import createTrackerApp from '@tracker/index';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import createExpressApp from '@application/express';
import { Context, RequestHeader } from '@application/types';
import createConfig from '../../../config';
import env from '../../../env';
import { queries, responses } from '../../tracker/graphQL';
import RedisWrapper from '../../../redisWrapper';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes('fetchLatestSchema')) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes('getFileTemporalData')) {
        return Promise.resolve({
          temporalDataObject: {
            id: 'a-file-id',
            details: {
              veritoneFile: {
                createdByName: 'createdByName',
                fileName: 'fileName',
                duration: 1000,
              },
              createdDateTime: new Date(),
            },
            folders: [
              {
                id: 'folder-event-id',
                name: 'folderEventName',
              },
            ],
            thumbnailUrl: 'thumbnailUrl',
            primaryAsset: {
              signedUri: 'signedUri',
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        !has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              aggregations: {
                recordingId: {
                  doc_count_error_upper_bound: 0,
                  sum_other_doc_count: 0,
                  buckets: [],
                },
              },
            },
          },
        });
      }
      if (query.includes('createStructuredData')) {
        return Promise.resolve({
          createStructuredData: {
            id: 'id',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
    }
  ),
}));

jest.mock('../../tracker/actions/getMatchGroups', () =>
  jest.fn(async (context: Context<object, responses.getMatchGroups>) => ({
    ...context,
    data: {
      ...context.data,
      matchGroups: {
        pagination: {
          pageSize: 50,
          currentPage: 1,
          totalPages: 10,
          totalCount: 500,
        },
        sort: {
          field: 'field',
          direction: 'direction',
        },
        eventId: 'eventId',
        searchResults: [
          {
            id: 'id',
            name: 'name',
            eventId: 'eventId',
            searches: [],
            selectedTracklets: [],
            modifiedDateTime: 'modifiedDateTime',
            timelineProject: {
              groups: [],
            },
            generatedTimelines: [],
          },
        ],
      },
    },
  }))
);

jest.mock('../../tracker/actions/getJsonData', () => ({
  fetchJsonData: jest.fn(),
}));

describe('delete file', () => {
  it('delete file', async () => {
    const config = await createConfig({ env });
    const redisWrapper = new RedisWrapper(new Redis(), config.log);

    redisWrapper.event = {
      ...redisWrapper.event,
      set: jest.fn(),
      get: jest.fn().mockResolvedValue({
        id: 'folder-event-id',
        name: 'folderEventName',
      }),
    };

    const trackerApp = createTrackerApp({
      log: config.log,
      cache: new NodeCache(),
      redisWrapper: redisWrapper,
    });
    const expressApp = await createExpressApp({
      config: { ...config, trackerApp },
    });

    const resp = await supertest(expressApp)
      .delete('/api/v1/file/a-file-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteTemporalData,
      {
        tdoId: 'a-file-id',
      }
    );

    expect(redisWrapper.event.get).toHaveBeenCalledWith(
      'folder-event-id',
      'mock-userOrganizationId'
    );

    expect(redisWrapper.event.set).toHaveBeenCalledWith(
      'folder-event-id',
      'mock-userOrganizationId',
      expect.anything()
    );
  });

  it('cannot delete file w/o fileId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/file')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
