const devEnv = {
  stylelint: {
    extends: [],
    rules: {},
  },
};

// plugins
devEnv.stylelint.extends.push('stylelint-config-standard-scss');
// devEnv.stylelint.extends.push('stylelint-config-recommended-scss');

// rules
devEnv.stylelint.rules['at-rule-no-unknown'] = null;
devEnv.stylelint.rules['at-rule-empty-line-before'] = null;
devEnv.stylelint.rules['no-invalid-position-at-import-rule'] = null;
devEnv.stylelint.rules['selector-class-pattern'] = null;
devEnv.stylelint.rules['declaration-empty-line-before'] = null;
devEnv.stylelint.rules['rule-empty-line-before'] = null;
devEnv.stylelint.rules['color-function-notation'] = null;
devEnv.stylelint.rules['property-no-vendor-prefix'] = null;
devEnv.stylelint.rules['alpha-value-notation'] = null;
devEnv.stylelint.rules['declaration-block-no-redundant-longhand-properties'] =
  null;
devEnv.stylelint.rules['shorthand-property-no-redundant-values'] = null;
devEnv.stylelint.rules['font-family-name-quotes'] = null;
devEnv.stylelint.rules['function-url-quotes'] = null;
devEnv.stylelint.rules['selector-pseudo-class-no-unknown'] = null;
devEnv.stylelint.rules['keyframes-name-pattern'] = null;
devEnv.stylelint.rules['scss/at-extend-no-missing-placeholder'] = null;
devEnv.stylelint.rules['scss/no-global-function-names'] = null;
devEnv.stylelint.rules['scss/double-slash-comment-empty-line-before'] = null;
devEnv.stylelint.rules['scss/dollar-variable-pattern'] = null;
devEnv.stylelint.rules['import-notation'] = null;

module.exports = devEnv.stylelint;
