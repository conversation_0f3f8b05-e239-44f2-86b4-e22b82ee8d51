{"name": "tracker-client", "private": true, "version": "2.4.0", "type": "module", "repository": "https://github.com/veritone/tracker-app", "license": "MIT", "scripts": {"start:dev": "vite", "build": "tsc && vite build", "lint": "concurrently \"yarn run lint:ts\" \"yarn run lint:tsc\"", "lint:ts": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:tsc": "tsc -p tsconfig.json --noEmit --checkJs false", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix && tsc --noEmit", "test": "TZ=UTC jest", "test:update": "TZ=UTC jest -u", "test:watch": "TZ=UTC jest --watch", "test:coverage": "TZ=UTC jest --coverage", "cy:open": "cypress open", "cy:run": "cypress run", "cy:run:headless": "cypress run --headless", "prepare": "cd ../ && yarn && yarn husky install", "release": "release-it"}, "dependencies": {"@date-io/luxon": "^3.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.3", "@mui/material": "^6.1.3", "@mui/styles": "^6.1.3", "@mui/x-date-pickers": "^7.20.0", "@reduxjs/toolkit": "^2.3.0", "@sentry/browser": "^8.34.0", "@veritone/glc-react": "^1.2.8", "@veritone/glc-redux": "^1.0.13", "axios": "^1.8.2", "classnames": "^2.5.1", "localforage": "^1.10.0", "localstorage-slim": "^2.7.1", "lodash": "^4.17.21", "luxon": "^3.5.0", "match-sorter": "^6.3.4", "mui-nested-menu": "^3.4.0", "qs": "^6.13.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-intl": "^6.8.0", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "redux-api-middleware": "^3.2.1", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "release-it": "^19.0.3", "uuid": "^10.0.0"}, "devDependencies": {"@badeball/cypress-cucumber-preprocessor": "^21.0.2", "@cspell/eslint-plugin": "^8.18.1", "@cypress/webpack-preprocessor": "^6.0.2", "@eslint/compat": "^1.2.8", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@fontsource/dosis": "^5.1.0", "@fontsource/material-icons": "^5.1.0", "@fontsource/material-icons-outlined": "^5.1.0", "@fontsource/nunito-sans": "^5.1.0", "@redux-saga/testing-utils": "^1.1.5", "@svgr/core": "^8.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@types/html-webpack-template": "^6.0.9", "@types/identity-obj-proxy": "^3.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/luxon": "^3.4.2", "@types/qs": "^6.9.18", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-redux": "^7.1.34", "@types/redux-api-middleware": "^3.2.7", "@types/redux-logger": "^3.0.13", "@types/redux-mock-store": "^1.0.6", "@types/uuid": "^10.0.0", "@types/video-react": "^0.15.8", "@vitejs/plugin-react": "^4.3.2", "babel-jest": "^29.7.0", "concurrently": "^9.1.2", "cypress": "^14.4.1", "cypress-file-upload": "^5.0.8", "eslint": "^9.23.0", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-formatjs": "^5.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-testing-library": "^7.1.1", "fs": "^0.0.1-security", "git-commit-info": "^2.0.2", "globals": "^16.0.0", "html-webpack-template": "^6.2.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jest-transform-stub": "^2.0.0", "redux-mock-store": "^1.5.4", "sass": "^1.79.5", "stylelint": "^16.10.0", "stylelint-config-standard": "^36.0.1", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typed-redux-saga": "^1.5.0", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "video-react": "^0.16.0", "vite": "^5.4.19", "vite-plugin-csp": "^1.1.2", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-static-copy": "^2.3.1", "vite-plugin-stylelint": "^5.3.1", "vite-plugin-svgr": "^4.2.0", "webpack": "^5.95.0"}, "packageManager": "yarn@4.1.0", "resolutions": {"cross-spawn": "^7.0.5", "esbuild": "^0.25.0", "nanoid": "^3.3.8", "@types/node": "^22.15.31"}, "cypress-cucumber-preprocessor": {"stepDefinitions": "cypress/e2e/**/[filepath]/*.{js,ts}"}}