.column {
  display: flex;
  justify-content: left;
  gap: 10px;
  &:last-child {
    .column__title {
      padding-right: 20px;
    }
    .column__sort {
      position: relative;
      left: -20px;
    }
  }
  .column__title {
    display: inline-block;
    line-height: 32px;
    color: var(--text-secondary);
    text-transform: uppercase;

    @include size-1-bold;
  }
  .column__sort {
    display: none;
    width: 10px;
    padding: 9px 0;
    margin-left: 2px;

    &.sortable {
      display: inline-block;
    }
  }
  .column__sort-dsc,
  .column__sort-asc {
    float: left;
    width: 13px;
    height: 7px;
    line-height: 8px;
    color: var(--primary);
    opacity: 0.3;
    transform: rotate(90deg);
    font-size: 12px;

    &.active {
      opacity: 1;
    }
  }
}
