import {
  all,
  fork,
  takeLatest,
  put,
  call,
  race,
  take,
  select,
  delay,
} from 'redux-saga/effects';
import { find, isEmpty } from 'lodash';
import { User, modules } from '@veritone/glc-redux';
import { onSetConfig, selectConfig } from '@store/modules/config/slice';
import { booting, bootFinished, appActions } from '@store/modules/app/slice';

import * as Sentry from '@sentry/browser';

import { AnyAction } from '@reduxjs/toolkit';
import HttpClient from '@store/dependencies/httpClient';
import { CheckCreateRootFolderResponse } from '@shared-types/responses';
import getApiAuthToken from '@utility/getApiAuthToken';
import { AlertLevel, createSnackNotification } from '@components/common';

const {
  user: {
    fetchUser,
    fetchEnabledApps,
    FETCH_USER,
    FETCH_USER_SUCCESS,
    FETCH_USER_FAILURE,
    FETCH_USER_APPLICATIONS,
    FETCH_USER_APPLICATIONS_SUCCESS,
    FETCH_USER_APPLICATIONS_FAILURE,
    LOGOUT,
    LOGOUT_FAILURE,
    LOGOUT_SUCCESS,
    selectUser,
  },
  auth: { setOAuthToken, OAUTH_GRANT_FLOW_SUCCESS },
  config: { getConfig },
} = modules;

export const getAppStartupDependencies = function* (): Generator<
  unknown,
  void,
  unknown
> {
  // fetch stuff
  yield all([
    // TODO: Remove any when redux-saga types are updated
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    put(fetchEnabledApps() as any),
    // ...other app dependencies
  ]);

  // wait for results
  const actions: AnyAction[] = [
    // @ts-expect-error TODO: Fix the types - use type effects
    ...(yield race([
      take(FETCH_USER_APPLICATIONS_SUCCESS),
      take([
        // requestError
        (a: AnyAction) => a.type === FETCH_USER_APPLICATIONS && a.error,
        // api error
        FETCH_USER_APPLICATIONS_FAILURE,
      ]),
    ])),
    // ...etc
  ];

  const error = find(actions, { error: true });
  if (error) {
    console.error('there was an error', error);
  }
};

export async function checkAndCreateRootFolder(
  config: Window['config'],
  token: string
) {
  let response;
  try {
    const http = new HttpClient(config);
    response = await http.post<CheckCreateRootFolderResponse>()(
      `/rootfolder`,
      undefined,
      {
        Authorization: `Bearer ${token}`,
      }
    );
  } catch (e) {
    console.error(e);
  }
  if (!response?.data?.rootFolderId) {
    createSnackNotification(
      AlertLevel.Error,
      'Error',
      'No root folder found. Contact organization admin to create root folder.'
    );
  }
  return response?.data;
}

export const redirectAndAwaitOAuthGrant = function* () {
  // TODO Add after routing is added
  // const routeType: string = yield select(selectRouteType);
  // const routePayload: string = yield select(selectCurrentRoutePayload);
  // if (routeType !== ROUTE_AUTH.type) {
  //   yield* put(
  //     ROUTE_AUTH({
  //       query: {
  //         nextType: routeType,
  //         nextPayload: !isEmpty(routePayload)
  //           ? JSON.stringify(routePayload)
  //           : undefined,
  //       },
  //     })
  //   );
  // }

  yield put(bootFinished());

  // retry boot after logging in
  yield take(OAUTH_GRANT_FLOW_SUCCESS);
  yield put(booting());
};

export const redirectToVeritoneInternalLogin = function* () {
  const config: Window['config'] = yield select(getConfig);
  if (config?.loginRoute) {
    const loginRoute = new URL(String(config?.loginRoute));
    if (loginRoute.hostname.match(/\.veritone\.com$/)) {
      const redirect = new URL(window.location.href);
      if (redirect.hostname.match(/\.veritone\.com$/)) {
        loginRoute.searchParams.set('redirect', redirect.href);
        window.location.href = loginRoute.href;
      } else {
        window.location.href = loginRoute.href;
      }
    }
  }
};

export const fetchUserWithStoredTokenOrCookie = function* (): Generator<
  unknown,
  User | false,
  unknown
> {
  const existingOAuthToken = (yield call(
    [localStorage, 'getItem'],
    'OAuthToken'
  )) as string | undefined; // TODO: Can this be justified?

  if (existingOAuthToken) {
    yield put(setOAuthToken(existingOAuthToken));
  }

  // TODO: Remove any when redux-saga types are updated
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  yield put(fetchUser() as any);

  // @ts-expect-error TODO: use typed effects and objcet style race instead of array
  const [successAction] = yield race([
    take(FETCH_USER_SUCCESS),
    take([(a: AnyAction) => a.type === FETCH_USER && a.error, FETCH_USER_FAILURE]),
  ]);

  // todo: this could differentiate between auth error (expired token) and failure (api error)
  return successAction ? successAction.payload : false;
};

export function* storeTokenAfterSuccessfulOAuthGrant() {
  yield takeLatest(
    OAUTH_GRANT_FLOW_SUCCESS,
    function* (action: ReturnType<typeof setOAuthToken>) {
      yield call([localStorage, 'setItem'], 'OAuthToken', action.payload);
    }
  );
}

export function* clearStoredTokenAfterLogoutHandle() {
  const user: User = yield select(selectUser);
  if (!isEmpty(user)) {
    yield call([localStorage, 'removeItem'], 'OAuthToken');
    // TODO Add after routing is added
    // yield put(redirect(ROUTE_LOGOUT()));
    yield take([LOGOUT_FAILURE, LOGOUT_SUCCESS]);
    yield delay(1000);
    const { loginRoute } = yield select(selectConfig);
    try {
      const loginUrl = new URL((loginRoute || '').toString());
      loginUrl.searchParams.set('redirect', window.location.origin);
      window.location.assign(loginUrl.href);
    } catch {
      console.warn('Warning: Login route is not a valid url');
    }
  }
}

export function* clearStoredTokenAfterLogout() {
  yield takeLatest(LOGOUT, clearStoredTokenAfterLogoutHandle);
}

export function* watchAppBootHandle() {
  // Define the watchAppBootHandle function before using it
  const config: Window['config'] = yield select(getConfig);
  const user: User = yield fetchUserWithStoredTokenOrCookie();

  yield put(onSetConfig(config));

  if (user) {
    // login success with stored credentials or cookie
    yield getAppStartupDependencies();

    // create root folder if no root folder exists.
    const token: string = yield select(getApiAuthToken);
    yield call(checkAndCreateRootFolder, config, token);

    yield put(bootFinished());

    const scope = Sentry.getCurrentScope();
    scope.setUser({
      id: user.userId,
      email: user.email?.toString(),
    });
  } else {
    if (config?.useOAuthGrant) {
      yield redirectAndAwaitOAuthGrant();
    } else {
      yield redirectToVeritoneInternalLogin();
    }
  }
}

export function* watchAppBoot() {
  yield takeLatest(appActions.booting, watchAppBootHandle);
}

export function* appSagas() {
  yield all([
    fork(watchAppBoot),
    fork(storeTokenAfterSuccessfulOAuthGrant),
    fork(clearStoredTokenAfterLogout),
  ]);
}

export default appSagas;
