import _ from 'lodash';
import { setDarkMode, detectThemeChange, initTheme } from '@theme/setTheme';

describe('setTheme.ts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('sets dark mode', () => {
    document.body.classList.remove('dark', 'light');
    setDarkMode(true);
    expect(document.body).toHaveClass('dark');
    expect(document.body).not.toHaveClass('light');
  });

  it('sets light mode', () => {
    document.body.classList.remove('dark', 'light');
    setDarkMode(false);
    expect(document.body).toHaveClass('light');
    expect(document.body).not.toHaveClass('dark');
  });

  it('handles undefined body', () => {
    const originalQuerySelector = document.querySelector;
    document.querySelector = jest.fn(() => undefined);

    expect(() => setDarkMode(true)).not.toThrow();
    expect(() => setDarkMode(false)).not.toThrow();

    document.querySelector = originalQuerySelector;
  });

  it('detects theme change and uses addEventListener', () => {
    const mockMatchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      addEventListener: jest.fn((_event, callback) => callback({ matches: true })),
      addListener: jest.fn(),
    }));
    window.matchMedia = mockMatchMedia;

    detectThemeChange();

    expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    expect(document.body).toHaveClass('dark');
  });

  it('detects theme change and uses addListener when addEventListener is not available', () => {
    const mockMatchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      addListener: jest.fn(callback => callback({ matches: false })),
    }));
    window.matchMedia = mockMatchMedia;

    detectThemeChange();

    expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    expect(document.body).toHaveClass('light');
  });

  it('initializes theme to dark', () => {
    jest.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = jest.fn(_.constant('Dark'));
    setDarkMode(true);

    initTheme();

    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('initializes theme to light', () => {
    jest.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = jest.fn(_.constant('Light'));
    setDarkMode(false);

    initTheme();

    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('initializes theme to unknown', () => {
    jest.spyOn(window.localStorage.__proto__, 'getItem');
    window.localStorage.__proto__.getItem = jest.fn(_.constant('Unknown'));
    setDarkMode(false);

    initTheme();

    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });
});
