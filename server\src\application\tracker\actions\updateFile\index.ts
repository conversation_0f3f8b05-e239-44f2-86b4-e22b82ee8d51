import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { UpdateFilePayloadResponse } from '../../../../../../types/responses';

const updateFile = async <
  ReqPayload,
  Data extends Partial<responses.getFile & UpdateFilePayloadResponse> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    Data & Partial<responses.getFile & UpdateFilePayloadResponse>
  >
> => {
  const { data, req, log, redisWrapper } = context;
  const { file, name } = data;

  if (!file) {
    throw new ActionError('Missing file');
  }

  if (!name) {
    throw new ActionError('Missing file name');
  }

  const headers = { Authorization: req.headers.authorization };

  const { temporalDataObject } = await callGQL<
    responses.getFileTemporalData,
    ReqPayload,
    Data
  >(context, headers, queries.getFileTemporalData, { tdoId: file.id });

  try {
    await callGQL(context, headers, queries.updateTdo, {
      tdoId: temporalDataObject.id,
      name: name,
      details: {
        ...temporalDataObject.details,
        name: name,
        veritoneFile: {
          ...temporalDataObject.details.veritoneFile,
          fileName: name,
          filename: name,
        },
      },
    });

    if (redisWrapper) {
      redisWrapper.file.del(file.id);
    }

    const newData = Object.assign({}, data, {
      file: {
        ...file,
        fileName: name,
      },
    });

    return { ...context, data: newData };
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default updateFile;
