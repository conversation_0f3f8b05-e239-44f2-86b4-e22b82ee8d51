import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import getEventByTdoIds from '../getEventByTdoIds';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';

let cxt: Context<object, { tdoIds: string[] }>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({})
  ),
}));

describe('getEventByTdoIds', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        tdoIds: ['123-123', '123-122'],
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {},
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('throws ActionError when no tdoIds are provided', async () => {
    cxt.data.tdoIds = [];

    await expect(getEventByTdoIds(cxt)).rejects.toThrowError(
      'No tdoIds provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Successfully queries TDO', async () => {
    const mockData = {
      temporalDataObject_123_0: {
        id: '123-123',
        folders: [
          {
            id: 'eventId1',
            name: 'test1',
          },
        ],
      },
      temporalDataObject_123_1: {
        id: '123-122',
        folders: [
          {
            id: 'eventId2',
            name: 'test2',
          },
        ],
      },
    };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve(mockData)
    );

    const response = await getEventByTdoIds(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {},
      true
    );
    expect(response).not.toBeNull();
    expect(response.data).toEqual({
      '123-123': {
        eventId: 'eventId1',
        eventName: 'test1',
      },
      '123-122': {
        eventId: 'eventId2',
        eventName: 'test2',
      },
    });
  });

  it('throws GraphQLError when there is an error in the GraphQL query', async () => {
    const mockError = new Error('GraphQL query failed');

    (callGQL as jest.Mock).mockImplementationOnce(() => {
      throw mockError;
    });

    await expect(getEventByTdoIds(cxt)).rejects.toThrowError(
      'GraphQL query failed'
    );
    expect(callGQL).toHaveBeenCalledTimes(1);
  });
});
