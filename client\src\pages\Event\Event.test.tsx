import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import Event from './Event';
import { configureAppStore, store } from '@store/store';
import axios from 'axios';
import { Settings, DateTime } from 'luxon';
import {
  CreateMatchGroupResponse,
  GetEventResponse,
  GetMatchGroupsResponse,
  GetTagsResponse,
  SearchEventsResponse,
  SearchFilesResponse,
} from '@shared-types/responses';
import { bytesToMb, millisToTimeFormatted } from '@utility/convert';
import { JobStatus } from '@shared-types/tracker';
import {
  createSnackNotification,
  AlertLevel,
} from '@components/common/Snackbar/Snackbar';
import ls from 'localstorage-slim';
import { I18nProvider, LOCALES } from '@i18n';
import { EventSliceState, getGeneratedTimeline } from '@store/modules/event/slice';

Settings.defaultZone = 'utc';

const mockNavigate = jest.fn();

jest.mock('@components/common/Snackbar/Snackbar', () => ({
  ...jest.requireActual('@components/common/Snackbar/Snackbar'),
  createSnackNotification: jest.fn(),
}));

const currentEventId = '947db3be-91ec-4e4b-a00f-6ad2ae06e25d';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({
    eventId: currentEventId,
  }),
}));
jest.mock('axios');

/* eslint-disable testing-library/no-node-access */
/* eslint-disable testing-library/prefer-presence-queries */

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetEventsResponse: SearchEventsResponse = {
  results: [
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 10,
      filesCount: 10,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 1',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: currentEventId,
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 10,
      filesCount: 10,
    },
  ],
  currentPage: 1,
  pageSize: 10000,
  totalCount: 2,
  totalPages: 1,
};

const mockGetMatchGroups: GetMatchGroupsResponse = {
  eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
  results: [
    {
      name: 'match group 5',
      id: '34670c7a-456c-4456-456f-4566ec4fdc1',
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      searches: [
        {
          searchName: 'testSearchName',
          referenceTrackletId: '7777',
          id: '1111111',
        },
      ],
      selectedTracklets: ['7777'],
      // createdDateTime: "2024-04-15T23:01:06.782Z",
      modifiedDateTime: '2024-04-15T23:01:06.782Z',
      generatedTimelines: [
        {
          id: '1',
          name: 'Timeline 1',
          createdDateTime: '2022-01-01T00:00:00Z',
          createdUserId: 'user1',
          tdoId: 'tdo1',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 60000,
          videoSizeBytes: 5000000,
          status: JobStatus.Pending,
        },
        {
          id: '2',
          name: 'Timeline 2',
          createdDateTime: '2022-01-02T00:00:00Z',
          createdUserId: 'user2',
          tdoId: 'tdo2',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 60000,
          videoSizeBytes: 5000000,
          status: JobStatus.Pending,
        },
        {
          id: '3',
          name: 'Timeline 3',
          createdDateTime: '2022-01-03T00:00:00Z',
          createdUserId: 'user3',
          tdoId: 'tdo3',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 0,
          videoSizeBytes: 0,
          status: JobStatus.Running,
        },
      ],
    },
    {
      name: 'match group 4',
      id: '2345787a-456c-4456-456f-4566ec4fdc1',
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      searches: [
        {
          searchName: 'testSearchName2',
          referenceTrackletId: '55555',
          id: '22222',
        },
      ],
      selectedTracklets: ['55555'],
      // createdDateTime: "2024-04-15T22:58:14.720Z",
      modifiedDateTime: '2024-04-15T22:58:14.720Z',
      generatedTimelines: [
        {
          id: '4',
          name: 'Timeline 4',
          createdDateTime: '2022-01-04T00:00:00Z',
          createdUserId: 'user4',
          tdoId: 'tdo4',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 70000,
          videoSizeBytes: 6000000,
          status: JobStatus.Complete,
        },
        {
          id: '5',
          name: 'Timeline 5',
          createdDateTime: '2022-01-05T00:00:00Z',
          createdUserId: 'user5',
          tdoId: 'tdo5',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 80000,
          videoSizeBytes: 7000000,
          status: JobStatus.Complete,
        },
      ],
    },
  ],
  currentPage: 1,
  pageSize: 30,
  totalPages: 1,
  sortType: 'modifiedDateTime',
  sortDirection: 'desc',
  totalCount: 2,
};

const mockGetMatchGroups2: GetMatchGroupsResponse = {
  eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
  results: [
    {
      name: 'match group 5',
      id: '34670c7a-456c-4456-456f-4566ec4fdc1',
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      searches: [
        {
          searchName: 'testSearchName',
          referenceTrackletId: '7777',
          id: '1111111',
        },
      ],
      selectedTracklets: ['7777'],
      // createdDateTime: "2024-04-15T23:01:06.782Z",
      modifiedDateTime: '2024-04-15T23:01:06.782Z',
      generatedTimelines: [
        {
          id: '1',
          name: 'Timeline 1b',
          createdDateTime: '2022-01-01T00:00:00Z',
          createdUserId: 'user1',
          tdoId: 'tdo1',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 60000,
          videoSizeBytes: 5000000,
          status: JobStatus.Complete,
        },
        {
          id: '2',
          name: 'Timeline 2b',
          createdDateTime: '2022-01-02T00:00:00Z',
          createdUserId: 'user2',
          tdoId: 'tdo2',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 60000,
          videoSizeBytes: 5000000,
          status: JobStatus.Complete,
        },
        {
          id: '3',
          name: 'Timeline 3b',
          createdDateTime: '2022-01-03T00:00:00Z',
          createdUserId: 'user3',
          tdoId: 'tdo3',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 0,
          videoSizeBytes: 0,
          status: JobStatus.Running,
        },
      ],
    },
    {
      name: 'match group 4',
      id: '2345787a-456c-4456-456f-4566ec4fdc1',
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      searches: [
        {
          searchName: 'testSearchName2',
          referenceTrackletId: '55555',
          id: '22222',
        },
      ],
      selectedTracklets: ['55555'],
      // createdDateTime: "2024-04-15T22:58:14.720Z",
      modifiedDateTime: '2024-04-15T22:58:14.720Z',
      generatedTimelines: [
        {
          id: '4',
          name: 'Timeline 4b',
          createdDateTime: '2022-01-04T00:00:00Z',
          createdUserId: 'user4',
          tdoId: 'tdo4',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 70000,
          videoSizeBytes: 6000000,
          status: JobStatus.Complete,
        },
        {
          id: '5',
          name: 'Timeline 5b',
          createdDateTime: '2022-01-05T00:00:00Z',
          createdUserId: 'user5',
          tdoId: 'tdo5',
          resolution: '1920x1080',
          outputFormat: 'mp4',
          videoLengthMs: 80000,
          videoSizeBytes: 7000000,
          status: JobStatus.Complete,
        },
      ],
    },
  ],
  currentPage: 1,
  pageSize: 30,
  totalPages: 1,
  sortType: 'modifiedDateTime',
  sortDirection: 'desc',
  totalCount: 2,
};

const mockPatchMatchGroups: CreateMatchGroupResponse = {
  matchGroup: {
    name: 'match group 5',
    id: '34670c7a-456c-4456-456f-4566ec4fdc1',
    eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
    searches: [
      {
        searchName: 'testSearchName',
        referenceTrackletId: '7777',
        id: '1111111',
      },
      {
        id: '830f2885-05c4-4ed3-a633-b7e91827a6a5',
        attributes: {
          person: [
            {
              key: 'Accessory',
              label: 'AccessoryBagAny',
              value: 'BagAny',
            },
            {
              key: 'Accessory',
              label: 'AccessoryBackpack',
              value: 'Backpack',
            },
            {
              key: 'UpperColor',
              label: 'UpperColorBlack',
              value: 'Black',
            },
            {
              key: 'UpperColor',
              label: 'UpperColorBlue',
              value: 'Blue',
            },
          ],
        },
        searchName: 'Attribute Search 1',
        searchTime: '2024-11-01T18:49:34.492Z',
      },
    ],
    modifiedDateTime: '2024-11-01T18:49:34.499Z',
    selectedTracklets: [],
  },
};

const mockGetEventByIdResponse: GetEventResponse = {
  event: {
    eventEndDate: '2024-04-10T17:02:10Z',
    createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
    createdByName: 'Test User',
    name: 'Event Test 1',
    description: 'New description',
    createdDateTime: '2024-04-11T00:10:13.876Z',
    modifiedDateTime: '2024-04-11T00:10:24.681Z',
    id: currentEventId,
    eventStartDate: '2024-04-10T17:02:10Z',
    tags: ['Tag 1', 'Tag 2'],
    matchGroupsCount: 10,
    filesCount: 10,
  },
};

const mockGetFilesResponse: SearchFilesResponse = {
  results: [
    {
      id: '30510000',
      createdByName: 'Test User',
      fileName: 'test_file_1.mp4',
      status: 'processed',
      length: 256,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 128,
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: '30510001',
      createdByName: 'Test User',
      fileName: 'test_file_2.mp4',
      status: 'processing',
      length: 512,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 1024,
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: '30510002',
      createdByName: 'Test User',
      fileName: 'test_file_3.mp4',
      status: 'error',
      length: 128,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 512,
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: '30510003',
      createdByName: 'Test User',
      fileName: 'test_file_4.mp4',
      status: 'unknown',
      length: 128,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 512,
      eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 30,
    },
  ],
  currentPage: 1,
  pageSize: 100,
  totalCount: 4,
  totalPages: 1,
};

const mockGetTagsResponse: GetTagsResponse = {
  results: ['Tag 1', 'Tag 2', 'Tag 3', 'Tag 4', 'Tag 5'],
};

const initialStateForMock: { event: EventSliceState } = {
  event: {
    files: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    fileSelected: {
      file: undefined,
      status: 'idle',
      error: '',
    },
    matchGroups: {
      eventId: '',
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
      sortType: '',
      sortDirection: '',
    },
    event: {
      id: '',
      name: '',
      tags: [],
      createdBy: '',
      createdByName: '',
      description: '',
      eventStartDate: '',
      eventEndDate: '',
      createdDateTime: '',
      modifiedDateTime: '',
      status: 'idle',
    },
    eventDeletion: {
      id: '',
      status: 'idle',
      message: '',
    },
    matchGroupSearchDeletion: {
      status: 'idle',
      message: '',
      matchGroupId: '',
      searchId: '',
    },
    tags: {
      results: [],
      status: 'idle',
      error: '',
    },
    generatedTimelines: {
      results: [],
      status: 'idle',
      error: '',
    },
    generatedTimelineDeletions: [],
    attributes: {
      person: {},
      vehicle: {},
    },
    createMatchGroup: {
      status: 'idle',
      error: '',
      id: '',
    },
    updateMatchGroup: {
      status: 'idle',
      error: '',
    },
  },
};

async function switchToTab(dataTestId: string) {
  const desiredTab = dataTestId.split('-')[1];

  // Find specified tab
  const tab = screen.getByTestId(dataTestId);

  fireEvent.click(tab);

  await waitFor(() => {
    // is the tab selected?
    expect(tab).toHaveClass('Mui-selected');
  });
  await waitFor(() => {
    if (desiredTab !== 'files') {
      expect(screen.getByTestId('event-files-tab')).not.toHaveClass(
        'Mui-selected'
      );
    }
  });
  mockNavigate(`/?activeTab=${desiredTab}&page=1&limit=50`);
}

jest
  .spyOn(ls, 'set')
  .mockImplementation((key: string, value: unknown) =>
    localStorage.setItem(key, JSON.stringify(value))
  );
jest
  .spyOn(ls, 'get')
  .mockImplementation((key: string) => localStorage.getItem(key));

describe('Events page', () => {
  test('displays an event and polls for data', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    let callToEvent = 0;

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        callToEvent++;
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    expect(callToEvent).toBe(1);
    // comment out because the polling is disabled
    // await sleep(7501);
    // // Poll twice
    // expect(callToEvent).toBe(2);

    // await sleep(7501);
    // // Poll third
    // expect(callToEvent).toBe(3);
  }, 16000);

  test('Updates an event name and saves it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = '    Updated Event name ';
    const nameUpdatedTrim = 'Updated Event name';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/event') && method === 'patch') {
        // has same response as GET event by ID except adding updated description
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventByIdResponse.event,
              name: nameUpdatedTrim,
            },
          },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-edit-event'));

    // wait for the name input to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-change-name')).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update event name
    fireEvent.change(screen.getByTestId('event-change-name'), {
      target: { value: nameUpdated },
    });

    // Save the update
    fireEvent.click(screen.getByTestId('event-save-event'));

    await waitFor(() => {
      expect(mockedAxios.request).toHaveBeenCalledWith(expect.objectContaining({
        url: 'http://localhost/api/v1/event/947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
        method: 'patch',
        data: expect.objectContaining({ name: 'Updated Event name' })
      }));
    });

    // wait for the updated name to appear, trimmed
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toHaveTextContent(
        nameUpdatedTrim
      );
    });
  });

  test('Updates an event name and cancels it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = 'Updated Event name';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/event') && method === 'patch') {
        // has same response as GET event by ID except adding updated description
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventByIdResponse.event,
              name: nameUpdated,
            },
          },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-edit-event'));

    // wait for the description input to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-change-name')).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update event name
    fireEvent.change(screen.getByTestId('event-change-description'), {
      target: { value: nameUpdated },
    });

    // Cancel the update
    fireEvent.click(screen.getByTestId('event-cancel-event'));

    // wait for the name to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toHaveTextContent(
        mockGetEventByIdResponse.event.name
      );
    });
  });

  test('Updates an event description and saves it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const descriptionUpdated = 'Updated Event description';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/event') && method === 'patch') {
        // has same response as GET event by ID except adding updated description
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventByIdResponse.event,
              description: descriptionUpdated,
            },
          },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-edit-event'));

    // wait for the description input to appear
    await waitFor(() => {
      expect(
        screen.getByTestId('event-change-description')
      ).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update event description
    fireEvent.change(screen.getByTestId('event-change-description'), {
      target: { value: descriptionUpdated },
    });

    // Save the updated description
    fireEvent.click(screen.getByTestId('event-save-event'));

    // wait for the updated description to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-description')).toHaveTextContent(
        descriptionUpdated
      );
    });
  });

  test('Updates an event description and cancels it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const descriptionUpdated = 'Updated Event description';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/event') && method === 'patch') {
        // has same response as GET event by ID except adding updated description
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventByIdResponse.event,
              description: descriptionUpdated,
            },
          },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-edit-event'));

    // wait for the description input to appear
    await waitFor(() => {
      expect(
        screen.getByTestId('event-change-description')
      ).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update event description
    fireEvent.change(screen.getByTestId('event-change-description'), {
      target: { value: descriptionUpdated },
    });

    // Cancel the updated description
    fireEvent.click(screen.getByTestId('event-cancel-event'));

    // wait for the description to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-description')).toHaveTextContent(
        mockGetEventByIdResponse.event.description
      );
    });
  });

  test('Updates an events tag', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const _descriptionUpdated = 'Updated Event description';

    const tagsUpdated = ['Tag 1', 'Tag 2'];

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/event') && method === 'patch') {
        // has same response as GET event by ID except adding updated description
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventByIdResponse.event,
              tags: tagsUpdated,
            },
          },
        });
      }
      if (url?.includes('/tags') && method === 'get') {
        return Promise.resolve({ data: mockGetTagsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-edit-event'));

    await act(async () => {
      screen.getByTestId('event__tags-autocomplete').click();
    });

    // autocomplete is open
    await waitFor(() => {
      expect(
        screen.getByTestId('event__tags-autocomplete')
      ).toBeInTheDocument();
    });

    // Type in event autocomplete
    fireEvent.change(
      screen.getByTestId('event-tags-autocomplete-field-input'),
      {
        target: { value: 'Tag' },
      }
    );

    // Id is needed to find autocomplete options
    const inputId = screen.getByTestId(
      'event-tags-autocomplete-field-input'
    ).id;

    // Autocomplete open with options
    await waitFor(() => {
      const autocompleteOption = document.getElementById(`${inputId}-option-0`);
      expect(autocompleteOption).toBeInTheDocument();
    });

    // Click second option
    const autocompleteOption = document.getElementById(
      `${inputId}-option-1`
    ) as Element;
    fireEvent.click(autocompleteOption);

    // Save the updated description
    fireEvent.click(screen.getByTestId('event-save-event'));

    // wait for the updated tags to appear
    await waitFor(() => {
      expect(screen.getByTestId('event-tables')).toHaveTextContent('Tag 1');
    });

    await waitFor(() => {
      expect(screen.getByTestId('event-tables')).toHaveTextContent('Tag 2');
    });
  });

  test('Open SearchAllFilesDialog and select attributes to search', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/tags') && method === 'get') {
        return Promise.resolve({ data: mockGetTagsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
      if (url?.includes('/match-groups') && method === 'patch') {
        return Promise.resolve({ data: mockPatchMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name and search all files button to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });
    const searchAllFilesButton = screen.getByTestId('search-and-upload-search-all-file-button');
    await waitFor(() => {
      expect(searchAllFilesButton).toBeInTheDocument();
      expect(searchAllFilesButton).toBeEnabled();
    });

    fireEvent.click(searchAllFilesButton);

    // wait for the dialog to become available
    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog')).toBeInTheDocument();
      expect(within(screen.getByTestId('search-all-files-dialog__content-attribute-type')).getByLabelText('People')).toBeInTheDocument();
      expect(within(screen.getByTestId('search-all-files-dialog__content-attribute-type')).getByLabelText('Vehicles')).toBeInTheDocument();
    });

    // check if all attributes are available for Person
    await waitFor(() => {
      expect(within(screen.getByTestId('search-all-files-dialog__content-attribute-type')).getByLabelText('People')).toBeChecked();
      expect(screen.getByTestId('search-all-files-dialog__content-attribute-selection-category-menu')).toBeInTheDocument();
      expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-category-menu-item')).toHaveLength(8);
    });

    // click on first category (Accessories)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-category-menu-item')[0]);

    // check if accessories are available for Person
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')).toHaveLength(9);
    });

    // click on first 2 accessory (BagAny and BagBackpack)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[0]);
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[1]);

    // check attribute count
    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toHaveTextContent('Choose Attributes: 2 Selected');
    });

    // click clear all
    fireEvent.click(screen.getByTestId('search-all-files-dialog__content-attribute-selected-clear-all'));

    // check attribute count
    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toHaveTextContent('Choose Attributes: 0 Selected');
    });

    // reselect the first 2 accessory (BagAny and BagBackpack)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[0]);
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[1]);

    // click on last category (Upper)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-category-menu-item')[7]);

    // check if Upper is available for Person
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')).toHaveLength(0);
      expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-sub-category-menu-item')).toHaveLength(3);
    });

    // click on first sub category (Color)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-sub-category-menu-item')[0]);

    // check if Color is available for Upper
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')).toHaveLength(9);
    });

    // click on first 2 color (Black and Blue)
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[0]);
    fireEvent.click(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-checkbox-menu-item')[1]);

    // check attribute count
    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toHaveTextContent('Choose Attributes: 4 Selected');
    });

    // click on match group select dropdown
    const selectElement = within(screen.getByTestId('search-all-files-dialog__content-match-group-select')).getByRole('combobox');
    fireEvent.mouseDown(selectElement);

    // check if match group options are available
    const option1 = screen.getByTestId('search-all-files-dialog__content-match-0');
    await waitFor(() => {
      expect(option1).toBeInTheDocument();
    });

    // select first match group
    fireEvent.click(option1);

    // click search button
    fireEvent.click(screen.getByTestId('search-all-files-dialog__actions-search'));

    // check if search is successful, snackbar should appear, and dialog should close
    await waitFor(() => {
      expect(mockedStore.getState().event.matchGroups.results[0].searches[1]).toEqual({
        id: '830f2885-05c4-4ed3-a633-b7e91827a6a5',
        attributes: {
          person: [
            {
              key: 'Accessory',
              label: 'AccessoryBagAny',
              value: 'BagAny',
            },
            {
              key: 'Accessory',
              label: 'AccessoryBackpack',
              value: 'Backpack',
            },
            {
              key: 'UpperColor',
              label: 'UpperColorBlack',
              value: 'Black',
            },
            {
              key: 'UpperColor',
              label: 'UpperColorBlue',
              value: 'Blue',
            },
          ],
        },
        searchName: 'Attribute Search 1',
        searchTime: '2024-11-01T18:49:34.492Z',
      });
      expect(createSnackNotification).toHaveBeenCalledWith(AlertLevel.Success, 'Update Match Group', 'Match Group match group 5 was updated successfully');
      expect(screen.queryByTestId('search-all-files-dialog')).not.toBeInTheDocument();
    });
  });
});

describe('Events page on Files tab', () => {
  test('Displays files within an event and has sorting', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    // Use the column for queries below
    const { getByTestId } = within(screen.getByTestId('column-0'));

    // sorting should exist
    expect(getByTestId('column-up-arrow')).toBeInTheDocument();
    expect(getByTestId('column-down-arrow')).toBeInTheDocument();

    // Select first column up arrow
    screen.getAllByTestId('column-up-arrow')[0].click();

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('?page=1&limit=10');
    });
  });

  test('Double clicks file and navigates to file', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    const file = mockGetFilesResponse.results[0];

    // Single click file
    fireEvent.click(screen.getByTestId(`file-row-${file.id}`));

    // Show details
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-content')).toBeInTheDocument();
    });

    expect(screen.getByTestId('event-detail-name')).toHaveTextContent(
      file.fileName
    );
    expect(screen.getByTestId('event-detail-location')).toHaveTextContent(
      `File GPS Location:${file.location}`
    );
    expect(screen.getByTestId('event-detail-type')).toHaveTextContent(
      `File Type:${file.fileType}`
    );
    expect(screen.getByTestId('event-detail-size')).toHaveTextContent(
      `File Size:${bytesToMb(file.fileSize)} Mb`
    );
    expect(screen.getByTestId('event-detail-length')).toHaveTextContent(
      `Video Length:${millisToTimeFormatted(file.length * 1000)}`
    );

    // Double click file
    fireEvent.dblClick(screen.getByTestId(`file-row-${file.id}`));

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(
        `/event/${file.eventId}/file/${file.id}`
      );
    });
  });

  test('Double clicks file that is not processed and display snack warning', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // file is still processing
    const processingFile = mockGetFilesResponse.results[1];

    // double click file
    fireEvent.dblClick(screen.getByTestId(`file-row-${processingFile.id}`));

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not finished processing'
      );
    });

    // file has processing error
    const errorFile = mockGetFilesResponse.results[2];

    // double click file
    fireEvent.dblClick(screen.getByTestId(`file-row-${errorFile.id}`));

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Error,
        'Error',
        'File failed to process'
      );
    });
  });

  test('Selects a file and renders MediaPlayer and details for non-processed file', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    const file = mockGetFilesResponse.results[1];

    // Single click file
    fireEvent.click(screen.getByTestId(`file-row-${file.id}`));

    // Check preview container
    await waitFor(() => {
      expect(
        screen.getByTestId('event__detail-preview-container')
      ).toBeInTheDocument();
    });

    // Check that MediaPlayer is here
    expect(
      screen.getByTestId('event__detail-preview-container').firstChild
    ).toHaveClass('media-player');

    // Show details
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-content')).toBeInTheDocument();
    });

    expect(file.status === 'processing').toBeTruthy();

    // Check the content being rendered
    expect(screen.getByTestId('event-detail-name')).toHaveTextContent(
      file.fileName
    );

    expect(screen.queryByTestId('event-detail-location')).not.toBeInTheDocument();

    expect(screen.getByTestId('event-detail-type')).toHaveTextContent(
      `File Type:${file.fileType}`
    );

    expect(screen.queryByTestId('event-detail-size')).not.toBeInTheDocument();

    expect(screen.queryByTestId('event-detail-length')).not.toBeInTheDocument();
  });

  test('Selects a file and renders MediaPlayer and details for a processed file', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    const file = mockGetFilesResponse.results[0];

    // Single click file
    fireEvent.click(screen.getByTestId(`file-row-${file.id}`));

    // Check preview container
    await waitFor(() => {
      expect(
        screen.getByTestId('event__detail-preview-container')
      ).toBeInTheDocument();
    });

    // Check that MediaPlayer is here
    expect(
      screen.getByTestId('event__detail-preview-container').firstChild
    ).toHaveClass('media-player');

    // Show details
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-content')).toBeInTheDocument();
    });

    expect(file.status === "processed").toBeTruthy();

    // Check the content being rendered
    expect(screen.getByTestId('event-detail-name')).toHaveTextContent(
      file.fileName
    );

    expect(screen.getByTestId('event-detail-location')).toHaveTextContent(
      `File GPS Location:${file.location}`
    );

    expect(screen.getByTestId('event-detail-type')).toHaveTextContent(
      `File Type:${file.fileType}`
    );

    expect(screen.getByTestId('event-detail-size')).toHaveTextContent(
      `File Size:${bytesToMb(file.fileSize)} Mb`
    );

    expect(screen.getByTestId('event-detail-length')).toHaveTextContent(
      `Video Length:${millisToTimeFormatted(file.length * 1000)}`
    );
  });

  test('Updated file name', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = "    Updated file name     ";

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/file') && method === 'patch') {
        return Promise.resolve({
          data: {
            file: {
              ...mockGetFilesResponse.results[1], // Update second file
              fileName: nameUpdated.trim(),
            },
          },
          status: 200,
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    const file = mockGetFilesResponse.results[1];

    // Single click file
    fireEvent.click(screen.getByTestId(`file-row-${file.id}`));

    // Check preview container
    await waitFor(() => {
      expect(
        screen.getByTestId('event__detail-preview-container')
      ).toBeInTheDocument();
    });

    // check if the file name is present
    expect(screen.getByTestId('event-detail-name')).toHaveTextContent(
      mockGetFilesResponse.results[1].fileName
    );

    // click on the edit button
    fireEvent.click(screen.getByTestId('event-detail-file-name-edit'));

    // Update file name
    fireEvent.change(screen.getByTestId('event-detail-name-textfield'), {
      target: { value: nameUpdated },
    });

    // click enter
    fireEvent.keyDown(screen.getByTestId('event-detail-name-textfield'), {
      key: 'Enter',
    });

    // check if the file name in the home detail is updated (trimmed)
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-file-name')).toHaveTextContent(
        nameUpdated.trim()
      );
    });
  });

  test('Disable upload file button if events are not loaded', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for Upload File button to load and check it is disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('search-and-upload-upload-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('search-and-upload-upload-button')
      ).toBeDisabled();
    });

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for event to be loaded and check if Upload File button is enabled
    await waitFor(() => {
      expect(
        screen.getByTestId('search-and-upload-upload-button')
      ).toBeEnabled();
    });
  });

  test('Load pending deletion file with PENDING DELETION status', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // file is pending deletion
    const pendingDeletionFile = mockGetFilesResponse.results[3];

    // add deleted tdo id to localStorage deletedFileIds
    localStorage.setItem(
      'deletedFileIds',
      JSON.stringify([
        { value: pendingDeletionFile.id, expiry: new Date().getTime() + 10000 },
      ])
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    // wait for deleted file to be displayed and check if it has PENDING DELETION status
    await waitFor(() => {
      const deletedFileRow = screen.getByTestId(
        `file-row-cell-3-${pendingDeletionFile.id}-1`
      );
      expect(deletedFileRow).toBeInTheDocument();
      expect(deletedFileRow).toHaveTextContent('PENDING DELETION');
    });
  });

  test('Delete a file and check status', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/file') && method === 'delete') {
        return Promise.resolve({
          data: { message: 'File successfully deleted' },
          status: 200,
          statusText: 'OK',
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for files to become available
    mockGetFilesResponse.results.forEach(async (file, index) => {
      await waitFor(() => {
        expect(
          screen.getByTestId(`file-row-cell-${index}-${file.id}-0`)
        ).toBeInTheDocument();
      });
    });

    // wait for column to become available
    await waitFor(() => {
      expect(screen.getByTestId('column-0')).toBeInTheDocument();
    });

    const file = mockGetFilesResponse.results[1];

    // Single click file
    fireEvent.click(screen.getByTestId(`file-row-${file.id}`));

    // Check preview container
    await waitFor(() => {
      expect(
        screen.getByTestId('event__detail-preview-container')
      ).toBeInTheDocument();
    });

    // Check if Delete File button is available
    await waitFor(() => {
      expect(
        screen.getByTestId('event-delete-file-button')
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('event-delete-file-button'));

    // Check if delete file dialog is displayed
    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog-title')).toBeInTheDocument();
      expect(
        screen.getByTestId('confirm-dialog-confirm-action')
      ).toBeInTheDocument();
    });

    // Click confirm delete
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    // Check if delete file dialog is closed
    await waitFor(() => {
      expect(
        screen.queryByTestId('confirm-dialog-title')
      ).not.toBeInTheDocument();
    });

    // Check localStorage for deletedFileIds
    await waitFor(() => {
      const deletedFileIds = ls.get('deletedFileIds');
      expect(deletedFileIds).not.toBeNull();
      if (deletedFileIds && typeof deletedFileIds === 'string') {
        expect(JSON.parse(deletedFileIds)).toContainEqual({
          value: file.id,
          expiry: expect.any(Number),
        });
      }
    });

    // Check if file's status is PENDING DELETION
    await waitFor(() => {
      const deletedFileRow = screen.getByTestId(`file-row-cell-1-${file.id}-1`);
      expect(deletedFileRow).toBeInTheDocument();
      expect(deletedFileRow).toHaveTextContent('PENDING DELETION');
    });
  });

  test('Show empty message when there are no pending upload files or file results found', async () => {

    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', JSON.stringify([]));

    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: { ...mockGetFilesResponse, results: [] } });
      }
      if (url?.includes('/file') && method === 'delete') {
        return Promise.resolve({
          data: { message: 'File successfully deleted' },
          status: 200,
          statusText: 'OK',
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // wait for empty message to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-tab-container-no-results')).toBeInTheDocument();
    });
  });

  test('Show pending upload files when there are no file results found', async () => {

    const expirationDate = DateTime.now().plus({ days: 7 }).toISO();

    const pendingUploadFiles = [
      {
        fileName: 'test.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '1',
        expirationDate,
      },
      {
        fileName: 'test1.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '2',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '3',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '12345678-1111-2222-3333-444444444444',
        status: 'new',
        id: '4',
        expirationDate,
      },
    ];

    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', pendingUploadFiles);

    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: { ...mockGetFilesResponse, results: [] } });
      }
      if (url?.includes('/file') && method === 'delete') {
        return Promise.resolve({
          data: { message: 'File successfully deleted' },
          status: 200,
          statusText: 'OK',
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // empty message should not be displayed
    await waitFor(() => {
      expect(screen.queryByTestId('event-tab-container-no-results')).not.toBeInTheDocument();
    });

    const filteredPendingUpload = pendingUploadFiles.filter(file => file.eventId === currentEventId);

    await waitFor(() => {
      expect(screen.queryAllByTestId('pending-file-row').length).toBe(filteredPendingUpload.length);
    });
  });

  test('Show pending upload files but remove when found in file results or expired', async () => {

    const expirationDate = DateTime.now().plus({ days: 7 }).toISO();
    const alreadyExpiredDate = DateTime.now().minus({ days: 7 }).toISO();

    const pendingUploadFiles = [
      {
        fileName: 'test.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '30510000', // should be removed
        expirationDate,
      },
      {
        fileName: 'test1.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '2',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '3',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '12345678-1111-2222-3333-444444444444', // should be removed
        status: 'new',
        id: '4',
        expirationDate,
      },
      {
        fileName: 'test3.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: currentEventId,
        status: 'new',
        id: '5',
        expirationDate: alreadyExpiredDate, // should be removed
      },
    ];

    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', pendingUploadFiles);

    const mockedStore = configureAppStore(initialStateForMock);
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/file') && method === 'delete') {
        return Promise.resolve({
          data: { message: 'File successfully deleted' },
          status: 200,
          statusText: 'OK',
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // empty message should not be displayed
    await waitFor(() => {
      expect(screen.queryByTestId('event-tab-container-no-results')).not.toBeInTheDocument();
    });

    const now = new Date().getTime();

    const filteredPendingUpload = pendingUploadFiles.filter(file => file.eventId === currentEventId && (
      !mockGetFilesResponse.results.some(f => f.id === file.id)
    ) && new Date(file.expirationDate).getTime() > now);

    await waitFor(() => {
      expect(screen.queryAllByTestId('pending-file-row').length).toBe(filteredPendingUpload.length);
    });

    const pendingFirstFile = screen.getByTestId(`pending-file-row-${filteredPendingUpload[0].id}`);

    // double click file
    fireEvent.dblClick(pendingFirstFile);

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not yet processed.'
      );
    });

    // double click file
    fireEvent.click(pendingFirstFile);

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not yet processed.'
      );
    });
  });
});

describe('Events page on Matches tab', () => {
  beforeAll(() => {
    jest.useFakeTimers();
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  afterAll(() => {
    jest.useRealTimers();
  });
  test('switches tab to match groups', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');
  });

  test('selects a match group and clicks View Search via the accordion', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');

    await waitFor(() => {
      // is Match Group tab selected?
      expect(
        screen.getByTestId(
          'match-group-row-1-3c370c7a-325c-4256-8fff-b34a6ec4fdc1'
        )
      ).toBeInTheDocument();
    });

    // should not be visible
    expect(screen.getByTestId('event-detail-container')).toHaveClass('hide');

    fireEvent.click(document.querySelectorAll('.MuiAccordionSummary-root')[1]);

    // wait for view search to be displayed
    await waitFor(() => {
      expect(
        screen.queryAllByTestId('potential-match-search-row-cell-0-1')[0]
      ).toBeInTheDocument();
    });

    mockNavigate.mockClear();

    // Click View Search Row
    fireEvent.click(
      screen.queryAllByTestId('potential-match-search-row-cell-0-1')[0]
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(
        '/event/947db3be-91ec-4e4b-a00f-6ad2ae06e25d/match-group/34670c7a-456c-4456-456f-4566ec4fdc1/potential-match-search/1111111'
      );
    });
  });

  test('deletes a match group search', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
      if (url?.includes('/match-groups') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');

    await waitFor(() => {
      // is Match Group tab selected?
      expect(
        screen.getByTestId(
          'match-group-row-1-3c370c7a-325c-4256-8fff-b34a6ec4fdc1'
        )
      ).toBeInTheDocument();
    });

    fireEvent.click(document.querySelectorAll('.MuiAccordionSummary-root')[1]);

    // wait for view search to be displayed
    await waitFor(() => {
      expect(
        screen.queryAllByTestId('potential-match-search-row-cell-0-1')[0]
      ).toBeInTheDocument();
    });

    // Click Delete
    fireEvent.click(
      screen.getByTestId(
        `matchgroup-delete-${mockGetMatchGroups.results?.[0]?.searches?.[0]?.id ?? ''
        }`
      )
    );

    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument();
    });

    // Confirm delete
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    await waitFor(() => {
      expect(
        screen.queryByTestId('confirm-dialog-confirm-action')
      ).not.toBeInTheDocument();
    });
  });

  test('edit a match group search name', async () => {
    const newSearchName = 'New Search Name';
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }

      if (url?.includes('/match-groups') && method === 'patch') {
        const existingSearch = mockGetMatchGroups.results[0].searches?.[0] ?? {};
        const updatedResults = { ...mockGetMatchGroups.results[0], searches: [{ ...existingSearch, searchName: newSearchName }] };

        const updateMockMatchGroups = { matchGroup: updatedResults };
        return Promise.resolve({ data: updateMockMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');

    await waitFor(() => {
      // is Match Group tab selected?
      expect(screen.getByTestId('match-group-row-1-3c370c7a-325c-4256-8fff-b34a6ec4fdc1')).toBeInTheDocument();
    });

    fireEvent.click(document.querySelectorAll('.MuiAccordionSummary-root')[1]);

    // wait for view search to be displayed
    await waitFor(() => {
      expect(screen.queryAllByTestId('potential-match-search-row-cell-0-1')[0]).toBeInTheDocument();
    });

    // Click Edit icon
    fireEvent.click(screen.getByTestId(`matchgroup-edit-${mockGetMatchGroups.results?.[0]?.searches?.[0]?.id ?? ''}`));

    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-dialog-edit-input')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-dialog-edit-input')).toHaveValue('testSearchName');
    });

    fireEvent.change(screen.getByRole('textbox'), { target: { value: newSearchName } });

    // Confirm Edit
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    // Confirm Dialog is closed
    await waitFor(() => {
      expect(screen.queryByTestId('confirm-dialog-confirm-action')).not.toBeInTheDocument();
    });

    // Check if the search name has been updated
    await waitFor(() => {
      expect(mockedStore.getState().event.matchGroups.results[0].searches[0].searchName).toBe(newSearchName);
      expect(screen.queryAllByTestId('potential-match-search-row-cell-0-0')[0]).toHaveTextContent(newSearchName);
    });
  });

  test('renames a match-group', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    const newMatchGroupName = 'New match-group name';

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');

    await waitFor(() => {
      // is Match Group tab selected?
      expect(
        screen.getByTestId(
          'match-group-row-1-3c370c7a-325c-4256-8fff-b34a6ec4fdc1'
        )
      ).toBeInTheDocument();
    });

    expect(screen.getByTestId('match-group-row-menu-icon-0')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('match-group-row-menu-icon-0'));

    // open match-group confirm rename modal
    expect(screen.getByTestId('match-group-row-menu-rename-0')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('match-group-row-menu-rename-0'));

    expect(screen.getByTestId('confirm-dialog-matchGroup')).toBeInTheDocument();
    const renameInput = screen.getByTestId('confirm-dialog-rename-input') as HTMLInputElement;
    expect(renameInput).toBeInTheDocument();

    const saveButton = screen.getByRole('button', {
      name: /save/i
    });

    // change input to empty string and expect save button to be disabled
    fireEvent.change(renameInput, { target: { value: "" } });
    expect(saveButton).toBeDisabled();

    // change input to new match group name and expect save button to be enabled
    fireEvent.change(renameInput, { target: { value: newMatchGroupName } });
    expect(saveButton).toBeEnabled();

    // click save button to call api update match group name
    fireEvent.click(saveButton);
    expect(screen.queryByTestId('confirm-dialog-matchGroup')).not.toBeInTheDocument();
  });

  test('delete a match-group', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // wait for event heading name to become available
    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    // Files tab
    await waitFor(() => {
      expect(screen.getByTestId('event-files-tab')).toBeInTheDocument();
    });

    const filesTab = screen.getByTestId('event-files-tab');

    // Is File tab selected?
    expect(filesTab).toHaveClass('Mui-selected');

    switchToTab('event-match-groups-tab');

    expect(filesTab).not.toHaveClass('Mui-selected');

    await waitFor(() => {
      // is Match Group tab selected?
      expect(
        screen.getByTestId(
          'match-group-row-1-3c370c7a-325c-4256-8fff-b34a6ec4fdc1'
        )
      ).toBeInTheDocument();
    });

    expect(screen.getByTestId('match-group-row-menu-icon-0')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('match-group-row-menu-icon-0'));

    // open match-group confirm delete modal
    expect(screen.getByTestId('match-group-row-menu-delete-0')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('match-group-row-menu-delete-0'));

    expect(screen.getByTestId('confirm-dialog-matchGroup')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.')).toBeInTheDocument();

    fireEvent.click(screen.getByRole('button', {
      name: /yes, delete/i
    }));

    expect(screen.queryByTestId('confirm-dialog-matchGroup')).not.toBeInTheDocument();
  });
});

describe('Events page on Timeline Generated Videos tab', () => {
  test('switches tab to timeline generated videos and render correctly', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    const { container: _container } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );
    fireEvent.click(
      screen.getByRole('tab', {
        name: /timeline generated videos/i,
      })
    );
    await waitFor(() => {
      expect(
        screen.getByTestId('generated-timeline-row-1')
      ).toBeInTheDocument();
    });
  });

  test('click on a timeline generated video and render detail container', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(
      screen.getByRole('tab', {
        name: /timeline generated videos/i,
      })
    );

    await waitFor(() => {
      expect(
        screen.getByTestId('generated-timeline-row-1')
      ).toBeInTheDocument();
    });

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups2 });
      }
    });

    // should not be visible
    expect(screen.getByTestId('event-detail-container')).toHaveClass('hide');

    const timelineRow = screen.getByTestId('generated-timeline-row-1');
    fireEvent.click(timelineRow);

    // wait for the detail container to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-container')).not.toHaveClass(
        'hide'
      );
    });

    expect(screen.queryAllByText(/timeline 1/i).length).toBe(3);

    const eventId = '947db3be-91ec-4e4b-a00f-6ad2ae06e25d';

    store.dispatch(getGeneratedTimeline({ eventId }));

    const timelineRow2 = screen.getByTestId('generated-timeline-row-1');
    fireEvent.click(timelineRow2);


    await waitFor(() => {
      expect(screen.queryAllByText(/timeline 1b/i).length).toBe(3);
    }, { timeout: 10000 });
  });

  test('click on a timeline video with status running and render detail container with hidden fields', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(
      screen.getByRole('tab', {
        name: /timeline generated videos/i,
      })
    );

    await waitFor(() => {
      expect(
        screen.getByTestId('generated-timeline-row-3')
      ).toBeInTheDocument();
    });

    // The file size value should show as two dashes for this timeline video, because it
    // does not have a status of 'complete'.
    expect(screen.getByTestId('timeline-row-video-size-test-id-3')).toHaveTextContent('--');

    // should not be visible
    expect(screen.getByTestId('event-detail-container')).toHaveClass('hide');

    const timelineRow = screen.getByTestId('generated-timeline-row-3');
    fireEvent.click(timelineRow);

    // wait for the detail container to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-container')).not.toHaveClass(
        'hide'
      );
    });

    // The clicked-on timeline video is in a 'running' state, and so should have the
    // 'timeline__video-status-not-complete' class for its filesize field
    expect(screen.getByTestId('timeline-details-file-size-container')).toHaveClass('timeline__video-status-not-complete');

    // The clicked-on timeline video is in a 'running' state, and so should have the
    // 'timeline__video-status-not-complete' class for its video length field
    expect(screen.getByTestId('timeline-details-video-length-container')).toHaveClass('timeline__video-status-not-complete');

    expect(
      screen.getByRole('heading', {
        name: /timeline 3/i,
      })
    ).toBeInTheDocument();
  });

  test('deleting a timeline puts it into a pending deletion state', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/match-group') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroups });
      }
      if (url?.includes('/timeline') && method === 'delete') {
        return Promise.resolve({
          data: { message: 'Timeline successfully deleted' },
          status: 200,
          statusText: 'OK',
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <Event />
          </Router>
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(
      screen.getByRole('tab', {
        name: /timeline generated videos/i,
      })
    );

    await waitFor(() => {
      expect(
        screen.getByTestId('generated-timeline-row-2')
      ).toBeInTheDocument();
    });

    // should not be visible
    expect(screen.getByTestId('event-detail-container')).toHaveClass('hide');

    const timelineRow = screen.getByTestId('generated-timeline-row-2');
    fireEvent.click(timelineRow);

    // wait for the detail container to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('event-detail-container')).not.toHaveClass('hide');
    });

    // Check if Delete File button is available
    await waitFor(() => {
      expect(
        screen.getByTestId('timeline-delete-button')
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('timeline-delete-button'));

    // Check if delete file dialog is displayed
    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog-title')).toBeInTheDocument();
      expect(
        screen.getByTestId('confirm-dialog-confirm-action')
      ).toBeInTheDocument();
    });

    // Click confirm delete
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    // Check if delete timeline dialog is closed
    await waitFor(() => {
      expect(
        screen.queryByTestId('confirm-dialog-title')
      ).not.toBeInTheDocument();
    });

    const timelineFile = mockGetMatchGroups.results[0]?.generatedTimelines?.[1];

    // Check that the timeline file has been added to the deletedGeneratedTimelineIds list in localStorage.
    await waitFor(() => {
      const deletedTimelineIds = ls.get('deletedGeneratedTimelineIds');
      expect(deletedTimelineIds).not.toBeNull();
      if (deletedTimelineIds && typeof deletedTimelineIds === 'string') {
        expect(JSON.parse(deletedTimelineIds)).toContainEqual({
          value: timelineFile ? timelineFile.id : '',
          expiry: expect.any(Number),
        });
      }
    });

    // Check if timeline file's status is PENDING DELETION
    await waitFor(() => {
      const deletedTimelineRow = screen.getByTestId(`GTimeline-row-cell-1-2-1`);
      expect(deletedTimelineRow).toBeInTheDocument();
      expect(deletedTimelineRow).toHaveTextContent('PENDING DELETION');
    });

  });
});
