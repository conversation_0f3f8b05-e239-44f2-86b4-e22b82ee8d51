
import { Box } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import { bootDidFinish } from "@store/modules/app/slice";
import { useSelector } from 'react-redux';
import useRouteChanged from '../Routes/useRouteChanged';

interface Props {
  children: React.ReactNode;
}

const PageContainer = ({ children }: Props) => {
  useRouteChanged();

  const bootFinished = useSelector(bootDidFinish);
  return bootFinished ? children : (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      height="100vh"
    >
      <CircularProgress size={75} />
    </Box>
  );
};

export default PageContainer;
