import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';
import { Search } from '../../../../../../types/tracker';
import { DeleteMatchGroupSearchPayload } from '../../../../../../types/requests';

const deleteMatchGroupSearch = async <
  ReqPayload,
  Data extends Partial<responses.getMatchGroup> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<DeleteMatchGroupSearchPayload>> | undefined
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { matchGroupId, matchGroup } = data;
  if (!matchGroup) {
    throw new ActionError('No match group provided');
  }

  const { searchId } = req.params;

  if (!searchId) {
    throw new ActionError('No searchId provided');
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  // check if id exists in match group
  const searchIdFound = matchGroup.searches?.find(
    (search: Search) => search.id === searchId
  );

  if (!searchIdFound) {
    throw new ActionError('Search does not exist in match group searches');
  }

  const updatedMatchGroupSearches = matchGroup.searches?.filter(
    (search: Search) => search.id !== searchId
  );

  try {
    const { createStructuredData } = await callGQL<
      responses.createStructuredData,
      ReqPayload,
      Data
    >(context, headers, queries.createStructuredData, {
      id: matchGroupId,
      schemaId,
      data: {
        ...matchGroup,
        searches: updatedMatchGroupSearches,
      },
    });

    if (createStructuredData) {
      const new_data = Object.assign({}, data, {
        matchGroup: matchGroup,
        searchId: searchId,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default deleteMatchGroupSearch;
