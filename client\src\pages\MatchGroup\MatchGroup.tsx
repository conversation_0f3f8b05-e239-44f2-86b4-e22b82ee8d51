import {
  Box,
  Button,
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Skeleton,
  Tab,
  Tabs,
  styled,
  OutlinedInput,
} from '@mui/material';
import {
  DetectedAttributes,
  Breadcrumbs,
  ExportDialog,
  Tracklet as TrackletComp,
  TrackletLoading,
} from '@components/common';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import {
  clearSelectedTracklets,
  deleteSelectedTracklets,
  getBoundingBoxes,
  getEvent,
  getFile,
  getMatchGroup,
  getMatchGroupSelectedTracklets,
  selectAllTracklets,
  selectEvent,
  selectMatchGroup,
  selectMatchSelectedTracklets,
  selectUserSelectedTrackletFile,
  selectUserSelectedTracklets,
  setSelectedTracklet,
  unselectAllTracklets,
  selectBoundingBoxes,
  setFileNameFilter,
  selectFileFilter,
  getThumbnails,
  selectThumbnails,
} from '@store/modules/matchGroup/slice';
import {
  addFileNameFilter,
  selectFileFilter as selectSearchFileFilter,
  selectSearchResults,
  getSearchResultsById,
  selectSelectedTracklet,
} from '@store/modules/searchResults/slice';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { useAppDispatch } from '@store/hooks';
import { useSelector } from 'react-redux';
import { range, uniq } from 'lodash';
import { Tracklet } from '@shared-types/tracker';
import './MatchGroup.scss';
import ConfirmDialog from '@components/common/ConfirmDialog';
import { TimelineEditor } from '@components/index';
import SearchResults from '@components/SearchResults/SearchResults';
import { SelectedTrackletsResults } from '@store/modules/matchGroup/types';
import TrackletSelectionHeader from '@components/TrackletSelectionHeader/TrackletSelectionHeader';
import {
  getUpdateMatchGroup,
  saveAndGenerateTimeline,
  saveTimeline,
  selectUpdateMatchGroup,
} from '@store/modules/timelineEditor/slice';
import FileMetadata from '@components/common/FileMetadata/FileMetadata';
import { PlayerReference } from 'video-react';
import { MediaPlayer } from '@veritone/glc-react';
import TimelineGenerateVideoDialog from '@components/TimelineGenerateVideoDialog/TimelineGenerateVideoDialog';
import ThumbnailScaler from '@components/common/ThumbnailScaler/ThumbnailScaler';
import { frameAlignStartTimeMs, frameAlignStopTimeMs } from '@utility/frames';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import FindAndFilterMatches from '@components/FindAndFilterMatches/FindAndFilterMatches';
import { CheckCircle } from '@mui/icons-material';

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&': {
    borderBottom: 0,
  },
  '&::before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  flexDirection: 'row',
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  paddingTop: 0,
  paddingLeft: theme.spacing(2),
  paddingBottom: theme.spacing(2),
}));

const filterTrackletsByFileName = (
  tracklets: SelectedTrackletsResults,
  selectedFileNames: string[]
) => {
  if (!selectedFileNames.length) {
    return tracklets;
  }
  return {
    ...tracklets,
    results: tracklets?.results?.filter((item) =>
      selectedFileNames.includes(item.fileName)
    ),
  };
};

enum MatchGroupTab {
  Detail = 'detail',
  Search = 'search',
  Timeline = 'timeline',
}

const MatchGroup = () => {
  const intl = useIntl();
  const { eventId, matchGroupId, searchId: searchIdFromParams } = useParams();
  const [searchId, setSearchId] = useState(searchIdFromParams);

  const [expanded, setExpanded] = useState<
    '' | 'attributes' | 'file-meta' | 'ai-engines'
  >('');
  const [tab, setTab] = useState<MatchGroupTab>(MatchGroupTab.Detail);
  const [deleteTrackletsDialog, setDeleteTrackletsDialog] = useState(false);
  const [exportDialog, setExportDialog] = useState<HTMLElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const playerRef = useRef<PlayerReference | null>(null);
  const [openGenerateTimelineDialog, setOpenGenerateTimelineDialog] =
    useState(false);
  const [thumbnailScale, setThumbnailScale] = useState(
    Number(localStorage.getItem('thumbnailScale') ?? 100)
  );
  const [selectViewingTracklet, setSelectViewingTracklet] = useState<
    Tracklet | undefined
  >(undefined);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const matchSelectedTracklets = useSelector(selectMatchSelectedTracklets);
  const userSelectedTracklets = useSelector(selectUserSelectedTracklets);
  const userSelectedTrackletFile = useSelector(selectUserSelectedTrackletFile);
  const matchGroup = useSelector(selectMatchGroup);
  const updateMatchGroup = useSelector(selectUpdateMatchGroup);

  const event = useSelector(selectEvent);
  const fileFilter = useSelector(selectFileFilter);
  const boundingBoxes = useSelector(selectBoundingBoxes);
  const thumbnails = useSelector(selectThumbnails);

  const searchFileFilter = useSelector(selectSearchFileFilter);
  const searchResults = useSelector(selectSearchResults);
  const selectedTracklet = useSelector(selectSelectedTracklet);

  const fileIds = searchFileFilter?.fileIds;
  const { pageSize, currentPage } = searchResults;
  const trackletsInMatchGroup = matchGroup.data?.selectedTracklets;

  const breadcrumbLoading =
    event.apiStatus === 'loading' || matchGroup.apiStatus === 'loading';
  const matchGroupsLoading = matchSelectedTracklets.apiStatus === 'loading';
  const searchResultsLoading = searchResults.apiStatus === 'loading';
  const currentFile = userSelectedTrackletFile.file;
  const fileLoading = userSelectedTrackletFile.apiStatus === 'loading';
  const noFile = !currentFile && !fileLoading;
  const hasFile = currentFile && !fileLoading;

  const currentSearchName =
    matchGroup?.data?.searches?.find((s) => s.id === searchId)?.searchName ??
    '';
  const streams = currentFile?.streams;
  const frameRate = currentFile?.frameRate ?? 30;
  const thumbnailAssets = currentFile?.thumbnailAssets;
  const isFileAndBoundingBoxesLoaded =
    userSelectedTrackletFile?.selectedTracklet?.trackletId ===
    boundingBoxes[0]?.trackletId;

  const filteredTrackletsByFilename = filterTrackletsByFileName(
    matchSelectedTracklets,
    fileFilter?.selectedFileNames
  );

  // Autoplay logic
  const autoplayStatusRef = useRef<
    '' | 'Seeking' | 'Seeked' | 'Playing' | 'Done'
  >('');
  const autoplayStopRef = useRef(0);
  const seekToAfterFileSet = useRef({ trackletId: '', seekTime: 0 });
  const previousPlayRef = useRef<'' | 'Play' | 'Pause'>('');

  const setDefaultAutoplay = () => {
    autoplayStatusRef.current = '';
    autoplayStopRef.current = 0;
  };

  useEffect(() => {
    const missingTracklets =
      filteredTrackletsByFilename?.results?.reduce<
        Array<{ trackletId: string; orgId: string; fileId: string }>
      >((arr, { trackletId, orgId, fileId }) => {
        const thumbnailInCache = thumbnails && trackletId in thumbnails;
        const isExpired =
          thumbnailInCache &&
          new Date(thumbnails[trackletId].expiresDateTime) <= new Date();

        if (!thumbnailInCache || isExpired) {
          arr.push({ trackletId, orgId, fileId });
        }
        return arr;
      }, []) ?? [];

    if (missingTracklets.length) {
      dispatch(getThumbnails(missingTracklets));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filteredTrackletsByFilename]);

  useEffect(() => {
    if (isFileAndBoundingBoxesLoaded) {
      playerRef.current?.subscribeToStateChange((state) => {
        setIsPlaying(!state.paused);

        // 1.) Track when seeking
        if (autoplayStatusRef.current === 'Seeking' && !state.seeking) {
          autoplayStatusRef.current = 'Seeked';
        }
        // 2.) Track when seeked
        else if (autoplayStatusRef.current === 'Seeked' && !state.paused) {
          autoplayStatusRef.current = 'Playing';
          playerRef.current?.play();
          if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        } else if (autoplayStatusRef.current === 'Seeked' && state.paused) {
          if (
            previousPlayRef.current === 'Pause' ||
            previousPlayRef.current === ''
          ) {
            playerRef.current?.pause();
          } else if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        }
        // 3.) Track while playing
        else if (autoplayStatusRef.current === 'Playing' && !state.paused) {
          previousPlayRef.current = 'Play';
          const alignedStopTimeMs = frameAlignStopTimeMs(
            autoplayStopRef.current,
            frameRate
          );
          const frameAlignCurrentTimeMs = frameAlignStartTimeMs(
            state.currentTime * 1000,
            frameRate
          );
          if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
            autoplayStatusRef.current = 'Done';
            playerRef.current?.pause();
            previousPlayRef.current = 'Pause';
          }
        } else if (autoplayStatusRef.current === 'Playing' && state.paused) {
          previousPlayRef.current = 'Pause';
          playerRef.current?.pause();
        }
        // 4.) Track when done playing
        else if (autoplayStatusRef.current === 'Done') {
          setDefaultAutoplay();
        }
      });
    }
  }, [isFileAndBoundingBoxesLoaded, frameRate]);

  useEffect(() => {
    if (!eventId) {
      return navigate(`/`);
    }

    if (!matchGroupId) {
      return navigate(`/event/${eventId}`);
    }

    if (!event.data) {
      dispatch(getEvent({ eventId }));
    }
    dispatch(getMatchGroup({ matchGroupId }));
    dispatch(getMatchGroupSelectedTracklets({ matchGroupId }));
    dispatch(getUpdateMatchGroup({ matchGroupId }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventId, matchGroupId, searchId]);

  useEffect(() => {
    if (
      userSelectedTrackletFile?.selectedTracklet &&
      boundingBoxes.length &&
      isFileAndBoundingBoxesLoaded
    ) {
      if (
        seekToAfterFileSet.current.trackletId ===
        userSelectedTrackletFile?.selectedTracklet.trackletId
      ) {
        // Bypass normal autoplay mode if a timestamp was clicked on a tracklet
        const seekTime =
          Math.ceil((seekToAfterFileSet.current.seekTime / 1000) * frameRate) /
          frameRate;
        playerRef.current?.seek(seekTime);
        seekToAfterFileSet.current.trackletId = '';
      } else {
        // Normal autoplay mode (stop after over)
        autoplayStopRef.current =
          userSelectedTrackletFile?.selectedTracklet.stopTimeMs;
        autoplayStatusRef.current = 'Seeking';
        const seekTime =
          Math.ceil(
            (userSelectedTrackletFile?.selectedTracklet.startTimeMs / 1000) *
              frameRate
          ) / frameRate;
        playerRef.current?.seek(seekTime);
      }
    }
  }, [
    boundingBoxes,
    frameRate,
    userSelectedTrackletFile,
    isFileAndBoundingBoxesLoaded,
  ]);

  useEffect(() => {
    if (!fileLoading && userSelectedTracklets) {
      setTimeout(() => setExpanded('attributes'), 100);
    } else {
      setExpanded('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileLoading]);

  useEffect(() => {
    if (selectViewingTracklet) {
      dispatch(getFile({ tracklet: selectViewingTracklet }));
      dispatch(
        getBoundingBoxes({
          trackletId: selectViewingTracklet.trackletId,
        })
      );
    }
  }, [selectViewingTracklet, dispatch]);

  useEffect(() => {
    if (searchIdFromParams) {
      setSearchId(searchIdFromParams);
      setTab(MatchGroupTab.Search);
    }
  }, [searchIdFromParams]);

  const getFileAndSeekTo = (tracklet: Tracklet, time: number) => {
    if (
      tracklet.trackletId ===
      userSelectedTrackletFile?.selectedTracklet?.trackletId
    ) {
      const seekTime = Math.ceil((time / 1000) * frameRate) / frameRate;
      playerRef.current?.seek(seekTime);
    }
    setSelectViewingTracklet(tracklet);
    seekToAfterFileSet.current = {
      trackletId: tracklet.trackletId,
      seekTime: time,
    };
  };

  const handleDetailAccordionChange =
    (panel: 'attributes' | 'file-meta' | 'ai-engines') =>
    (_event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : '');
    };

  const handleTrackletClick = (tracklet: Tracklet, event?: React.MouseEvent) => {
    dispatch(setSelectedTracklet({ tracklet, shiftKey: event?.shiftKey }));
  };

  const handleSelectAllTracklets = () => {
    dispatch(
      selectAllTracklets({ tracklets: matchSelectedTracklets?.results ?? [] })
    );
  };

  const handleUnselectAllTracklets = () => {
    dispatch(unselectAllTracklets());
  };

  const clearSelections = () => {
    dispatch(clearSelectedTracklets());
  };

  // TODO Implement sorting filtering and viewing potential searches
  const handleSortChange = (_event: SelectChangeEvent<string>) => {
    // dispatch(setSort(event.target.value));
  };

  const handleFileFilterChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    const results = matchSelectedTracklets?.results ?? [];
    const fileIds = [];
    for (const result of results) {
      if (value.includes(result.fileName)) {
        fileIds.push(result.fileId);
      }
    }
    dispatch(
      setFileNameFilter({
        selectedFileNames: typeof value === 'string' ? value.split(',') : value,
        displayString: typeof value === 'string' ? value : value.join(', '),
        selectedFileIds: fileIds,
      })
    );
  };

  const handleClearFileFilter = () => {
    dispatch(
      setFileNameFilter({
        selectedFileNames: [],
        displayString: '',
        selectedFileIds: [],
      })
    );
  };

  const handleTabChange = (newTab: MatchGroupTab) => {
    const lastedSearchId = matchGroup?.data?.searches?.[0]?.id;
    setSearchId(lastedSearchId);
    let newUrl = `/event/${eventId}/match-group/${matchGroupId}`;
    if (newTab === MatchGroupTab.Search && lastedSearchId) {
      newUrl += `/potential-match-search/${lastedSearchId}`;
    }
    navigate(newUrl);
    setTab(newTab);
  };

  const handleSearchFileFilterChange = (value: string | string[]) => {
    const { allFiles } = searchResults;
    const fileIds = [];
    for (const file of allFiles) {
      if (value.includes(file.fileName)) {
        fileIds.push(file.tdoId);
      }
    }
    dispatch(
      addFileNameFilter({
        selectedFileNames: typeof value === 'string' ? value.split(',') : value,
        displayString: typeof value === 'string' ? value : value.join(', '),
        selectedFileIds: fileIds,
      })
    );
    getSearchResultsByIdRequest(1, pageSize, fileIds);
  };
  const getSearchResultsByIdRequest = (
    page?: number,
    limit?: number,
    newFileIds?: string[]
  ) => {
    if (searchId) {
      dispatch(
        getSearchResultsById({
          matchGroupId,
          searchId,
          fileIds: uniq(newFileIds ? newFileIds : fileIds),
          page: page ? page : currentPage,
          limit: limit ?? pageSize,
        })
      );
    }
  };
  const handleClickExportDialog = (event: React.MouseEvent<HTMLElement>) => {
    setExportDialog(event.currentTarget);
  };

  const handleCloseExportDialog = () => {
    setExportDialog(null);
  };

  const getUserSelectedTrackletFile = (tracklet: Tracklet) => {
    setSelectViewingTracklet(tracklet);
  };

  return (
    <div className="match-group" data-testid="match-group">
      <div className="match-group__header">
        <Breadcrumbs
          loading={breadcrumbLoading}
          event={event.data}
          matchGroup={matchGroup.data}
          searchName={currentSearchName}
        />
        <div className="match-group__header-actions">
          {(tab === MatchGroupTab.Detail || tab === MatchGroupTab.Timeline) && (
            <>
              {fileFilter.selectedFileNames.length > 0 && (
                <Button
                  className="match-group__clear_file-filter"
                  variant="text"
                  onClick={handleClearFileFilter}
                  data-testid="match-group-clear-file-filter-button"
                >
                  {I18nTranslate.TranslateMessage('clearFilter')}
                </Button>
              )}
              <FormControl sx={{ display: 'none' }}>
                <InputLabel id="match-group-sort-label">Sort</InputLabel>
                <Select
                  size="small"
                  labelId="match-group-sort-label"
                  label="Sort"
                  value={'Placeholder'}
                  onChange={handleSortChange}
                  data-testid="match-group-sort-select"
                >
                  <MenuItem value={'Placeholder'}>System Timeline</MenuItem>
                </Select>
              </FormControl>
              <FormControl
                sx={{ width: 230 }}
                className="match-group__file-filter"
                size="small"
              >
                {!matchGroupsLoading &&
                (!fileFilter?.fileNames ||
                  fileFilter.fileNames.length === 0) ? (
                  <InputLabel>
                    {I18nTranslate.TranslateMessage('noFilesAvailable')}
                  </InputLabel>
                ) : (
                  <InputLabel>
                    {I18nTranslate.TranslateMessage('fileName')}
                  </InputLabel>
                )}
                <Select
                  multiple
                  value={fileFilter?.selectedFileNames ?? []}
                  onChange={handleFileFilterChange}
                  input={<OutlinedInput label="Name" />}
                  data-testid="match-group-filter-select"
                  disabled={
                    matchGroupsLoading ||
                    !fileFilter?.fileNames ||
                    fileFilter.fileNames.length === 0
                  }
                  renderValue={(selected) => selected.join(', ')}
                >
                  {uniq(fileFilter?.fileNames)?.map((fileName, i) => (
                    <MenuItem
                      key={fileName}
                      value={fileName}
                      data-testid={`match-group-filter-select-item-${i}`}
                    >
                      <Checkbox
                        size="small"
                        checked={fileFilter.selectedFileNames.includes(
                          fileName
                        )}
                        data-testid={`match-group-filter-select-item-checkbox-${i}`}
                        icon={<CheckCircle />}
                        checkedIcon={<CheckCircle />}
                      />
                      {fileName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </>
          )}

          {tab === MatchGroupTab.Search && (
            <FindAndFilterMatches
              filesFilter={searchFileFilter}
              filesFilterLoading={searchResultsLoading}
              handleFileFilterChange={handleSearchFileFilterChange}
              selectedTracklets={selectedTracklet}
              clearAll
            />
          )}
          {tab === MatchGroupTab.Detail && (
            <div className="match-group-export-button-container">
              <Button
                className="match-group-export-button"
                variant="outlined"
                color="primary"
                onClick={handleClickExportDialog}
              >
                Export
              </Button>
              <ExportDialog
                matchGroupName={matchGroup?.data?.name || 'Match Group'}
                anchorEl={exportDialog}
                onClose={handleCloseExportDialog}
              />
            </div>
          )}

          {tab === MatchGroupTab.Timeline && (
            <>
              <Button
                className="match-group__save-button"
                variant="outlined"
                color="primary"
                data-testid="match-group-save-timeline-button"
                onClick={() => dispatch(saveTimeline({ matchGroupId }))}
              >
                Save
              </Button>
              <Button
                className="match-group__save-generate-button"
                variant="outlined"
                color="primary"
                data-testid="match-group-save-generate-timeline-button"
                onClick={() => setOpenGenerateTimelineDialog(true)}
              >
                Generate
              </Button>
              {updateMatchGroup.matchGroup && (
                <TimelineGenerateVideoDialog
                  open={openGenerateTimelineDialog}
                  onClose={() => setOpenGenerateTimelineDialog(false)}
                  matchGroup={updateMatchGroup.matchGroup}
                  onSaveAndGenerateTimeline={(generatedTimelineName) =>
                    dispatch(
                      saveAndGenerateTimeline({
                        matchGroupId,
                        generatedTimelineName,
                      })
                    )
                  }
                />
              )}
            </>
          )}
        </div>
      </div>
      <div className="match-group__main-content-tabs">
        <Tabs
          data-testid="match-group-tabs"
          value={tab}
          indicatorColor="primary"
          onChange={(_, t) => handleTabChange(t)}
        >
          <Tab
            value="search"
            label={intl.formatMessage({ id: 'searchResults', defaultMessage: 'Search Results' })}
            data-testid="match-group-tab-search"
          />
          <Tab
            value="detail"
            label={intl.formatMessage({ id: 'verifiedMatches', defaultMessage: 'Verified Matches' })}
            data-testid="match-group-tab-detail"
          />
          <Tab
            value="timeline"
            label={intl.formatMessage({ id: 'timelineEditor', defaultMessage: 'Timeline Editor' })}
            data-testid="match-group-tab-timeline"
          />
        </Tabs>
      </div>
      <div className="match-group__main-content">
        {tab === MatchGroupTab.Search && (
          <div className="match-group__search">
            <SearchResults
              eventId={eventId}
              matchGroupId={matchGroupId}
              searchId={searchId}
            />
          </div>
        )}
        {tab === MatchGroupTab.Detail && (
          <>
            <div className="match-group__video_and_attributes">
              <div className="match-group__video">
                {!fileLoading && streams ? (
                  <MediaPlayer
                    uri=""
                    ref={playerRef}
                    streams={streams}
                    frameRate={frameRate}
                    boundingBoxes={boundingBoxes}
                    maxHeight={500}
                    thumbnailAssets={thumbnailAssets}
                  />
                ) : null}
              </div>
              <div className="match-group__tracklet-detail">
                {noFile && (
                  <div className="match-group__tracklet-detail-no-file">
                    {I18nTranslate.TranslateMessage(
                      'selectTrackletToViewDetails'
                    )}
                    .
                  </div>
                )}
                {fileLoading ? (
                  <>
                    <Skeleton
                      className="match-group__tracklet-detail-loading"
                      variant="rectangular"
                      height={48}
                    />
                    <Skeleton
                      className="match-group__tracklet-detail-loading match-group__accordion-ai-engines"
                      variant="rectangular"
                      height={48}
                    />
                    <Skeleton
                      className="match-group__tracklet-detail-loading"
                      variant="rectangular"
                      height={48}
                    />
                  </>
                ) : (
                  <>
                    {hasFile && (
                      <>
                        <Accordion
                          expanded={expanded === 'attributes'}
                          onChange={handleDetailAccordionChange('attributes')}
                        >
                          <AccordionSummary>
                            Detected Attributes
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box
                              className="detected-attributes__box"
                              display="flex"
                              gap={1}
                              flexWrap="wrap"
                              maxWidth={500}
                            >
                              <DetectedAttributes
                                attributes={
                                  userSelectedTrackletFile?.selectedTracklet
                                    ?.attributes
                                }
                              />
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                        <Accordion
                          className="match-group__accordion-ai-engines"
                          expanded={expanded === 'ai-engines'}
                          onChange={handleDetailAccordionChange('ai-engines')}
                        >
                          <AccordionSummary>AI Engines</AccordionSummary>
                          <AccordionDetails>
                            <Box
                              className="detected-attributes__box"
                              display="flex"
                              flexWrap="wrap"
                              maxWidth={500}
                            >
                              {I18nTranslate.TranslateMessage(
                                'vehicleAndPersonDetection'
                              )}
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                        <Accordion
                          expanded={expanded === 'file-meta'}
                          onChange={handleDetailAccordionChange('file-meta')}
                        >
                          <AccordionSummary>
                            {I18nTranslate.TranslateMessage('fileMetadata')}
                          </AccordionSummary>
                          <AccordionDetails>
                            <FileMetadata {...currentFile} />
                          </AccordionDetails>
                        </Accordion>
                      </>
                    )}
                  </>
                )}
              </div>
            </div>
            <div className="match-group__detail">
              <div className="match-group__tabbed-detections-main-container-header">
                <TrackletSelectionHeader
                  userSelectedTracklets={userSelectedTracklets}
                  tracklets={matchSelectedTracklets?.results ?? []}
                  selectAllTracklets={handleSelectAllTracklets}
                  unselectAllTracklets={handleUnselectAllTracklets}
                />
                <div
                  className="match-group__tabbed-detections-main-container-actions"
                  style={{
                    visibility:
                      userSelectedTracklets && userSelectedTracklets.length > 0
                        ? 'visible'
                        : 'hidden',
                  }}
                >
                  <Button
                    className="match-group__tabbed-detections-main-container-action"
                    variant="text"
                    color="primary"
                    onClick={() => setDeleteTrackletsDialog(true)}
                    data-testid="match-group-delete-selections"
                  >
                    <div className="material-symbols-outlined">delete</div>
                    {I18nTranslate.TranslateMessage('deleteSelections')}
                  </Button>
                  <Button
                    className="match-group__tabbed-detections-main-container-action"
                    variant="text"
                    color="primary"
                    onClick={clearSelections}
                    data-testid="match-group-clear-selections"
                  >
                    {I18nTranslate.TranslateMessage('clearSelections')}
                  </Button>
                </div>
              </div>
              <div
                className="match-group__tabbed-detections-main-container"
                data-testid="tabbed-detections"
              >
                <div className="match-group__tabbed-detections-tab-panels main__scrollbar">
                  <div
                    className="match-group__matched-detection-tab-container"
                    data-testid="matched-detection-tab"
                  >
                    <div className="main__tracklet_thumbnails-tracklets">
                      {!matchGroupsLoading &&
                        (filteredTrackletsByFilename.results &&
                        (filteredTrackletsByFilename?.results?.length ?? 0) >
                          0 ? (
                          filteredTrackletsByFilename.results?.map(
                            (tracklet, index) => {
                              const selected =
                                selectViewingTracklet?.trackletId ===
                                tracklet.trackletId;
                              const checked =
                                userSelectedTracklets?.find(
                                  (selected) =>
                                    selected.trackletId === tracklet.trackletId
                                ) !== undefined;
                              const thumbnailUrl =
                                thumbnails?.[tracklet.trackletId]?.thumbnailUrls
                                  .best;
                              const thumbnailIsExpired =
                                new Date(
                                  thumbnails?.[
                                    tracklet.trackletId
                                  ]?.expiresDateTime
                                ) <= new Date();

                              return thumbnailUrl && !thumbnailIsExpired ? (
                                <TrackletComp
                                  checked={checked}
                                  selected={selected}
                                  playing={isPlaying}
                                  showCheck={true}
                                  isInMatchGroup={trackletsInMatchGroup?.includes(
                                    tracklet.trackletId
                                  )}
                                  onTrackletStartTimeClick={(time) =>
                                    getFileAndSeekTo(tracklet, time)
                                  }
                                  onTrackletStopTimeClick={(time) =>
                                    getFileAndSeekTo(tracklet, time)
                                  }
                                  handleTrackletClick={
                                    getUserSelectedTrackletFile
                                  }
                                  handleTrackletCheck={handleTrackletClick}
                                  thumbnailUrl={thumbnailUrl}
                                  thumbnailScale={thumbnailScale}
                                  tracklet={tracklet}
                                  index={index}
                                />
                              ) : (
                                <TrackletLoading
                                  thumbnailScale={thumbnailScale}
                                  index={index}
                                />
                              );
                            }
                          )
                        ) : (
                          <div
                            className="match-group__no-tracklets-found"
                            data-testid="match-group-no-tracklets-found"
                          >
                            {intl.formatMessage({ id: 'addFirstTracklet', defaultMessage: 'Add your first detections by selecting them in the Potential Search Results.' })}
                          </div>
                        ))}
                      {matchGroupsLoading &&
                        range(100).map((i) => (
                          <Skeleton
                            className="main__tracklet_thumbnails-tracklet skeleton"
                            data-testid={`matched-detection-tab-tracklet-skeleton-${i}`}
                            key={`matched-detection-tab-tracklet-${i}`}
                            variant="rectangular"
                            style={{
                              width: `${(113 * thumbnailScale) / 100}px`,
                              height: `${(139 * thumbnailScale) / 100}px`,
                            }}
                          />
                        ))}
                    </div>
                  </div>
                </div>
                <div className="match-group__detections-footer">
                  <ThumbnailScaler
                    scale={thumbnailScale}
                    setScale={setThumbnailScale}
                    loading={matchGroupsLoading}
                  />
                </div>
              </div>
            </div>
          </>
        )}
        {tab === MatchGroupTab.Timeline && (
          <TimelineEditor
            matchGroup={matchGroup}
            matchSelectedTracklets={filteredTrackletsByFilename}
          />
        )}
      </div>
      {userSelectedTracklets && userSelectedTracklets.length > 0 && (
        <ConfirmDialog
          open={deleteTrackletsDialog}
          title="Delete Selections"
          content={intl.formatMessage(
            { id: 'deleteSelectedTrackletsMessage', defaultMessage: 'You are about to delete {noTracklets} selected detections. Are you sure you want to delete them?' },
            { noTracklets: userSelectedTracklets.length }
          )}
          confirmText="Delete"
          cancelText="Cancel"
          onConfirm={() => {
            if (matchGroupId !== undefined) {
              dispatch(
                deleteSelectedTracklets({
                  matchGroupId,
                  selectedTrackletIds: userSelectedTracklets.map(
                    ({ trackletId }) => trackletId
                  ),
                })
              );
              setDeleteTrackletsDialog(false);
            }
          }}
          onClose={() => setDeleteTrackletsDialog(false)}
        />
      )}
    </div>
  );
};

export default MatchGroup;
