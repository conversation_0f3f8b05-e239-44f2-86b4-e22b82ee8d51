import { Context } from '@application/types';
import { ValidationError } from '@common/errors';
import { queries, responses } from '@tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { fingerprintSearch } from '../searchTracklets/elasticSearch';
import { SearchTrackletsWithIntersectingBoxResponse } from '../../../../../../types/responses';

const validDetectionType = ['person', 'vehicle'];

export interface SearchTrackletsWithIntersectingBoxData {
  searchData: SearchTrackletsWithIntersectingBoxResponse;
}

const searchTrackletsWithIntersectingBox = async <
  ReqPayload,
  Data extends Partial<responses.getEvent> & Partial<responses.getFile>,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & SearchTrackletsWithIntersectingBoxData>
> => {
  const { data, req, log } = context;
  const { pageSize, currentPage, type, xMin, xMax, yMin, yMax } = req.query;
  const { fileId } = req.params;
  const headers = { Authorization: req.headers.authorization };

  if (!type || !validDetectionType.includes(type as string)) {
    log.error("Missing or invalid query param 'type' (person | vehicle)");
    throw new ValidationError(
      "Missing or invalid query param 'type' (person | vehicle)"
    );
  }

  if (
    xMin === undefined ||
    xMax === undefined ||
    yMin === undefined ||
    yMax === undefined ||
    Number.isNaN(parseInt(xMin as string)) ||
    Number.isNaN(parseInt(xMax as string)) ||
    Number.isNaN(parseInt(yMin as string)) ||
    Number.isNaN(parseInt(yMax as string))
  ) {
    log.error('Missing or invalid box query param (xMin, xMax, yMin, yMax)');
    throw new ValidationError(
      'Missing or invalid box query param (xMin, xMax, yMin, yMax)'
    );
  }

  // Pull the engine id stored in the event, otherwise use the default.
  // Do not ever change the default engine GUID. This will break old events that do not store the engine id.
  const trackerEngineId =
    data?.event?.trackerEngineId ?? 'd77d6133-a801-472c-bc7e-48ddafec8590';

  const pgSize = Number(pageSize ?? 100);
  const currentPg = Number(currentPage ?? 1);
  const limit = pgSize;
  const offset = (currentPg - 1) * pgSize;

  // Via Stefan Minkov:
  // The performance/load consideration here is for the sum offset+limit. There are no actual support for offset in elasticsearch, so in the search service we just request aggregations.size=offset+limit and then we trim offset count bucket items before sending the response to the client. i.e. the performance should get progressively slower, the more pages you go trough.

  const vars = {
    search: {
      index: ['mine'],
      limit: 1000,
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'term',
            field: 'recordingId',
            value: fileId,
          },
        ],
      },
      aggregate: [
        {
          operator: 'filter',
          name: 'prefilter',
          field: 'object-recognition.series',
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'bounding_box',
                field: 'object-recognition.series.boundingBox',
                relation: 'intersects',
                coordinates: [
                  [xMin, yMin],
                  [xMax, yMax],
                ],
              },
            ],
          },
          aggregate: [
            {
              operator: 'term',
              field: 'object-recognition.series.found',
              aggregate: [
                {
                  operator: 'term',
                  field: 'object-recognition.series.referenceId',
                  offset,
                  limit,
                  name: 'referenceIds',
                  distinct: true,
                  sort: {
                    field: 'object-recognition.series.start',
                    criteria: 'value',
                    order: 'asc',
                  },
                },
                {
                  operator: 'count',
                  name: 'TotalCount',
                  field: 'object-recognition.series.referenceId',
                  distinct: true,
                },
              ],
            },
          ],
        },
      ],
    },
  };

  // const varsCount = { ...vars };
  // varsCount.search.aggregate[0].aggregate[0].aggregate[0].operator = 'count';

  const { searchMedia: results } = await callGQL<
    responses.IntersectingTrackletsAggregation,
    ReqPayload,
    Data
  >(context, headers, queries.searchMedia, vars);

  console.log('searchMedia', queries.searchMedia);
  console.log('vars', JSON.stringify(vars));
  console.log('results', JSON.stringify(results));

  const aggregationForType =
    results.jsondata.aggregations.prefilter.prefilter.prefilter.buckets.prefilter[
      'object-recognition.series.found'
    ].buckets.find((b) => b.key === type);

  let intersectingTracklets: string[] = [''];
  let totalResults = 0;
  if (aggregationForType) {
    intersectingTracklets = aggregationForType.referenceIds.buckets.map(
      (b) => b.key
    );
    totalResults = aggregationForType.TotalCount.value;
  }

  let { tracklets } = await fingerprintSearch({
    referenceTrackletIds: intersectingTracklets,
    limit: totalResults,
    offset: 0,
    context,
    headers,
    type: [type as string],
    times: true,
    trackerEngineId,
  });

  // Reorder according to the original search order
  tracklets = intersectingTracklets.map(
    (t) =>
      tracklets.find((fsTracklet) => fsTracklet.trackletId === t) ||
      tracklets[0]
  );

  return Object.assign({}, context, {
    data: Object.assign({}, data, {
      searchData: {
        results: tracklets.filter((t) => !!t), // Remove [null] as a possible result if no results found
        type: type as string,
        fileId: fileId as string,
        currentPage: currentPg,
        pageSize: pgSize,
        totalCount: totalResults,
        totalPages: Math.ceil(totalResults / pgSize),
      },
    }),
  });
};

export default searchTrackletsWithIntersectingBox;
