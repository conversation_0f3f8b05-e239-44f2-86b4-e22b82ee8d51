import './index.scss';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { ChangeEvent, ReactElement, useEffect, useState } from 'react';
import { Button, DialogContentText, TextField } from '@mui/material';
import { useIntl } from 'react-intl';

const ConfirmDialog = ({
  open,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onClose,
  width,
  reEnterName,
  name,
  type,
  editName,
}: ConfirmDialogProps) => {
  const intl = useIntl();
  const [input, setInput] = useState('');

  const disableConfirm = (reEnterName && input !== name) || (editName && input === '');

  const handleChangeInput = (e: ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleClose = () => {
    setInput('');
    onClose();
  };

  const handleConfirm = () => {
    onConfirm(input);
    setInput('');
  };

  useEffect(() => {
    if (name && editName && open) {
      setInput(name);
    }
  }, [name, editName, open, setInput]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="confirm-dialog-title"
      aria-describedby="confirm-dialog-description"
      data-testid="confirm-dialog"
      PaperProps={{
        style: {
          width: `${width ?? 450}px`,
          padding: '12px',
        },
      }}
    >
      <DialogTitle
        className="confirm-dialog-title"
        data-testid="confirm-dialog-title"
      >
        {title}
      </DialogTitle>
      <DialogContent
        className='confirm-dialog-content'
      >
        <DialogContentText
          className="confirm-dialog-description"
          data-testid="confirm-dialog-description"
        >
          {content}
        </DialogContentText>
        {reEnterName && name && type && (
          <TextField
            value={input}
            fullWidth size='small'
            onChange={handleChangeInput}
            error={input !== '' && input !== name}
            label={intl.formatMessage({ id: 'confirmMessage', defaultMessage: 'Enter {type} name to confirm.'}, { type })}
            inputProps={{ 'data-testid': 'confirm-dialog-delete-input' }}
          />
        )}
        {name && editName && (
          <TextField
            value={input}
            fullWidth size='small'
            onChange={handleChangeInput}
            inputProps={{ 'data-testid': 'confirm-dialog-edit-input' }}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="inherit" data-testid="confirm-dialog-cancel-action">
          {cancelText}
        </Button>
        <Button
          data-testid="confirm-dialog-confirm-action"
          onClick={handleConfirm}
          variant="contained"
          color="primary"
          autoFocus
          disabled={disableConfirm}
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};


interface ConfirmDialogProps {
  open: boolean;
  title: string;
  content?: string | ReactElement;
  confirmText: string;
  cancelText: string;
  onConfirm: (input?: string) => void;
  onClose: () => void;
  width?: number;
  reEnterName?: boolean;
  name?: string;
  type?: string;
  editName?: boolean;
}

export default ConfirmDialog;
