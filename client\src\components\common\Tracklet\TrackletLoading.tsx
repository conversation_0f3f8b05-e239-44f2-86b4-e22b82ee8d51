import { Skeleton } from "@mui/material";

interface Props {
  thumbnailScale: number;
  index: number;
}

const TrackletLoading = (props: Props) => {
  const { thumbnailScale, index } = props;

  return <div style={{ position: 'relative', cursor: 'pointer' }}>
    <Skeleton
      className="main__tracklet_thumbnails-tracklet skeleton"
      data-testid={`LoadingTracklet-${index}`}
      key={`FileResultsTrackletContainers-${index}`}
      variant="rectangular"
      style={{
        width: `${113 * thumbnailScale / 100}px`,
        height: `${113 * thumbnailScale / 100}px`,
      }}
    />
  </div>;
};

export default TrackletLoading;
