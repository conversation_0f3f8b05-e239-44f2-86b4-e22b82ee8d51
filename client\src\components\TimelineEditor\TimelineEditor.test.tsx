import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import axios from 'axios';
import { configureAppStore } from '@store/store';
import { GetMatchGroupResponse } from '@shared-types/responses';
import TimelineEditor from './TimelineEditor';
import {
  MatchGroupState,
  SelectedTrackletsResults,
} from '@store/modules/matchGroup/types';
import { TrackletAttributes } from '../../../test/testConstants';
import { setThumbnails } from '@store/modules/timelineEditor/slice';
import { I18nProvider, LOCALES } from '@i18n';

class MockEvent extends Event {
  clientX: number;
  dataTransfer: DataTransfer;

  constructor(
    type: string,
    options: { clientX: number; dataTransfer: DataTransfer }
  ) {
    super(type, { bubbles: true, cancelable: true });
    this.clientX = options.clientX;
    this.dataTransfer = options.dataTransfer;
  }
}

const mockDataTransfer: DataTransfer = {
  setData: jest.fn(),
  getData: jest.fn(),
  dropEffect: 'move',
  setDragImage: jest.fn(),
  effectAllowed: 'move',
  files: {
    length: 0,
    item: jest.fn(),
    [Symbol.iterator]: jest.fn(),
  },
  items: {
    add: jest.fn(),
    clear: jest.fn(),
    remove: jest.fn(),
    length: 0,
    [Symbol.iterator]: jest.fn(),
  },
  types: [],
  clearData: jest.fn(),
};

jest.mock('axios');

jest.setTimeout(20000);

const mockedAxios = axios as jest.Mocked<typeof axios>;

const groupId = 'fed5a705-98c8-44ef-8097-2067f552657f';

const startTimeMs = 10000;

jest.mock('uuid', () => ({
  v4: jest.fn(() => groupId),
}));

const mockGetSelectedTrackletsState: SelectedTrackletsResults = {
  results: [
    {
      orgId: '1',
      trackletId: '11',
      fileId: '1',
      fileName: 'fileName 1',
      startTimeMs,
      stopTimeMs: 20000,
      type: 'person',
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      confidence: 0.9,
    },
    {
      orgId: '1',
      trackletId: '22',
      fileId: '2',
      fileName: 'fileName 2',
      startTimeMs: 10000,
      stopTimeMs: 13000,
      type: 'person',
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      confidence: 0.8,
    },
    {
      orgId: '1',
      trackletId: '33',
      fileId: '3',
      fileName: 'fileName 3',
      startTimeMs: 10000,
      stopTimeMs: 20000,
      type: 'person',
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      confidence: 0.6,
    },
  ],
  matchGroupId: 'a85f9b41-f550-4754-8871-518266dbae49',
  matchGroupName: 'John Doe',
  eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
  apiStatus: 'idle',
};

const mockGetMatchGroupsResponse: GetMatchGroupResponse = {
  matchGroup: {
    id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
    name: 'test match group',
    eventId: '1b9c7f1a-f759-4065-94c3-4dd48868fb06',
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Potential Match Search 1',
        referenceTrackletId: 'reference-tracketlet-id-mock-1',
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Potential Match Search 2',
        referenceTrackletId: 'reference-tracketlet-id-mock-2',
      },
    ],
    modifiedDateTime: '2024-05-16T22:50:21.111Z',
    selectedTracklets: ['11', '22', '33'],
  },
};

const mockMatchGroup: MatchGroupState = {
  data: mockGetMatchGroupsResponse.matchGroup,
  apiStatus: 'idle',
};

const mockGetFileResponse = {
  data: {
    file: {
      createdByName: 'Sam Claybrook',
      eventId: '2fc73b9e-f5f8-4dd4-8483-1c4971b4041e',
      eventName: "Sam's Test",
      fileName: 'Walking Tour 2(1).mp4',
      fileSize: 1764379024,
      fileType: 'video/mp4',
      frameRate: 30,
      id: '1',
      length: 3600.733333,
      location: 'unavailable',
      primaryAsset: {
        signedUri:
          'https://stage.us-1.veritone.com/media-streamer/download/tdo/3230017547',
      },
      status: 'processed',
      streams: [
        {
          uri: 'https://stage.us-1.veritone.com/media-streamer/stream/3230017547/master.m3u8',
          protocol: 'hls',
        },
        {
          uri: 'https://stage.us-1.veritone.com/media-streamer/stream/3230017547/dash.mpd',
          protocol: 'dash',
        },
      ],
      thumbnailUrl:
        'https://s3.amazonaws.com/stage-api.veritone.com/7682/asset/2024/7/3/3230017547/3230017547_5NhzhM3e8G.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQMR5VATUHU3MEGOA%2F20240911%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240911T071811Z&X-Amz-Expires=86400&X-Amz-Signature=fe2408893f6f486f333c7b5cc276e2e866388789ef1d42d8a8662c77f1eac5a3&X-Amz-SignedHeaders=host',
      uploadDate: '2024-08-14T16:12:38.204Z',
    },
  },
};

describe('Timeline tab', () => {
  beforeEach(() => {
    jest
      .spyOn(HTMLMediaElement.prototype, 'pause')
      .mockImplementation(() => {});
    jest.spyOn(HTMLMediaElement.prototype, 'load').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Timeline shows selected tracklets from match group', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineEditor
            matchSelectedTracklets={mockGetSelectedTrackletsState}
            matchGroup={mockMatchGroup}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
      expect(
        screen.queryByTestId('timeline-editor-no-tracklets-found')
      ).not.toBeInTheDocument();
    });
  });

  test('Timeline render best thumbnail from matchGroup', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineEditor
            matchSelectedTracklets={mockGetSelectedTrackletsState}
            matchGroup={mockMatchGroup}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });
    expect(screen.getByTestId('Tracklet-0-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-1-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-2-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
  });

  test('Timeline shows no tracklets detected message when results are empty', async () => {
    const mockedStore = configureAppStore();

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineEditor
            matchSelectedTracklets={{
              ...mockGetSelectedTrackletsState,
              results: [],
            }}
            matchGroup={mockMatchGroup}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        screen.getByTestId('timeline-editor-no-tracklets-found')
      ).toBeInTheDocument();
    });
  });

  test('Timeline shows skeleton and does not show no tracklets detected message', async () => {
    const mockedStore = configureAppStore();

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineEditor
            matchSelectedTracklets={{
              ...mockGetSelectedTrackletsState,
              apiStatus: 'loading',
              results: [],
            }}
            matchGroup={mockMatchGroup}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Skeleton should be visible and message should not be visible
    await waitFor(() => {
      expect(screen.getByTestId('matched-detection-tab-skeleton-0'))
        .toBeInTheDocument;
      expect(
        screen.queryByTestId('timeline-editor-no-tracklets-found')?.style
          .display
      ).toBe('none');
    });
  });

  test('User can drag tracklets into timeline, zoom in, and drag the scrubber', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <div style={{ width: 2000, height: 1000 }}>
            <TimelineEditor
              matchSelectedTracklets={mockGetSelectedTrackletsState}
              matchGroup={mockMatchGroup}
            />
          </div>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // Drag the first tracklet
    fireEvent.drag(screen.getByTestId('Tracklet-0'));

    // Drag over drop zone
    fireEvent.dragEnter(screen.getByTestId('TimelineEditorEmpty-Clip-0'));
    fireEvent.dragOver(screen.getByTestId('TimelineEditorEmpty-Clip-0'));

    // Drop the first tracklet
    fireEvent.drop(screen.getByTestId('TimelineEditorEmpty-Clip-0'));

    // Dragged Tracklet appears in the timeline
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-Clip-0-11')
      ).toBeInTheDocument();
    });

    // Video length updated
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-VideoLength')
      ).toHaveTextContent('00:00:10');
    });

    // Drag the second tracklet
    fireEvent.drag(screen.getByTestId('Tracklet-1'));

    // Drag over drop zone
    fireEvent.dragEnter(screen.getByTestId('TimelineEditor-Clip-0-11'));
    fireEvent.dragOver(screen.getByTestId('TimelineEditor-Clip-0-11'));

    // Drop the second tracklet
    fireEvent.drop(screen.getByTestId('TimelineEditor-Clip-0-11'));

    // Dragged Tracklet appears in the timeline
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-Clip-1-33')
      ).toBeInTheDocument();
    });

    // Video length updated
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-VideoLength')
      ).toHaveTextContent('00:00:20');
    });

    // Api called with timeline update
    await waitFor(
      () => {
        expect(mockedAxios.request).toHaveBeenCalledWith({
          url: 'http://localhost/api/v1/match-groups/44573a2a-fc44-45bb-ba03-4d6f25762f1e',
          headers: {
            Authorization: 'Bearer null',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
          data: {
            timelineProject: {
              groups: [
                {
                  id: groupId,
                  name: 'Default Group',
                  tracklets: [
                    {
                      orgId: '1',
                      attributes: TrackletAttributes,
                      confidence: 0.9,
                      fileId: '1',
                      fileName: 'fileName 1',
                      startTimeMs: 10000,
                      stopTimeMs: 20000,
                      thumbnailUrls: {
                        best: '/tracklet-example-best.png',
                      },
                      trackletId: '11',
                      type: 'person',
                    },
                  ],
                },
                {
                  id: groupId,
                  name: 'Default Group',
                  tracklets: [
                    {
                      orgId: '1',
                      attributes: TrackletAttributes,
                      confidence: 0.6,
                      fileId: '3',
                      fileName: 'fileName 3',
                      startTimeMs: 10000,
                      stopTimeMs: 20000,
                      thumbnailUrls: {
                        best: '/tracklet-example-best.png',
                      },
                      trackletId: '33',
                      type: 'person',
                    },
                  ],
                },
              ],
            },
          },
          maxRedirects: 0,
          withCredentials: false,
          method: 'patch',
          signal: expect.any(AbortSignal),
        });
      },
      { timeout: 10000 }
    );
  });

  test('Timeline PATCH replaces undefined thumbnailUrls with cached thumbnails', async () => {
    const mockedStore = configureAppStore();

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <TimelineEditor
            matchSelectedTracklets={{
              results: [
                {
                  orgId: '1',
                  trackletId: '11',
                  fileId: '1',
                  fileName: 'fileName 1',
                  startTimeMs: 10000,
                  stopTimeMs: 20000,
                  type: 'person',
                  attributes: TrackletAttributes,
                  thumbnailUrls: {
                    best: 'the best url',
                  },
                  confidence: 0.9,
                },
              ],
              matchGroupId: 'a85f9b41-f550-4754-8871-518266dbae49',
              matchGroupName: 'John Doe',
              eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
              apiStatus: 'idle',
            }}
            matchGroup={{
              ...mockMatchGroup,
              data: {
                id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
                name: 'test match group',
                eventId: '1b9c7f1a-f759-4065-94c3-4dd48868fb06',
                modifiedDateTime: '2024-05-16T22:50:21.111Z',
                timelineProject: {
                  groups: [
                    {
                      id: 'fed5a705-98c8-44ef-8097-2067f552657f',
                      name: 'Default Group',
                      tracklets: [
                        {
                          orgId: '1',
                          trackletId: '11',
                          fileId: '1',
                          fileName: 'fileName 1',
                          startTimeMs: 10000,
                          stopTimeMs: 20000,
                          type: 'person',
                          attributes: TrackletAttributes,
                          thumbnailUrls: {
                            best: 'the best url',
                          },
                          confidence: 0.9,
                        },
                      ],
                    },
                  ],
                  modifiedDateTime: '2024-05-16T22:50:21.111Z',
                  modifiedUserId: '1',
                },
              },
            }}
          />
        </I18nProvider>
      </Provider>
    );

    act(() => {
      mockedStore.dispatch(
        setThumbnails({
          '11': {
            thumbnailUrls: {
              best: '/tracklet-example-best.png',
            },
            expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          },
        })
      );
    });

    expect(axios.request).toHaveBeenCalledWith(
      expect.objectContaining({
        url: 'http://localhost/api/v1/match-groups/44573a2a-fc44-45bb-ba03-4d6f25762f1e',
        data: {
          timelineProject: {
            groups: [
              {
                id: 'fed5a705-98c8-44ef-8097-2067f552657f',
                name: 'Default Group',
                tracklets: [
                  {
                    orgId: '1',
                    trackletId: '11',
                    fileId: '1',
                    fileName: 'fileName 1',
                    startTimeMs: 10000,
                    stopTimeMs: 20000,
                    type: 'person',
                    attributes: TrackletAttributes,
                    thumbnailUrls: {
                      best: '/tracklet-example-best.png',
                    },
                    confidence: 0.9,
                  },
                ],
              },
            ],
          },
        },
      })
    );
  });

  test('Thumbnails scale', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <div style={{ width: 2000, height: 1000 }}>
            <TimelineEditor
              matchSelectedTracklets={mockGetSelectedTrackletsState}
              matchGroup={mockMatchGroup}
            />
          </div>
        </I18nProvider>
      </Provider>
    );

    const tracklet = await screen.findByTestId('Tracklet-0');
    expect(tracklet).toBeInTheDocument();
    expect(tracklet).toHaveStyle({ width: '113px' });

    const thumbnailScaler = screen.getByTestId('thumbnail-scaler');
    const thumbnailScalerButton = screen.getByTestId('thumbnail-scaler-button');
    expect(thumbnailScaler).toBeInTheDocument();
    expect(thumbnailScalerButton).toBeInTheDocument();

    // Show slider
    fireEvent.click(thumbnailScalerButton);
    const thumbnailScalerSlider = screen.getByTestId('thumbnail-scaler-slider');
    expect(thumbnailScalerSlider).toBeInTheDocument();

    // Increment scale to 200%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: 1 });
    expect(tracklet).toHaveStyle({ width: '226px' });

    // Decrement scale to 10%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: -1 });
    expect(tracklet).toHaveStyle({ width: '67.8px' });
  });

  test("Drag and drop red time indicator forward, video shouldn't be played in selected tracklet's start time", async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    mockedAxios.request.mockResolvedValue(mockGetFileResponse);

    // mock getBoundingClientRect().width for timeline-scrubber
    const mockGetBoundingClientRect = jest.fn(() => ({
      width: 1500,
      height: 700,
      top: 0,
      left: 0,
      right: 500,
      bottom: 300,
      x: 0,
      y: 0,
      toJSON: jest.fn(),
    }));
    Object.defineProperty(HTMLElement.prototype, 'getBoundingClientRect', {
      value: mockGetBoundingClientRect,
    });

    const { container } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <div style={{ width: 2000, height: 1000 }}>
            <TimelineEditor
              matchSelectedTracklets={mockGetSelectedTrackletsState}
              matchGroup={mockMatchGroup}
            />
          </div>
        </I18nProvider>
      </Provider>
    );
    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // Drag the first tracklet
    fireEvent.drag(screen.getByTestId('Tracklet-0'));

    // Drag over drop zone
    fireEvent.dragEnter(screen.getByTestId('TimelineEditorEmpty-Clip-0'));
    fireEvent.dragOver(screen.getByTestId('TimelineEditorEmpty-Clip-0'));

    // Drop the first tracklet
    fireEvent.drop(screen.getByTestId('TimelineEditorEmpty-Clip-0'));

    // Dragged Tracklet appears in the timeline
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-Clip-0-11')
      ).toBeInTheDocument();
    });

    // Video length updated
    await waitFor(() => {
      expect(
        screen.getByTestId('TimelineEditor-VideoLength')
      ).toHaveTextContent('00:00:10');
    });

    fireEvent.click(screen.getByTestId('TimelineEditor-Clip-0-11'));

    // call getFile action
    await waitFor(() => {
      expect(mockedAxios.request).toHaveBeenCalledWith({
        url: 'http://localhost/api/v1/file/1',
        headers: {
          Authorization: 'Bearer null',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        maxRedirects: 0,
        withCredentials: false,
        method: 'get',
        signal: expect.any(AbortSignal),
      });
    });

    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const videoPlayer = container.querySelector(
      '.video-react-video'
    ) as HTMLVideoElement;
    expect(videoPlayer).toBeInTheDocument();

    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const currentTimeControl = container.querySelector(
      '.timeline-editor__timeline-current-time-container'
    ) as HTMLDivElement;
    expect(currentTimeControl).toBeInTheDocument();

    // drag and drop the time indicator within the same tracklet
    const dragStartEvent = new MockEvent('dragStart', {
      clientX: 0,
      dataTransfer: mockDataTransfer,
    });
    fireEvent(currentTimeControl, dragStartEvent);

    // drag the time indicator to the right
    const dragEvent = new MockEvent('drag', {
      clientX: 200,
      dataTransfer: mockDataTransfer,
    });
    fireEvent(currentTimeControl, dragEvent);

    fireEvent.dragOver(currentTimeControl, {
      dataTransfer: mockDataTransfer,
    });

    fireEvent.dragEnd(currentTimeControl, {
      dataTransfer: mockDataTransfer,
    });

    expect(videoPlayer.currentTime).toBeGreaterThan(startTimeMs / 1000);
  });
});
