.file-row {
  display: block;
  cursor: pointer;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  &:hover {
    background-color: var(--row-active);
    filter: contrast(1.05);
  }

  &.selected {
    background-color: var(--row-active);
  }

  .file-row__row-details {
    margin-top: -10px;
    margin-bottom: 10px;
    transition: height 300ms;
    overflow: hidden;

    .file-row__row-details-line {
      opacity: 0.5;
      background-color: var(--text-primary);
      width: 3px;
      height: 131px;
      margin-left: 70px;
      margin-right: 27px;
      transition: height 300ms;
      float: left;
    }
  }

  .file-row__row {
    position: relative;
    display: flex;
    padding: 11.5px 0 11.5px 18px;

    .file-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .file-row__cell-select {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .file-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 23px 16px 23px 0;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;

        &.pending {
          font-style: italic;
        }
      }

      .file-row__cell-fileName, .file-row__cell-uploadDate {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .file-row__cell-fileName {
        padding-right: 5px;
      }

      .file-row__cell-status {
        .processed {
          color: var(--status-complete);
          border-color: var(--status-complete);
        }

        .processing {
          color: var(--status-running);
          border-color: var(--status-running);
        }

        .error {
          color: var(--status-failed);
          border-color: var(--status-failed);
        }

        .pending-deletion {
          color: var(--disabled);
          border-color: var(--disabled);
        }

        .pending {
          color: var(--status-pending);
          border-color: var(--status-pending);
        }

        * {
          text-align: center;
          line-height: 24px;
          height: 24px;

          @include size-1-bold;
        }

      }
    }
  }
}