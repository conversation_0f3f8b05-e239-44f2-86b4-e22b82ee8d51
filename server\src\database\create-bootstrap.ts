// import { Knex } from 'knex';

// function createBootstrap({
//   dbConnection,
//   log,
// }: {
//   dbConnection: Knex;
//   log: Logger;
// }) {
//   if (!dbConnection) {
//     log.error('Cannot run migrations without db connection');
//     return;
//   }
//   log.info('Running migrations...');

//   try {
//     dbConnection.migrate.latest();
//     log.info('Running migrations complete');
//     return dbConnection;
//   } catch (err) {
//     log.error('Running migrations failed.', err);
//     throw err;
//   }
// }

// export default createBootstrap;
