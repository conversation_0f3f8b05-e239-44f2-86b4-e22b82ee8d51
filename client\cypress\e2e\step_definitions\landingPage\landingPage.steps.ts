import { Before, Given, Then, When, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';
import { ButtonActions, ValidationButtonTypes, ButtonActionMap } from '../../../support/helperFunction/eventScreenHelper';

Before(() => {
  cy.LoginLandingPage();
});

Given('The main page is loaded', () => {
  cy.waitMainPageIsLoaded();
});

Then('The user should see the correct column headers for Event Name and Event Time', () => {
  landingPage.verifyColumnHeaders();
});

Then('The user should see the application title Track', () => {
  landingPage.verifyAppTitle();
});

When('The user clicks on the {string} tab', (tabName: string) => {
  landingPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

Then('The {string} tab should be active', (activeTabName: string) => {
  landingPage.verifyTabIsActive(activeTabName);
});

When('The user clicks the {string} breadcrumb', (breadcrumbText: string) => {
  landingPage.clickBreadcrumb(breadcrumbText);
});

Then('The page should not navigate away', () => {
  landingPage.verifyPageNotNavigated();
});

When('The user selects the event named {string}', (eventName: string) => {
  landingPage.selectEvent(eventName);
});

Then('The following event details should be visible:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ Field: string; 'Expected Value': string }>;
  landingPage.verifyEventDetails(rows);
});

Then('Event Time has format: {string}', (format: string) => {
  landingPage.verifyEventTimeFormat(format);
});

When('The user clicks on event name on the right side', () => {
  landingPage.clickEventNameEdit();
});

When('The user changes the name to {string}', (newName: string) => {
  landingPage.changeEventName(newName);
});

Then('The user can change name for event successfully', () => {
  landingPage.verifyEventNameChanged();
});

When('The user clicks on {string}', (buttonText: string) => {
  const buttonActionMap: ButtonActionMap = {
    [ButtonActions.VIEW_EVENT]: () => landingPage.clickViewEventButton(),
    [ButtonActions.DELETE_EVENT]: () => landingPage.clickDeleteEventButton(),
  };
  const action = buttonActionMap[buttonText as ButtonActions];
  action();
});

Then('The user should navigate to event details page', () => {
  landingPage.verifyNavigationToEventDetails();
});

When('The user enters wrong event name {string}', (wrongName: string) => {
  landingPage.enterWrongEventName(wrongName);
});

Then('The {string} button should still be disabled and textbox highlighted in red', (buttonText: string) => {
  if (buttonText === ValidationButtonTypes.DELETE) {
    landingPage.verifyDeleteButtonDisabledAndTextboxError();
  }
});

When('The user clicks on the {string} column header', (columnName: string) => {
  landingPage.clickColumnHeader(columnName);
});

Then('{string} is sorted by {string}', (columnName: string, sortedBy: string) => {
  // Click until we get the desired sort state, then verify
  landingPage.clickColumnHeaderUntilSorted(columnName, sortedBy);
  landingPage.verifyColumnSortState(columnName, sortedBy);
});

When('The user enters {string} into the search bar', (keyword: string) => {
  landingPage.enterSearchKeyword(keyword);
});

Then('The displayed {word} results should contain {string}', (searchType: string, keyword: string) => {
  if (searchType === 'event' || searchType === 'file') {
    const pluralSearchType = `${searchType}s` as 'events' | 'files';
    landingPage.verifySearchResults(keyword, pluralSearchType);
  } else {
    throw new Error(`Unsupported search type: ${searchType}. Expected 'event' or 'file'.`);
  }
});

Then('The user should see the {string} label', (label: string) => {
  if (label === 'Results Per Page') {
    landingPage.verifyResultsPerPageLabel();
  }
});

When('The user changes the results per page and verifies the following options:', (dataTable: DataTable) => {
  const rows = dataTable.hashes();
  rows.forEach((row) => {
    const perPage = row.PerPage;
    landingPage.changeResultsPerPage(perPage);
    landingPage.verifyResultsPerPageChanged(perPage);
  });
});

Then('The user should see the initial pagination state', () => {
  landingPage.verifyPaginationInitialState();
});

When('The user navigates to the {string} page', (direction: 'next' | 'previous') => {
  if (direction === 'next') {
    landingPage.clickNextPage();
  } else {
    landingPage.clickPreviousPage();
  }
});

Then('The pagination should update for the next page', () => {
  landingPage.verifyNavigatedToNextPage();
});

Then('The pagination should return to the initial state', () => {
  landingPage.verifyNavigatedToPreviousPage();
});

Given('A test event named {string} exists for deletion testing', (eventName: string) => {
  landingPage.createTestEvent(eventName);
  cy.waitMainPageIsLoaded();
});

When('The user navigates to the event deletion functionality for {string}', (eventName: string) => {
  landingPage.selectEvent(eventName);
});

When('The user clicks on the Delete event button', () => {
  landingPage.clickDeleteEventButton();
});

Then('A confirmation dialog should appear with the message for {string}', (eventName: string) => {
  landingPage.verifyDeleteConfirmationDialog(eventName);
});

When('The user enters the event name {string} in the confirmation textbox', (eventName: string) => {
  landingPage.enterEventNameForDeletion(eventName);
});

When('The user clicks on the Delete button in the confirmation dialog', () => {
  landingPage.confirmEventDeletion();
});

Then('The event {string} should be successfully removed from the system', (eventName: string) => {
  landingPage.verifyEventDeleted(eventName);
});

When('The user clicks upload files button', () => {
  landingPage.clickUploadFilesButton();
});

When('The user clicks on new event button', () => {
  landingPage.clickNewEvent();
});

Then('The user enter {string} in the event name textbox', (eventName: string) => {
  landingPage.enterEventName(eventName);
});

Then('The user clicks on create event button', () => {
  landingPage.clickCreateEvent();
});

Then('The user should see a success snackbar with message {string}', (message: string) => {
  landingPage.verifySuccessSnackbar(message);
});

Then('The user clicks on cancel upload button', () => {
  landingPage.clickCancelUpload();
});

Then('The user should see {string} in the event table', (eventName: string) => {
  landingPage.verifyEventInTable(eventName);
});

Then('The user verifies delete button is enabled and clicks it', () => {
  landingPage.verifyDeleteButtonEnabledAndClick();
});

When('The user selects a file to upload {string}', (fileName: string) => {
  cy.SelectFile({ fileName: fileName });
});

Then('The file should be uploaded successfully', () => {
  cy.get('[data-testid="drop-input"]').should('exist');
});

Then('The user click upload button', ()=>{
  landingPage.clickUploadButton();
});

Then('The upload should complete {string}', (progress: string) => {
  // Wait for upload progress to reach 100%
  cy.get('.search-and-upload__files-file-progress-text')
    .should('be.visible')
    .and('contain', progress);

  // Wait for upload to complete
  cy.get('[data-testid="search-and-upload-files-file-complete"]', { timeout: 30000 })
    .should('be.visible')
    .and('contain', 'Complete');
});
