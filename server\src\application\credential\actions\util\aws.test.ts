import env from '../../../../env';
import { generateS3TempCredential, signedUrl } from './aws';

describe('generateS3TempCredential', () => {
  // it is a driver program to troubleshoot aws sts api call
  // it is not a unit test and disable for this purpose
  // to troubleshoot aws sts api, enable this test (remove x) and
  // provide account accessKey and secretKey
  xit('generateS3TempCredential', async () => {
    const bucketName = 'tracker2-dev';
    const accessKey = env?.s3?.accessKey ?? '';
    const secretKey = env?.s3?.secretKey ?? '';
    const region = 'us-east-1';
    const writable = true;
    const key = 'thumbnail/chachapoyas1.jpeg';
    const got = await generateS3TempCredential({
      bucketName,
      accessKey,
      secretKey,
      region,
      writable,
    });
    console.log('====>', got);
    expect(got).not.toBeNull();

    const uploadUrl = await signedUrl({
      bucketName,
      accessKey,
      secretKey,
      region,
      key,
      writable,
    });
    console.log(`uploadUrl====>${uploadUrl}`);

    const getUrl = await signedUrl({
      bucketName,
      accessKey,
      secretKey,
      region,
      key,
      writable: false,
    });
    console.log(`getUrl====>${getUrl}`);
  });

  // it is a driver program to troubleshoot generated tmp credential
  // it is not a unit test and disable for this purpose
  // to troubleshoot aws sts api, enable this test (remove x) and
  // provide account accessKey and secretKey
  it('signedUrl', async () => {
    const bucketName = 'tracker2-dev';
    const accessKey = env?.s3?.accessKey ?? '';
    const secretKey = env?.s3?.secretKey ?? '';
    const sessionToken = '';
    const region = 'us-east-1';
    const writable = true;
    const key = 'thumbnail/chachapoyas2.jpeg';

    const uploadUrl = await signedUrl({
      bucketName,
      accessKey,
      secretKey,
      region,
      sessionToken,
      key,
      writable,
    });
    console.log(`uploadUrl====>${uploadUrl}`);

    const getUrl = await signedUrl({
      bucketName,
      accessKey,
      secretKey,
      region,
      sessionToken,
      key,
      writable: false,
    });
    console.log(`getUrl====>${getUrl}`);
  });
});
