.file-and-filter-matches__detail {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: right;
  padding: 0 15px 0 0;

  .file-and-filter-matches__detail-clearall-button {
    text-transform: none;
    text-wrap: nowrap;

    @include size-2;
  }

  .file-and-filter-matches__files {
    .MuiFormLabel-root {
      background-color: var(--select-label-background);
      padding-right: 3px;
    }
  }

  .file-and-filter-matches__attributes-selected-text {
    @include size-2-thin
  }

  .file-and-filter-matches__detail-dropdown {
    width: 180px;

    .file-and-filter-matches__detail-label {
      text-align: left;
    }

    .MuiOutlinedInput-root {
      border-radius: 5px;
      padding: 2px 2px 2px 0;
    }

    select {
      height: 45px;
    }
  }

  .file-and-filter-matches__detail-find-button {
    padding: 6px 24px;
    text-transform: none;
    background-color: var(--find-matches-button-background);
    color: var(--find-matches-button-text);
    width: 145px;

    @include size-2-bold;
  }

  .file-and-filter-matches__detail-upload-button,
  .file-and-filter-matches__add-to-button {
    padding: 6px 24px;
    text-transform: none;


    @include size-2;
  }

  .file-and-filter-matches__add-to-button {
    margin-right: 10px;
    white-space: nowrap;
  }
}