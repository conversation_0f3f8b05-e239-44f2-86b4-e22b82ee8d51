import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';
import { UpdateMatchGroupPayloadResponse } from '../../../../../../types/responses';

const updateMatchGroupStructuredData = async <
  ReqPayload,
  Data extends Partial<
    UpdateMatchGroupPayloadResponse & responses.getMatchGroup & responses.getMe
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          UpdateMatchGroupPayloadResponse &
            responses.structuredMatchGroupData &
            responses.getMatchGroup
        >
    >
  | undefined
> => {
  const { cache, data, req, log, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };
  const {
    matchGroup,
    name,
    searches,
    selectedTracklets,
    currentTime,
    timelineProject,
    generatedTimelines,
  } = data;
  if (!matchGroup) {
    throw new ActionError('Missing match group data');
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    const { createStructuredData } = await callGQL<
      responses.structuredMatchGroupData,
      ReqPayload,
      Data
    >(context, headers, queries.createStructuredData, {
      id: matchGroup.id,
      schemaId,
      data: {
        ...matchGroup,
        ...(name && { name }),
        ...(searches && { searches }),
        ...(selectedTracklets && { selectedTracklets }),
        ...(timelineProject && { timelineProject }),
        ...(generatedTimelines && {
          generatedTimelines: [
            ...(matchGroup?.generatedTimelines ?? []),
            ...(generatedTimelines ?? []),
          ],
        }),
        modifiedDateTime: currentTime,
      },
    });

    if (createStructuredData) {
      if (redisWrapper) {
        const orgId = String(data.userOrganizationId);
        await redisWrapper.event.del(
          createStructuredData.data?.eventId ?? matchGroup.eventId,
          orgId
        );
      }

      const new_data = Object.assign({}, data, {
        createStructuredData: createStructuredData,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default updateMatchGroupStructuredData;
