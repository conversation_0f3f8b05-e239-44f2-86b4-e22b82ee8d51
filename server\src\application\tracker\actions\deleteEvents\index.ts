import { Context } from '../../../types';
import getFolder from '../getFolder';
import searchFiles from '../searchFiles';
import deleteTemporalData from '../deleteTemporalData';
import deleteContentTemplate from '../deleteContentTemplate';
import deleteStructuredData from '../deleteStructuredData';
import deleteFolder from '../deleteFolder';
import { responses } from '@tracker/graphQL';
import { ActionError } from '@common/errors';

const deleteEvents = async <ReqPayload, Data extends responses.searchEvents>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, log, req } = context;

  if (req.query?.code !== 'its ok, i know what i am doing 🤟') {
    const msg = 'Are you sure you should be calling this?';
    log.error(msg);
    throw new ActionError(msg);
  }

  for (const { id: eventId } of data.searchEvents.searchResults) {
    const next_cxt = { ...context, data: { eventId } };
    await getFolder(next_cxt)
      .then(searchFiles)
      .then((new_cxt) => {
        for (const { id: fileId } of new_cxt?.data?.searchFiles
          ?.searchResults ?? []) {
          const next_cxt = {
            ...new_cxt,
            data: {
              ...new_cxt.data,
              fileId,
            },
          };
          deleteTemporalData(next_cxt);
        }
        return new_cxt;
      })
      .then(deleteContentTemplate)
      .then(deleteStructuredData)
      .then(deleteFolder);
  }

  return context;
};
export default deleteEvents;
