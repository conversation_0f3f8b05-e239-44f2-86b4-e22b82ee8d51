/* eslint-disable formatjs/enforce-default-message */
import {
  FormattedMessage,
  FormattedDate,
  FormatDateOptions,
  useIntl,
} from 'react-intl';
import { ComponentProps } from 'react';

const TranslateMessage = (
  id: string,
  values: ComponentProps<typeof FormattedMessage>['values'] = {}
) => <FormattedMessage id={id} values={{ ...values }} />;

const TranslateDate = (value: string | number | Date | undefined) => {
  const options: FormatDateOptions = {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  };
  return <FormattedDate value={value} {...options} />;
};

const TranslateMessageWithBoldValue = (
  id: string,
  values: ComponentProps<typeof FormattedMessage>['values'] = {}
) => {
  const boldValues = Object.fromEntries(
    Object.entries(values).map(
      ([key, value]) => [key, <b style={{ fontWeight: 'bold' }} key={key}>{`${value}`}</b>]
    )
  );

  return <FormattedMessage
    id={id}
    values={
      boldValues
    }
  />;
};

const Intl = () => useIntl();

export default { TranslateMessage, TranslateDate, TranslateMessageWithBoldValue, Intl };
