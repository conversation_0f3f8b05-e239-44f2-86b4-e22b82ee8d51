import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import FindAndFilterMatches from './FindAndFilterMatches';
import configureMockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { ApiStatus } from '@store/types';
import { I18nProvider, LOCALES } from '@i18n';

const mockStore = configureMockStore();

const mockAttributes = {
    "person": {
        "Accessory": [
            {
                "label": "AccessoryBagAny",
                "value": "BagAny",
                "key": "Accessory"
            },
            {
                "label": "AccessoryBackpack",
                "value": "Backpack",
                "key": "Accessory"
            },
            {
                "label": "AccessoryBox",
                "value": "Box",
                "key": "Accessory"
            },
            {
                "label": "AccessoryGlasses",
                "value": "Glasses",
                "key": "Accessory"
            },
            {
                "label": "AccessoryHandbag",
                "value": "Handbag",
                "key": "Accessory"
            },
            {
                "label": "AccessoryHandtrunk",
                "value": "Handtrunk",
                "key": "Accessory"
            },
            {
                "label": "AccessoryHat",
                "value": "Hat",
                "key": "Accessory"
            },
            {
                "label": "AccessoryMuffler",
                "value": "Muffler",
                "key": "Accessory"
            },
            {
                "label": "AccessoryShoulderbag",
                "value": "Shoulderbag",
                "key": "Accessory"
            }
        ],
        "Body": [
            {
                "label": "BodyOverweight",
                "value": "Overweight",
                "key": "Body"
            },
            {
                "label": "BodyAverage",
                "value": "Average",
                "key": "Body"
            },
            {
                "label": "BodyThin",
                "value": "Thin",
                "key": "Body"
            }
        ],
        "Face": [
            {
                "label": "FaceBack",
                "value": "Back",
                "key": "Face"
            },
            {
                "label": "FaceFront",
                "value": "Front",
                "key": "Face"
            },
            {
                "label": "FaceLeft",
                "value": "Left",
                "key": "Face"
            },
            {
                "label": "FaceRight",
                "value": "Right",
                "key": "Face"
            }
        ],
        "FootwearColor": [
            {
                "label": "FootwearColorBlack",
                "value": "Black",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorBlue",
                "value": "Blue",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorBrown",
                "value": "Brown",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorGray",
                "value": "Gray",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorGreen",
                "value": "Green",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorRed",
                "value": "Red",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorWhite",
                "value": "White",
                "key": "FootwearColor"
            },
            {
                "label": "FootwearColorYellow",
                "value": "Yellow",
                "key": "FootwearColor"
            }
        ],
        "FootwearType": [
            {
                "label": "FootwearTypeBoots",
                "value": "Boots",
                "key": "FootwearType"
            },
            {
                "label": "FootwearTypeLeather",
                "value": "Leather",
                "key": "FootwearType"
            },
            {
                "label": "FootwearTypeSports",
                "value": "Sports",
                "key": "FootwearType"
            }
        ],
        "Gender": [
            {
                "label": "GenderFemale",
                "value": "Female",
                "key": "Gender"
            },
            {
                "label": "GenderMale",
                "value": "Male",
                "key": "Gender"
            }
        ],
        "HairColor": [
            {
                "label": "HairColorBlack",
                "value": "Black",
                "key": "HairColor"
            },
            {
                "label": "HairColorBrown",
                "value": "Brown",
                "key": "HairColor"
            },
            {
                "label": "HairColorGray",
                "value": "Gray",
                "key": "HairColor"
            },
            {
                "label": "HairColorWhite",
                "value": "White",
                "key": "HairColor"
            },
            {
                "label": "HairColorYellow",
                "value": "Yellow",
                "key": "HairColor"
            }
        ],
        "HairType": [
            {
                "label": "HairTypeBald",
                "value": "Bald",
                "key": "HairType"
            },
            {
                "label": "HairTypeLong",
                "value": "Long",
                "key": "HairType"
            },
            {
                "label": "HairTypeShort",
                "value": "Short",
                "key": "HairType"
            }
        ],
        "LowerColor": [
            {
                "label": "LowerColorBlack",
                "value": "Black",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorBlue",
                "value": "Blue",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorBrown",
                "value": "Brown",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorGray",
                "value": "Gray",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorGreen",
                "value": "Green",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorRed",
                "value": "Red",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorWhite",
                "value": "White",
                "key": "LowerColor"
            },
            {
                "label": "LowerColorYellow",
                "value": "Yellow",
                "key": "LowerColor"
            }
        ],
        "LowerType": [
            {
                "label": "LowerTypeShorts",
                "value": "Shorts",
                "key": "LowerType"
            },
            {
                "label": "LowerTypeSkirt",
                "value": "Skirt",
                "key": "LowerType"
            },
            {
                "label": "LowerTypeTrousers",
                "value": "Trousers",
                "key": "LowerType"
            }
        ],
        "UpperColor": [
            {
                "label": "UpperColorBlack",
                "value": "Black",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorBlue",
                "value": "Blue",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorGray",
                "value": "Gray",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorGreen",
                "value": "Green",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorOrange",
                "value": "Orange",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorPink",
                "value": "Pink",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorRed",
                "value": "Red",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorWhite",
                "value": "White",
                "key": "UpperColor"
            },
            {
                "label": "UpperColorYellow",
                "value": "Yellow",
                "key": "UpperColor"
            }
        ],
        "UpperSleeve": [
            {
                "label": "UpperSleeveLong",
                "value": "Long",
                "key": "UpperSleeve"
            },
            {
                "label": "UpperSleeveShort",
                "value": "Short",
                "key": "UpperSleeve"
            }
        ],
        "UpperType": [
            {
                "label": "UpperTypeCotton",
                "value": "Cotton",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeJacket",
                "value": "Jacket",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeShirt",
                "value": "Shirt",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeSuit",
                "value": "Suit",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeSweater",
                "value": "Sweater",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeTshirt",
                "value": "Tshirt",
                "key": "UpperType"
            },
            {
                "label": "UpperTypeVest",
                "value": "Vest",
                "key": "UpperType"
            }
        ]
    },
    "vehicle": {
        "Color": [
            {
                "label": "Color_black",
                "value": "black",
                "key": "Color"
            },
            {
                "label": "Color_blue",
                "value": "blue",
                "key": "Color"
            },
            {
                "label": "Color_brown",
                "value": "brown",
                "key": "Color"
            },
            {
                "label": "Color_gray",
                "value": "gray",
                "key": "Color"
            },
            {
                "label": "Color_green",
                "value": "green",
                "key": "Color"
            },
            {
                "label": "Color_orange",
                "value": "orange",
                "key": "Color"
            },
            {
                "label": "Color_pink",
                "value": "pink",
                "key": "Color"
            },
            {
                "label": "Color_purple",
                "value": "purple",
                "key": "Color"
            },
            {
                "label": "Color_red",
                "value": "red",
                "key": "Color"
            },
            {
                "label": "Color_silver",
                "value": "silver",
                "key": "Color"
            },
            {
                "label": "Color_white",
                "value": "white",
                "key": "Color"
            },
            {
                "label": "Color_yellow",
                "value": "yellow",
                "key": "Color"
            }
        ],
        "Make": [
            {
                "label": "Make_acura",
                "value": "acura",
                "key": "Make"
            },
            {
                "label": "Make_audi",
                "value": "audi",
                "key": "Make"
            },
            {
                "label": "Make_bentley",
                "value": "bentley",
                "key": "Make"
            },
            {
                "label": "Make_bmw",
                "value": "bmw",
                "key": "Make"
            },
            {
                "label": "Make_cadillac",
                "value": "cadillac",
                "key": "Make"
            },
            {
                "label": "Make_chevrolet",
                "value": "chevrolet",
                "key": "Make"
            },
            {
                "label": "Make_chrysler",
                "value": "chrysler",
                "key": "Make"
            },
            {
                "label": "Make_dodge",
                "value": "dodge",
                "key": "Make"
            },
            {
                "label": "Make_ferrari",
                "value": "ferrari",
                "key": "Make"
            },
            {
                "label": "Make_fiat",
                "value": "fiat",
                "key": "Make"
            },
            {
                "label": "Make_ford",
                "value": "ford",
                "key": "Make"
            },
            {
                "label": "Make_gmc",
                "value": "gmc",
                "key": "Make"
            },
            {
                "label": "Make_honda",
                "value": "honda",
                "key": "Make"
            },
            {
                "label": "Make_hyundai",
                "value": "hyundai",
                "key": "Make"
            },
            {
                "label": "Make_infiniti",
                "value": "infiniti",
                "key": "Make"
            },
            {
                "label": "Make_jaguar",
                "value": "jaguar",
                "key": "Make"
            },
            {
                "label": "Make_jeep",
                "value": "jeep",
                "key": "Make"
            },
            {
                "label": "Make_kia",
                "value": "kia",
                "key": "Make"
            },
            {
                "label": "Make_land-rover",
                "value": "land-rover",
                "key": "Make"
            },
            {
                "label": "Make_lexus",
                "value": "lexus",
                "key": "Make"
            },
            {
                "label": "Make_lincoln",
                "value": "lincoln",
                "key": "Make"
            },
            {
                "label": "Make_maserati",
                "value": "maserati",
                "key": "Make"
            },
            {
                "label": "Make_mazda",
                "value": "mazda",
                "key": "Make"
            },
            {
                "label": "Make_mercedes-benz",
                "value": "mercedes-benz",
                "key": "Make"
            },
            {
                "label": "Make_mini",
                "value": "mini",
                "key": "Make"
            },
            {
                "label": "Make_mitsubishi",
                "value": "mitsubishi",
                "key": "Make"
            },
            {
                "label": "Make_nissan",
                "value": "nissan",
                "key": "Make"
            },
            {
                "label": "Make_porsche",
                "value": "porsche",
                "key": "Make"
            },
            {
                "label": "Make_ram",
                "value": "ram",
                "key": "Make"
            },
            {
                "label": "Make_smart",
                "value": "smart",
                "key": "Make"
            },
            {
                "label": "Make_subaru",
                "value": "subaru",
                "key": "Make"
            },
            {
                "label": "Make_tesla",
                "value": "tesla",
                "key": "Make"
            },
            {
                "label": "Make_toyota",
                "value": "toyota",
                "key": "Make"
            },
            {
                "label": "Make_volkswagen",
                "value": "volkswagen",
                "key": "Make"
            },
            {
                "label": "Make_volvo",
                "value": "volvo",
                "key": "Make"
            }
        ],
        "Model": [
            {
                "label": "Model_201ka",
                "value": "201ka",
                "key": "Model"
            },
            {
                "label": "Model_3-series",
                "value": "3-series",
                "key": "Model"
            },
            {
                "label": "Model_488",
                "value": "488",
                "key": "Model"
            },
            {
                "label": "Model_5-series",
                "value": "5-series",
                "key": "Model"
            },
            {
                "label": "Model_500",
                "value": "500",
                "key": "Model"
            },
            {
                "label": "Model_7-series",
                "value": "7-series",
                "key": "Model"
            },
            {
                "label": "Model_7ur",
                "value": "7ur",
                "key": "Model"
            },
            {
                "label": "Model_8-series",
                "value": "8-series",
                "key": "Model"
            },
            {
                "label": "Model_911",
                "value": "911",
                "key": "Model"
            },
            {
                "label": "Model_a3",
                "value": "a3",
                "key": "Model"
            },
            {
                "label": "Model_a4",
                "value": "a4",
                "key": "Model"
            },
            {
                "label": "Model_a6",
                "value": "a6",
                "key": "Model"
            },
            {
                "label": "Model_a8",
                "value": "a8",
                "key": "Model"
            },
            {
                "label": "Model_acadia",
                "value": "acadia",
                "key": "Model"
            },
            {
                "label": "Model_accent",
                "value": "accent",
                "key": "Model"
            },
            {
                "label": "Model_accord",
                "value": "accord",
                "key": "Model"
            },
            {
                "label": "Model_acura",
                "value": "acura",
                "key": "Model"
            },
            {
                "label": "Model_altima",
                "value": "altima",
                "key": "Model"
            },
            {
                "label": "Model_beetle",
                "value": "beetle",
                "key": "Model"
            },
            {
                "label": "Model_bolt",
                "value": "bolt",
                "key": "Model"
            },
            {
                "label": "Model_c-class",
                "value": "c-class",
                "key": "Model"
            },
            {
                "label": "Model_camaro",
                "value": "camaro",
                "key": "Model"
            },
            {
                "label": "Model_camry",
                "value": "camry",
                "key": "Model"
            },
            {
                "label": "Model_car",
                "value": "car",
                "key": "Model"
            },
            {
                "label": "Model_cayenne",
                "value": "cayenne",
                "key": "Model"
            },
            {
                "label": "Model_cayman",
                "value": "cayman",
                "key": "Model"
            },
            {
                "label": "Model_challenger",
                "value": "challenger",
                "key": "Model"
            },
            {
                "label": "Model_charger",
                "value": "charger",
                "key": "Model"
            },
            {
                "label": "Model_cherokee",
                "value": "cherokee",
                "key": "Model"
            },
            {
                "label": "Model_civic",
                "value": "civic",
                "key": "Model"
            },
            {
                "label": "Model_cooper",
                "value": "cooper",
                "key": "Model"
            },
            {
                "label": "Model_corolla",
                "value": "corolla",
                "key": "Model"
            },
            {
                "label": "Model_corvette",
                "value": "corvette",
                "key": "Model"
            },
            {
                "label": "Model_cr-v",
                "value": "cr-v",
                "key": "Model"
            },
            {
                "label": "Model_cruze",
                "value": "cruze",
                "key": "Model"
            },
            {
                "label": "Model_ctac347",
                "value": "ctac347",
                "key": "Model"
            },
            {
                "label": "Model_ctp-1577",
                "value": "ctp-1577",
                "key": "Model"
            },
            {
                "label": "Model_ctp-2015-2018",
                "value": "ctp-2015-2018",
                "key": "Model"
            },
            {
                "label": "Model_cts",
                "value": "cts",
                "key": "Model"
            },
            {
                "label": "Model_cx-5",
                "value": "cx-5",
                "key": "Model"
            },
            {
                "label": "Model_discovery",
                "value": "discovery",
                "key": "Model"
            },
            {
                "label": "Model_ds05",
                "value": "ds05",
                "key": "Model"
            },
            {
                "label": "Model_durango",
                "value": "durango",
                "key": "Model"
            },
            {
                "label": "Model_e-150",
                "value": "e-150",
                "key": "Model"
            },
            {
                "label": "Model_e-class",
                "value": "e-class",
                "key": "Model"
            },
            {
                "label": "Model_econoline",
                "value": "econoline",
                "key": "Model"
            },
            {
                "label": "Model_elantra",
                "value": "elantra",
                "key": "Model"
            },
            {
                "label": "Model_es",
                "value": "es",
                "key": "Model"
            },
            {
                "label": "Model_es-350",
                "value": "es-350",
                "key": "Model"
            },
            {
                "label": "Model_es350",
                "value": "es350",
                "key": "Model"
            },
            {
                "label": "Model_escalade",
                "value": "escalade",
                "key": "Model"
            },
            {
                "label": "Model_escape",
                "value": "escape",
                "key": "Model"
            },
            {
                "label": "Model_excursion",
                "value": "excursion",
                "key": "Model"
            },
            {
                "label": "Model_explorer",
                "value": "explorer",
                "key": "Model"
            },
            {
                "label": "Model_f-150",
                "value": "f-150",
                "key": "Model"
            },
            {
                "label": "Model_f12-berlinetta",
                "value": "f12-berlinetta",
                "key": "Model"
            },
            {
                "label": "Model_f40",
                "value": "f40",
                "key": "Model"
            },
            {
                "label": "Model_f430",
                "value": "f430",
                "key": "Model"
            },
            {
                "label": "Model_fairlane",
                "value": "fairlane",
                "key": "Model"
            },
            {
                "label": "Model_fiesta",
                "value": "fiesta",
                "key": "Model"
            },
            {
                "label": "Model_flex",
                "value": "flex",
                "key": "Model"
            },
            {
                "label": "Model_flying-spur",
                "value": "flying-spur",
                "key": "Model"
            },
            {
                "label": "Model_focus",
                "value": "focus",
                "key": "Model"
            },
            {
                "label": "Model_fusion",
                "value": "fusion",
                "key": "Model"
            },
            {
                "label": "Model_g-class",
                "value": "g-class",
                "key": "Model"
            },
            {
                "label": "Model_g37",
                "value": "g37",
                "key": "Model"
            },
            {
                "label": "Model_ghibli",
                "value": "ghibli",
                "key": "Model"
            },
            {
                "label": "Model_ginger",
                "value": "ginger",
                "key": "Model"
            },
            {
                "label": "Model_glc",
                "value": "glc",
                "key": "Model"
            },
            {
                "label": "Model_gle",
                "value": "gle",
                "key": "Model"
            },
            {
                "label": "Model_golf",
                "value": "golf",
                "key": "Model"
            },
            {
                "label": "Model_gonza-5",
                "value": "gonza-5",
                "key": "Model"
            },
            {
                "label": "Model_grand-cherokee",
                "value": "grand-cherokee",
                "key": "Model"
            },
            {
                "label": "Model_grandcherokee",
                "value": "grandcherokee",
                "key": "Model"
            },
            {
                "label": "Model_highlander",
                "value": "highlander",
                "key": "Model"
            },
            {
                "label": "Model_i3",
                "value": "i3",
                "key": "Model"
            },
            {
                "label": "Model_i30",
                "value": "i30",
                "key": "Model"
            },
            {
                "label": "Model_i8",
                "value": "i8",
                "key": "Model"
            },
            {
                "label": "Model_ilx",
                "value": "ilx",
                "key": "Model"
            },
            {
                "label": "Model_impreza",
                "value": "impreza",
                "key": "Model"
            },
            {
                "label": "Model_is",
                "value": "is",
                "key": "Model"
            },
            {
                "label": "Model_jetta",
                "value": "jetta",
                "key": "Model"
            },
            {
                "label": "Model_lancer",
                "value": "lancer",
                "key": "Model"
            },
            {
                "label": "Model_land-cruiser",
                "value": "land-cruiser",
                "key": "Model"
            },
            {
                "label": "Model_leaf",
                "value": "leaf",
                "key": "Model"
            },
            {
                "label": "Model_legacy",
                "value": "legacy",
                "key": "Model"
            },
            {
                "label": "Model_little",
                "value": "little",
                "key": "Model"
            },
            {
                "label": "Model_ls",
                "value": "ls",
                "key": "Model"
            },
            {
                "label": "Model_luxury",
                "value": "luxury",
                "key": "Model"
            },
            {
                "label": "Model_lx",
                "value": "lx",
                "key": "Model"
            },
            {
                "label": "Model_lx-470",
                "value": "lx-470",
                "key": "Model"
            },
            {
                "label": "Model_lx-570",
                "value": "lx-570",
                "key": "Model"
            },
            {
                "label": "Model_lx-570l",
                "value": "lx-570l",
                "key": "Model"
            },
            {
                "label": "Model_m35",
                "value": "m35",
                "key": "Model"
            },
            {
                "label": "Model_m37",
                "value": "m37",
                "key": "Model"
            },
            {
                "label": "Model_malibu",
                "value": "malibu",
                "key": "Model"
            },
            {
                "label": "Model_maxima",
                "value": "maxima",
                "key": "Model"
            },
            {
                "label": "Model_mdx",
                "value": "mdx",
                "key": "Model"
            },
            {
                "label": "Model_ml",
                "value": "ml",
                "key": "Model"
            },
            {
                "label": "Model_model-3",
                "value": "model-3",
                "key": "Model"
            },
            {
                "label": "Model_model-s",
                "value": "model-s",
                "key": "Model"
            },
            {
                "label": "Model_model-x",
                "value": "model-x",
                "key": "Model"
            },
            {
                "label": "Model_mustang",
                "value": "mustang",
                "key": "Model"
            },
            {
                "label": "Model_navigator",
                "value": "navigator",
                "key": "Model"
            },
            {
                "label": "Model_novel",
                "value": "novel",
                "key": "Model"
            },
            {
                "label": "Model_odyssey",
                "value": "odyssey",
                "key": "Model"
            },
            {
                "label": "Model_optima",
                "value": "optima",
                "key": "Model"
            },
            {
                "label": "Model_outback",
                "value": "outback",
                "key": "Model"
            },
            {
                "label": "Model_outlander",
                "value": "outlander",
                "key": "Model"
            },
            {
                "label": "Model_passat",
                "value": "passat",
                "key": "Model"
            },
            {
                "label": "Model_pathfinder",
                "value": "pathfinder",
                "key": "Model"
            },
            {
                "label": "Model_picco",
                "value": "picco",
                "key": "Model"
            },
            {
                "label": "Model_pilot",
                "value": "pilot",
                "key": "Model"
            },
            {
                "label": "Model_police",
                "value": "police",
                "key": "Model"
            },
            {
                "label": "Model_q5",
                "value": "q5",
                "key": "Model"
            },
            {
                "label": "Model_q50",
                "value": "q50",
                "key": "Model"
            },
            {
                "label": "Model_q7",
                "value": "q7",
                "key": "Model"
            },
            {
                "label": "Model_qx",
                "value": "qx",
                "key": "Model"
            },
            {
                "label": "Model_qx50",
                "value": "qx50",
                "key": "Model"
            },
            {
                "label": "Model_qx56",
                "value": "qx56",
                "key": "Model"
            },
            {
                "label": "Model_ram",
                "value": "ram",
                "key": "Model"
            },
            {
                "label": "Model_range",
                "value": "range",
                "key": "Model"
            },
            {
                "label": "Model_range-rover",
                "value": "range-rover",
                "key": "Model"
            },
            {
                "label": "Model_rangesport",
                "value": "rangesport",
                "key": "Model"
            },
            {
                "label": "Model_rav4",
                "value": "rav4",
                "key": "Model"
            },
            {
                "label": "Model_rdx",
                "value": "rdx",
                "key": "Model"
            },
            {
                "label": "Model_ride",
                "value": "ride",
                "key": "Model"
            },
            {
                "label": "Model_rogue",
                "value": "rogue",
                "key": "Model"
            },
            {
                "label": "Model_rsx",
                "value": "rsx",
                "key": "Model"
            },
            {
                "label": "Model_rx",
                "value": "rx",
                "key": "Model"
            },
            {
                "label": "Model_rx-350",
                "value": "rx-350",
                "key": "Model"
            },
            {
                "label": "Model_s60",
                "value": "s60",
                "key": "Model"
            },
            {
                "label": "Model_santa-fe",
                "value": "santa-fe",
                "key": "Model"
            },
            {
                "label": "Model_santafe",
                "value": "santafe",
                "key": "Model"
            },
            {
                "label": "Model_savana",
                "value": "savana",
                "key": "Model"
            },
            {
                "label": "Model_sienna",
                "value": "sienna",
                "key": "Model"
            },
            {
                "label": "Model_sierra",
                "value": "sierra",
                "key": "Model"
            },
            {
                "label": "Model_silverado",
                "value": "silverado",
                "key": "Model"
            },
            {
                "label": "Model_sl",
                "value": "sl",
                "key": "Model"
            },
            {
                "label": "Model_sonata",
                "value": "sonata",
                "key": "Model"
            },
            {
                "label": "Model_spectrum",
                "value": "spectrum",
                "key": "Model"
            },
            {
                "label": "Model_sport",
                "value": "sport",
                "key": "Model"
            },
            {
                "label": "Model_sportage",
                "value": "sportage",
                "key": "Model"
            },
            {
                "label": "Model_sports",
                "value": "sports",
                "key": "Model"
            },
            {
                "label": "Model_suv",
                "value": "suv",
                "key": "Model"
            },
            {
                "label": "Model_tacoma",
                "value": "tacoma",
                "key": "Model"
            },
            {
                "label": "Model_tahoe",
                "value": "tahoe",
                "key": "Model"
            },
            {
                "label": "Model_taurus",
                "value": "taurus",
                "key": "Model"
            },
            {
                "label": "Model_telluria",
                "value": "telluria",
                "key": "Model"
            },
            {
                "label": "Model_tiguan",
                "value": "tiguan",
                "key": "Model"
            },
            {
                "label": "Model_titan",
                "value": "titan",
                "key": "Model"
            },
            {
                "label": "Model_touareg",
                "value": "touareg",
                "key": "Model"
            },
            {
                "label": "Model_town-and-country",
                "value": "town-and-country",
                "key": "Model"
            },
            {
                "label": "Model_trailblazer",
                "value": "trailblazer",
                "key": "Model"
            },
            {
                "label": "Model_transit",
                "value": "transit",
                "key": "Model"
            },
            {
                "label": "Model_traverse",
                "value": "traverse",
                "key": "Model"
            },
            {
                "label": "Model_trax",
                "value": "trax",
                "key": "Model"
            },
            {
                "label": "Model_truck",
                "value": "truck",
                "key": "Model"
            },
            {
                "label": "Model_tucson",
                "value": "tucson",
                "key": "Model"
            },
            {
                "label": "Model_tundra",
                "value": "tundra",
                "key": "Model"
            },
            {
                "label": "Model_van",
                "value": "van",
                "key": "Model"
            },
            {
                "label": "Model_venture",
                "value": "venture",
                "key": "Model"
            },
            {
                "label": "Model_versa",
                "value": "versa",
                "key": "Model"
            },
            {
                "label": "Model_vista",
                "value": "vista",
                "key": "Model"
            },
            {
                "label": "Model_wrangler",
                "value": "wrangler",
                "key": "Model"
            },
            {
                "label": "Model_x5",
                "value": "x5",
                "key": "Model"
            },
            {
                "label": "Model_xc90",
                "value": "xc90",
                "key": "Model"
            },
            {
                "label": "Model_xdrive",
                "value": "xdrive",
                "key": "Model"
            },
            {
                "label": "Model_xj",
                "value": "xj",
                "key": "Model"
            },
            {
                "label": "Model_xl",
                "value": "xl",
                "key": "Model"
            },
            {
                "label": "Model_xts",
                "value": "xts",
                "key": "Model"
            },
            {
                "label": "Model_yukon",
                "value": "yukon",
                "key": "Model"
            },
            {
                "label": "Model_z4",
                "value": "z4",
                "key": "Model"
            }
        ],
        "Type": [
            {
                "label": "Type_convertible",
                "value": "convertible",
                "key": "Type"
            },
            {
                "label": "Type_coupe",
                "value": "coupe",
                "key": "Type"
            },
            {
                "label": "Type_hatchback",
                "value": "hatchback",
                "key": "Type"
            },
            {
                "label": "Type_minivan",
                "value": "minivan",
                "key": "Type"
            },
            {
                "label": "Type_pickup",
                "value": "pickup",
                "key": "Type"
            },
            {
                "label": "Type_sedan",
                "value": "sedan",
                "key": "Type"
            },
            {
                "label": "Type_suv",
                "value": "suv",
                "key": "Type"
            },
            {
                "label": "Type_wagon",
                "value": "wagon",
                "key": "Type"
            }
        ]
    }
};

const store = mockStore({
    file: {
        tracklets: {
            vehicle: {
                data: [],
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                status: 'idle',
                error: '',
            },
            person: {
                data: [],
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                status: 'idle',
                error: '',
            },
        },
        attributes: mockAttributes,
        selectedAttributes: {
            person: {},
            vehicle: {},
        },
    },
    searchResults: {
        selectedTracklet: undefined,
        createMatchGroup: {
            id: '',
        },
        matchGroups: {
            results: [],
        },
        event: {
            data: {
                id: '123',
            },
        },
    },
});

const Props = {
  filesFilter: {
    fileNames: ['file1.mp4', 'file2.mp4', 'file2.mp4'],
    selectedFileNames: [],
    displayString: '',
    fileIds: ['123'],
    apiStatus: 'complete' as ApiStatus,
  },
  attributeFilter: true,
  clearAll: true,
};
store.dispatch = jest.fn();

describe('FindAndFilterMatches', () => {
  test('renders FindAndFilterMatches component', () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindAndFilterMatches {...Props} />
        </I18nProvider>
      </Provider>
    );

    expect(screen.getByTestId('file-and-filter-matches')).toBeInTheDocument();
    expect(screen.getByTestId('file-and-filter-matches-filter-select')).toBeInTheDocument();
    expect(screen.getByTestId('file-and-filter-matches-attributes-select')).toBeInTheDocument();
  });

  test('renders FindAndFilterMatches component with uniq filename menuItem and checkbox', async () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindAndFilterMatches {...Props} />
        </I18nProvider>
      </Provider>
    );

    const filterSelect = screen.getByTestId('file-and-filter-matches-filter-select');

    // Component renders
    expect(screen.getByTestId('file-and-filter-matches')).toBeInTheDocument();
    expect(filterSelect).toBeInTheDocument();
    expect(screen.getByTestId('file-and-filter-matches-attributes-select')).toBeInTheDocument();
    expect(filterSelect).not.toHaveClass('Mui-disabled');
    
    // Click on the select dropdown
    const element = within(filterSelect).getByRole('combobox');
    fireEvent.mouseDown(element);

    // Check if the options and checkboxes are rendered
    await waitFor(() => {
      const option1 = screen.getByTestId('file-and-filter-matches-filter-select-item-0');
      const option2 = screen.getByTestId('file-and-filter-matches-filter-select-item-1');
      const checkbox1 = screen.getByTestId('file-and-filter-matches-filter-select-item-checkbox-0');
      const checkbox2 = screen.getByTestId('file-and-filter-matches-filter-select-item-checkbox-1');
      expect(option1).toBeInTheDocument();
      expect(option1).toHaveTextContent('file1.mp4');
      expect(checkbox1).toBeInTheDocument();
      expect(option2).toBeInTheDocument();
      expect(option2).toHaveTextContent('file2.mp4');
      expect(checkbox2).toBeInTheDocument();

      expect(screen.queryByTestId('file-and-filter-matches-filter-select-item-2')).not.toBeInTheDocument();
      expect(screen.queryByTestId('file-and-filter-matches-filter-select-item-checkbox-2')).not.toBeInTheDocument();
    });
  });

  test('FindAndFilterMatches component is disabled', async () => {

    // Filter is loading
    const newProps = {
        ...Props,
        filesFilterLoading: true,
    };

    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindAndFilterMatches { ...newProps } />
        </I18nProvider>
      </Provider>
    );

    const filterSelect = screen.getByTestId('file-and-filter-matches-filter-select');

    // Component renders
    expect(screen.getByTestId('file-and-filter-matches')).toBeInTheDocument();
    expect(filterSelect).toBeInTheDocument();
    expect(screen.getByTestId('file-and-filter-matches-attributes-select')).toBeInTheDocument();

    expect(filterSelect).toHaveClass('Mui-disabled');
  });
});
