.export-dialog {
  display: flex;
  flex-direction: column;
  width: 410px;
  padding: 30px;
  background-color: var(--white-background);

  .export-dialog__heading {
    @include size-4-bold;

    font-weight: 400;
    color: var(--text-primary);
    margin: 0;
  }

  .export-dialog__description {
    @include size-2;

    color: var(--tab-text-disabled);
    margin: 10px 0 22px;
    font-weight: 300;
  }

  .export-dialog__checkbox {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 16px;
    flex: 1;

    .export-dialog__checkbox-item {
      display: flex;
      align-items: center;
      gap: 10px;

      .export-dialog__checkbox-label {
        @include size-2;

        color: var(--tab-text-disabled);
        margin: 0;
        font-weight: 300;
      }
    }
  }

  .export-dialog__buttons {
    text-align: right;

    .MuiButtonBase-root {
      text-transform: capitalize;
    }

    .export-dialog__button {
      @include size-2;

      padding: 8px 15px;
    }

    .export-dialog__button--cancel {
      color: var(--text-primary);
      margin-right: 10px;
    }
  }
}
