import { Attributes } from "@shared-types/tracker";

export const TrackletAttributes: Attributes = {
    accessories: [
        {
            key: 'accessories',
            value: 'BagAny',
            label: 'accessoriesBagAny'
        }
    ],
    body: [
        {
            key: 'body',
            value: 'BodyAverage',
            label: 'bodyBodyAverage'
        }
    ],
    face: [
        {
            key: 'face',
            value: 'Right',
            label: 'faceRight'
        }
    ],
    upperColor: [
        {
            key: 'upperColor',
            value: 'Blue',
            label: 'upperColorBlue'
        },
        {
            key: 'upperColor',
            value: 'Black',
            label: 'upperColorBlack'
        },
        {
            key: 'upperColor',
            value: 'White',
            label: 'upperColorWhite'
        }
    ],
    lowerColor: [
        {
            key: 'lowerColor',
            value: 'Black',
            label: 'lowerColorBlack'
        },
        {
            key: 'lowerColor',
            value: 'Blue',
            label: 'lowerColorBlue'
        }
    ],
    upperType: [
        {
            key: 'upperType',
            value: 'Tshirt',
            label: 'upperTypeTshirt'
        },
        {
            key: 'upperType',
            value: 'Shirt',
            label: 'upperTypeShirt'
        }
    ],
    lowerType: [
        {
            key: 'lowerType',
            value: 'Jeans',
            label: 'lowerTypeJeans'
        },
        {
            key: 'lowerType',
            value: 'Shorts',
            label: 'lowerTypeShorts'
        }
    ],
    footwearColor: [
        {
            key: 'footwearColor',
            value: 'Black',
            label: 'footwearColorBlack'
        },
        {
            key: 'footwearColor',
            value: 'White',
            label: 'footwearColorWhite'
        }
    ],
    footwearType: [
        {
            key: 'footwearType',
            value: 'Sneakers',
            label: 'footwearTypeSneakers'
        },
        {
            key: 'footwearType',
            value: 'Boots',
            label: 'footwearTypeBoots'
        }
    ],
};

export function getAttributeCount(attributes: Attributes): number {
    return Object.values(attributes).reduce((count, arr) => count + arr.length, 0);
}
