import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { GraphQLError, ValidationError } from '@common/errors';
import * as ResTypes from '../../../../../../types/responses';
import env from '../../../../env';

export const TRACK_FILE_TAG = 'veritone_track';

const createFileTemporalData = async <
  ReqPayload,
  Data extends Partial<
    ResTypes.UploadFilePayloadResponse & responses.getMe
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    Data &
      Partial<
        ResTypes.UploadFilePayloadResponse & responses.createFileTemporalData
      >
  >
> => {
  const { data, req, log } = context;

  const { currentTime, eventId, fileName, fileType, firstName, lastName } =
    data;

  if (!eventId || !fileName || !fileType || !firstName || !lastName) {
    if (!firstName || !lastName) {
      throw new ValidationError(
        `Missing required data: ${!firstName || !lastName ? "User's first or last name" : ''}`
      );
    } else {
      throw new ValidationError(
        `Missing required data: ${[
          !eventId && 'eventId',
          !fileName && 'fileName',
          !fileType && 'fileType',
        ]
          .filter(Boolean)
          .join(', ')}`
      );
    }
  }

  try {
    const formData = new FormData();
    const file = new Blob(['upload_in_progress'], { type: 'text/plain' });
    const fileURI = URL.createObjectURL(file);

    const url = `${env.apiRoot}/${env.graphQLEndpoint}`;
    const headers = { Authorization: req.headers.authorization };
    const variables = {
      input: {
        startDateTime: currentTime,
        stopDateTime: currentTime,
        name: fileName,
        parentFolderId: eventId,
        addToIndex: true,
        assets: [
          {
            assetType: 'upload_pending',
            contentType: `"${file.type}"`,
            uri: fileURI,
          },
        ],
        details: {
          veritoneProgram: {
            programLiveImage: '',
          },
          veritoneFile: {
            fileName,
            fileType,
            createdByName: `${firstName} ${lastName}`,
          },
          tags: [{ value: TRACK_FILE_TAG }],
        },
      },
    };

    formData.append('query', queries.createTemporalData);
    formData.append('variables', JSON.stringify(variables));
    formData.append('filename', 'upload_in_progress');
    formData.append('file', file, 'upload_in_progress');

    const result = await fetch(url, {
      method: 'post',
      body: formData,
      headers,
    }).then(async (r) => {
      const { status } = r;
      try {
        const response = await r.json();
        return { ...response, status };
      } catch (e: unknown) {
        console.error(e);
      }
    });

    const new_data = Object.assign({}, data, {
      temporalData: result.data.createTDO,
    });
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createFileTemporalData;
