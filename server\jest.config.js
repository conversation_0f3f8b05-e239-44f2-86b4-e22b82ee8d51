/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  modulePathIgnorePatterns: ['dist'],
  moduleNameMapper: {
    '^@tracker/redis$': '<rootDir>/src/redis.ts',
    '^@util/(.*)$': '<rootDir>/src/util/$1',
    '^@common/(.*)$': '<rootDir>/src/application/common/$1',
    '^@tracker/(.*)$': '<rootDir>/src/application/tracker/$1',
    '^@application/(.*)$': '<rootDir>/src/application/$1',
    '^@server/(.*)$': '<rootDir>/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/src/application/__test__/setup.ts'],
};
