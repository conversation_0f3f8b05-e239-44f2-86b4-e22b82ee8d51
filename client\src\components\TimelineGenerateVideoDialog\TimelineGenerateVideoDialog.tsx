import { Button, Dialog, <PERSON>alogA<PERSON>, <PERSON>alogContent, DialogContentText, DialogTitle, TextField, Typography } from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { MatchGroup } from '@shared-types/tracker';
import './index.scss';
import { AlertLevel, createSnackNotification } from '@components/common';
import { useIntl } from 'react-intl';

interface TimelineGenerateVideoDialogProps {
  open: boolean;
  onClose: () => void;
  matchGroup: MatchGroup;
  onSaveAndGenerateTimeline: (timelineName: string) => void;
}

const validFilenameInput = (input: string) => {
  // Regular expression for valid filename characters, allowing spaces within but not at the beginning or end
  const validFilenameRegex = /^(?!\s)[^<>:"\\|?\[\]]+(?<!\s)$/;
  return input.trim().length > 0 && validFilenameRegex.test(input) && !input.endsWith('.');
};

const TimelineGenerateVideoDialog = ({ open, onClose, matchGroup, onSaveAndGenerateTimeline }: TimelineGenerateVideoDialogProps) => {
  const intl = useIntl();
  const [input, setInput] = useState<string>('');

  const matchGroupName = matchGroup.name;
  const _matchGroupId = matchGroup.id;
  const generatedTimeLineCount = (matchGroup.generatedTimelines ?? []).length + 1;

  const isInputValid = useMemo(() => validFilenameInput(input), [input]);

  useEffect(() => {
    setInput(`${matchGroupName}-timeline-${generatedTimeLineCount}.mp4`);
  }, [open, matchGroupName, generatedTimeLineCount]);

  const handleChangeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);

    if (!validFilenameInput(e.target.value)) {
      createSnackNotification(AlertLevel.Error, 'Please enter a valid file name.', `<strong>${e.target.value}</strong> is not valid file name.`);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === ' ' || event.keyCode === 32 || event.keyCode === 37 || event.keyCode === 39) {
      event.stopPropagation();
    }
  };

  const handleGenerateVideo = () => {
    const mp4Input = input.endsWith('.mp4') ? input : `${input}.mp4`;
    onSaveAndGenerateTimeline(mp4Input);
    onClose();
  };

  return (
    <div>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="timeline-generate-video-dialog-title"
        aria-describedby="timeline-generate-video-dialog-description"
        data-testid="timeline-generate-video-dialog"
        PaperProps={{
          style: {
            width: `${450}px`,
            padding: '12px',
          },
        }}
      >
        <DialogTitle className="timeline-generate-video-dialog-title">Generate Timeline Video</DialogTitle>
        <DialogContent className="timeline-generate-video-dialog-content">
          <DialogContentText>
            {`You can optionally name your file below. This timeline generated video will be saved to `}
            <Typography component="span" style={{ fontWeight: 'bold', color: 'black' }}>
              {matchGroupName}
            </Typography>
          </DialogContentText>
          <TextField
            label={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
            data-testid="timeline-generate-video-dialog-input"
            value={input}
            onChange={handleChangeInput}
            onKeyDown={handleKeyDown}
            fullWidth
            size="small"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} data-testid="timeline-generate-video-dialog-cancel-action" color="inherit">
            {'Cancel'}
          </Button>
          <Button data-testid="timeline-generate-video-dialog-confirm-action" onClick={handleGenerateVideo} variant="contained" color="primary" autoFocus disabled={!isInputValid}>
            {'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default TimelineGenerateVideoDialog;
