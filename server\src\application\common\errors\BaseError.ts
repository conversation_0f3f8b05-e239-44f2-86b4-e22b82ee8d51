class BaseError extends Error {
  constructor(error: Error | string | { message: string }, name = 'BaseError') {
    let errorMessage: string;
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error instanceof Error || (error && error.message)) {
      errorMessage = error.message;
    } else {
      errorMessage = 'An unknown error has occurred';
    }

    super(errorMessage);
    Error.captureStackTrace(this, this.constructor);
    this.message = errorMessage;
    this.name = name;
  }
}

export default BaseError;
