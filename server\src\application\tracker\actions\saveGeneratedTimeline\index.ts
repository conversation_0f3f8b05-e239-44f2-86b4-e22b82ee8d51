import { isEmpty } from 'lodash';
import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import {
  CreateTimelineJobPayloadResponse,
  SaveGeneratedTimeline,
} from '../../../../../../types/responses';
import { TemporalData } from '../../../../../../types/tracker';
import { getUsers } from '../getUsers';

const saveGeneratedTimeline = async <
  ReqPayload,
  Data extends Partial<responses.createSpliceJob> &
    Partial<CreateTimelineJobPayloadResponse> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & SaveGeneratedTimeline> | undefined> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };
  const { spliceJob, timelineProject, generatedTimelineName } = data;

  if (!spliceJob) {
    throw new ActionError('Missing splice job data');
  }
  if (!timelineProject) {
    throw new ActionError('Missing timeline project data');
  }
  let temporalDataObject: TemporalData | undefined;
  try {
    const resp = await callGQL<responses.getFileTemporalData, ReqPayload, Data>(
      context,
      headers,
      queries.getFileTemporalData,
      {
        tdoId: spliceJob.tdoId,
      }
    );
    temporalDataObject = resp.temporalDataObject;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
  if (temporalDataObject) {
    const new_data = Object.assign({}, data, {
      generatedTimelines: [
        {
          id: temporalDataObject.id,
          name: generatedTimelineName ?? temporalDataObject.name,
          tdoId: temporalDataObject.id,
          timeline: timelineProject,
          resolution: '',
          outputFormat: 'video/mp4',
          createdUserId: temporalDataObject.createdBy,
          createdUserName: '',
          videoLengthMs: 0,
          videoSizeBytes: 0,
          createdDateTime: temporalDataObject.createdDateTime,
        },
      ],
    });
    const createdUserIds: string[] = [temporalDataObject.createdBy];
    try {
      const createdUsers = await getUsers({
        userIds: createdUserIds,
        context,
        headers,
      });
      if (!isEmpty(createdUsers)) {
        new_data.generatedTimelines[0].createdUserName = `${createdUsers[0].firstName} ${createdUsers[0].lastName}`;
      } else {
        log.error('Could not find user for createdUserId', createdUserIds);
      }
    } catch (e) {
      log.error(`failed to get user for ${createdUserIds}`, e);
    }
    const new_context = Object.assign({}, context, {
      data: new_data,
    });
    return new_context;
  }
};

export default saveGeneratedTimeline;
