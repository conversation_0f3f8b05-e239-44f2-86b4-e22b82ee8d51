import { all, fork } from 'redux-saga/effects';
import { modules } from '@veritone/glc-redux';
import homeSagas from '@store/modules/home/<USER>';
import appSagas from '@store/modules/app/saga';
import routes from '@store/modules/location/saga';
import eventSagas from './modules/event/saga';
import uploadSagas from '@store/modules/upload/saga';
import fileSagas from './modules/file/saga';
import matchGroupSagas from './modules/matchGroup/saga';
import searchResultsSagas from './modules/searchResults/saga';

const {
  auth: { authRootSaga },
} = modules;

export default function* rootSaga() {
  yield all([
    fork(homeSagas),
    fork(appSagas),
    fork(authRootSaga),
    fork(routes),
    fork(fileSagas),
    fork(eventSagas),
    fork(uploadSagas),
    fork(searchResultsSagas),
    fork(matchGroupSagas)
  ]);
}
