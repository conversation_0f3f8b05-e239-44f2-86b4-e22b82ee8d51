import Dropzone, { FileWithPath } from "react-dropzone";
import cn from 'classnames';
import { useEffect, useState, MouseE<PERSON>, ChangeEventHandler, useMemo, useRef, useCallback } from 'react';
import { Autocomplete, TextField, CircularProgress, Button, LinearProgress, Popover, ListItemText, Tooltip } from '@mui/material';
import { isEqual, throttle, isFinite, isInteger } from 'lodash';
import { useAppDispatch } from "@store/hooks";
import { searchEvents, searchFiles, selectCreateEvent, selectEvents, createEvent, addFilesToUpload, selectFilesToUpload, removeFileToUpload, startUpload, setSelectedEvent, setCreatedEvent, selectAllEvents, searchAllEvents, selectSelectedEvent, poll as pollSearch } from '@store/modules/upload/slice';
import { AlertLevel, createSnackNotification } from "@components/common";
import './SearchAndUpload.scss';
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { getEvents, getFiles, poll as pollHome, selectSort, setSort } from "@store/modules/home/<USER>";
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import SearchAllFilesDialog from "@components/SearchAllFilesDialog/SearchAllFilesDialog";

interface CurrentPagination {
  currentTab: string;
  page: string;
  limit: string;
  term: string;
  sort?: { dir: string; sort: string };
}

const SearchAndUpload = ({ disableSearch, disableButton, searchAllFiles }: { disableSearch?: boolean; disableButton?: boolean; searchAllFiles?: boolean }) => {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { search } = useLocation();
  const { eventId } = useParams();
  const events = useSelector(selectEvents);
  const allEvents = useSelector(selectAllEvents);
  const createEventData = useSelector(selectCreateEvent);
  const filesToUpload = useSelector(selectFilesToUpload);
  const selectedEvent = useSelector(selectSelectedEvent);
  const sort = useSelector(selectSort);

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [newEventEdit, setNewEventEdit] = useState(false);
  const [newEventName, setNewEventName] = useState('');
  const [newEventError, setNewEventError] = useState(false);
  const [openSearchAllDialog, setOpenSearchAllDialog] = useState(false);

  const [eventsAutocompleteOpen, setEventsAutocompleteOpen] = useState(false);
  const [currentPagination, setCurrentPagination] = useState<CurrentPagination>({
    currentTab: '',
    page: '1',
    limit: '50',
    term: '',
    sort: undefined,
  });

  const loading = events.status === 'loading';
  const uploadPopoverOpen = Boolean(anchorEl);
  const popoverId = uploadPopoverOpen ? 'upload-popover' : undefined;

  const pollPromiseRef = useRef<{ [pollKey: string]: DispatchPromise }>({});
  const { terms: searchTerm, activeTab, page, limit } = useMemo(() => Object.fromEntries(new URLSearchParams(search)), [search]);

  const doPoll = useCallback((pollType: 'events' | 'files' | 'search-events' | 'search-files', dispatchPromise: DispatchPromise) => {
    const { current: pollPromise } = pollPromiseRef;
    pollPromise?.[pollType]?.abort();

    if ('events' === pollType || 'files' === pollType) {
      pollPromise?.['search-files']?.abort();
      pollPromise?.['search-events']?.abort();
      // @ts-expect-error TODO: Can we type dispatchPromise arg better?
      pollPromise[pollType] = dispatch(pollHome({ type: pollType, ...dispatchPromise?.arg }));
    }
    if ('search-events' === pollType || 'search-files' ===  pollType) {
      pollPromise?.['files']?.abort();
      pollPromise?.['events']?.abort();
      // @ts-expect-error TODO: Can we type dispatchPromise arg better?
      pollPromise[pollType] = dispatch(pollSearch({ type: pollType, ...dispatchPromise?.arg }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pollPromiseRef.current]);

  const searchForEvent = useMemo(
    () =>
      throttle(
        (searchTerm, pageSize = limit, currentPage = 1, dir = 'desc', sort = 'eventStartDate') =>
          doPoll('search-events', dispatch(searchEvents({ searchTerm, pageSize, currentPage, dir, sort }))),
        1000,
        { leading: false, trailing: true }
      ),
    [dispatch, doPoll, limit]
  );

  const searchForFile = useMemo(
    () =>
      throttle(
        (searchTerm, pageSize = limit, currentPage = 1, dir = 'desc', sort = 'uploadDate') =>
          doPoll('search-files', dispatch(searchFiles({ searchTerm, pageSize, currentPage, dir, sort }))),
        1000,
        { leading: false, trailing: true }
      ),
    [dispatch, doPoll, limit]
  );

  const uploadInProgress = filesToUpload.some((f) => f.status === 'loading');

  useEffect(() => {
    const { current: pollPromise } = pollPromiseRef;
    dispatch(searchAllEvents({}));

    return () => {
      Object.values(pollPromise).forEach(dp => dp?.abort());
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (allEvents.results.length > 0 && eventId) {
      const foundEvent = allEvents.results.find(e => e.id === eventId);
      if (foundEvent) {
        dispatch(setSelectedEvent({ event: foundEvent }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allEvents]);

  useEffect(() => {
    if (activeTab) {
      setCurrentPagination({ currentTab: activeTab, page: page, limit: limit, term: searchTerm, sort });
    }
  }, [activeTab, searchTerm, limit, page, sort]);

  useEffect(() => {

    if (disableSearch) {
      return;
    }
    if (searchTerm) {
      // search for all when search term changes
      if (currentPagination.term !== searchTerm) {
        searchForEvent.cancel();
        searchForFile.cancel();
        searchForEvent(searchTerm);
        searchForFile(searchTerm);
      }
      // events tab pagination
      else if (currentPagination.currentTab === 'events' && activeTab === currentPagination.currentTab && (currentPagination.page !== page || currentPagination.limit !== limit || !isEqual(currentPagination.sort, sort))) {
        searchForEvent.cancel();
        searchForEvent(searchTerm, limit, page, sort?.dir, sort?.sort);
      }
      // files tab pagination
      else if (currentPagination.currentTab === 'files' && activeTab === currentPagination.currentTab && (currentPagination.page !== page || currentPagination.limit !== limit || !isEqual(currentPagination.sort, sort))) {
        searchForFile.cancel();
        searchForFile(searchTerm, limit, page, sort?.dir, sort?.sort);
      }
    } else {
      // search for all when search term is empty or when no parameters are passed
      if (currentPagination.currentTab === '' || !search) {
        doPoll('events', dispatch(getEvents({ pageSize: activeTab === 'events' ? limit : 50, currentPage: activeTab === 'events' ? page : 1, dir: activeTab === 'events' ? sort?.dir : 'desc', sort: activeTab === 'events' ? sort?.sort : 'eventStartDate' })));
        doPoll('files', dispatch(getFiles({ pageSize: activeTab === 'files' ? limit : 50, currentPage: activeTab === 'files' ? page : 1, dir: activeTab === 'files' ? sort?.dir : 'desc', sort: activeTab === 'files' ? sort?.sort : 'uploadDate' })));
      }
      // events tab pagination
      else if (currentPagination.currentTab === 'events' && activeTab === currentPagination.currentTab && (currentPagination.page !== page || currentPagination.limit !== limit || !isEqual(currentPagination.sort, sort))) {
        doPoll('events', dispatch(getEvents({ pageSize: activeTab === 'events' ? limit : 50, currentPage: activeTab === 'events' ? page : 1, dir: activeTab === 'events' ? sort?.dir : 'desc', sort: activeTab === 'events' ? sort?.sort : 'eventStartDate' })));
      }
      // files tab pagination
      else if (currentPagination.currentTab === 'files' && activeTab === currentPagination.currentTab && (currentPagination.page !== page || currentPagination.limit !== limit || !isEqual(currentPagination.sort, sort))) {
        doPoll('files', dispatch(getFiles({ pageSize: activeTab === 'files' ? limit : 50, currentPage: activeTab === 'files' ? page : 1, dir: activeTab === 'files' ? sort?.dir : 'desc', sort: activeTab === 'files' ? sort?.sort : 'uploadDate' })));
      }
    }
  }, [disableSearch, searchTerm, searchForEvent, searchForFile, dispatch, limit, page, currentPagination, search, activeTab, sort, doPoll, pollPromiseRef]);

  const handleUploadButtonClick = (e: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNewEventClick = () => {
    setNewEventEdit(true);
    dispatch(setCreatedEvent({ event: undefined }));
  };

  const handleCancelNewEvent = () => {
    setNewEventEdit(false);
  };

  const handleConfirmNewEvent = () => {
    if (!newEventName.trim()) {
      setNewEventError(true);
      createSnackNotification(AlertLevel.Warning, 'Warning', 'Enter an Event name');
    } else {
      setNewEventEdit(false);
      dispatch(createEvent({ name: newEventName.trim() }));
      setNewEventName('');
      dispatch(setSort(undefined));
    }
  };

  const handleNewEventNameChange: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> = e => {
    setNewEventName(e.target.value);
    setNewEventError(false);
  };

  const handleSearchChange: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> = (e) => {
    const searchTermQuery = '?terms=' + e.target.value;

    const tabs = ['events', 'files', 'tags'];
    const searchTabQuery = '&activeTab=' + (activeTab && tabs.includes(activeTab) ? activeTab : 'events');

    const pageQuery = '&page=1';
    const limitQuery = '&limit=' + (isFinite(Number(limit)) && isInteger(Number(limit)) ? limit : '50');
    navigate(e.target.value === '' ? '/' : searchTermQuery + searchTabQuery + pageQuery + limitQuery, { replace: true });
  };

  const hasEventOnEventPage = eventId !== undefined && selectedEvent?.id === eventId;

  return (
    <div
      className="search-and-upload__detail-search-and-upload"
      data-testid="search-and-upload"
    >
      {!disableSearch && (
        <TextField
          className="search-and-upload__detail-search"
          variant="outlined"
          placeholder={intl.formatMessage({ id: 'searchEventsTagsAndFiles', defaultMessage: 'Search Events, Tags, and Files' })}
          InputProps={{
            startAdornment: (
              <>
                <div className="material-icons">search</div>
              </>
            ),
            endAdornment: (
              <>
                {loading ? <CircularProgress className={"search-and-upload__circular-progress"} size={20} /> : null}
              </>
            ),
            onChange: handleSearchChange,
            inputProps: { 'data-testid': 'search-and-upload-search_input' }
          }}
          value={searchTerm ?? ''}
        />
      )}
      {searchAllFiles && (
        <Button
          data-testid="search-and-upload-search-all-file-button"
          className="search-and-upload__detail-search-button"
          variant="outlined"
          onClick={() => setOpenSearchAllDialog(true)}
          disabled={disableButton}
        >
          {I18nTranslate.TranslateMessage('searchAllFile')}
        </Button>
      )}
      <Button
        data-testid="search-and-upload-upload-button"
        className="search-and-upload__detail-upload-button"
        variant="outlined"
        onClick={handleUploadButtonClick}
        disabled={disableButton}
      >
        {I18nTranslate.TranslateMessage('uploadFile')}
      </Button>
      {searchAllFiles && (
        <SearchAllFilesDialog
          open={openSearchAllDialog}
          onClose={() => setOpenSearchAllDialog(false)}
        />
      )}
      <Popover
        classes={{
          paper: "search-and-upload__detail-upload-popover"
        }}
        id={popoverId}
        open={uploadPopoverOpen}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <div
          className="search-and-upload__detail-upload-title"
          data-testid="search-and-upload-upload-popover"
        >
          {I18nTranslate.TranslateMessage('uploadFile')}
        </div>
        <div className="search-and-upload__detail-upload-desc">
          {I18nTranslate.TranslateMessage('dragAndDropFiles')}
        </div>
        <div className="search-and-upload__detail-upload-question">
          {I18nTranslate.TranslateMessage('uploadToWhichEvent')}
        </div>
        <div className="search-and-upload__detail-upload-container">
          {newEventEdit && (
            <TextField
              value={newEventName}
              error={newEventError}
              size="small"
              label={intl.formatMessage({ id: 'newEventName', defaultMessage: 'New Event Name' })}
              variant="outlined"
              onChange={handleNewEventNameChange}
              inputProps={{ 'data-testid': 'search-and-upload-detail-upload-new-event-input' }}
            />
          )}
          {!newEventEdit && !createEventData.event && createEventData.status !== 'loading' && (
            <Autocomplete
              className="search-and-upload__detail-upload-select-event"
              data-testid="search-and-upload-upload-popover-select-event"
              open={eventsAutocompleteOpen}
              onOpen={() => {
                setEventsAutocompleteOpen(true);
              }}
              onClose={() => {
                setEventsAutocompleteOpen(false);
              }}
              loading={allEvents.status === 'loading'}
              options={allEvents.results}
              value={selectedEvent}
              disabled={hasEventOnEventPage}
              getOptionLabel={(option) => option.name ?? ''}
              isOptionEqualToValue={(opt, val) => opt.id === val.id}
              onChange={(_, event) => {
                dispatch(setSelectedEvent({ event }));
              }}
              renderOption={(props, item) => (
                <li {...props} key={item.id}>
                  <ListItemText>{item.name}</ListItemText>
                </li>
              )}
              noOptionsText={intl.formatMessage({ id: 'noEventFound', defaultMessage:'No Event Found'})}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder={`${intl.formatMessage({ id: 'searchEvents', defaultMessage: 'Search Events' })}...`}
                  InputProps={{
                    ...params.InputProps,
                    inputProps: {
                      ...params.inputProps,
                      'data-testid': "search-and-upload-upload-popover-select-event-input",
                    },
                    startAdornment: <>
                      <div className="material-icons">search</div>
                      {params.InputProps.startAdornment}
                    </>,
                    endAdornment: <>
                      {loading ? <CircularProgress color="inherit" size={20} /> : null}
                    </>,
                  }}
                />
              )}
            />
          )}
          {!newEventEdit && !createEventData.event && createEventData.status === 'loading' &&
            <CircularProgress color="inherit" size={20} />
          }
          {!newEventEdit && createEventData.event &&
            <div>
              <TextField
                value={createEventData.event.name}
                disabled
                size="small"
                label={intl.formatMessage({ id: 'eventName', defaultMessage: 'Event Name' })}
                variant="outlined"
                onChange={handleNewEventNameChange}
                inputProps={{ 'data-testid': 'search-and-upload-detail-upload-new-event-entered' }}
              />
              <div
                className={cn("search-and-upload__cancel-new-event material-icons", {
                  disabled: createEventData.status === 'loading'
                })}
                onClick={() => dispatch(setCreatedEvent({ event: undefined }))}
              > close </div>
            </div>
          }
          <div className="search-and-upload__detail-upload-browse-container">
            {newEventEdit && (
              <Button
                className="search-and-upload__detail-upload-browse-confirm"
                data-testid="search-and-upload-detail-upload-browse-confirm"
                variant="contained"
                onClick={handleConfirmNewEvent}
                disabled={newEventName === ''}
              >
                <div className="material-icons">done</div>
                <span>{I18nTranslate.TranslateMessage('confirm')}</span>
              </Button>
            )}
            {newEventEdit && (
              <Button variant="outlined" onClick={handleCancelNewEvent}>
                <div className="material-icons">cancel</div>
                <span>{I18nTranslate.TranslateMessage('cancel')}</span>
              </Button>
            )}
            {(!newEventEdit && !hasEventOnEventPage) && (
              <Button
                className="search-and-upload__detail-upload-browse-add"
                data-testid="search-and-upload-detail-upload-browse-add"
                variant="outlined"
                onClick={handleNewEventClick}
                disabled={createEventData.status === 'loading'}
              >
                <div className="material-icons">add</div>
                <span>
                  {I18nTranslate.TranslateMessage('newEvent')}
                </span>
              </Button>
            )}
          </div>
        </div>
        {filesToUpload.length !== 0 && (
          <div className="search-and-upload__files">
            {filesToUpload.map((f, i) => (
              <div key={`search-and-upload-files-file-${i}`} className="search-and-upload__files-file">
                <span data-testid="search-and-upload-files-file">
                  <div className="material-symbols-outlined">videocam</div>
                  <Tooltip title={f.name} arrow>
                    <div className="search-and-upload__files-file-name">{f.name}</div>
                  </Tooltip>
                </span>
                {f.status === 'idle' && (
                  <span data-testid="search-and-upload-files-file-idle">
                    <div className={cn('search-and-upload__files-file-size', { uploading: uploadInProgress })}>
                      {`${(f.size / 1000000).toFixed(1)} MB`}
                    </div>
                    {!filesToUpload.some(f => f.status === 'loading') && (
                      <div
                        style={{ cursor: 'pointer' }}
                        onClick={() => dispatch(removeFileToUpload({ path: f.path ?? 'no_path' }))}
                        className={cn('material-symbols-outlined', { disabled: uploadInProgress })}
                      >
                        delete
                      </div>
                    )}
                  </span>
                )}
                {f?.status === 'loading' && (
                  <span>
                    <LinearProgress className="search-and-upload__files-file-progress" variant="determinate" value={f?.progress * 100} />
                    <div className="search-and-upload__files-file-progress-text">{`${intl.formatMessage({ id: 'uploading', defaultMessage: 'Uploading' })} ${Math.floor(f?.progress * 100)}%`}</div>
                  </span>
                )}
                {f?.status === 'complete' && (
                  <span className="search-and-upload__files-file-progress-text" data-testid="search-and-upload-files-file-complete">
                    {I18nTranslate.TranslateMessage('complete')}
                  </span>
                )}
                {f?.status === 'failure' && (
                  <span className="search-and-upload__files-file-progress-text" data-testid="search-and-upload-files-file-failure">
                    {I18nTranslate.TranslateMessage('retryUpload')}
                  </span>
                )}
              </div>
            )
            )}
          </div>
        )}
        {filesToUpload.length === 0 && (
          <div className="search-and-upload__detail-upload-img">
            <img src="/upload.svg" alt="upload" />
          </div>
        )}
        <div className="search-and-upload__detail-upload-browse">
          <Dropzone
            onDrop={(files: FileWithPath[]) => {
              try {
                dispatch(addFilesToUpload({
                  filesToUpload: files.map(f =>
                    ({ name: f.name, path: f.path ?? 'no_path', size: f.size, file: f, status: 'idle', progress: 0, complete: false, error: '' })
                  )
                }));
              } catch (err) {
                if (err instanceof Error) {
                  createSnackNotification(AlertLevel.Error, 'Something went wrong', `Please try again </br>  <small>${err.message}</small>`);
                }
              }
            }}
          >
            {({ getRootProps, getInputProps }) => (
              <section>
                <div {...getRootProps()}>
                  <Button variant="contained">
                    <div className="material-icons">add</div>
                    <input
                      multiple
                      type="file"
                      data-testid="drop-input"
                      {...getInputProps()}
                    />
                    {I18nTranslate.TranslateMessage('browse')}
                  </Button>
                </div>
              </section>
            )}
          </Dropzone>
        </div>
        <div className="search-and-upload__detail-upload-controls">
          <Button variant="outlined" onClick={handleClose}>
            {I18nTranslate.TranslateMessage('cancel')}
          </Button>
          <Button
            data-testid="search-and-upload-detail-upload-controls-upload"
            variant="contained"
            onClick={() => dispatch(startUpload())}
          >
            {I18nTranslate.TranslateMessage('upload')}
          </Button>
        </div>
      </Popover>
    </div>
  );
};

export default SearchAndUpload;
