import { fetchJsonData } from './';

global.fetch = jest.fn();
const fetchMock = fetch as jest.MockedFunction<typeof fetch>;

describe('fetchJsonData', () => {
  beforeEach(() => {
    fetchMock.mockClear();
  });

  it('should return JSON data when request is successful', async () => {
    const mockData = { key: 'value' };
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    } as Response);

    const data = await fetchJsonData('url');
    expect(data).toEqual(mockData);
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch).toHaveBeenCalledWith('url');
  });

  it('should throw an error when response is not ok', async () => {
    fetchMock.mockResolvedValueOnce({
      ok: false,
      status: 404,
    } as Response);
    const data = await fetchJsonData('url');
    expect(data).toBeNull();
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch).toHaveBeenCalledWith('url');
  });

  it('should return null when fetch throws an error', async () => {
    fetchMock.mockRejectedValueOnce(new Error('Network error'));

    const data = await fetchJsonData('url');
    expect(data).toBeNull();
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch).toHaveBeenCalledWith('url');
  });
});
