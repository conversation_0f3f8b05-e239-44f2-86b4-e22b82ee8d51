import NodeCache from 'node-cache';
import deleteFolder from '../deleteFolder';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

let cxt: Context<object, responses.getFolder>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

describe('Delete Folder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    cxt = {
      data: {
        folder: {
          id: 'folderId',
          contentTemplates: [
            {
              id: 'id',
              sdo: {
                data: {
                  id: '',
                  name: '',
                  tags: [],
                  createdBy: '',
                  createdByName: '',
                  description: '',
                  eventStartDate: '',
                  eventEndDate: '',
                  createdDateTime: '',
                  modifiedDateTime: '',
                  matchGroupsCount: 0,
                  filesCount: 0,
                },
                id: '',
                schemaId: '',
              },
            },
          ],
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Successfully deletes a content template', async () => {
    const response = await deleteFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { folderId: 'folderId' }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no folder', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.folder = undefined;

    expect(async () => await deleteFolder(cxt)).rejects.toThrowError(
      'No folder provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Delete event from redis', async () => {
    if (!cxt?.redisWrapper?.event) {
      throw new Error('Event not found');
    }

    cxt.redisWrapper.event.set('folderId', 'orgId', {
      id: 'folderId',
      name: 'Sample Event',
      tags: ['tag1', 'tag2'],
      createdBy: 'user123',
      createdByName: 'User 123',
      description: 'Sample Description',
      eventStartDate: '2021-01-01',
      eventEndDate: '2021-01-02',
      createdDateTime: '2021-01-01',
      modifiedDateTime: '2021-01-02',
      matchGroupsCount: 1,
      filesCount: 1,
    });
    const redisData = await cxt.redisWrapper.event.get('folderId', 'orgId');
    expect(redisData).toEqual({
      id: 'folderId',
      name: 'Sample Event',
      tags: ['tag1', 'tag2'],
      createdBy: 'user123',
      createdByName: 'User 123',
      description: 'Sample Description',
      eventStartDate: '2021-01-01',
      eventEndDate: '2021-01-02',
      createdDateTime: '2021-01-01',
      modifiedDateTime: '2021-01-02',
      matchGroupsCount: 1,
      filesCount: 1,
    });

    const response = await deleteFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { folderId: 'folderId' }
    );
    expect(response).not.toBeNull();
    const nullRedisData = await cxt.redisWrapper.event.get('folderId', 'orgId');
    expect(nullRedisData).toBeNull();
  });
});
