import { CreateEventResponse, SearchEventsResponse } from "@shared-types/responses";
import { ApiStatus } from "@store/types";
import { FileWithPath } from "react-dropzone";

export interface FileToUpload {
  path: string;
  size: number;
  name: string;
  file: FileWithPath;
  status: ApiStatus;
  progress: number;
  complete: boolean;
  error?: string;
}

export interface EventsState extends SearchEventsResponse {
  status: ApiStatus;
  error?: string;
}

export interface CreateEventState extends CreateEventResponse {
  status: ApiStatus;
  error?: string;
}
