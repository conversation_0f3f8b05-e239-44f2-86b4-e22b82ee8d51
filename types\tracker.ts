export interface Event {
  id: string;
  name: string;
  tags: Array<string>;
  createdBy: string;
  createdByName: string;
  description: string;
  eventStartDate: string;
  eventEndDate: string;
  createdDateTime: string;
  modifiedDateTime: string;
  matchGroupsCount?: number;
  filesCount?: number;
  trackerEngineId?: string;
}

export enum JobStatus {
  Pending = 'pending',
  Complete = 'complete',
  Running = 'running',
  Cancelled = 'cancelled',
  Queued = 'queued',
  Failed = 'failed',
}

export type VideoSpliceDetails = {
  status?: JobStatus;
  downloadUrl?: string;
  resolution?: string;
  outputFormat?: string;
  videoLengthMs?: number;
  videoSizeBytes?: number;
};

export type FileStatus =
  | 'processed'
  | 'processing'
  | 'error'
  | 'pending'
  | 'unknown';

export type AttributeType = 'person' | 'vehicle';
export interface AttributeRecord {
  key: string;
  value: string;
  label: string;
}

export interface AttributesJson {
  [key: string]: AttributeRecord;
}

export interface Attributes {
  [key: string]: AttributeRecord[];
}
export interface ConvertedAttributes {
  [key: string]: string[];
}

export interface File {
  id: string;
  fileName: string;
  status: FileStatus;
  length: number;
  createdByName: string;
  uploadDate: string;
  location: string;
  fileType: string;
  fileSize: number;
  eventId: string;
  eventName: string;
  thumbnailUrl: string;
  primaryAsset: {
    signedUri: string;
  };
  streams: {
    uri: string;
    protocol: string;
  }[];
  frameRate: number;
  thumbnailAssets?: {
    records: Array<{
      id: string;
      name: string;
      contentType: string;
      signedUri: string;
      details: Record<string, any>;
    }>;
  };
}

export interface Search {
  id: string;
  searchName: string;
  referenceTrackletId?: string;
  searchTime?: string;
  attributes?: Attributes;
  engineId?: string;
}

export interface MatchGroup {
  id: string;
  name: string;
  eventId: string;
  searches?: Search[];
  selectedTracklets?: string[];
  modifiedDateTime: string;
  timelineProject?: TimelineProject;
  generatedTimelines?: GeneratedTimeline[];
  createdDateTime?: string;
}

export interface TemporalData {
  id: string;
  name: string;
  status: string;
  jobs?: { records: { name: string; status: JobStatus }[] };
  recording?: {
    recordingId: string;
  };
  details: {
    tags: Array<{
      value: string;
      redactionStatus: string;
    }>;
    veritoneProgram: {
      programLiveImage: string;
    };
    veritoneFile: {
      duration: number;
      fileSize: number;
      fileName: string;
      fileType: string;
      mimetype: string;
      createdByName: string;
      videoFrameRate: number;
    };
    veritonePermissions: {
      isPublic: boolean;
      acls: Array<{
        groupId: string;
        permission: string;
      }>;
    };
    addToIndex: boolean;
    startDateTime: number;
    stopDateTime: number;
    veritoneAppId: string;
    status: string;
    createdDateTime: number;
    modifiedDateTime: number;
    recordingId: number;
  };
  createdDateTime: string;
  modifiedDateTime: string;
  folders?: {
    id: string;
    name: string;
    parent: {
      organizationId: string;
    };
  }[];
  assets?: {
    records: {
      assetType: string;
      signedUri: string;
      assetSize?: number;
      duration?: number;
      details?: {
        width?: number;
        height?: number;
        duration?: number;
        fileType?: string;
        videoFrameRate?: number;
        audioSampleRate?: number;
        audioNumChannels?: number;
      };
    }[];
  };
  thumbnailUrl: string;
  createdBy: string;
  primaryAsset: {
    signedUri: string;
  };
  streams: {
    uri: string;
    protocol: string;
  }[];
  thumbnailAssets?: {
    records: Array<{
      id: string;
      name: string;
      contentType: string;
      signedUri: string;
      details: Record<string, any>;
    }>;
  };
}

export interface Tracklet {
  orgId: string;
  trackletId: string;
  fileId: string;
  fileName: string;
  startTimeMs: number;
  stopTimeMs: number;
  attributes: Attributes;
  thumbnailUrls: { best: string };
  type: string;
  confidence?: number;
}

export interface TimelineProject {
  groups?: Array<{
    id: string;
    name: string;
    tracklets: Tracklet[];
  }>;
  modifiedDateTime?: string;
  modifiedUserId?: string;
}

export interface GeneratedTimeline extends VideoSpliceDetails {
  id: string;
  timeline?: TimelineProject;
  name?: string;
  createdDateTime?: string;
  createdUserId?: string;
  createdUserName?: string;
  tdoId?: string;
  status?: JobStatus;
}

export interface GeneratedTimelineWithMatchGroup extends GeneratedTimeline {
  matchGroup: Pick<MatchGroup, 'id' | 'name'>;
}

export interface Point {
  x: number;
  y: number;
}
export type BoundingPoly = [Point, Point, Point, Point];

export interface TrackletBoundingBox {
  boundingPoly: BoundingPoly;
  trackletId: string;
  startTimeMs: number;
  stopTimeMs: number;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
}

export interface RootFolder {
  id: string;
  name: string;
  ownerId: string;
}

export type CheckCreateRootFolderStatus = 'created' | 'existing';
