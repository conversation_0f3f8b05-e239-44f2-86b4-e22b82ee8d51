import { validateSortParams } from './validateSortParams';
describe('validateSortParams', () => {
  it('should return null if sortBy and sortDirection are valid', () => {
    const validSortByValues = [
      'veritone-file.filename',
      'recordingId',
      'createdTime',
    ];
    const validSortDirectionValues = ['desc', 'asc'];
    const sortBy = 'recordingId';
    const sortDirection = 'desc';
    const result = validateSortParams(
      sortBy,
      sortDirection,
      validSortByValues,
      validSortDirectionValues
    );
    expect(result).toBeNull();
  });

  it('should return error message if sortBy is invalid', () => {
    const validSortByValues = [
      'veritone-file.filename',
      'recordingId',
      'createdTime',
    ];
    const validSortDirectionValues = ['desc', 'asc'];
    const sortBy = 'invalidSortBy';
    const sortDirection = 'desc';
    const result = validateSortParams(
      sortBy,
      sortDirection,
      validSortByValues,
      validSortDirectionValues
    );
    expect(result).toEqual({
      error: `Invalid sortBy or sortDirection parameter. Received sortBy: ${sortBy}, sortDirection: ${sortDirection}`,
      details: {
        sortBy: 'invalidSortBy',
        sortDirection: 'desc',
      },
    });
  });

  it('should return error message if sortDirection is invalid', () => {
    const validSortByValues = [
      'veritone-file.filename',
      'recordingId',
      'createdTime',
    ];
    const validSortDirectionValues = ['desc', 'asc'];
    const sortBy = 'recordingId';
    const sortDirection = 'invalidSortDirection';
    const result = validateSortParams(
      sortBy,
      sortDirection,
      validSortByValues,
      validSortDirectionValues
    );
    expect(result).toEqual({
      error: `Invalid sortBy or sortDirection parameter. Received sortBy: ${sortBy}, sortDirection: ${sortDirection}`,
      details: {
        sortBy: 'recordingId',
        sortDirection: 'invalidSortDirection',
      },
    });
  });

  it('should return error message if sortBy and sortDirection are both invalid', () => {
    const validSortByValues = [
      'veritone-file.filename',
      'recordingId',
      'createdTime',
    ];
    const validSortDirectionValues = ['desc', 'asc'];
    const sortBy = 'invalidSortBy';
    const sortDirection = 'invalidSortDirection';
    const result = validateSortParams(
      sortBy,
      sortDirection,
      validSortByValues,
      validSortDirectionValues
    );
    expect(result).toEqual({
      error: `Invalid sortBy or sortDirection parameter. Received sortBy: ${sortBy}, sortDirection: ${sortDirection}`,
      details: {
        sortBy: 'invalidSortBy',
        sortDirection: 'invalidSortDirection',
      },
    });
  });
});
