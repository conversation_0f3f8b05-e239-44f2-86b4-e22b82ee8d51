name: Release

on:
  push:
    branches:
      - 'master'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Get commit message
        id: get_commit_message
        run: echo 'message=$(git log -1 --pretty=%B)' >> $GITHUB_ENV

      - name: Check if commit message is a release commit
        if: ${{ contains(steps.get_commit_message.outputs.message, 'Release ') }}
        run: echo 'Skipping release job for release commit.'

      - name: Set commit user
        if: ${{ !contains(steps.get_commit_message.outputs.message, 'Release ') }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "aiWARE Apps Bot"

      - name: Setup Node.js
        if: ${{ !contains(steps.get_commit_message.outputs.message, 'Release ') }}
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install dependencies in root
        if: ${{ !contains(steps.get_commit_message.outputs.message, 'Release ') }}
        run: yarn

      - name: Install dependencies in client
        if: ${{ !contains(steps.get_commit_message.outputs.message, 'Release ') }}
        run: yarn
        working-directory: client

      - name: Create release branch
        if: ${{ !contains(steps.get_commit_message.outputs.message, 'Release ') }}
        run: |
          git checkout -b feature/release-${{ github.sha }}
          git push --set-upstream origin feature/release-${{ github.sha }}

      - name: Switch to release branch
        run: git checkout feature/release-${{ github.sha }}

      - name: Run release-it
        run: npx release-it --ci --increment patch
        working-directory: client

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "Release ${env.version}"
          branch: "feature/release-${{ github.sha }}"
          title: "Release ${env.version}"
          base: master

      - name: Approve Pull Request
        uses: actions/github-script@v4
        with:
          script: |
            const { data: pullRequests } = await github.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `feature/release-${{ github.sha }}`
            });
            if (pullRequests.length > 0) {
              const pullRequest = pullRequests[0];
              await github.pulls.createReview({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: pullRequest.number,
                event: 'APPROVE'
              });
            }
      - name: Merge Pull Request
        uses: actions/github-script@v4
        with:
          script: |
            const { data: pullRequests } = await github.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `feature/release-${{ github.sha }}`
            });
            if (pullRequests.length > 0) {
              const pullRequest = pullRequests[0];
              await github.pulls.merge({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: pullRequest.number,
                merge_method: 'merge'
              });
            }
