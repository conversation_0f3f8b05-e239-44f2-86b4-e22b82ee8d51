import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { GraphQLError, ActionError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';

const getParentFolderId = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & responses.getParentFolderId> | undefined
> => {
  const { data, req } = context;
  const headers = { Authorization: req.headers.authorization };
  let resp;
  try {
    resp = await callGQL<responses.getRootFolder, ReqPayload, Data>(
      context,
      headers,
      queries.getRootFolder,
      {}
    );
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }

  const nullOwnerIdRootFolder = resp.rootFolders?.find(
    (folder) => folder.ownerId === null
  );

  if (!nullOwnerIdRootFolder?.id) {
    throw new ActionError('no root folder found.');
  }

  const rootFolderId = nullOwnerIdRootFolder.id;
  const new_data = Object.assign({}, data, {
    parentFolderId: rootFolderId,
  });

  const new_context = Object.assign({}, context, { data: new_data });
  return new_context;
};

export default getParentFolderId;
