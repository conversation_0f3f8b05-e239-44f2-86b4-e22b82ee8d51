import { MatchGroupsState } from '@store/modules/matchGroup/types';
import { has } from 'lodash';

export const getLastSearchName = (matchGroups: MatchGroupsState, matchGroupId: string, isAttributeSearch = false): number => {
  const matchGroup = matchGroups?.results.find((matchGroup) => matchGroup.id === matchGroupId);
  const matchGroupSameSearch = matchGroup?.searches?.filter((search) => has(search, 'attributes') === isAttributeSearch);
  const lastSearch = matchGroupSameSearch?.[matchGroupSameSearch?.length - 1];
  const lastSearchNameNumber = lastSearch?.searchName?.match(/\d+/)?.[0] ?? '0';
  return Number(lastSearchNameNumber) + 1;
};
