import { Messages } from '.';
import { LOCALES } from '../locales';

const messagesFR: Messages = {
  actions: 'Actions',
  addTag: 'Ajouter un tag...',
  aFile: 'un fichier',
  all: 'Tous',
  allEvents: 'Tous les événements',
  anEvent: 'un événement',
  attributes: 'Les Attributs',
  browse: 'Naviguer',
  cancel: 'Annuler',
  clearSelections: 'Effacer les sélections',
  clearFilter: 'Tout effacer',
  complete: 'Compléter',
  confirm: 'Confirmer',
  confirmMessage: 'Entrez le nom {type} pour confirmer.',
  confirmDeleteEvent: 'Vous êtes sur le point de supprimer {eventName}. Tous les fichiers associés, les groupes de correspondance, les recherches et les exportations seront supprimés. Êtes-vous sûr de vouloir le supprimer ?',
  confirmDeleteMatchGroup: 'Êtes-vous sûr de vouloir supprimer ce groupe de correspondances ? Cela supprimera toutes les recherches et chronologies qui y sont associées.',
  continue: 'Continuer',
  create: 'C<PERSON><PERSON>',
  createdBy: 'Créé•e par',
  createdDate: 'La date de création',
  createFirstMatchGroup: 'Ouvrez un fichier et créez votre premier groupe de concordance avec « Rechercher des concordances ».',
  createFirstTimelineGeneratedVideo: 'Ouvrez un groupe de match et accédez à l\'éditeur de chronologie pour créer votre première vidéo générée par la chronologie.',
  date: 'Date',
  delete: 'Supprimer',
  deleteEvent: 'Supprimer l\'événement',
  deleteFile: 'Supprimer le fichier',
  deleteFileMessage: 'Vous êtes sur le point de supprimer {fileName}. Êtes-vous sûr de vouloir le supprimer ?',
  deleteMatchGroupSearchMessage: 'Vous êtes sur le point de supprimer {searchName}. Êtes-vous sûr de vouloir le supprimer ?',
  deleteSelectedTrackletsMessage: 'Vous êtes sur le point de supprimer {noTracklets} les detections sélectionnés. Êtes-vous sûr de vouloir les supprimer ?',
  deleteSelections: 'Supprimer les sélections',
  deleteVideo: 'Supprimer la vidéo',
  detections: 'Les détections',
  downloadVideo: 'Télécharger la vidéo',
  dragAndDropFiles: 'Glissez-déposez vos fichiers ici ou naviguez jusqu\'à eux',
  enterDescriptionOrNotes: 'Entrer une description ou des notes...',
  error: 'Erreur',
  events: 'Événements',
  eventCreator: 'Créateur de l\'événement',
  eventName: 'Nom de l\'événement',
  eventTime: 'Heure de l\'événement',
  existingMatchGroup: 'Existing Match Group',
  failed: 'Échoué',
  fewerMatches: 'Moins de correspondances',
  file: 'Le fichier',
  files: 'Fichiers',
  fileGPSLocation: 'Localisation GPS du fichier',
  fileMetadata: 'Métadonnées du fichier',
  fileName: 'Nom du fichier',
  fileSize: 'Taille du fichier',
  fileStatus: 'État du fichier',
  fileType: 'Type du fichier',
  fileUploadedBy: 'Fichier téléchargé par',
  filter: 'Le filtre',
  findMatches: 'Trouver des correspondances',
  items: 'éléments',
  loading: 'En cours de chargement',
  matchGroup: 'Groupe de correspondance',
  matchGroups: 'Groupes de correspondance',
  matchGroupName: 'Nom du groupe de correspondance',
  matchGroupSearchResults: 'Résultats de la recherche d\'un groupe de correspondance',
  searchResults: 'Résultats de la recherche',
  markAsVerified: 'Marquer comme vérifié',
  Mb: 'Mo',
  moreMatches: 'Plus de correspondances',
  new: 'Nouveau',
  newEvent: 'Nouvel événement',
  newEventName: 'Nouveau nom de l\'événement',
  newMatchGroup: 'New Match Group',
  noEventFound: 'Aucun événement trouvé',
  noEvents: 'Il n\'y a pas d\'événements',
  noFilesAvailable: 'Aucun fichier disponible',
  noFilesFound: 'Aucun fichier trouvé',
  noMatchGroupsFound: 'Aucun groupe de correspondance trouvé',
  noSearches: 'Il n\'y a pas de recherches',
  none: 'Aucun',
  noTimelineGeneratedVideosFound: 'Aucune vidéo générée par la chronologie n\'a été trouvée',
  noTrackletsDetected: 'Aucun detection détecté',
  of: 'de',
  pending: 'En attente',
  pendingDeletion: 'Suppression en attente',
  people: 'les personnes',
  potentialMatchSearch: 'Recherche de correspondances potentielle',
  potentialMatchSearches: 'Recherche de correspondances potentielles',
  processing: 'Traitement',
  selectTrackletToViewDetails: 'Sélectionner un detection pour voir les détails',
  selectPotentialMatchSearch: 'Sélectionner un groupe de correspondants potentiels',
  processed: 'Traité',
  running: 'En cours',
  resultsPerPage: 'Résultats par page',
  retryUpload: 'Échoué. Cliquez sur Télécharger pour réessayer.',
  search: 'Rechercher',
  searchEvents: 'Rechercher des événements',
  searchEventsTagsAndFiles: 'Recherche d\'événements, de tags et de fichiers',
  searchAllFile: 'Rechercher tous les fichiers de cet événement',
  save: 'Enregistrer',
  select: 'Sélectionner',
  selected: 'Sélectionné',
  selectAFile: 'Sélectionner un fichier',
  selectATrackletToViewDetails: 'Select a Detection to View Details',
  showMatchScore: 'Afficher le score de match',
  thumbnailScale: 'Échelle des vignettes',
  timelineEditor: 'Éditeur de chronologie',
  timelineGeneratedVideos: 'Vidéos générées par la chronologie',
  timelineVideoCreatedBy: 'Vidéo de la chronologie Créée par',
  timelineVideoInfoLocation: 'Informations et emplacement de la vidéo de la chronologie',
  tracklets: 'Detections',
  typeNewTag: 'Taper pour ajouter un nouveau tag',
  unavailable: 'Indisponible',
  unknown: 'Inconnu',
  upload: 'Télécharger',
  uploaded: 'Téléchargé',
  uploading: 'Téléchargement',
  uploadFile: 'Télécharger le fichier',
  uploadFirstFile: 'Veuillez télécharger votre premier fichier pour {eventName}.',
  uploadDate: 'Date de téléchargement',
  uploadToWhichEvent: 'Quel est l\'événement pour lequel vous téléchargez ?',
  videoLength: 'Durée de la vidéo',
  vehicles: 'Véhicules',
  vehicleAndPersonDetection: 'Détection de véhicules et de personnes',
  verifiedMatches: 'Correspondances vérifiées',
  viewEvent: 'Voir l\'événement',
  viewFile: 'Voir le fichier',
  viewMatchGroup: 'Voir le groupe de correspondance',
  viewPotentialMatchSearches: 'Voir les recherches de correspondances potentielles',
  viewSearch: 'Voir la recherche',
  Accessory: 'Accessory',
  Backpack: 'Backpack',
  Footwear: 'Footwear',
  Female: 'Female',
  Male: 'Male',
  Lower: 'Lower',
  Upper: 'Upper',
  Body: 'Body',
  Face: 'Face',
  FootwearColor: 'Footwear Color',
  FootwearType: 'Footwear Type',
  Gender: 'Gender',
  HairColor: 'Hair Color',
  HairType: 'Hair Type',
  LowerColor: 'Lower Color',
  LowerType: 'Lower Type',
  UpperColor: 'Upper Color',
  UpperType: 'Upper Type',
  Color: 'Color',
  Make: 'Make',
  Model: 'Model',
  Type: 'Type',
  UpperSleeve: 'Upper Sleeve',
  Hair: 'Hair',
  Sleeve: 'Sleeve',
  snackSelectMatchGroup: 'Veuillez sélectionner un groupe de correspondance ou en créer un nouveau.',
  snackSelectAttribute: 'Veuillez sélectionner un attribut à rechercher.',
  findAttributeMatch: 'Trouver des correspondances potentielles avec les attributs que vous choisissez.',
  addResultsToMatchGroup: 'Ajouter les résultats à un groupe de match.',
  addFirstTracklet:
    'Ajoutez vos premiers detections en les sélectionnant dans les résultats de recherche potentiels.',
  noSearchResultsFound: 'Il n’y a encore aucune recherche pour "{matchGroup}". Pour ajouter une recherche, ouvrez un fichier, sélectionnez un Tracklet et cliquez sur Trouver des correspondances.',
  attributeSearch: 'Recherche d\'attributs',
  viewModifyAttributeSearch: 'Afficher ou modifier les paramètres de recherche',
  disableConfidence: 'La confiance est désactivée',
  noTrackletsFound: 'Aucun detection trouvé',
  selectMatchGroup: 'Sélectionner un groupe de correspondance',
  searchPeople: 'Rechercher des personnes',
  searchVehicles: 'Rechercher des véhicules',
  chooseAttributes: 'Choisir des attributs : {attributeCount} Sélectionné(s)',
  resetSelection: 'Réinitialiser la sélection',
  removeBoundingBox: 'Supprimer',
  filterTrackletsByRegion: 'Afficher les detections dans la région sélectionnée',
};

export default {
  [LOCALES.FRENCH]: messagesFR,
};
