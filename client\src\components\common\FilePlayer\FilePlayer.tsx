import { Skeleton } from '@mui/material';
import { File } from "@shared-types/tracker";
import './FilePlayer.scss';

interface FilePlayerProps {
  file?: File;
  loading?: boolean;
  hide?: boolean;
  fileViewer?: boolean;
}

const FilePlayer = ({ file, loading, hide, fileViewer }: FilePlayerProps) => <div className="file-player__main-container" >
  <div className="file-player__header">
    {loading ?
      <Skeleton
        className="file-player__file_name"
        variant="rectangular"
        width={150}
        height={15}
      /> :
      <>
        {!hide && (
          fileViewer ? <div className="file-player__file_preview">Video Timeline Preview</div> : <div className="file-player__file_name">{file?.fileName}</div>
        )}
      </>
    }
  </div>
  <div className="file-player__video-container">
    {loading ?
      <Skeleton
        className="file-player__loading-br"
        variant="rectangular"
        height={273}
      /> : <>
        {!hide && <video controls>
          <source src="movie.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>}
      </>}
  </div>
</div>;

export default FilePlayer;
