import NodeCache from 'node-cache';
import { Context } from '../../../types';
import { responses } from '../../graphQL';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import deleteGeneratedTimeline from '../deleteGeneratedTimeline';

let cxt: Context<
  object,
  responses.getMatchGroup & { generatedTimelineId: string }
>;

describe('Delete generated timeline', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        generatedTimelineId: 'deleteId',
        matchGroupId: 'id',
        matchGroup: {
          id: 'id',
          name: 'name',
          eventId: 'eventId',
          modifiedDateTime: 'modifiedDateTime',
          generatedTimelines: [
            {
              id: 'deleteId',
              name: 'name',
              timeline: {},
            },
            {
              id: 'keepId',
              name: 'name',
              timeline: {},
            },
          ],
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'schemaId');
  });

  it('Successfully deletes a generated timeline', async () => {
    const response = await deleteGeneratedTimeline(cxt);
    expect(cxt.data.matchGroup.generatedTimelines).toHaveLength(2);
    expect(response.data.matchGroup.generatedTimelines).toHaveLength(1);
    expect(response?.data?.matchGroup?.generatedTimelines?.[0]?.id).toBe(
      'keepId'
    );
  });

  it('Throws error if generatedTimelineId is undefined', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.generatedTimelineId = undefined;
    await expect(
      async () => await deleteGeneratedTimeline(cxt)
    ).rejects.toThrow('No generatedTimelineId provided');
  });

  it('Throws error if matchGroup is undefined', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.matchGroup = undefined;
    expect(async () => await deleteGeneratedTimeline(cxt)).rejects.toThrowError(
      'No matchGroup found'
    );
  });
});
