import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import createContentTemplate from '../createContentTemplate';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';

let cxt: Context<
  object,
  responses.createFolder & responses.createStructuredData
>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createFolderContentTemplate: {} })
  ),
}));

describe('Create Content Template', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        createFolder: {
          id: 'folderId',
          name: '',
          description: '',
          ownerId: '',
        },
        createStructuredData: {
          id: 'sdoId',
          schemaId: 'schemaId',
          data: {
            id: 'id',
            name: 'name',
            tags: [],
            createdBy: 'createdBy',
            createdByName: 'createdByName',
            description: 'description',
            eventStartDate: 'eventStartDate',
            eventEndDate: 'eventEndDate',
            createdDateTime: 'createdDateTime',
            modifiedDateTime: 'modifiedDateTime',
          },
          createdDateTime: '',
          modifiedDateTime: '',
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully create an asset', async () => {
    const response = await createContentTemplate(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        folderId: 'folderId',
        sdoId: 'sdoId',
        schemaId: 'schemaId',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no createFolder', async () => {
    // @ts-expect-error TODO: does this make sense
    cxt.data.createFolder = undefined;

    expect(async () => await createContentTemplate(cxt)).rejects.toThrowError(
      'No folder or structured data provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is no createStructuredData', async () => {
    // @ts-expect-error TODO: does this make sense
    cxt.data.createStructuredData = undefined;

    expect(async () => await createContentTemplate(cxt)).rejects.toThrowError(
      'No folder or structured data provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
