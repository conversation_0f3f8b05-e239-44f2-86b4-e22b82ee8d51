# Local Development

set up env variables ENV_AZURE_BLOB_ACCOUNT_KEY for local development. You can find the azure dev account key at
https://github.com/veritone/datacenter-config/blob/master/zdsc01-usgoveast1/tracker-app.json#L9

```
export ENV_AZURE_BLOB_ACCOUNT_KEY=xxx
yarn install
yarn start:dev
```

## Create API Config

Copy apiConfig-${environment}.js to apiConfig.json depending on which version you want.
Customize if necessary.

# Tracker API

A RESTful API for the Tracker application. Below are the endpoints that can be accessed.

## GET

| Endpoint                 | Body (Required) | Params (Optional)                                                                                                                     | Description                |
| ------------------------ | --------------- | ------------------------------------------------------------------------------------------------------------------------------------- | -------------------------- |
| `/api/v1/event/:eventId` | N/A             | N/A                                                                                                                                   | Query a specific event     |
| `/api/v1/events`         | N/A             | `pageSize: number`<br/>`currentPage: number`<br/>`query: string`<br/>`sort: [{ field: string, order: string }]`                       | Search for events          |
| `/api/v1/match-groups`   | N/A             | N/A                                                                                                                                   | Get all match groups       |
| `/api/v1/tags`           | N/A             | N/A                                                                                                                                   | Get all tags               |
| `/api/v1/file/:fileId`   | N/A             | N/A                                                                                                                                   | Query a specific file      |
| `/api/v1/files`          | N/A             | `pageSize: number`<br/>`currentPage: number`<br/>`query: string`<br/>`eventId: string`<br/>`sort: [{ field: string, order: string }]` | Search for files           |
| `/api/v1/credential`     | N/A             | N/A                                                                                                                                   | Get a temporary credential |

## PATCH

| Endpoint                             | Body (Required) | Params (Optional)                                                                                                     | Description                   |
| ------------------------------------ | --------------- | --------------------------------------------------------------------------------------------------------------------- | ----------------------------- |
| `/api/v1/event/:eventId`             | N/A             | `name: string`<br/>`description: string`<br/>`tags: string[]`<br/>`eventStartDate: string`<br/>`eventEndDate: string` | Update a specific event       |
| `/api/v1/match-groups/:matchGroupId` | N/A             | `name: string`<br/>`searches: Search[]`<br/>`selectedTracklets: string[]`                                             | Update a specific match group |

## POST

| Endpoint               | Required                                                                                         | Optional | Description               |
| ---------------------- | ------------------------------------------------------------------------------------------------ | -------- | ------------------------- |
| `/api/v1/event`        | `name: string`<br/>`description: string`<br/>`eventStartDate: string`<br/>`eventEndDate: string` | N/A      | Create an event           |
| `/api/v1/file`         | `eventId: string`<br/>`fileName: string`<br/>`fileType: string`                                  | N/A      | Generate file upload link |
| `/api/v1/match-groups` | `name: string`<br/>`eventId: string`                                                             | N/A      | Creates a match group     |

## DELETE

| Endpoint                 | Required | Optional | Description     |
| ------------------------ | -------- | -------- | --------------- |
| `/api/v1/event/:eventId` | N/A      | N/A      | Delete an event |
| `/api/v1/file/:fileId`   | N/A      | N/A      | Delete a file   |
