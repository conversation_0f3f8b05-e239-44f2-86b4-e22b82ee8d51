import './NoSearchResults.scss';

const NoSearchResults = ({ hasSearchResults, title, description }: { hasSearchResults: boolean; title?: string; description?: string }) => {
  if (hasSearchResults) {
    return;
  }

  return <div className="no-search-results">
    <div className="no-search-results__img">
      <div className="no-search-results__img-container">
        <img className="no-search-results__img-bubble" src="/no-search-results-bubble.svg" />
        <img className="no-search-results__img-box" src="/no-search-results-box.svg" />
      </div>
    </div>
    <div className="no-search-results__text">
      <div className="no-search-results__text-title">
        {title ?? 'Nothing here'}
      </div>
      <div className="no-search-results__text-desc" dangerouslySetInnerHTML={{ __html: description ?? 'Please use the search bar to conduct a new search' }} />
    </div>
  </div>;
};

export default NoSearchResults;
