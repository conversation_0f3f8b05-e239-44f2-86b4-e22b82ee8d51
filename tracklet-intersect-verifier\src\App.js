import './App.css';
import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import JsonView from 'react18-json-view'
import 'react18-json-view/src/style.css'

const bearerToken = 'd180f916-082f-48f0-b510-295d4e73b68a';

// const eventId = '012cab93-933b-4f91-90ee-686ae7342dfc';
// const tdoId = 3370013370;

const eventId = '6cebd892-5e52-48e9-a1c5-7e1ff03b44f4';
const tdoId = 3340012926;


// xMin=&xMax=&yMin=&yMax=

const source = {
  minY: 0.1,
  maxY: 0.5,
  minX: 0.1,
  maxX: 0.5
};

// const source = {
//   minY: 0.0034482758620689655,
//   maxY: 0.36551724137931035,
//   minX: 0.6982758620689655,
//   maxX: 1
// };

// const source = {
//   maxX: 0.75,
//   minX: 0.25,
//   maxY: 0.75,
//   minY: 0.25
// };


// const source = {
//   minY: 0.1,
//   maxY: 0.9,
//   minX: 0.1,
//   maxX: 0.9
// };

const scale = 800;

function App() {
  const [trackletIndex, setTrackletIndex] = useState(0);
  const [trackletIds, setTrackletIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [lastResult, setLastResult] = useState({});

  const map = useRef(null);

  function drawSource() {
    let ctx = map.current.getContext('2d')

    ctx.lineWidth = "2";
    ctx.strokeStyle = "red";

    ctx.beginPath();
    ctx.rect(source.minX * scale, source.minY * scale, source.maxX * scale - source.minX * scale, source.maxY * scale - source.minY * scale);
    ctx.stroke();
  }

  // Function to draw polygons
  function drawPolygons(boundingPolys) {
    let ctx = map.current.getContext('2d')

    boundingPolys.forEach(polygon => {
      const minX = Math.min(...polygon.map(p => p.x)) * scale;
      const minY = Math.min(...polygon.map(p => p.y)) * scale;
      const maxX = Math.max(...polygon.map(p => p.x)) * scale;
      const maxY = Math.max(...polygon.map(p => p.y)) * scale;

      ctx.lineWidth = "1";
      ctx.strokeStyle = "black";

      ctx.beginPath();

      ctx.rect(minX, minY, maxX - minX, maxY - minY);
      ctx.stroke();
    });
  }

  useEffect(() => {
    map.current = document.getElementById('map');
    setLoading(true);
    axios.get(`https://local.veritone.com:3002/api/v1/event/${eventId}/file/${tdoId}/results/intersection?type=person&pageSize=100&currentPage=1&xMin=${source.minX}&xMax=${source.maxX}&yMin=${source.minY}&yMax=${source.maxY}`, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`
      }
    }).then(function (response) {
      setLoading(false);
      setError('');
      setLastResult(response.data);
      setTrackletIds(response.data.results.map(t => t.trackletId));

    }).catch(function (error) {
      console.error(error);
      setError(error.message);
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    drawSource();

    if (trackletIds.length === 0) return;
    setLoading(true);
    axios.get(`https://local.veritone.com:3002/api/v1/bounding-boxes/?trackletId=${trackletIds[trackletIndex]}`, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`
      }
    }).then(function (response) {
      setLoading(false);
      setError('');
      drawPolygons(response.data.results.map(r => r.boundingPoly))

    }).catch(function (error) {
      console.error(error);
      setError(error.message);
      setLoading(false);
    });

  }, [trackletIds, trackletIndex]);


  const next = () => {
    if (trackletIndex < trackletIds.length - 1) {
      setTrackletIndex(trackletIndex + 1);
    } else {
      setTrackletIndex(0);
    }
  }

  const clear = () => {
    let ctx = map.current.getContext('2d')
    ctx.reset();
  }

  const nextRand = () => {
    setTrackletIndex(Math.floor(Math.random() * trackletIds.length));
  }

  return (
    <div className="App">
      <div style={{ display: 'flex', gap: 0, justifyContent: 'center', alignItems: 'middle' }}>
        <div style={{ marginTop: 30 }}>
          <canvas style={{ border: 'dashed 1px blue' }} height={`${scale}`} width={`${scale}`} id="map" />
          <div style={{ width: '100%' }} />
          <div style={{ display: 'flex', justifyContent: 'center', gap: '30px', marginTop: '10px' }}>
            <button onClick={() => window.location.reload()}>Reload</button>

            <button onClick={clear}>Clear</button>
            <button onClick={next}>Next</button>
            <button onClick={nextRand}>Next Random</button>
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', marginTop: '20px' }}>
            <span> Tracklet {trackletIndex + 1} {trackletIds[trackletIndex]} </span>
          </div>
          {loading && <div style={{ display: 'flex', color: 'green', justifyContent: 'center', gap: '10px', marginTop: '10px' }}>
            <span> Loading... </span>
          </div>}
          {error && <div style={{ display: 'flex', color: 'red', justifyContent: 'center', gap: '10px', marginTop: '10px' }}>
            <span> Error: {error} </span>
          </div>}
        </div>

        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'middle', padding: '30px 50px', scrollY: 'auto', width: 500 }}>
          <JsonView src={lastResult} />
        </div>
      </div>
    </div>
  );
}

export default App;
