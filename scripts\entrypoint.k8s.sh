#!/bin/sh

cd /usr/share/nginx/html
echo "👉 Running the config script..."
/usr/share/nginx/html/config --serviceName $SERVICE_NAME --environmentName $ENVIRONMENT

# Remove the config script from public directory.
rm /usr/share/nginx/html/config

# Check if aiware-config.json exists
if [ ! -f "/usr/share/nginx/html/aiware-config.final.json" ]; then
  echo "❌ Error: aiware-config.final.json does not exist in the working directory. There must be an error while fetching the config."
  exit 1
fi

remove_http_https() {
  local url="$1"
  # Using parameter expansion to remove http:// and https://
  echo "${url#http*://}"
}

# Fetch and export the configurations
echo "👉 Setting up nginx configuration..."
url=$API_HOST
export api_host_without_protocol=$(remove_http_https "$url")
export CONFIG=$(cat /usr/share/nginx/html/aiware-config.final.json | tr -d '\r\n')
: "${CONFIG:={__error: 'Failed to fetch config.'}}"
export uri="\$uri"
export full_url="\$full_url"
export scheme="\$scheme"
export host="\$host"
export uri="\$uri"
export stripped_uri="\$stripped_uri"
export http_host="\$http_host"
export http_x_forwarded_proto="\$http_x_forwarded_proto"
export real_scheme="\$real_scheme"
export remote_addr="\$remote_addr"
export proxy_add_x_forwarded_for="\$proxy_add_x_forwarded_for"

# Update the nginx config
echo "👉 Updating nginx config..."
envsubst < /etc/nginx.conf.template > /etc/nginx/conf.d/default2.conf

apiConfigFile="/server/apiConfig.json"

cd /server
mv apiConfigWhitelist.json aiware-config.json

echo "👉 Running the second script..."
/server/config --serviceName $SERVICE_NAME --environmentName $ENVIRONMENT

mv /server/aiware-config.final.json $apiConfigFile

. /dynamicConfig-index-html.sh

cp $apiConfigFile /server/dist/server/apiConfig.json

shouldStartApi=$( jq -r .startApi $apiConfigFile )

if [ $shouldStartApi = "true" ]; then
  echo "Starting ngnix server...."
  nginx -g 'daemon on;';

  echo "Starting Node server...."
  exec node /server/dist/server/src/start-server.js
else
  echo "Not Starting API"
  nginx -g 'daemon off;';
fi
