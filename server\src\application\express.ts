import cors from 'cors';
import express, { NextFunction, Response } from 'express';
import { Config } from '../config';
import { v4 as uuidv4 } from 'uuid';
import bodyParser from 'body-parser';
import { RequestWithMeta } from './types';
import swaggerUi from 'swagger-ui-express';
import { swaggerDocument } from '../../docs/swagger/swagger';

async function createExpressApp({ config }: { config: Config }) {
  const app = express();

  function primeRequestContext(
    req: RequestWithMeta<void>,
    res: Response,
    next: NextFunction
  ) {
    const correlationId = uuidv4();

    req.metadata = { correlationId };
    res.set({ correlationId });

    next();
  }

  app.use(bodyParser.json());
  app.use(primeRequestContext);
  app.use(cors());

  const customOptions = {
    customSiteTitle: 'Track API',
    customCss: `.topbar { display: none ; };`,
  };
  app.use(
    '/api-docs',
    swaggerUi.serve,
    swaggerUi.setup(swaggerDocument, customOptions)
  );

  /* Health Check */
  app.route('/api/health').get(config.healthCheckApp.handlers.get.health);

  /* Events */
  app
    .route('/api/v1/event/:eventId')
    .delete(config.trackerApp.handlers.delete.event);
  app.route('/api/v1/event/:eventId').get(config.trackerApp.handlers.get.event);
  app.route('/api/v1/event').post(config.trackerApp.handlers.post.event);
  app
    .route('/api/v1/event/:eventId')
    .patch(config.trackerApp.handlers.patch.event);

  /* Tags */
  app.route('/api/v1/tags').get(config.trackerApp.handlers.get.tags);

  /* Match Groups */
  app
    .route('/api/v1/match-groups/:matchGroupId')
    .get(config.trackerApp.handlers.get.matchGroup);
  app
    .route('/api/v1/match-groups')
    .get(config.trackerApp.handlers.get.matchGroups);
  app
    .route('/api/v1/match-groups')
    .post(config.trackerApp.handlers.post.matchGroup);
  app
    .route('/api/v1/match-groups/:matchGroupId')
    .patch(config.trackerApp.handlers.patch.matchGroup)
    .delete(config.trackerApp.handlers.delete.matchGroup);
  app
    .route('/api/v1/match-groups/:matchGroupId/generate-timeline')
    .patch(config.trackerApp.handlers.patch.generateTimelines);
  app
    .route('/api/v1/match-groups/:matchGroupId/timeline')
    .delete(config.trackerApp.handlers.delete.timeline);
  /* Match Group Selected Tracklets */
  app
    .route('/api/v1/match-groups/:matchGroupId/selected-tracklets')
    .get(config.trackerApp.handlers.get.matchGroupSelectedTracklets);
  app
    .route('/api/v1/match-groups/:matchGroupId/selected-tracklets')
    .delete(config.trackerApp.handlers.delete.matchGroupSelectedTracklets);

  /* Match Group Search */
  app
    .route('/api/v1/match-groups/:matchGroupId/search/:searchId')
    .delete(config.trackerApp.handlers.delete.matchGroupSearch)
    .get(config.trackerApp.handlers.get.matchGroupSearch);

  /* File Results */
  app
    .route('/api/v1/event/:eventId/file/:fileId/results')
    .get(config.trackerApp.handlers.get.fileResultsSearch);
  app
    .route('/api/v1/event/:eventId/file/:fileId/results/intersection')
    .get(config.trackerApp.handlers.get.fileResultsSearchIntersectingBox);

  /* Files */
  app
    .route('/api/v1/file/:fileId')
    .delete(config.trackerApp.handlers.delete.file);
  app.route('/api/v1/file/:fileId').get(config.trackerApp.handlers.get.file);
  app
    .route('/api/v1/file/:fileId')
    .patch(config.trackerApp.handlers.patch.file);
  app
    .route('/api/v1/file/:fileId/ingest')
    .post(config.trackerApp.handlers.post.ingest);
  app.route('/api/v1/file').post(config.trackerApp.handlers.post.file);

  /* Search */
  app.route('/api/v1/events').get(config.trackerApp.handlers.get.events);
  app
    .route('/api/v1/events/ids')
    .get(config.trackerApp.handlers.get.eventsByIds);
  app.route('/api/v1/files').get(config.trackerApp.handlers.get.files);

  /* bounding boxes */
  app
    .route('/api/v1/bounding-boxes')
    .get(config.trackerApp.handlers.get.boundingBoxes);

  /* Credential for cloud storage */
  app
    .route('/api/v1/credential')
    .get(config.getCredentialApp.handlers.get.credential);

  /* Thumbnails */
  app
    .route('/api/v1/thumbnails')
    .post(config.trackerApp.handlers.post.thumbnails);

  /* Check and create root folder */
  app
    .route('/api/v1/rootfolder')
    .post(config.trackerApp.handlers.post.rootFolder);

  /* Delete Everything */
  // app
  //   .route('/api/v1/delete-everything')
  //   .delete(config.trackerApp.handlers.delete._events);
  return app;
}

export default createExpressApp;
