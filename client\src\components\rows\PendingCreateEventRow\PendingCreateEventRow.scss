.pending-event-row {
  display: block;
  cursor: pointer;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  .pending-event-row__row {
    position: relative;
    display: flex;
    padding: 11.5px 0 11.5px 18px;

    .pending-event-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .pending-event-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 23px 16px 23px 0;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;
      }
    }

    .pending-event-row__cell-name {
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: left;
      white-space: nowrap;
      padding-right: 5px;
    }
  }
}
