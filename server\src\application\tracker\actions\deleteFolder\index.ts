import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';

const deleteFolder = async <
  ReqPayload,
  Data extends Partial<responses.getFolder> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, req, log, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };

  const { folder } = data;
  if (!folder) {
    throw new ActionError('No folder provided');
  }

  try {
    const folderId = folder.treeObjectId ?? folder.id;
    await callGQL<responses.deleteFolder, ReqPayload, Data>(
      context,
      headers,
      queries.deleteFolder,
      {
        folderId,
      }
    );

    if (redisWrapper && data.folder) {
      const orgId = data.folder.parent.organization.id;
      await redisWrapper.event.del(folder.id, orgId);
    }

    return context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default deleteFolder;
