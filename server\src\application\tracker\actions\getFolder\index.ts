import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';

const getFolder = async <
  ReqPayload,
  Data extends { eventId?: string } & Partial<responses.getMatchGroup> &
    Partial<responses.getFile> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.getEvent>> | undefined
> => {
  const { data, req } = context;
  const headers = { Authorization: req.headers.authorization };

  const eventId =
    req.params?.eventId ??
    data?.eventId ??
    data?.file?.eventId ??
    data?.matchGroup?.eventId ??
    req.query?.eventId;
  if (!eventId) {
    throw new ActionError('No eventId provided');
  }

  try {
    const { folder } = await callGQL<responses.getFolder, ReqPayload, Data>(
      context,
      headers,
      queries.getFolder,
      { folderId: eventId }
    );

    if (folder) {
      const new_data = Object.assign({}, data, { folder: folder });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    console.error(e);
    throw new GraphQLError(
      'The event does not exist in current organization or belongs to another organization'
    );
  }
};

export default getFolder;
