import cx from 'classnames';
import './TimelineDetail.scss';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import { Button } from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import CamIcon from '../../assets/icon-cam.svg?react';
import DeleteIcon from '../../assets/x-circle.svg?react';
import { bytesToMb, millisToTimeFormatted, toLocalTime } from '@utility/convert';
import { downloadFromUrl } from '@utility/downloadFromUrl';
import ConfirmDialog from '@components/common/ConfirmDialog';
import DownloadIcon from '../../assets/icon-download.svg?react';
import GovernmentIcon from '../../assets/icon-government.svg?react';
import { deleteGeneratedTimeline } from '@store/modules/event/slice';
import { GeneratedTimelineWithMatchGroup } from '@shared-types/tracker';

const TimelineDetail = ({ timeline, setSelectedTimeline }: Props) => {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleDownload = () => {
    if (timeline.downloadUrl && timeline.name) {
      downloadFromUrl(timeline.downloadUrl, timeline.name);
    }
  };

  const handleDelete = () => {
    dispatch(
      deleteGeneratedTimeline({
        matchGroupId: timeline.matchGroup.id,
        generatedTimelineId: timeline.id,
      })
    );
    setSelectedTimeline(null);
    setDeleteDialogOpen(false);
  };

  return (
    <div className="timeline__detail">
      <div className="timeline__header">
        <h2 className="timeline__title">{timeline.name}</h2>
        <div
          className={cx('timeline__status', timeline.status)}
          data-testid="timeline-status"
        >
         {/* eslint-disable-next-line formatjs/enforce-default-message*/}
          {intl.formatMessage({ id: timeline.status }).toUpperCase()}
        </div>
      </div>
      <div className="timeline__date-created">
        {`${intl.formatMessage({ id: 'createdDate', defaultMessage: 'Created Date' })}: `}
        {I18nTranslate.TranslateDate(toLocalTime(timeline?.createdDateTime ?? ''))}
      </div>
      <div className="timeline__info">
        <p className="timeline__created-by">
          {`${intl.formatMessage({ id: 'timelineVideoCreatedBy', defaultMessage: 'Timeline Video Created by' })}: ${timeline.createdUserName ?? 'Officer Name'}`}
        </p>
        <div className="timeline__details">
          <div className="timeline__info">
            {I18nTranslate.TranslateMessage('timelineVideoInfoLocation')}
          </div>
          <div className="timeline__meta">
            <div className="timeline__file-name">
              <CamIcon />
              {timeline.name}
            </div>
            <div data-testid={"timeline-details-file-size-container"} className={cx({ 'timeline__file-size': timeline.status === 'complete' },
              { 'timeline__video-status-not-complete': timeline.status !== 'complete' })}>
              <div className="file-size-label">
                {`${intl.formatMessage({ id: 'fileSize', defaultMessage: 'File Size' })}: `}
              </div>
              <div className="file-size-value">
                {`${bytesToMb(timeline.videoSizeBytes || 0)} ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`}
              </div>
            </div>
            <div data-testid={"timeline-details-video-length-container"} className={cx({ 'timeline__video-length': timeline.status === 'complete' },
              { 'timeline__video-status-not-complete': timeline.status !== 'complete' })}>
              <div className="video-length-label">
                {`${intl.formatMessage({ id: 'videoLength', defaultMessage: 'Video Length' })}: `}</div>
              <div className="video-length-value">
                {millisToTimeFormatted(timeline?.videoLengthMs ?? 0)}
              </div>
            </div>
          </div>
          <div className="timeline__detail-divider" />
          <div className="timeline__match-group">
            <GovernmentIcon />
            <div>{timeline.matchGroup.name}</div>
          </div>
        </div>
      </div>
      <div className="timeline__detail-divider" />
      <div className="timeline__actions">
        <Button
          data-testid="timeline-download-button"
          className={cx({
            'timeline__detail-disabled-button':
              !timeline.downloadUrl || !timeline.name,
          })}
          onClick={handleDownload}
        >
          <DownloadIcon />
          <span>
            {I18nTranslate.TranslateMessage('downloadVideo')}
          </span>
        </Button>
        <Button
          data-testid="timeline-delete-button"
          onClick={() => setDeleteDialogOpen(true)}
        >
          <DeleteIcon />
          <span>
            {I18nTranslate.TranslateMessage('deleteVideo')}
          </span>
        </Button>
        {deleteDialogOpen && (
          <ConfirmDialog
            title={intl.formatMessage({ id: 'deleteFile', defaultMessage: 'Delete File' })}
            open={deleteDialogOpen}
            content={intl.formatMessage({ id: 'deleteFileMessage', defaultMessage: 'You are about to delete {fileName}. Are you sure you want to delete it?' }, { fileName: timeline.name })}
            confirmText={intl.formatMessage({ id: 'delete', defaultMessage: 'Delete' })}
            cancelText={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
            onConfirm={handleDelete}
            onClose={() => setDeleteDialogOpen(false)}
          />
        )}
      </div>
    </div>
  );
};

interface Props {
  timeline: GeneratedTimelineWithMatchGroup;
  setSelectedTimeline: (
    timeline: GeneratedTimelineWithMatchGroup | null
  ) => void;
}

export default TimelineDetail;
