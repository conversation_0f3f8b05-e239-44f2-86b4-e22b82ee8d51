// Example migration file
// import { Knex } from 'knex';

// exports.up = (knex: Knex) =>
//   Promise.all([
//     knex.schema.hasTable('ContactStopData').then(async (tableExists: boolean) => {
//       if (!tableExists) {
//         await knex.schema.raw(`CREATE TABLE ExampleTable (
//           id int IDENTITY(1,1) PRIMARY KEY,
//           );`
//         );
//         return true;
//       }
//     })
//   ])

// exports.down = (knex: Knex) =>
//   Promise.all([
//     knex.schema.dropTable('ExampleTable')
//   ])
