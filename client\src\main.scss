html,
body {
  height: 100%;
  margin: 0;
  overflow: hidden;

  #root {
    color-scheme: light dark;
    text-rendering: optimizelegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--main-content-background);

    /* 55px is the app bar */
    height: calc(100% - 55px);

    > :nth-child(2) {
      padding-bottom: 0 !important;
      overflow: hidden;
    }
  }

  .main__scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .main__scrollbar::-webkit-scrollbar-track {
    background: #fff;
    width: 10px;
    margin-bottom: 4px;
  }

  .main__scrollbar::-webkit-scrollbar-thumb {
    background: #DADFE8;
    border-radius: 30px;
  }

  .main__scrollbar::-webkit-scrollbar-thumb:hover {
    background: #C1C9D1;
  }

  .main__snackbar {
    position: fixed;
    right: 15px;
    bottom: 5px;
    z-index: 9999999
  }

  .MuiTabs-root {
    .MuiTab-root {
      text-transform: capitalize;
    }
  }

  .MuiButtonBase-root {
    &.Mui<PERSON>utton-outlined {
      border-radius: 25px;
    }

    &.MuiButton-root {
      &.Mui-disabled {
        color: var(--text-tertiary);
        background-color: var(--button-disabled);
        border: none;
      }
    }
  }

  .main__tracklet_thumbnails-tracklet-details {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
    gap: 3px;
    height: 18px;

    .main__tracklet_thumbnails-tracklet-in-match-group,
    .main__tracklet_thumbnails-tracklet-reference {
      position: relative;

      svg {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

  }

  .main__tracklet_thumbnails-tracklets {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: left;
    align-items: center;

    .main__tracklet_thumbnails-tracklet-border {
      border: solid 2px var(--tracklet-inner-border);

      .main__tracklet_thumbnails-tracklet-container {
        position: relative;
        border: 2px solid var(--tracklet-inner-border);
      }

      .main__tracklet_thumbnails-tracklet-container-border {
        height: 100%;
        width: 100%;
        padding: 2px;
        position: relative;
      }

      .main__tracklet_thumbnails-tracklet {
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: black;
        object-fit: contain;

        &.skeleton {
          cursor: inherit;
          background-color: var(--skeleton-background);
        }
      }

      .main__tracklet_thumbnails-tracklet-timestamp {
        text-align: center;
        margin-top: 3px;

        @include size-1;

        a {
          color: #1565C0;
        }
      }

      .main__tracklet_thumbnails-tracklet-confidence {
        text-align: center;

        &.green {
          color: green;
        }
        &.red {
          color: #ce0000;
        }
        &.yellow {
          color: #c9971b;
        }

        @include size-1-bold;
      }

      .main__tracklet_thumbnails-tracklet-checkbox {
        position: absolute;
        top: 7px;
        left: 7px;
        padding: 0;
        color: var(--background);
        box-shadow: 0 1px 5px 0 #000000b3 inset;

        &.Mui-checked {
          opacity: 1;
          color: var(--tracklet-icon-mui-checked-color);
          background-color: var(--tracklet-border-selected);
          height: 20px;
          width: 20px;
          border-radius: 2px;
          border: solid 1px var(--tracklet-icon-selected);
          box-shadow: none;

          svg {
            padding: 4px;
          }
        }

        .main__tracklet_thumbnails-tracklet-checkbox-icon {
          background-color: var(--tracklet-icon-checked-background);
          height: 20px;
          width: 20px;
          box-shadow: 0 1px 5px 0 #000000b3 inset;
          border-radius: 2px;
        }

        .main__tracklet_thumbnails-tracklet-checkbox-icon-checked {
          background-color: var(--tracklet-icon-checkbox-icon-checked);
        }

        .MuiTouchRipple-root {
          opacity: 1;
          color: var(--tracklet-border-selected);
          padding: 0;
          height: 20px;
          width: 20px;
          border-radius: 0;
        }
      }

      &:hover {
        .main__tracklet_thumbnails-tracklet-container-border {
          border: solid 3px var(--tracklet-hover);
          padding: 2px;
        }
        .main__tracklet_thumbnails-tracklet-timestamp {
          cursor: pointer;
        }
      }
      &.checked,
      &.selected {
        color: black;

        .main__tracklet_thumbnails-tracklet-container-border {
          border: solid 3px var(--tracklet-border-selected);
        }

        .main__tracklet_thumbnails-tracklet-timestamp {
          color: black;
        }

        &:hover {
          .main__tracklet_thumbnails-tracklet-container-border {
            border: solid 3px var(--tracklet-border-selected-hover);
          }
  
        }
      }

    }
  }
}


.main__tracklet_playing {
  position: absolute;
  bottom: 7px;
  left: 5px;
  transform: scale(.7);
}

.eq-bar {
  transform: scale(1, -1) translate(0, -24px);
  fill: #cfedff;
}

.eq-bar--1 {
  animation-name: tall-eq;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
  animation-delay: 0.17s;
}

.eq-bar--2 {
  animation-name: short-eq;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
  animation-delay: 0s;
}

.eq-bar--3 {
  animation-name: short-eq;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
  animation-delay: 0.34s;
}

.eq-bar--4 {
  animation-name: medium-eq;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
  animation-delay: 0.17s;
}

@keyframes short-eq {
  0% {
    height: 5px
  }

  50% {
    height: 11px
  }

  100% {
    height: 5px
  }
}

@keyframes medium-eq {
  0% {
    height: 4px
  }

  50% {
    height: 11px
  }

  100% {
    height: 4px
  }
}

@keyframes tall-eq {
  0% {
    height: 16px
  }

  50% {
    height: 6px
  }

  100% {
    height: 16px
  }
}