# Define build type
ARG K8S_BUILD=FALSE
ARG BASE_IMAGE=registry.central.aiware.com/fed-nginx:latest
ARG NODE_VERSION=22

FROM node:${NODE_VERSION} AS frontend

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app && \
    rm -rf /vars/lib/apt/lists/*
COPY . /app
WORKDIR /app/client

RUN ls -a && \
    chmod +x /app/scripts/*.sh && \
    yarn && \
    yarn build

RUN echo '### /app/scripts/buildinfo.sh...' && /app/scripts/buildinfo.sh

FROM node:${NODE_VERSION} AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

RUN mkdir -p /app/server
RUN rm -rf /vars/lib/apt/lists/*
WORKDIR /app/server

COPY types/ ../types
COPY assets/ ../assets

RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .

RUN yarn
RUN yarn build

# Set the final base image based on build arg BASE_NAME
FROM ${BASE_IMAGE} AS final

# Set the user to root
USER root

# Create a non-root user
RUN if command -v addgroup > /dev/null; then \
      addgroup --system appgroup && adduser --system --ingroup appgroup appuser; \
    else \
      groupadd --system appgroup && useradd --system --gid appgroup appuser; \
    fi

ARG K8S_BUILD
ARG NODE_VERSION

# Check if K8S_BUILD is TRUE or FALSE and check if the package manager is apk or dnf
RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo ncurses curl && \
        apk del tar; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo ncurses curl && \
        dnf remove -y tar && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    else \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache nginx envsubst && \
        mkdir -p /etc/nginx/conf.d && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo ncurses curl && \
        apk del tar; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y nginx gettext && \
        mkdir -p /etc/nginx/conf.d && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo ncurses curl && \
        dnf remove -y tar && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    fi

RUN node -v && npm -v

ENV NGINX_PORT=9000

EXPOSE ${NGINX_PORT}/tcp

# Regular Build
COPY --from=frontend /app/scripts/getconfig-dynamicConfig.sh /getconfig-dynamicConfig.sh
COPY --from=frontend /app/scripts/dynamicConfig-index-html.sh /dynamicConfig-index-html.sh
COPY --from=frontend /app/scripts/entrypoint.sh /entrypoint.sh
COPY --from=frontend /app/client/configWhitelist.json /configWhitelist.json
COPY --from=frontend /app/client/dist /usr/share/nginx/html
COPY --from=frontend /app/client/build-manifest.yml /build-manifest.yml
COPY --from=frontend /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

COPY --from=backend /app/server/ /server/
COPY --from=backend /app/server/apiConfigWhitelist.json /apiConfigWhitelist.json

# Copy nginx.conf to a temporary location
COPY --from=frontend /app/scripts/nginx.conf /etc/nginx/conf.d/nginx.conf.temp

# K8S Build
COPY --from=frontend /app/scripts/dynamicConfig-k8s-index-html.sh /dynamicConfig-k8s-index-html.sh
COPY --from=frontend /app/scripts/entrypoint.k8s.sh /entrypoint-k8s.sh
COPY --from=frontend /app/scripts/nginx.k8s.conf /etc/nginx.conf.template
COPY --from=frontend /app/client/configWhitelist.json /usr/share/nginx/html/aiware-config.json

# COPY --from=veritone/aiware-spa:sha-ceadca5 /usr/share/nginx/config /usr/share/nginx/html/config
# COPY --from=veritone/aiware-spa:sha-ceadca5 /usr/share/nginx/config /server/config
COPY --from=registry.central.aiware.com/aiware-spa:latest /usr/share/nginx/config /usr/share/nginx/html/config
COPY --from=registry.central.aiware.com/aiware-spa:latest /usr/share/nginx/config /server/config

ARG K8S_BUILD
# Merge entrypoint.sh, remove unnecessary files, and chmod configs
RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
        mv /etc/nginx/conf.d/nginx.conf.temp /etc/nginx/conf.d/default.conf; \
        rm /entrypoint-k8s.sh; \
        rm /usr/share/nginx/html/aiware-config.json; \
        rm /etc/nginx.conf.template; \
        rm /dynamicConfig-k8s-index-html.sh; \
        chown -R appuser:appgroup /tmp && chmod 700 /tmp && \
        chown appuser:appgroup /getconfig-dynamicConfig.sh && chmod 700 /getconfig-dynamicConfig.sh; \
    else \
        mv /entrypoint-k8s.sh /entrypoint.sh; \
        mv /dynamicConfig-k8s-index-html.sh /dynamicConfig-index-html.sh; \
        chmod +x /usr/share/nginx/html/config && \
        chmod +x /server/config && \
        chmod +x /entrypoint.sh; \
    fi

RUN chown appuser:appgroup /dynamicConfig-index-html.sh && chmod 700 /dynamicConfig-index-html.sh && \
    chown appuser:appgroup /entrypoint.sh && chmod 700 /entrypoint.sh && \
    chown -R appuser:appgroup /usr/share/nginx/html && chmod 700 /usr/share/nginx/html/config && \
    chown -R appuser:appgroup /server && chmod 700 /server/config && \
    chown -R appuser:appgroup /etc/nginx/conf.d

# Create necessary directories and set ownership
RUN mkdir -p /config && chown -R appuser:appgroup /config && \
    mkdir -p /var/cache/nginx && chown -R appuser:appgroup /var/cache/nginx

# Remove setuid and setgid bits from files
RUN find / -perm /6000 -type f -exec chmod a-s {} \; || true    

# Set the user to appuser
USER appuser

HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:${NGINX_PORT}/ || exit 1

ENTRYPOINT ["/entrypoint.sh"]
