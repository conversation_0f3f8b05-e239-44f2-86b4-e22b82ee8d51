import NodeCache from 'node-cache';
import { responses } from '../../graphQL';
import { Variables } from 'graphql-request';
import consoleLogger from '../../../../logger';
import deleteTracklets from '../deleteTracklets';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<
  object,
  (responses.searchFileTracklets | responses.searchMatchTracklets) &
    responses.getFile
>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createdStructuredData: { id: 'id' } })
  ),
}));

jest.mock('../getMatchGroups', () =>
  jest.fn(async (context: Context<object, responses.getMatchGroups>) =>
    Object.assign(context, {
      data: {
        matchGroups: {
          pagination: {
            pageSize: 50,
            currentPage: 1,
            totalPages: 10,
            totalCount: 500,
          },
          sort: {
            field: 'field',
            direction: 'direction',
          },
          eventId: 'eventId',
          searchResults: [
            {
              id: 'id',
              name: 'name',
              eventId: 'eventId',
              searches: [],
              selectedTracklets: [],
              modifiedDateTime: 'modifiedDateTime',
              timelineProject: {
                groups: [],
              },
              generatedTimelines: [],
            },
          ],
        },
      },
    })
  )
);

describe('Delete Tracklets', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        file: {
          id: 'id',
          fileName: 'fileName',
          status: 'processing',
          length: 0,
          createdByName: 'createdBy',
          uploadDate: 'uploadDate',
          location: 'location',
          fileType: 'fileType',
          fileSize: 0,
          eventId: 'eventId',
          eventName: 'eventName',
          thumbnailUrl: 'thumbnailUrl',
          primaryAsset: {
            signedUri: 'signedUri',
          },
          streams: [
            {
              uri: 'uri',
              protocol: 'protocol',
            },
          ],
          frameRate: 24,
        },
        fileSearch: {
          results: [
            {
              orgId: '1',
              trackletId: 'trackletId1',
              fileId: 'fileId1',
              fileName: 'fileName1',
              startTimeMs: 0,
              stopTimeMs: 5000,
              attributes: {},
              thumbnailUrls: { best: 'best' },
              type: 'type',
              confidence: 0.5,
            },
            {
              orgId: '1',
              trackletId: 'trackletId2',
              fileId: 'fileId2',
              fileName: 'fileName2',
              startTimeMs: 0,
              stopTimeMs: 5000,
              attributes: {},
              thumbnailUrls: { best: 'best' },
              type: 'type',
              confidence: 0.5,
            },
          ],
          type: 'type',
          referenceTrackletId: 'referenceTrackletId',
          searchId: 'searchId',
          searchName: 'searchName',
          searchTime: 'searchTime',
          matchGroupId: 'matchGroupId',
          matchGroupName: 'matchGroupName',
          eventId: 'eventId',
          currentPage: 1,
          pageSize: 50,
          totalCount: 500,
          totalPages: 10,
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'schemaId');
  });

  it('Successfully deletes tracklets w/ file', async () => {
    const response = await deleteTracklets(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        data: {
          eventId: 'eventId',
          generatedTimelines: [],
          id: 'id',
          modifiedDateTime: undefined,
          name: 'name',
          searches: [],
          selectedTracklets: [],
          timelineProject: {
            groups: [],
          },
        },
        id: 'id',
        schemaId: 'schemaId',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Successfully deletes tracklets w/ matchGroup', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.file = undefined;
    Object.assign(cxt.data, {
      matchGroup: {
        id: 'id',
        name: 'name',
        eventId: 'eventId',
        searches: [],
        selectedTracklets: [],
        modifiedDateTime: 'modifiedDateTime',
        timelineProject: {
          groups: [],
        },
        generatedTimelines: [],
      },
      selectedTrackletIds: ['trackletId1', 'trackletId2'],
    });

    const response = await deleteTracklets(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        data: {
          eventId: 'eventId',
          generatedTimelines: [],
          id: 'id',
          modifiedDateTime: undefined,
          name: 'name',
          searches: [],
          selectedTracklets: [],
          timelineProject: {
            groups: [],
          },
        },
        id: 'id',
        schemaId: 'schemaId',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws error if fileSearch is undefined', async () => {
    if ('fileSearch' in cxt.data) {
      // @ts-expect-error Does it make sense to test this? Can it be called with
      delete cxt.data.fileSearch;
      await expect(async () => await deleteTracklets(cxt)).rejects.toThrow(
        'No tracklets found'
      );
      expect(callGQL).not.toHaveBeenCalled();
    }
  });

  it('Throws error if schemaId is undefined', async () => {
    cxt.cache.del('matchGroupsSchemaId');
    await expect(async () => await deleteTracklets(cxt)).rejects.toThrow(
      'schemaId not found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
