server {
    listen       9000;
    server_name  localhost;

    # Set the nonce variable using the request ID
    set $cspNonce $request_id;

    # Inject the nonce into the response
    sub_filter_once off;
    sub_filter_types *;
    sub_filter NGINX_CSP_NONCE $cspNonce;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    server_tokens off;
    # can't run this under we work out how to build nginx
    # more_clear_headers Server;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    location /api {     
        proxy_pass http://localhost:3002;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location ~ ^/api-docs$|/api-docs/ {
            proxy_pass http://localhost:3002;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #error_page 404 and 403 to 200 and index.html
    error_page 404 =200 /index.html;
    error_page 403 =200 /index.html;
    location = /index.html {
        root /usr/share/nginx/html;
        internal;

        add_header Content-Security-Policy "font-src 'self' data: fonts.googleapis.com cdn.jsdelivr.net fonts.gstatic.com static.veritone.com; object-src 'none'; script-src 'self' 'unsafe-eval' get.aiware.com veritone.my.site.com cdn.jsdelivr.net cdnjs.cloudflare.com 'nonce-$cspNonce'; style-src 'self' fonts.googleapis.com 'unsafe-inline' cdn.jsdelivr.net static.veritone.com veritone.my.site.com; base-uri 'self'; frame-ancestors 'self' support.veritone.com;";
        add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options nosniff;
        add_header Referrer-Policy "strict-origin";
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
        add_header Cache-Control "must-revalidate";
    }

    location ~* \.(js|css)$ {
        root /usr/share/nginx/html;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
