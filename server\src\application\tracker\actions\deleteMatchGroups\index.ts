import { Context } from '../../../types';
import { queries, responses } from '@tracker/graphQL';
import deleteTemporalData from '../deleteTemporalData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';

const deleteMatchGroups = async <
  ReqPayload,
  Data extends Partial<responses.getMatchGroups> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { cache, data, log, req } = context;
  const headers = { Authorization: req.headers.authorization };

  const { matchGroups } = data;
  if (!matchGroups) {
    throw new ActionError('No matchGroups provided');
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    for (const { id, generatedTimelines } of matchGroups.searchResults) {
      if (generatedTimelines !== undefined) {
        for (const timeline of generatedTimelines) {
          await deleteTemporalData({
            ...context,
            data: { fileId: timeline.id },
          });
        }
      }

      await callGQL<responses.deleteStructuredData, ReqPayload, Data>(
        context,
        headers,
        queries.deleteStructuredData,
        {
          sdoId: id,
          schemaId,
        }
      );
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }

  return context;
};
export default deleteMatchGroups;
