import { SearchFilesResponse } from '@shared-types/responses';
import { configureAppStore } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import axios from 'axios';
import { searchFiles, selectFiles } from './slice';

jest.mock('@utility/getApiAuthToken', () => jest.fn());
jest.mock('axios');
(getApiAuthToken as jest.Mock).mockReturnValue('test-token');

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetFilesResponse: SearchFilesResponse = {
  results: [
    {
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_1.mp4',
      status: 'processed',
      length: 256,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 128,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: 'abcdefg-56a3-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_2.mp4',
      status: 'processing',
      length: 512,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 1024,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: 'abcdefg-1234-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_3.mp4',
      status: 'error',
      length: 128,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 512,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
  ],
  currentPage: 1,
  pageSize: 100,
  totalCount: 3,
  totalPages: 1,
};

const initialStateForMock = {
  upload: {
    filesToUpload: [],
    events: {
      results: [],
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    allEvents: {
      results: [],
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    files: {
      results: [],
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    createEvent: {
      status: 'idle',
      event: undefined,
    },
    selectedEvent: undefined,
  },
};

describe('upload slice', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should get files and convert uploadDate to createdTime', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url }) => {
      if (url?.includes('/files') && url.includes('sortDirection=desc&sortBy=createdTime')) {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(searchFiles({
      currentPage: 1, pageSize: 10000,
      searchTerm: '',
      dir: 'desc',
      sort: 'uploadDate'
    }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse,
      status: 'idle',
    });
  });

  it('should get files and convert fileName to veritone-file.filename', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url }) => {
      if (url?.includes('/files') && url.includes('sortDirection=desc&sortBy=veritone-file.filename')) {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(searchFiles({
      currentPage: 1, pageSize: 10000,
      searchTerm: '',
      dir: 'desc',
      sort: 'fileName'
    }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse,
      status: 'idle',
    });
  });

  it('should get files and default to desc and createdTime', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url }) => {
      if (url?.includes('/files') && url.includes('sortDirection=desc&sortBy=createdTime')) {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(searchFiles({
      currentPage: 1, pageSize: 10000,
      searchTerm: '',
      dir: '',
      sort: ''
    }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse,
      status: 'idle',
    });
  });
});
