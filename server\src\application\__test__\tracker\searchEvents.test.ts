import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
    }
  ),
}));

describe('search events', () => {
  it('search events w/o query', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/events?pageSize=50&currentPage=1')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.searchMedia,
      {
        search: {
          index: ['mine'],
          type: 'schemaId',
          limit: 50,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [],
          },
        },
      }
    );
  });

  it('search events w/ query', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/events?pageSize=50&currentPage=1&event=foo&tag=bar')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.searchMedia,
      {
        search: {
          index: ['mine'],
          type: 'schemaId',
          limit: 50,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'name.fulltext',
                    operator: 'query_string',
                    value: '*foo*',
                  },
                ],
              },
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'tags.fulltext',
                    operator: 'query_string',
                    value: '*bar*',
                  },
                ],
              },
            ],
          },
        },
      }
    );
  });
});
