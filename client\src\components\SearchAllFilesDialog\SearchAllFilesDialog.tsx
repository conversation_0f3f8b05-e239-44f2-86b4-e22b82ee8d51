import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  MenuItem,
  MenuList,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './SearchAllFilesDialog.scss';
import { ChangeEvent, useState, MouseEvent, useEffect } from 'react';
import { AttributeRecord, Attributes, AttributeType } from '@shared-types/tracker';
import { useSelector } from 'react-redux';
import { createMatchGroup, getMatchGroups, selectAttributes, selectMatchGroups, selectNewMatchGroupId, setAttributes, updateMatchGroupAttributeSearch } from '@store/modules/event/slice';
import { setAttributes as setSearchResultsAttributes, selectAttributes as selectSearchResultsAttributes } from '@store/modules/searchResults/slice';
import { NestedAttributes, transformAttributesIntoCategories } from '@utility/transformAttributesIntoCategories';
import { isArray, keys } from 'lodash';
import { ArrowRightIcon } from '@mui/x-date-pickers/icons';
import { I18nTranslate } from '@i18n';
import { useAppDispatch } from '@store/hooks';
import { useParams } from 'react-router-dom';
import { v4 as uuidV4 } from 'uuid';
import { AlertLevel, createSnackNotification } from '@components/common';
import { useIntl } from 'react-intl';
import { getLastSearchName } from '@utility/getLastSearchName';
import cn from 'classnames';
import { getAttributeValue } from '@utility/getAttributeValue';

interface SearchAllFilesDialogProps {
  open: boolean;
  onClose: () => void;
  selectedSearchAttributes?: Attributes;
  updateSearchResults?: (page?: number, limit?: number, newFileIds?: string[]) => void;
}

const allowedAttributeTypes: AttributeType[] = ['person', 'vehicle'];
const isAttributeType = (value: string): value is AttributeType => allowedAttributeTypes.includes(value as AttributeType);
const isChangeEvent = (event: ChangeEvent<HTMLInputElement> | MouseEvent<HTMLLIElement>): event is ChangeEvent<HTMLInputElement> => event && 'target' in event && 'checked' in event.target;
const upperCaseFirstLetter = (value: string) => value.charAt(0).toUpperCase() + value.slice(1);

const SearchAllFilesDialog = ({ open, onClose, selectedSearchAttributes, updateSearchResults }: SearchAllFilesDialogProps) => {
  const intl = useIntl();
  const { eventId, matchGroupId, searchId } = useParams();
  const [attributeType, setAttributeType] = useState<AttributeType>('person');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | null>(null);
  const [checkedItems, setCheckedItems] = useState<{ [key: string]: boolean }>({});
  const [selectedAttributes, setSelectedAttributes] = useState<AttributeRecord[]>([]);

  const [newMatchGroup, setNewMatchGroup] = useState(false);
  const [existingMatch, setExistingMatch] = useState('');
  const [newMatchGroupName, setNewMatchGroupName] = useState('');
  const [disableInput, setDisableInput] = useState(false);
  const searchResultsAttributes = useSelector(selectSearchResultsAttributes);
  const eventAttributes = useSelector(selectAttributes);
  const attributes = searchId ? searchResultsAttributes: eventAttributes;
  const matchGroups = useSelector(selectMatchGroups);
  const createdMatchGroupId = useSelector(selectNewMatchGroupId);

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (selectedSearchAttributes && searchId && open) {
      const selectedAttributeType = keys(selectedSearchAttributes)[0];

      if (isAttributeType(selectedAttributeType)) {
        setAttributeType(selectedAttributeType);

        const savedAttributes = selectedSearchAttributes[selectedAttributeType];
        setSelectedAttributes(savedAttributes);
        const savedCheckedItems = savedAttributes.map((attr) => attr.label).reduce((acc, curr) => ({ ...acc, [curr]: true }), {});
        setCheckedItems(savedCheckedItems);
      }
    }
  }, [selectedSearchAttributes, searchId, open]);

  useEffect(() => {
    if (eventId) {
      dispatch(getMatchGroups({ eventId }));
    }
  }, [eventId, dispatch]);

  useEffect(() => {
    if (keys(attributes.person).length === 0 && keys(attributes.vehicle).length === 0) {
      if (searchId) {
        dispatch(setSearchResultsAttributes({ searchId }));
      } else {
        dispatch(setAttributes());
      }
    }
  }, [dispatch, attributes, searchId]);

  const handleClearAll = () => {
    setCheckedItems({});
    setSelectedAttributes([]);
    setSelectedSubCategory(null);
  };

  const handleAttributeTypeChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (isAttributeType(event.target.value)) {
      setAttributeType(event.target.value);
    } else {
      setAttributeType('person');
    }
    handleClearAll();
  };

  const handleCategoryChange = (event: MouseEvent<HTMLLIElement>, subCategory = false) => {
    const value = event.currentTarget.getAttribute('value');
    if (value) {
      if (subCategory) {
        setSelectedSubCategory(value);
      } else {
        setSelectedCategory(value);
        setSelectedSubCategory(null);
      }
    }
  };

  const handleAttributeChange = (event: ChangeEvent<HTMLInputElement> | MouseEvent<HTMLLIElement>, attribute: AttributeRecord) => {
    const isChecked = isChangeEvent(event) ? event.target.checked : !checkedItems[attribute.label];
    setCheckedItems({
      ...checkedItems,
      [attribute.label]: isChecked,
    });

    if (isChecked) {
      setSelectedAttributes([...selectedAttributes, attribute]);
    } else {
      setSelectedAttributes(selectedAttributes.filter((attr) => attr.value !== attribute.value));
    }
  };

  const handleChangeExistingMatch = (event: SelectChangeEvent<string>) => {
    setExistingMatch(event.target.value);
  };

  const handleNewMatchGroup = () => {
    setDisableInput(false);
    setNewMatchGroupName('');
    setNewMatchGroup(true);
  };

  const handleNewMatchGroupInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewMatchGroupName(event.target.value);
  };

  const handleNewMatchGroupKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === ' ' || event.keyCode === 32 || event.keyCode === 37 || event.keyCode === 39) {
      event.stopPropagation();
    }
  };

  const handleCreateNewMatchGroup = () => {
    if (eventId && newMatchGroupName) {
      dispatch(createMatchGroup({ name: newMatchGroupName, eventId }));
      setDisableInput(true);
      dispatch(getMatchGroups({ eventId }));
    }
  };

  const handleCancelNewMatchGroup = () => {
    setExistingMatch('');
    setNewMatchGroup(false);
    setDisableInput(false);
    if (eventId) {
      dispatch(getMatchGroups({ eventId }));
    }
  };

  const handleConfirm = () => {
    const selectedMatchGroupId = newMatchGroup ? createdMatchGroupId : existingMatch;
    const trackletId = uuidV4();
    const searchName = newMatchGroup
      ? `${intl.formatMessage({ id: 'attributeSearch', defaultMessage: 'Attribute Search' })} 1`
      : `${intl.formatMessage({ id: 'attributeSearch', defaultMessage: 'Attribute Search' })} ${getLastSearchName(matchGroups, selectedMatchGroupId, true)}`;

    if (selectedMatchGroupId && trackletId && attributeType && selectedAttributes.length > 0 && (newMatchGroupName || existingMatch)) {
      dispatch(updateMatchGroupAttributeSearch({ matchGroupId: selectedMatchGroupId, trackletId, attributeType, attributes: selectedAttributes, searchName }));
      handleClose();
    } else if (matchGroupId && trackletId && attributeType && selectedAttributes.length > 0 && selectedSearchAttributes && searchId && updateSearchResults) {
      dispatch(updateMatchGroupAttributeSearch({ matchGroupId, trackletId: searchId, attributeType, attributes: selectedAttributes })).then(() => updateSearchResults());
      handleClose();
    } else {
      if (selectedAttributes.length < 1) {
        createSnackNotification(AlertLevel.Warning, intl.formatMessage({ id: 'snackSelectAttribute', defaultMessage: 'Please select an attribute to search.' }));
      } else {
        createSnackNotification(AlertLevel.Warning, intl.formatMessage({ id: 'snackSelectMatchGroup', defaultMessage: 'Please select a match group or create a new one.' }));
      }
    }
  };

  const handleClose = () => {
    onClose();
    setExistingMatch('');
    setNewMatchGroup(false);
    handleClearAll();
  };

  const transformedAttributes = transformAttributesIntoCategories(attributes[attributeType]);
  return (
    <div>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="search-all-files-dialog__title"
        aria-describedby="search-all-files-dialog__content-text"
        data-testid="search-all-files-dialog"
        classes={{
          paper: 'search-all-files-dialog',
        }}
      >
        <DialogTitle className="search-all-files-dialog__title" data-testid="search-all-files-dialog__title" id="search-all-files-dialog__title">
          {intl.formatMessage({ id: 'searchAllFile', defaultMessage: 'Search All Files in this Event' })}
        </DialogTitle>
        <IconButton aria-label="close" onClick={onClose} className="search-all-files-dialog__close-button">
          <CloseIcon />
        </IconButton>
        <DialogContent className="search-all-files-dialog__content" id="search-all-files-dialog__content">
          <DialogContentText className="search-all-files-dialog__content-text">{intl.formatMessage({ id: 'findAttributeMatch', defaultMessage: 'Find potential matches against attributes you choose.' })}</DialogContentText>

          <RadioGroup
            className="search-all-files-dialog__content-attribute-type"
            name="search-all-files-dialog__content-attribute-type"
            data-testid="search-all-files-dialog__content-attribute-type"
            row
            value={attributeType}
            onChange={handleAttributeTypeChange}
          >
            <FormControlLabel value="person" control={<Radio />} label={upperCaseFirstLetter(intl.formatMessage({ id: 'people', defaultMessage: 'people' }))} className="search-all-files-dialog__content-attribute-type-radio-button" />
            <FormControlLabel value="vehicle" control={<Radio />} label={upperCaseFirstLetter(intl.formatMessage({ id: 'vehicles', defaultMessage: 'Vehicles' }))} className="search-all-files-dialog__content-attribute-type-radio-button" />
          </RadioGroup>

          <div className="search-all-files-dialog__content-attribute-selected">
            <DialogContentText className="search-all-files-dialog__content-attribute-selected-count" data-testid="search-all-files-dialog__content-attribute-selected-count">
              {intl.formatMessage({ id: 'chooseAttributes', defaultMessage: "Choose Attributes: {attributeCount} Selected" }, { attributeCount: selectedAttributes.length })}
            </DialogContentText>

            <Button
              color="primary"
              size="small"
              variant="text"
              disabled={selectedAttributes.length < 1}
              className="search-all-files-dialog__content-attribute-selected-clear-all"
              data-testid="search-all-files-dialog__content-attribute-selected-clear-all"
              onClick={handleClearAll}
            >
              {intl.formatMessage({ id: 'clearFilter', defaultMessage: 'Clear All' })}
            </Button>
          </div>
          <div className={cn('search-all-files-dialog__content-attribute-selection', { hasSelectedSearchAttribute: Boolean(selectedSearchAttributes && searchId) })}>
            <div className="search-all-files-dialog__content-attribute-selection-column" data-testid="search-all-files-dialog__content-attribute-selection-column">
              <MenuList data-testid="search-all-files-dialog__content-attribute-selection-category-menu">
                {keys(transformedAttributes).map((category) => (
                  <MenuItem
                    key={category}
                    onClick={handleCategoryChange}
                    value={category}
                    className="search-all-files-dialog__content-attribute-selection-category"
                    data-testid="search-all-files-dialog__content-attribute-selection-category-menu-item"
                  >
                    {upperCaseFirstLetter(category)}
                    <ArrowRightIcon />
                  </MenuItem>
                ))}
              </MenuList>
            </div>
            <div className="search-all-files-dialog__content-attribute-selection-column" data-testid="search-all-files-dialog__content-attribute-selection-column">
              <MenuList>
                {selectedCategory &&
                  (isArray(transformAttributesIntoCategories(attributes[attributeType])[selectedCategory])
                    ? (transformAttributesIntoCategories(attributes[attributeType])[selectedCategory] as AttributeRecord[]).map((attribute: AttributeRecord) => (
                        <MenuItem
                          key={attribute.label}
                          className="search-all-files-dialog__content-attribute-selection-checkbox"
                          data-testid="search-all-files-dialog__content-attribute-selection-checkbox-menu-item"
                          onClick={(event) => handleAttributeChange(event, attribute)}
                        >
                          <Checkbox size="small" checked={!!checkedItems[attribute.label]} onChange={(event) => handleAttributeChange(event, attribute)} />
                          {upperCaseFirstLetter(getAttributeValue(attribute.value))}
                        </MenuItem>
                      ))
                    : keys(transformAttributesIntoCategories(attributes[attributeType])[selectedCategory]).map((subCategory) => (
                        <MenuItem
                          key={subCategory}
                          onClick={(event) => handleCategoryChange(event, true)}
                          value={subCategory}
                          className="search-all-files-dialog__content-attribute-selection-sub-category"
                          data-testid="search-all-files-dialog__content-attribute-selection-sub-category-menu-item"
                        >
                          {upperCaseFirstLetter(subCategory)}
                          <ArrowRightIcon />
                        </MenuItem>
                      )))}
              </MenuList>
            </div>
            <div className="search-all-files-dialog__content-attribute-selection-column" data-testid="search-all-files-dialog__content-attribute-selection-column">
              <MenuList>
                {selectedSubCategory &&
                  selectedCategory &&
                  isArray((transformedAttributes[selectedCategory] as NestedAttributes)[selectedSubCategory]) &&
                  ((transformedAttributes[selectedCategory] as NestedAttributes)[selectedSubCategory] as AttributeRecord[]).map((attribute) => (
                    <MenuItem
                      key={attribute.label}
                      className="search-all-files-dialog__content-attribute-selection-checkbox"
                      data-testid="search-all-files-dialog__content-attribute-selection-checkbox-menu-item"
                      onClick={(event) => handleAttributeChange(event, attribute)}
                    >
                      <Checkbox size="small" checked={!!checkedItems[attribute.label]} onChange={(event) => handleAttributeChange(event, attribute)} />
                      {upperCaseFirstLetter(getAttributeValue(attribute.value))}
                    </MenuItem>
                  ))}
              </MenuList>
            </div>
          </div>

          {!selectedSearchAttributes && (
            <>
              <DialogContentText className="search-all-files-dialog__content-match-group-text">{intl.formatMessage({ id: 'addResultsToMatchGroup', defaultMessage: 'Add results to a match group.' })}</DialogContentText>

              {!newMatchGroup && (
                <div className="search-all-files-dialog__content-match-group-container">
                  <FormControl size="small" className="search-all-files-dialog__content-match-group-select">
                    {!existingMatch && <InputLabel>{I18nTranslate.TranslateMessage('selectMatchGroup')}</InputLabel>}
                    <Select
                      label={matchGroups.results?.length > 0 ? intl.formatMessage({ id: 'matchGroups', defaultMessage: 'Match Groups' }) : intl.formatMessage({ id: 'noMatchGroupsFound', defaultMessage: 'No Match Groups Found' })}
                      size="small"
                      labelId="search-all-files-dialog__content-match-group-select"
                      id="search-all-files-dialog__content-match-group-select"
                      disabled={matchGroups.results?.length < 1}
                      data-testid="search-all-files-dialog__content-match-group-select"
                      value={existingMatch}
                      onChange={handleChangeExistingMatch}
                    >
                      {matchGroups.results?.map((match, i) => (
                        <MenuItem key={match.id} value={match.id} data-testid={`search-all-files-dialog__content-match-${i}`}>
                          {match.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Button
                    className="search-all-files-dialog__content-match-group-new-match-add"
                    data-testid="search-all-files-dialog__content-match-group-new-match-add"
                    variant="text"
                    size="small"
                    onClick={handleNewMatchGroup}
                  >
                    <div className="material-icons">add</div>
                    <span>{I18nTranslate.TranslateMessage('newMatchGroup')}</span>
                  </Button>
                </div>
              )}
              {newMatchGroup && (
                <div className="search-all-files-dialog__content-match-group-container">
                  <TextField
                    size="small"
                    className="search-all-files-dialog__content-match-group-new-match-input"
                    data-testid="search-all-files-dialog__content-match-group-new-match-input"
                    value={newMatchGroupName}
                    disabled={disableInput}
                    onChange={handleNewMatchGroupInput}
                    onKeyDown={handleNewMatchGroupKeyDown}
                    placeholder="New Match Group Name"
                  />
                  <div className="search-all-files-dialog__content-match-group-new-match-button-container">
                    {!disableInput && (
                      <Button
                        className="search-all-files-dialog__content-match-group-new-match-confirm"
                        data-testid="search-all-files-dialog__content-match-group-new-match-confirm"
                        variant="text"
                        size="small"
                        onClick={handleCreateNewMatchGroup}
                      >
                        <div className="material-icons">add</div>
                        {I18nTranslate.TranslateMessage('create')}
                      </Button>
                    )}
                    {disableInput && (
                      <div
                        className="search-all-files-dialog__content-match-group-new-match-cancel-new-event material-icons"
                        data-testid="search-all-files-dialog__content-match-group-new-match-cancel-icon"
                        onClick={handleCancelNewMatchGroup}
                      >
                        close
                      </div>
                    )}
                    {!disableInput && (
                      <Button
                        className="search-all-files-dialog__content-match-group-new-match-cancel"
                        data-testid="search-all-files-dialog__content-match-group-new-match-cancel"
                        variant="text"
                        size="small"
                        onClick={handleCancelNewMatchGroup}
                      >
                        {I18nTranslate.TranslateMessage('cancel')}
                      </Button>
                    )}
                    {disableInput && (
                      <Button
                        className="search-all-files-dialog__content-match-group-new-match-add"
                        data-testid="search-all-files-dialog__content-match-group-new-match-add"
                        variant="text"
                        size="small"
                        onClick={handleNewMatchGroup}
                      >
                        <div className="material-icons">add</div>
                        <span>{I18nTranslate.TranslateMessage('newMatchGroup')}</span>
                      </Button>
                    )}
                  </div>
                </div>
              )}
              {!newMatchGroup && (
                <DialogContentText className="search-all-files-dialog__content-match-group-existing-group-text" data-testid="search-all-files-dialog__content-match-group-existing-group-text">
                  {intl.formatMessage({ id: 'existingMatchGroup', defaultMessage: 'Existing Match Group' })}
                </DialogContentText>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions className="search-all-files-dialog__actions" id="search-all-files-dialog__actions">
          <Button onClick={handleClose} variant="outlined" color="primary" data-testid="search-all-files-dialog__actions-cancel">
            {intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
          </Button>
          <Button onClick={handleConfirm} variant="contained" color="primary" data-testid="search-all-files-dialog__actions-search">
            {attributeType === 'person' ? intl.formatMessage({ id: 'searchPeople', defaultMessage: "Search People" }) : intl.formatMessage({ id: 'searchVehicles', defaultMessage: "Search Vehicles" })}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default SearchAllFilesDialog;
