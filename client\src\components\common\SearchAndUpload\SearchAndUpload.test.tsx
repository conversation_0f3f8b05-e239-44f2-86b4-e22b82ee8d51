import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import axios from 'axios';
import { Provider } from 'react-redux';
import SearchAndUpload from './SearchAndUpload';
import { configureAppStore } from '@store/store';
import {
  act,
  ReactElement,
  ReactNode,
  ReactPortal
} from 'react';
import { constant } from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import { uploadSlice, UploadSliceState } from '@store/modules/upload/slice';
import { CreateEventResponse, SearchEventsResponse, UploadFileResponse } from '@shared-types/responses';
import { AlertLevel, createSnackNotification } from '@components/common';
import { I18nProvider, LOCALES } from '@i18n';

/* eslint-disable react/display-name */
/* eslint-disable testing-library/no-node-access */

jest.mock('axios');
jest.mock('@mui/material/Popover/Popover', () => (props: { children: string | number | boolean | ReactElement | Iterable<ReactNode> | ReactPortal | null | undefined }) =>
  <div data-testid="popover-mock">
    {props.children}
  </div>
);
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: jest.fn(),
  useLocation: jest.fn(),
}));

jest.mock('@components/common', () => ({
  ...jest.requireActual('@components/common'),
  createSnackNotification: jest.fn(),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetEventsResponse: SearchEventsResponse = {
  results: [
    {
      eventEndDate: "2024-04-10T17:02:10Z",
      createdBy: "c38b16a7-f623-4dd4-9847-0f135bef9dc5",
      createdByName: "Test User",
      name: "New event name",
      description: "New description",
      createdDateTime: "2024-04-11T21:44:35.441Z",
      modifiedDateTime: "2024-04-11T21:44:52.430Z",
      id: "b17597d1-56a3-4c2e-a7c5-6c094e5fa25b",
      eventStartDate: "2024-04-10T17:02:10Z",
      tags: [
        "Tag 1",
        "Tag 2"
      ],
      matchGroupsCount: 10,
      filesCount: 20
    },
    {
      eventEndDate: "2024-04-10T17:02:10Z",
      createdBy: "c38b16a7-f623-4dd4-9847-0f135bef9dc5",
      createdByName: "Test User",
      name: "Event Test 1",
      description: "New description",
      createdDateTime: "2024-04-11T00:10:13.876Z",
      modifiedDateTime: "2024-04-11T00:10:24.681Z",
      id: "947db3be-91ec-4e4b-a00f-6ad2ae06e25d",
      eventStartDate: "2024-04-10T17:02:10Z",
      tags: [
        "Tag 1",
        "Tag 2"
      ],
      matchGroupsCount: 10,
      filesCount: 20
    }
  ],
  currentPage: 1,
  pageSize: 10000,
  totalCount: 2,
  totalPages: 1
};

const mockPostEventResponse: CreateEventResponse = {
  event: {
    id: "3d155106-ac92-4f78-b9d4-dabe42b5fa80",
    name: "test new event",
    tags: [],
    createdBy: "8e71aba4-205c-4029-b662-db37ccd3e08f",
    createdByName: "Test User",
    description: "Event Description",
    eventEndDate: "2024-04-17T13:23:19.512-06:00",
    eventStartDate: "2024-04-17T13:23:19.512-06:00",
    createdDateTime: "2024-04-17T19:23:19.545Z",
    modifiedDateTime: "2024-04-17T19:23:19.545Z",
    matchGroupsCount: 10,
    filesCount: 10
  }
};

const mockGetNoneEventsResponse: SearchEventsResponse = {
  results: [],
  currentPage: 1,
  pageSize: 10000,
  totalCount: 0,
  totalPages: 1
};

const mockPostNoneEventResponse: CreateEventResponse = {};

const mockPutFileResponse: UploadFileResponse = {
  uploadUrl: 'www.uploadUrl.com',
  fileId: 'fileid-test-12345',
  getUrl: 'www.geturl.com'
};

const initialStateForMock = {
  upload: {
    filesToUpload: [],
    events: {
      results: [],
      currentPage: 1,
      pageSize: 10000,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: ''
    },
    allEvents: {
      results: [],
      currentPage: 1,
      pageSize: 10000,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: ''
    },
    files: {
      results: [],
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: ''
    },
    createEvent: {
      status: 'idle',
      event: undefined
    },
    selectedEvent: undefined
  } as UploadSliceState
};

describe('SearchAndUpload component', () => {
  beforeAll(() => {
    (useLocation as jest.Mock).mockReturnValue({ search: "" });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('uploads 2 files from existing event', async () => {
    const mockStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/file') && method === 'post') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
      if (url?.includes(mockPutFileResponse.uploadUrl) && method === 'put') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
    });

    render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    // Click upload button
    await act(async () => {
      screen.getByTestId('search-and-upload-upload-button').click();
    });

    // popover is open
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover')).toBeInTheDocument();
    });

    // Type in event autocomplete
    fireEvent.change(screen.getByTestId('search-and-upload-upload-popover-select-event-input'), {
      target: { value: 'test' }
    });

    // Id is needed to find autocomplete options
    const inputId = screen.getByTestId('search-and-upload-upload-popover-select-event-input').id;

    // Autocomplete open with options
    await waitFor(() => {
      const autocompleteOption = document.getElementById(`${inputId}-option-0`);
      expect(autocompleteOption).toBeInTheDocument();
    });

    // Click second option
    const autocompleteOption = document.getElementById(`${inputId}-option-1`) as Element;
    fireEvent.click(autocompleteOption);

    window.URL.createObjectURL = jest.fn().mockImplementation(constant("url"));
    const inputEl = screen.getByTestId("drop-input");
    const file = new File(["file"], "test-file.mp4", {
      type: "video/mp4",
    });
    const file2 = new File(["file"], "test-file-2.mp4", {
      type: "video/mp4",
    });
    Object.defineProperty(inputEl, "files", {
      value: [file, file2],
    });
    fireEvent.drop(inputEl);

    // UI shows dropped files
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file')[0]).toHaveTextContent("test-file.mp4");
    });
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file')[1]).toHaveTextContent("test-file-2.mp4");
    });

    // Click upload
    await act(async () => {
      screen.getByTestId('search-and-upload-detail-upload-controls-upload').click();
    });
    // Two files are uploaded successfully
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file-complete')).toHaveLength(2);
    });
  });

  it('uploads 2 files to new event', async () => {
    const mockStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'post') {
        return Promise.resolve({ data: mockPostEventResponse });
      }
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/file') && method === 'post') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
      if (url?.includes(mockPutFileResponse.uploadUrl) && method === 'put') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
    });

    render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    // Click upload button
    await act(async () => {
      screen.getByTestId('search-and-upload-upload-button').click();
    });

    // popover is open
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover')).toBeInTheDocument();
    });

    // Click new event button
    await act(async () => {
      screen.getByTestId('search-and-upload-detail-upload-browse-add').click();
    });

    // New event input appears
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-detail-upload-new-event-input')).toBeInTheDocument();
    });

    // Input new event name
    fireEvent.change(screen.getByTestId('search-and-upload-detail-upload-new-event-input'), {
      target: { value: '   test new event  ' }
    });

    // Click confirm new event button
    await act(async () => {
      screen.getByTestId('search-and-upload-detail-upload-browse-confirm').click();
    });

    // New event input appears without leading or trailing white space
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-detail-upload-new-event-entered')).toHaveValue('test new event');
    });

    window.URL.createObjectURL = jest.fn().mockImplementation(constant("url"));
    const inputEl = screen.getByTestId("drop-input");
    const file = new File(["file"], "test-file.mp4", {
      type: "video/mp4",
    });
    const file2 = new File(["file"], "test-file-2.mp4", {
      type: "video/mp4",
    });
    Object.defineProperty(inputEl, "files", {
      value: [file, file2],
    });
    fireEvent.drop(inputEl);

    // UI shows dropped files
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file')[0]).toHaveTextContent("test-file.mp4");
    });
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file')[1]).toHaveTextContent("test-file-2.mp4");
    });

    // Click upload
    await act(async () => {
      screen.getByTestId('search-and-upload-detail-upload-controls-upload').click();
    });

    // Two files are uploaded sucessfully
    await waitFor(() => {
      expect(screen.queryAllByTestId('search-and-upload-files-file-complete')).toHaveLength(2);
    });
  });

  it("update search input and url with search parameter", async () => {
    // Mock useNavigation and store
    const mockNavigate = jest.fn();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    const mockStore = configureAppStore(initialStateForMock);

    const { rerender } = render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    // Search input is present
    await act(async () => {
      expect(
        screen.getByTestId("search-and-upload-search_input")
      ).toBeInTheDocument();
    });

    // Update search input
    fireEvent.change(screen.getByTestId("search-and-upload-search_input"), {
      target: { value: "Event" },
    });

    // Url contains search parameter
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenLastCalledWith("?terms=Event&activeTab=events&page=1&limit=50", { "replace": true });
    });

    // Rerender component with search parameter in url
    (useLocation as jest.Mock).mockReturnValue({
      search: "?terms=Event&activeTab=events&page=1&limit=50",
    });
    rerender(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    // Search input is updated
    await waitFor(() => {
      expect(screen.getByTestId("search-and-upload-search_input")).toHaveValue(
        "Event"
      );
    });
  });

  it('should have disabled autocomplete and selected event on event page', async () => {
    const mockStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'post') {
        return Promise.resolve({ data: mockPostEventResponse });
      }
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/file') && method === 'post') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
      if (url?.includes(mockPutFileResponse.uploadUrl) && method === 'put') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
    });

    const event = mockGetEventsResponse.results[0];

    jest.spyOn(require('react-router-dom'), 'useParams').mockReturnValue({
      eventId: event.id,
    });

    // Set selected event
    mockStore.dispatch(uploadSlice.actions.setSelectedEvent({event}));

    render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    // Click upload button
    await act(async () => {
      screen.getByTestId('search-and-upload-upload-button').click();
    });

    // popover is open
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover')).toBeInTheDocument();
    });

    // Should not be in document
    await act(async () => {
      expect(screen.queryByTestId('search-and-upload-detail-upload-browse-add')).not.toBeInTheDocument();
    });

    // Should be in document
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover-select-event-input')).toBeInTheDocument();
    });

    // Should be disabled
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover-select-event-input')).toBeDisabled();
    });

    // Should have value
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover-select-event-input')).toHaveValue(event.name);
    });
  });

  it('file should not been completed when users do not select event', async () => {
    const mockStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'post') {
        return Promise.resolve({ data: mockPostNoneEventResponse }); // there is no event created
      }
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetNoneEventsResponse }); // there is no event can be gotten
      }
      if (url?.includes('/file') && method === 'post') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
      if (url?.includes(mockPutFileResponse.uploadUrl) && method === 'put') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
    });

    render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(screen.getByRole('button', {
      name: /upload file/i
    }));

    // popover is open
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover')).toBeInTheDocument();
    });

    const eventInput = screen.getByTestId('search-and-upload-upload-popover-select-event-input') as HTMLInputElement;

    // do not select any event
    fireEvent.change(eventInput, {
      target: { value: '' }
    });

    window.URL.createObjectURL = jest.fn().mockImplementation(constant("url"));
    const inputEl = screen.getByTestId("drop-input");
    const file = new File(["file"], "test-file.mp4", {
      type: "video/mp4",
    });
    const file2 = new File(["file"], "test-file-2.mp4", {
      type: "video/mp4",
    });
    Object.defineProperty(inputEl, "files", {
      value: [file, file2],
    });
    fireEvent.drop(inputEl);

    await waitFor(() => {
      expect(screen.getAllByTestId('search-and-upload-files-file')[0]).toHaveTextContent("test-file.mp4");
    });
    await waitFor(() => {
      expect(screen.getAllByTestId('search-and-upload-files-file')[1]).toHaveTextContent("test-file-2.mp4");
    });

    expect(screen.getAllByTestId('search-and-upload-files-file-idle')).toHaveLength(2);

    fireEvent.click(screen.getByRole('button', {
      name: /upload/i
    }));

    // Two files should not be completed
    await waitFor(() => {
      expect(screen.getAllByTestId('search-and-upload-files-file-idle')).toHaveLength(2);
    });
  });

  it('render upload button disabled if disabledButton is true', async () => {
    const mockStore = configureAppStore(initialStateForMock);

    const { rerender } = render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload disableButton={true} />
        </I18nProvider>
      </Provider>
    );

    await act(async () => {
      expect(screen.getByTestId('search-and-upload-upload-button')).toBeInTheDocument();
    });

    await act(async () => {
      expect(screen.getByTestId('search-and-upload-upload-button')).toBeDisabled();
    });

    rerender(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload disableButton={false} />
        </I18nProvider>
      </Provider>
    );

    await act(async () => {
      expect(screen.getByTestId('search-and-upload-upload-button')).toBeInTheDocument();
    });

    await act(async () => {
      expect(screen.getByTestId('search-and-upload-upload-button')).toBeEnabled();
    });
  });

  it("should display 'Ingestion Failed' toast and delete file when ingestion process fails", async () => {
    const mockStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'post') {
        return Promise.resolve({ data: mockPostEventResponse });
      }
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/file') && !url?.includes('/ingest') && method === 'post') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
      // mock ingest failure
      if (url?.includes('/ingest') && method === 'post') {
        return Promise.reject({
          response: {
            status: 400,
            data: { message: 'Bad Request' }
          }
        });
      }
      if (url?.includes(mockPutFileResponse.uploadUrl) && method === 'put') {
        return Promise.resolve({ data: mockPutFileResponse });
      }
    });

    const event = mockGetEventsResponse.results[0];

    jest.spyOn(require('react-router-dom'), 'useParams').mockReturnValue({
      eventId: event.id,
    });

    // Set selected event
    mockStore.dispatch(uploadSlice.actions.setSelectedEvent({event}));

    render(
      <Provider store={mockStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAndUpload />
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(screen.getByRole('button', {
      name: /upload file/i
    }));

    // popover is open
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-upload-popover')).toBeInTheDocument();
    });

    window.URL.createObjectURL = jest.fn().mockImplementation(constant("url"));
    const inputEl = screen.getByTestId("drop-input");
    const file = new File(["file"], "test-file.mp4", {
      type: "video/mp4",
    });

    Object.defineProperty(inputEl, "files", {
      value: [file],
    });
    fireEvent.drop(inputEl);

    await waitFor(() => {
      expect(screen.getAllByTestId('search-and-upload-files-file')[0]).toHaveTextContent(`${file.name}`);
    });

    fireEvent.click(screen.getByRole('button', {
      name: /upload/i
    }));

    // upload file success toast shouldn't appear
    await waitFor(() => {
      expect(createSnackNotification).not.toHaveBeenCalledWith(
        AlertLevel.Success,
        'Upload Complete',
        'File uploaded successfully'
      );
    });

    // expect file that failed to ingest to be deleted
    await waitFor(() => {
      expect(mockedAxios.request).toHaveBeenCalledWith({
        url: `http://localhost/api/v1/file/${mockPutFileResponse.fileId}`,
        headers: {
          Authorization: "Bearer null",
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest"
        },
        maxRedirects: 0,
        withCredentials: false,
        method: 'delete',
        signal: expect.any(AbortSignal)
      });
    });

    // upload file error toast should appear
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Error,
        'Upload Error',
        `Ingestion failed to start for ${file.name}`
      );
    });
  });
});
