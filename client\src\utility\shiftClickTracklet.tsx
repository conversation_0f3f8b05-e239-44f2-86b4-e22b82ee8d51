import { Tracklet } from '@shared-types/tracker';

export const shiftClickTracklet = (
  allTracklets: Tracklet[],
  selectedTracklets: Tracklet[] | undefined,
  tracklet: Tracklet,
  lastSelectedIndex: number | null
): { updatedTracklets: Tracklet[]; lastSelectedIndex: number | null } => {
  if (!allTracklets || allTracklets.length === 0) {
    console.warn('No tracklets available for selection.');
    return { updatedTracklets: selectedTracklets ?? [], lastSelectedIndex: null };
  }
  const currentIndex = allTracklets.findIndex((item) => item.trackletId === tracklet.trackletId);

  if (!selectedTracklets || selectedTracklets.length === 0) {
    return { updatedTracklets: [tracklet], lastSelectedIndex: currentIndex };
  }

  const isAlreadySelected = selectedTracklets.some((selected) => selected.trackletId === tracklet.trackletId);
  if (isAlreadySelected) {
    const updatedSelectedTracklets = selectedTracklets.filter(
      (selected) => selected.trackletId !== tracklet.trackletId
    );
    return { updatedTracklets: updatedSelectedTracklets, lastSelectedIndex: null };
  }
  if (lastSelectedIndex === null) {
    return { updatedTracklets: [...selectedTracklets, tracklet], lastSelectedIndex: currentIndex };
  }

  const [start, end] = lastSelectedIndex < currentIndex
    ? [lastSelectedIndex, currentIndex]
    : [currentIndex, lastSelectedIndex];
  const trackletsToSelect = allTracklets.slice(start, end + 1);
  const newTracklets = trackletsToSelect.filter(
    (item) => !selectedTracklets.some((selected) => selected.trackletId === item.trackletId)
  );

  return { updatedTracklets: [...selectedTracklets, ...newTracklets], lastSelectedIndex: currentIndex };
};
