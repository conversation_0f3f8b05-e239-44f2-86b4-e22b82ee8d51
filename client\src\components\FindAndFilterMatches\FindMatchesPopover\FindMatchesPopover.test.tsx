import { render, fireEvent, screen } from '@testing-library/react';
import Find<PERSON>atchesPopover from './FindMatchesPopover';
import configureMockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { useParams } from 'react-router-dom';
import { I18nProvider, LOCALES } from '@i18n';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

const mockStore = configureMockStore();

const store = mockStore({
  file: {
    selectedTracklet: {
      person: undefined,
      vehicle: undefined,
    },
  },
  searchResults: {
    matchGroup: {
      id: '',
      name: '',
      eventId: '',
      trackletIds: [],
    },
    matchGroups: {
      results: [{
        name: 'mg'
      }],
    },
    createMatchGroup: { 
      id: '',
    },
    event: {
      data: {
        id: '123',
      },
    },
  },
});

store.dispatch = jest.fn();

describe('FindMatchesPopover', () => {
  beforeEach(() => {
    (useParams as jest.Mock).mockReturnValue({ eventId: 'test-event-id' });
  });
  it('renders without crashing', () => {
    const props = {
      open: true,
      onClose: jest.fn(),
      anchorEl: document.createElement('div'),
    };

    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindMatchesPopover {...props} />
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
  });

  it('calls onClose when the popover is closed', () => {
    const onClose = jest.fn();
    const props = {
      open: true,
      onClose,
      anchorEl: document.createElement('div'),
    };

    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindMatchesPopover {...props} />
        </I18nProvider>
      </Provider>
    );
    fireEvent.click(screen.getByTestId('find-matches-popover__close'));
    expect(onClose).toHaveBeenCalled();
  });

  it('renders the popover open and switch to new match group', () => {
    const props = {
      open: true,
      onClose: jest.fn(),
      anchorEl: document.createElement('div'),
    };

    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <FindMatchesPopover {...props} />
        </I18nProvider>
      </Provider>
    );

    fireEvent.click(screen.getByTestId('find-matches-popover__new-match-add'));
    expect(screen.getByTestId('find-matches-popover__new-match-confirm')).toBeInTheDocument();
    expect(screen.getByTestId('find-matches-popover__new-match-cancel')).toBeInTheDocument();

    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'new match' } });
    fireEvent.click(screen.getByTestId('find-matches-popover__new-match-confirm'));
    expect(screen.queryByTestId('find-matches-popover__new-match-confirm')).not.toBeInTheDocument();
    expect(screen.getByTestId('find-matches-popover__new-match-add')).toBeInTheDocument();
    expect(screen.getByTestId('find-matches-popover__new-match-cancel-icon')).toBeInTheDocument();
  });
});
