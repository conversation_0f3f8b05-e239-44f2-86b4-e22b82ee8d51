import { Duration } from 'luxon';

// convert bytes to mb and then round to 0 by default or to the number of decimals provided
export const bytesToMb = (bytes: number, decimals = 1) => {
  // fileSize of unknown or error file is undefined | number
  if (isNaN(bytes) || bytes <= 0) {
    return '0';
  }
  return (bytes / 1024 / 1024).toFixed(decimals);
};

// ms to time format hh:mm:ss or XXms
export const millisToTimeFormatted = (
  millis: number,
  {
    shortenIfPossible = false,
    render0asMMSS = false,
  }: { 
    shortenIfPossible?: boolean; 
    render0asMMSS?: boolean;
  } = {}) => {
  const oneHoursMs = 60 * 60 * 1000;
  const longerThanOneHour = millis >= oneHoursMs;

  if (render0asMMSS && millis === 0) {
    return '00:00';
  }

  if (millis >= 1000) {
    const duration = Duration.fromObject({
      milliseconds: millis,
    });

    const format = 
      shortenIfPossible && !longerThanOneHour ? 'mm:ss' : 'hh:mm:ss';

    return duration.toFormat(format);
  } else {
    return `${Duration.fromObject({ milliseconds: millis }).toFormat('S')}ms`
  }
};

// convert to local time
export const toLocalTime = (dateTime: string) =>
  // DateTime.fromISO(dateTime).toLocal().toLocaleString(DateTime.DATETIME_SHORT);

  // turning off this function for now so it passes directly through to the react-intl date formatting
  dateTime;

export const addSpacesToCamelCase = (str: string): string => str.replace(/([A-Z])/g, ' $1').trim();

export const toCapitalized = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
