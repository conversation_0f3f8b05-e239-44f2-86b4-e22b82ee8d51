.search-all-files-dialog {
  width: 80%;
  height: 85%;
  min-height: 350px;
  max-height: 700px !important;
  max-width: 750px !important;

  .search-all-files-dialog__close-button {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .search-all-files-dialog__title {
    @include size-4-bold;

    margin-bottom: 0;
    padding-bottom: 0;
  }

  .search-all-files-dialog__content {
    margin-top: 10px;
    padding: 0;

    .search-all-files-dialog__content-text {
      padding: 0 24px;

      @include size-2;
    }

    .search-all-files-dialog__content-attribute-type {
      display: flex;
      margin-top: 10px;
      margin-bottom: 10px;
      padding: 0 24px;

      .search-all-files-dialog__content-attribute-type-radio-button .MuiFormControlLabel-label {
        margin-right: 20px;

        @include size-2;
      }
    }

    .search-all-files-dialog__content-attribute-selected {
      display: flex;
      justify-content: space-between;
      padding: 0 24px;

      .search-all-files-dialog__content-attribute-selected-count,
      .search-all-files-dialog__content-attribute-selected-clear-all {
        display: flex;
        align-items: center;
        text-transform: none;
        background-color: transparent;

        @include size-2;
      }
      .search-all-files-dialog__content-attribute-selected-count {
        color: var(--text-primary);
      }
      .search-all-files-dialog__content-attribute-selected-clear-all.Mui-disabled {
        background-color: transparent;
      }
    }

    .search-all-files-dialog__content-attribute-selection {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      height: 60%;

      &.hasSelectedSearchAttribute {
        height: 75%;
      }

      .search-all-files-dialog__content-attribute-selection-column {
        display: flex;
        height: 100%;
        width: 100%;
        background: var(--surface-surface3, #f5f5f5);
        padding: 10px;
        border-top: 1px solid var(--stroke-primary, #e0e0e0);
        border-bottom: 1px solid var(--stroke-primary, #e0e0e0);
        overflow-y: auto;

        > * {
          width: 100%;
        }

        .search-all-files-dialog__content-attribute-selection-category,
        .search-all-files-dialog__content-attribute-selection-sub-category {
          display: flex;
          justify-content: space-between;

          @include size-3;
        }

        .search-all-files-dialog__content-attribute-selection-checkbox {
          > span {
            margin-right: 15px;
            padding: 0;
          }

          @include size-3;
        }
      }
      .search-all-files-dialog__content-attribute-selection-column:nth-child(1),
      .search-all-files-dialog__content-attribute-selection-column:nth-child(2) {
        border-right: 1px solid var(--stroke-primary, #e0e0e0);
      }
    }

    .search-all-files-dialog__content-match-group-text {
      margin-top: 10px;
      padding: 0 24px;

      @include size-2;
    }

    .search-all-files-dialog__content-match-group-existing-group-text {
      padding: 0 24px;

      @include size-2;
    }

    .search-all-files-dialog__content-match-group-container {
      display: flex;
      align-items: center;
    }

    .search-all-files-dialog__content-match-group-select,
    .search-all-files-dialog__content-match-group-new-match-input {
      width: 230px;
      margin: 5px 10px 5px 24px;
    }

    .search-all-files-dialog__content-match-group-select > .MuiOutlinedInput-root fieldset legend,
    .search-all-files-dialog__content-match-group-select > .MuiInputLabel-root.Mui-focused {
      width: 0;
    }

    .search-all-files-dialog__content-match-group-new-match-add,
    .search-all-files-dialog__content-match-group-new-match-button-container {
      width: 160px;
      display: flex;
      height: 40px;
      align-items: center;
      text-transform: none;
      line-height: 30px;
      gap: 5px;
      justify-content: space-between;

      @include size-2-thin;

      .material-icons {
        font-size: 16px;
      }
    }

    .search-all-files-dialog__content-match-group-new-match-button-container {
      width: 190px;
    }

    .search-all-files-dialog__content-match-group-new-match-add,
    .search-all-files-dialog__content-match-group-new-match-confirm,
    .search-all-files-dialog__content-match-group-new-match-cancel {
      justify-content: center;
      color: black;
      font-weight: 500;
    }

    .search-all-files-dialog__content-match-group-new-match-add:hover,
    .search-all-files-dialog__content-match-group-new-match-confirm:hover,
    .search-all-files-dialog__content-match-group-new-match-cancel:hover {
      background-color: var(--surface-surface3, #f5f5f5);
      border-radius: 20px;
    }

    .search-all-files-dialog__content-match-group-new-match-confirm,
    .search-all-files-dialog__content-match-group-new-match-cancel {
      width: 130px;
      margin-right: 5px;
      font-size: small;
      text-transform: none;

      @include size-2-small-bold;

      .material-icons {
        font-size: 14px;
        padding-right: 10px;
      }
    }

    .search-all-files-dialog__content-match-group-new-match-cancel-new-event {
      line-height: 40px;
      color: grey;
      margin-left: -5px;
      cursor: pointer;
      font-size: 24px !important;
    }
  }

  .search-all-files-dialog__actions {
    padding-right: 24px;
    padding-bottom: 16px;

    @include size-2;

    > * {
      text-transform: none;
    }
    .MuiButtonBase-root {
      border-radius: 25px;
    }
  }
}
