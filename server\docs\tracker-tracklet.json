{"type": "object", "title": "tracker-tracklet", "description": "Tracker: <PERSON><PERSON>", "required": ["id", "label", "thumbnail", "attributes", "type"], "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "thumbnail": {"type": "string"}, "attributes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}}, "type": {"type": "string", "enum": ["person", "vehicle"]}}}