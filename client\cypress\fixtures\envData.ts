interface EnvConfig {
  baseUrl: string;
  apiRoot: string;
}

export const envData: { [key: string]: EnvConfig } = {
  local: {
    baseUrl: 'https://local.veritone.com:3004',
    apiRoot: 'https://api.stage.us-1.veritone.com',
  },
  dev: {
    baseUrl: 'https://tracker.dev.us-1.veritone.com',
    apiRoot: 'https://api.dev.us-1.veritone.com',
  },
  stage: {
    baseUrl: 'https://tracker.stage.us-1.veritone.com',
    apiRoot: 'https://api.stage.us-1.veritone.com',
  },
  prod: {
    baseUrl: 'https://tracker.veritone.com',
    apiRoot: 'https://api.veritone.com',
  },
};
