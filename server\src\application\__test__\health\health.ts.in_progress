import supertest from 'supertest';
import expressApp from '../setupExpress';

describe('Returns the health check', () => {
  test('health returns ok', async () => {
    await supertest(expressApp)
      .get('/api/health')
      .set('Accept', 'text/plain')
      .expect(200)
      .then(res => {
        expect(res.headers['content-type']).toContain('text/plain');
        expect(res.text).toBe('OK');
      })
  })
})
