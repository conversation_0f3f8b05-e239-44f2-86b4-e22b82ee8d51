import { BoundingPoly, Event, File, MatchGroup } from "@shared-types/tracker";
import ls from 'localstorage-slim';

export interface UpdatedFileLocalStorage {
  value: {
    fileId: string;
    fileName: string;
  };
  expiry: number;
}

export interface UpdatedEventLocalStorage {
  value: {
    eventId: string;
    eventName: string;
  };
  expiry: number;
}

export interface UpdatedMatchGroupLocalStorage {
  value: {
    matchGroupId: string;
    matchGroupName: string;
  };
  expiry: number;
}

export interface DeletedMatchGroupLocalStorage {
  value: {
    matchGroupId: string;
  };
  expiry: number;
}

export interface BoundingBoxLocalStorage {
  value: {
    fileId: string;
    id: string; // Id of bounding box
    boundingPoly: BoundingPoly;
  };
  expiry: number;
}

export const setLocalStorage = (key: string, value: unknown, hours: number) => {
    const now = new Date();
    const ttl = hours * 60 * 60 * 1000;
    const item = {
        value,
        expiry: now.getTime() + ttl,
      };
    const valueStr = JSON.stringify(item);
    window.localStorage.setItem(key, valueStr);
};

export const getLocalStorage = (key: string) => {
    try {
        const value = window.localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        console.error(error);
    }
};

export const removeLocalStorage = (key: string) => {
    window.localStorage.removeItem(key);
};

const validateUpdatedFiles = (updatedFiles: UpdatedFileLocalStorage[], fileResults: File[]) => {
  const now = new Date().getTime();

  for (const updatedFile of updatedFiles) {
    // Remove from localStorage if the updated file if returned from ElasticSearch
    const fileIndex = fileResults.findIndex((file) => file.id === updatedFile.value.fileId);

    if ((fileIndex > -1 && fileResults[fileIndex].fileName === updatedFile.value.fileName) || updatedFile.expiry <= now) {
      updatedFiles.splice(
        updatedFiles.findIndex((updatedFile) => updatedFile.value.fileId === fileResults[fileIndex]?.id) - 1, 1
      );
    }
  }
  return updatedFiles;
};

export const getUpdatedFilesLocalStorage = (fileResults: File[]) => {
  let validFiles: UpdatedFileLocalStorage[] = [];

  const updatedFileStr = localStorage.getItem('updatedFiles');

  if (updatedFileStr) {
    try {
      const parsedUpdatedFiles: UpdatedFileLocalStorage[] = JSON.parse(updatedFileStr);

      const updatedFiles = Array.isArray(parsedUpdatedFiles)
        ? parsedUpdatedFiles
        : [];

      validFiles = validateUpdatedFiles(updatedFiles, fileResults);
      // Update localStorage
      localStorage.setItem('updatedFiles', JSON.stringify(validFiles));
      return validFiles;
    } catch (e) {
      console.error(e);
    }
  }
  return validFiles;
};

const validateUpdatedEvents = (updatedEvents: UpdatedEventLocalStorage[], eventResults: Event[]) => {
  const now = new Date().getTime();

  for (const updatedEvent of updatedEvents) {
    // Remove from localStorage if the updated file if returned from ElasticSearch
    const eventIndex = eventResults.findIndex((event) => event.id === updatedEvent.value.eventId);

    if ((eventIndex > -1 && eventResults[eventIndex].name === updatedEvent.value.eventName) || updatedEvent.expiry <= now) {
      updatedEvents.splice(
        updatedEvents.findIndex((updatedEvent) => updatedEvent.value.eventId === eventResults[eventIndex].id) - 1, 1
      );
    }
  }
  return updatedEvents;
};

export const getUpdatedEventsLocalStorage = (eventResults: Event[]) => {
  let validEvents: UpdatedEventLocalStorage[] = [];

  const updatedEventStr = localStorage.getItem('updatedEvents');

  if (updatedEventStr) {
    try {
      const parsedUpdatedEvents: UpdatedEventLocalStorage[] = JSON.parse(updatedEventStr);

      const updatedEvents = Array.isArray(parsedUpdatedEvents)
        ? parsedUpdatedEvents
        : [];

      validEvents = validateUpdatedEvents(updatedEvents, eventResults);
      // Update localStorage
      localStorage.setItem('updatedEvents', JSON.stringify(validEvents));
      return validEvents;
    } catch (e) {
      console.error(e);
    }
  }
  return validEvents;
};

export const removeUpdatedMatchGroup = (updatedMatchGroups: UpdatedMatchGroupLocalStorage[], results: MatchGroup[]): boolean => {
  if (updatedMatchGroups.length > 0) {
    const filterUpdatedMatchGroups = updatedMatchGroups.filter((updatedMatchGroup) => {
      const { value: { matchGroupId, matchGroupName }, expiry } = updatedMatchGroup;
      const matchGroup = results.find((result: MatchGroup) => result.id === matchGroupId);
      return !(matchGroup && matchGroup.name === matchGroupName) && expiry < new Date().getTime();
    });

    if (filterUpdatedMatchGroups) {
      ls.set('updatedMatchGroups', filterUpdatedMatchGroups);
      return true;
    }
    return false;
  }
  return true;
};

export const removeDeletedMatchGroup = (results: MatchGroup[]) => {
  const deletedMatchGroups: DeletedMatchGroupLocalStorage[] = ls.get('deletedMatchGroups') || [];
  if (deletedMatchGroups.length > 0) {
    const filterDeletedMatchGroups = deletedMatchGroups.filter((deletedMatchGroup) => {
      const { value: { matchGroupId }, expiry } = deletedMatchGroup;
      return results.some((result) => result.id === matchGroupId) && expiry < new Date().getTime();
    });
    ls.set('deletedMatchGroups', filterDeletedMatchGroups);
  }
};

export const getBoundingBoxLocalStorage = (fileId: string) => {
  const boundingBoxesStr = localStorage.getItem('boundingBoxes');

  if (boundingBoxesStr) {
    try {
      const parsedBoundingBoxes: BoundingBoxLocalStorage[] = JSON.parse(boundingBoxesStr);

      const boundingBoxes = Array.isArray(parsedBoundingBoxes)
        ? parsedBoundingBoxes
        : [];

      return boundingBoxes.filter((boundingBox) => (boundingBox.expiry > new Date().getTime() && boundingBox.value.fileId === fileId));
    } catch (e) {
      console.error(e);
    }
  }
  return [];
};

export const setBoundingBoxLocalStorage = (fileId: string, id: string, boundingPoly: BoundingPoly) => {
  const boundingBoxesStr = localStorage.getItem('boundingBoxes');

  if (boundingBoxesStr) {
    try {
      const parsedBoundingBoxes: BoundingBoxLocalStorage[] = JSON.parse(boundingBoxesStr);

      const boundingBoxes = Array.isArray(parsedBoundingBoxes)
        ? parsedBoundingBoxes
        : [];

      const filteredBoundingBoxes = boundingBoxes.filter((boundingBox) => (boundingBox.expiry > new Date().getTime()));
      const exitingBoundingBox = filteredBoundingBoxes.find((boundingBox) => boundingBox.value.id === id);
      if (exitingBoundingBox) {
        filteredBoundingBoxes.splice(filteredBoundingBoxes.indexOf(exitingBoundingBox), 1);
      }
      filteredBoundingBoxes.push({
        value: {
          fileId,
          id,
          boundingPoly
        },
        expiry: new Date().getTime() + 24 * 60 * 60 * 1000
      });
      localStorage.setItem('boundingBoxes', JSON.stringify(filteredBoundingBoxes));
    } catch (e) {
      console.error(e);
    }
  } else {
    localStorage.setItem('boundingBoxes', JSON.stringify([{
      value: {
        fileId,
        id,
        boundingPoly
      },
      expiry: new Date().getTime() + 24 * 60 * 60 * 1000
    }]));
  }
};

export const deleteBoundingBoxLocalStorage = (id: string) => {
  const boundingBoxesStr = localStorage.getItem('boundingBoxes');
  if (boundingBoxesStr) {
    try {
      const parsedBoundingBoxes: BoundingBoxLocalStorage[] = JSON.parse(boundingBoxesStr);

      const boundingBoxes = Array.isArray(parsedBoundingBoxes)
        ? parsedBoundingBoxes
        : [];

      const filteredBoundingBoxes = boundingBoxes.filter((boundingBox) =>
        (boundingBox.expiry > new Date().getTime())
        && (boundingBox.value.id !== id));

      localStorage.setItem('boundingBoxes', JSON.stringify(filteredBoundingBoxes));
    } catch (e) {
      console.error(e);
    }
  }
  return [];
};


