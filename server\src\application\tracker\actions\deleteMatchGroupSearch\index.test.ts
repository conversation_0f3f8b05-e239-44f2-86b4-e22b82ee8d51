import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import deleteMatchGroupSearch from '.';
import { responses } from '@tracker/graphQL';

let cxt: Context<object, responses.getMatchGroup>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

const mockMatchGroup = {
  id: 'matchGroupId',
  name: 'Match Group Test',
  eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
  searches: [
    {
      id: 'f7471ba6-b1cb-4b93-bfea-0401861d8d40',
      searchName: 'testSearchName',
      referenceTrackletId: '1234',
    },
    {
      id: 'abcd1234-5678-9abc-defg-12345678abcd',
      searchName: 'testSearchName',
      referenceTrackletId: '5678',
    },
  ],
  selectedTracklets: ['1234', '5678'],
  createdDateTime: '2022-01-01T00:00:00Z',
  modifiedDateTime: '2022-01-01T00:00:00Z',
};

describe('Delete MatchGroup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        matchGroup: mockMatchGroup,
        matchGroupId: 'matchGroupId',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {
          matchGroupId: 'matchGroupId',
          searchId: 'f7471ba6-b1cb-4b93-bfea-0401861d8d40',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'matchGroupsSchemaId');
  });

  it('Successfully deletes a content template', async () => {
    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        createStructuredData: mockMatchGroup,
      })
    );

    const response = await deleteMatchGroupSearch(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: {
          ...mockMatchGroup,
          searches: mockMatchGroup.searches.slice(1),
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Does not attempt to query w/o matchGroup defined', async () => {
    // @ts-expect-error Does it make sense to test this?
    cxt.data.matchGroup = undefined;
    await expect(async () => await deleteMatchGroupSearch(cxt)).rejects.toThrow(
      'No match group provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Does not attempt to query w/o searchId defined', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.req.params.searchId = undefined;

    await expect(async () => await deleteMatchGroupSearch(cxt)).rejects.toThrow(
      'No searchId provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Does not attempt to query w/o schemaId defined', async () => {
    cxt.cache.set('matchGroupsSchemaId', undefined);

    await expect(async () => await deleteMatchGroupSearch(cxt)).rejects.toThrow(
      'schemaId not found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Does not attempt call if id doesnt exist in search', async () => {
    cxt.req.params.searchId = 'unknownId';

    await expect(async () => await deleteMatchGroupSearch(cxt)).rejects.toThrow(
      'Search does not exist in match group searches'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
