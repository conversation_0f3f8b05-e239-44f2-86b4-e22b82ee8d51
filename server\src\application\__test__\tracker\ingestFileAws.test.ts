import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@server/src/env', () => ({
  ...jest.requireActual('@server/src/env').default,
  cloud: 'aws',
  gpuClusterId: 'my-gpu-cluster-id',
  cpuClusterId: 'my-cpu-cluster-id',
  nodeEnv: 'test',
}));

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getFileTemporalData)) {
        return Promise.resolve({
          temporalDataObject: {
            id: 'fileId',
            folders: [
              {
                id: 'an-event-id',
              },
            ],
            details: {
              veritoneFile: {
                fileName: 'fileName',
                duration: 100,
                fileType: 'fileType',
                fileSize: 1000,
              },
              createdDateTime: 'createdDateTime',
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'an-event-id',
            contentTemplates: [
              {
                id: 'id',
                sdo: {
                  id: 'sdoId',
                  data: {
                    id: 'eventId',
                    name: 'oldName',
                    tags: ['oldTag'],
                    createdBy: 'oldCreatedBy',
                    createdByName: 'oldCreatedByName',
                    description: 'oldDescription',
                    eventStartDate: 'oldEventStartDate',
                    eventEndDate: 'oldEventEndDate',
                    trackerEngineId: env.trackerEngineId,
                  },
                },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
    }
  ),
}));

describe('ingest file', () => {
  it('ingests file in aws env', async () => {
    env.credentialApi =
      'https://tracker2.stage.us-1.veritone.com/api/v1/credential';
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    await supertest(expressApp)
      .post('/api/v1/file/a-file-id/ingest')
      .set('Authorization', 'Bearer validToken')
      .send({ fileType: 'video/mp4', getUrl: 'http://example.com/video.mp4' })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.createIngestJob(true),
      {
        tdoId: 'a-file-id',
        clusterId: 'my-gpu-cluster-id',
        glcIngestorEngineId: env.glcIngestorEngineId,
        glcIngestorPayload: {
          fileType: 'video/mp4',
          tdoId: 'a-file-id',
          url: 'http://example.com/video.mp4',
          user: 'mock-userEmail',
          chunkDuration: 600,
          chunkOverlap: 0,
        },
        trackerEngineId: env.trackerEngineId,
        trackerEnginePayload: {
          adaptiveBoxPoolingThreshold: 10000,
          storageCredentialAPIUrl:
            'https://tracker2.stage.us-1.veritone.com/api/v1/credential',
        },
        outputWriterEngineId: '8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3',
      }
    );
  });

  it('ingests file in aws env without a cluster defined', async () => {
    env.credentialApi =
      'https://tracker2.stage.us-1.veritone.com/api/v1/credential';
    env.defaultClusterId = undefined;
    env.cpuClusterId = undefined;
    env.gpuClusterId = undefined;

    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    await supertest(expressApp)
      .post('/api/v1/file/a-file-id/ingest')
      .set('Authorization', 'Bearer validToken')
      .send({ fileType: 'video/mp4', getUrl: 'http://example.com/video.mp4' })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.createIngestJob(false),
      {
        tdoId: 'a-file-id',
        glcIngestorEngineId: env.glcIngestorEngineId,
        glcIngestorPayload: {
          fileType: 'video/mp4',
          tdoId: 'a-file-id',
          url: 'http://example.com/video.mp4',
          user: 'mock-userEmail',
          chunkDuration: 600,
          chunkOverlap: 0,
        },
        trackerEngineId: env.trackerEngineId,
        trackerEnginePayload: {
          adaptiveBoxPoolingThreshold: 10000,
          storageCredentialAPIUrl:
            'https://tracker2.stage.us-1.veritone.com/api/v1/credential',
        },
        outputWriterEngineId: '8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3',
      }
    );
  });
});
