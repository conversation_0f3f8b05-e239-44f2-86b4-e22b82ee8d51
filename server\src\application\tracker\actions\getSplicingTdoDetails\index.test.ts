import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { getSplicingTdoDetails } from './';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import { JobStatus } from '../../../../../../types/tracker';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({})
  ),
}));

describe('getSplicingTdoDetails', () => {
  let headers: RequestHeader = {
    authorization: 'Bearer validToken',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers,
        params: {},
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('throws ActionError when no tdoIds are provided', async () => {
    await expect(
      getSplicingTdoDetails({
        tdoIds: [],
        context: cxt,
        headers: headers,
      })
    ).rejects.toThrowError('No tdoIds provided');
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Successfully queries TDO', async () => {
    const mockData = {
      temporalDataObject_123_0: {
        id: '123',
        name: 'test1',
        assets: {
          records: [
            {
              assetSize: 485800,
              details: {
                width: 1440,
                height: 720,
                duration: 22,
                fileType: 'video/mp4',
                videoFrameRate: 24,
                audioSampleRate: 54444,
                audioNumChannels: 3,
              },
              signedUri: 'signedUri-1',
            },
          ],
        },
        jobs: {
          records: [
            {
              status: JobStatus.Complete,
            },
          ],
        },
      },
      temporalDataObject_456_1: {
        id: '456',
        name: 'test2',
        assets: {
          count: 1,
          records: [
            {
              assetSize: 997258,
              details: {
                width: 1920,
                height: 1080,
                duration: 34.12,
                fileType: 'video/mp4',
                videoFrameRate: 30,
                audioSampleRate: 44100,
                audioNumChannels: 2,
              },
              signedUri: 'signedUri-2',
            },
          ],
        },
        jobs: {
          records: [
            {
              status: JobStatus.Complete,
            },
          ],
        },
      },
    };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve(mockData)
    );
    const tdoIds = ['123', '456'];
    const response = await getSplicingTdoDetails({
      tdoIds,
      context: cxt,
      headers: headers,
    });

    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {},
      true
    );
    expect(response).not.toBeNull();
    expect(response).toEqual({
      '123': {
        downloadUrl: 'signedUri-1',
        status: JobStatus.Complete,
        outputFormat: 'video/mp4',
        videoLengthMs: 22000,
        videoSizeBytes: 485800,
        resolution: '1440x720',
      },
      '456': {
        downloadUrl: 'signedUri-2',
        status: JobStatus.Complete,
        outputFormat: 'video/mp4',
        videoLengthMs: 34120,
        videoSizeBytes: 997258,
        resolution: '1920x1080',
      },
    });
  });

  it('throws GraphQLError when there is an error in the GraphQL query', async () => {
    const mockError = new Error('GraphQL query failed');

    (callGQL as jest.Mock).mockImplementationOnce(() => {
      throw mockError;
    });

    const tdoIds = ['123', '456'];
    await expect(
      getSplicingTdoDetails({
        tdoIds,
        context: cxt,
        headers: headers,
      })
    ).rejects.toThrowError('GraphQL query failed');
    expect(callGQL).toHaveBeenCalledTimes(1);
  });
});
