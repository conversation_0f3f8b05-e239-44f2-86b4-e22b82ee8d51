import { FormControl<PERSON>abel, FormGroup, Checkbox, IconButton, Menu, MenuItem } from '@mui/material';
import './TrackletSelectionHeader.scss';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import CheckIcon from '@mui/icons-material/Check';
import { useState } from 'react';
import { Tracklet } from '@shared-types/tracker';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';

interface Props {
  userSelectedTracklets?: Tracklet[];
  tracklets: Tracklet[];
  selectAllTracklets: () => void;
  unselectAllTracklets: () => void;
}

const TrackletSelectionHeader = ({
  userSelectedTracklets,
  tracklets,
  selectAllTracklets,
  unselectAllTracklets,
}: Props) => {
  const intl = useIntl();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSelectAll = () => {
    areAllTrackletsSelected ? unselectAllTracklets() : selectAllTracklets();
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCheckboxOption = (option: 'all' | 'none') => () => {
    handleClose();
    if(option === 'all') {
      selectAllTracklets();
    } else {
      unselectAllTracklets();
    }
  };

  const areAllTrackletsSelected = userSelectedTracklets?.length === tracklets.length;
  const areNoTrackletsSelected = userSelectedTracklets?.length === 0;
  const areSomeTrackletsSelected = !areAllTrackletsSelected && (userSelectedTracklets?.length ?? 0) > 0;

  return (
    <div className="tracklet-selection-header__main-container-labels">
      <FormGroup>
        <FormControlLabel
          control={<Checkbox color="primary" data-testid="tracklet-selection-header-select-all" onClick={handleSelectAll} sx={{ padding: '0', }}
          checked={areAllTrackletsSelected}
          indeterminate={areSomeTrackletsSelected} />}
          label={
            <IconButton sx={{ padding: '0', }} onClick={handleClick}>
              <ArrowDropDownIcon />
            </IconButton>
          }
          sx={{ marginRight: 0 }}
        />
      </FormGroup>
      <Menu
        className="tracklet-selection-header__checkbox-menu"
        id="tracklet-selection-header__checkbox-tracklet-options"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              width: 150,
            },
          }
        }}>
        <MenuItem data-testid="tracklet-selection-header-select-all-option" onClick={handleCheckboxOption('all')}>
          {areAllTrackletsSelected ? (
            <div className='tracklet-selection-header__checkbox-menu-options'>
              <div>{I18nTranslate.TranslateMessage('all')}</div>
              <CheckIcon />
            </div>
          ) : intl.formatMessage({ id: 'all', defaultMessage: 'All' })
          }
        </MenuItem>
        <MenuItem data-testid="tracklet-selection-header-select-none-option" onClick={handleCheckboxOption('none')}>
          {areNoTrackletsSelected ? (
          <div className='tracklet-selection-header__checkbox-menu-options'>
            <div>{I18nTranslate.TranslateMessage('none')}</div>
            <CheckIcon />
          </div>
          ) : intl.formatMessage({ id: 'none', defaultMessage: 'None' })
          }
        </MenuItem>
      </Menu>
      <div className='tracklet-selection-header__label-detection'>
        {I18nTranslate.TranslateMessage('tracklets')}
      </div>
      <div
        className='tracklet-selection-header__label-selected-container'
        data-testid="tracklet-selection-header-selected-tracklets-label"
        style={{ visibility: userSelectedTracklets && userSelectedTracklets.length > 0 ? 'visible' : 'hidden' }}
      >
        {`${(userSelectedTracklets ?? []).length} `}
        <span className='tracklet-selection-header__label-selected'>
          {intl.formatMessage({ id: 'selected', defaultMessage: 'Selected' }).toLowerCase()}
        </span>
      </div>
    </div>
  );
};

export default TrackletSelectionHeader;
