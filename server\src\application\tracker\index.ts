import Redis from 'ioredis';
import NodeCache from 'node-cache';
import createHandlers from './handlers';
import actions from './actions';
import RedisWrapper from 'src/redisWrapper';

const createTrackerApp = ({
  log,
  cache,
  redis,
  redisWrapper,
}: {
  log: Logger;
  cache: NodeCache;
  redis?: Redis;
  redisWrapper?: RedisWrapper;
}) => {
  const handlers = createHandlers({ log, cache, redis, redisWrapper });
  return {
    actions,
    handlers,
  };
};

export default createTrackerApp;
