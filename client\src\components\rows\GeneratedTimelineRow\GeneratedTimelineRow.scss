.GTimeline-row {
  display: block;
  cursor: pointer;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  &:hover {
    background-color: var(--row-active);
    filter: contrast(1.05);
  }

  &.selected {
    background-color: var(--row-active);
  }

  .GTimeline-row__row-details {
    margin-top: -10px;
    margin-bottom: 10px;
    transition: height 300ms;
    overflow: hidden;

    .GTimeline-row__row-details-line {
      opacity: 0.5;
      background-color: var(--text-primary);
      width: 3px;
      height: 131px;
      margin-left: 70px;
      margin-right: 27px;
      transition: height 300ms;
      float: left;
    }
  }

  .GTimeline-row__row {
    position: relative;
    display: flex;
    padding: 11.5px 0 11.5px 18px;

    .GTimeline-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .GTimeline-row__cell-select {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .GTimeline-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 23px 16px 23px 0;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;

        &.pending {
          font-style: italic;
        }
      }

      .GTimeline-row__cell-status {
        .pending {
          color: var(--status-pending);
          border-color: var(--status-pending);
        }
        .complete {
          color: var(--status-complete);
          border-color: var(--status-complete);
        }
        .running {
          color: var(--status-running);
          border-color: var(--status-running);
        }
        .cancelled {
          color: var(--status-cancelled);
          border-color: var(--status-cancelled);
        }
        .queued {
          color: var(--status-queued);
          border-color: var(--status-queued);
        }
        .failed {
          color: var(--status-failed);
          border-color: var(--status-failed);
        }

        .pending-deletion {
          color: var(--disabled);
          border-color: var(--disabled);
        }

        * {
          text-align: center;
          line-height: 24px;
          height: 24px;

          @include size-1-bold;
        }
      }

      .GTimeline-row__cell-match-group {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}