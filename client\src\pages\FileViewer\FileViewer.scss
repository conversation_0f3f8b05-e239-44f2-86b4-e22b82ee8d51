.file-viewer {
  .file-viewer__header {
    display: flex;
    justify-content: space-between;
    background-color: var(--header-background);
  }

  .file-viewer__main-content-tabs {
    display: flex;
    justify-content: center;
    background-color: var(--header-background);
  }

  .file-viewer__main-content {
    display: flex;
    justify-content: left;
    gap: 20px;
    padding: 25px 20px 0;
    height: calc(100vh - 165px);
    background-color: var(--main-content-background);
    border-top: 2px solid var(--main-content-border);

    .file-viewer__video_and_attributes {
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex-grow: 1;
      width: 525px;
      height: 100%;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #fff;
        width: 10px;
        margin-bottom: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #DADFE8;
        border-radius: 30px;
      }

      &:-webkit-scrollbar-thumb:hover {
        background: #C1C9D1;
      }


      .file-viewer__video {
        display: flex;
        flex-direction: column;
        flex: 0 0 auto;
        background-color: #fafafa;
        border: solid 0.5px var(--divider);
        border-radius: 4px;
        padding: 14px;

        .file-viewer__video_filename {
          font-size: 16px;
          font-weight: bold;
          color: var(--text-black);
          margin-bottom: 10px;
          overflow: hidden;
          text-overflow: ellipsis;

          .file-viewer__video_filename-edit {
            display: flex;
            align-items: center;
            gap: 8px;

            .file-icon {
              font-size: 24px;
              margin-top: 2px;
              cursor: pointer;
              fill: var(--icon-selector);

              &:hover {
                fill: var(--text-tertiary);
              }
            }
          }
        }

        .file-viewer__video_player {
          flex: 1;
        }
      }

      .file-viewer__attributes {
        flex: 1 1 auto;
        overflow-y: auto;
      }
    }

    .file-viewer__detail {
      width: 60%;
    }
  }

  .file-viewer__potential-match-search {
    .file-viewer__potential-match-search-label {
      @include size-1-bold;

      color: var(--text-secondary);
      margin-bottom: 10px;
      padding-left: 10px;
    }

    .MuiFormControl-root {
      background-color: var(--select-background);
      width: 100%;
    }
  }

  .file-viewer__tabbed-detections-main-container-flex-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;

    .file-viewer__tabbed-detections-main-container-label {
      @include size-2-bold;

      padding-left: 15px;
      color: var(--text-secondary);
    }

    .file-viewer__show_detections_switch {
      margin-right: 6px;

      .file-viewer__show_detections_label {
        @include size-1-bold;

        padding-left: 15px;
        color: var(--text-secondary);
      }
    }
  }

  .file-viewer__tabbed-detections-main-container {
    border: solid 1px var(--divider);
    border-radius: 7px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--white-background);

    .file-viewer__tabbed-detections-wrapper {
      display: flex;

      .file-viewer__tabbed-detections-title {
        padding: 25px 22px 7px;
        color: var(--text-secondary);
  
        @include size-2-bold;
      }

      .file-viewer__tabbed-detections-clearall-button {
        margin-top: 18px;
        padding-bottom: 2px;
        padding-top: 2px;
        text-transform: none;
        text-wrap: nowrap;
    
        @include size-2;
      }
    }

    .file-viewer__tabbed-detections-tabs-container {
      padding: 0 25px;
    }

    .file-viewer__tabbed-detections-tab-panels {
      overflow-y: auto;
      flex-grow: 1;
      margin-bottom: 16px;
    }

    .file-viewer__tabbed-detections-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 23px;
      height: 50px;

      .file-viewer__tabbed-detections-footer-confidence {
        display: flex;
        justify-content: center;
        align-items: center;

        .material-symbols-outlined {
          margin-right: 10px;
          font-size: 18px;
          color: var(--help-icon);
        }

        .file-viewer__tabbed-detections-footer-confidence-slider {
          margin: 0 20px;
        }

        .file-viewer__tabbed-detections-footer-confidence-text {
          @include size-0;

          color: var(--text-secondary);
          width: 145px;
        }
      }
    }

    .file-viewer__no-tracklets-found {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: var(--disabled);
      margin: 30px auto;

      @include size-3;
    }
  }

  .file-viewer__tracklet-detail {
    .MuiPaper-root {
      margin-bottom: 3px;
      background-color: initial;
    }

    .MuiAccordionSummary-content {
      @include size-1-bold;
    }

    .MuiAccordionSummary-root {
      border-radius: 6px 6px 0 0;
      background-color: var(--accordion-background);

      &[aria-expanded='false'] {
        border-radius: 6px;
      }
    }

    .MuiCollapse-root {
      background-color: var(--accordion-background);
      border-radius: 0 0 6px 6px;
      padding-left: 7.5px;
    }

    .file-viewer__tracklet-detail-loading {
      margin-bottom: 3px;
      border-radius: 6px;
    }

    .file-viewer__tracklet-detail-no-file {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: var(--disabled);
      margin-top: 30px;
    }

    .file-viewer_metadata div {
      font-family: 'Nunito Sans', 'Roboto', sans-serif;
      font-size: 12px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      color: var(--text-secondary);
    }

    .file-viewer__accordion-ai-engines {
      display: none;
    }
  }
}