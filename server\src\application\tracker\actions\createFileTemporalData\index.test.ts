import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { Context } from '../../../types';
import createFileTemporalData from '../createFileTemporalData';
import { createRequest, createResponse } from 'node-mocks-http';
import * as ResTypes from '../../../../../../types/responses';
import env from '../../../../env';
import { responses } from '@tracker/graphQL';

let cxt: Context<object, ResTypes.UploadFilePayloadResponse & responses.getMe>;

global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () =>
      Promise.resolve({
        data: {
          createTDOWithAsset: {},
        },
      }),
  })
) as jest.Mock; // TODO: Is this cast really necessary?

describe('Create File Temporal Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        currentTime: 'currentTime',
        eventId: 'eventId',
        fileName: 'fileName',
        fileType: 'fileType',
        firstName: 'firstName',
        lastName: 'lastName',
        userId: 'userId',
        userEmail: 'userEmail',
        userOrganizationId: 'userOrganizationId',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully create temporal data for a file', async () => {
    const response = await createFileTemporalData(cxt);
    // expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch).toHaveBeenCalledWith(
      `${env.apiRoot}/v3/graphQL`,
      expect.objectContaining({
        headers: {
          Authorization: 'Bearer validToken',
        },
        method: 'post',
        body: expect.any(FormData),
      })
    );
    expect(response).not.toBeNull();
  });

  // it('Throws an error if there is no name', async () => {
  //   cxt.data.currentTime = undefined;

  //   expect(async () => await createFileTemporalData(cxt)).rejects.toThrow(
  //     'Missing required data'
  //   );
  //   expect(callGQL).not.toHaveBeenCalled();
  // });

  // it('Throws an error if there is no description', async () => {
  //   cxt.data.eventId = undefined;

  //   expect(async () => await createFileTemporalData(cxt)).rejects.toThrowError(
  //     'Missing required data'
  //   );
  //   expect(callGQL).not.toHaveBeenCalled();
  // });

  // it('Throws an error if there is no userId', async () => {
  //   cxt.data.fileName = undefined;

  //   expect(async () => await createFileTemporalData(cxt)).rejects.toThrowError(
  //     'Missing required data'
  //   );
  //   expect(callGQL).not.toHaveBeenCalled();
  // });

  // it('Throws an error if there is no userId', async () => {
  //   cxt.data.fileType = undefined;

  //   expect(async () => await createFileTemporalData(cxt)).rejects.toThrowError(
  //     'Missing required data'
  //   );
  //   expect(callGQL).not.toHaveBeenCalled();
  // });
});
