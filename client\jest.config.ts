/* eslint-disable @cspell/spellchecker */

export default {
    preset: 'ts-jest',
    automock: false,
    testEnvironment: 'jest-environment-jsdom',
    transform: {
        '^.+\\.(ts|tsx)?$': [
            'ts-jest',
        ],
        '^.+\\.(js|jsx)?$': 'babel-jest',
        '.+\\.(css|styl|less|sass|scss)$': 'jest-css-modules-transform',
        '.+\\.(png|jpg|ttf|woff|woff2|svg|mp3)$': 'jest-transform-stub',
        '^.+\\.svg$': '<rootDir>/test/jest-svg-transformer.cjs',
    },
    collectCoverage: true,
    // coverageThreshold: {
    //     global: {
    //         branches: 50,
    //         functions: 75,
    //         lines: 80,
    //         statements: 80
    //     }
    // },
    transformIgnorePatterns: ['/node_modules/(?!@veritone/glc-(react|redux)/)'],
    coveragePathIgnorePatterns: [
        "/node_modules/",
        "\\.scss$",
    ],
    moduleNameMapper: {
        '^@common-modules(.*)$': '<rootDir>/src/common-modules$1',
        '^@components(.*)$': '<rootDir>/src/components$1',
        '^@helpers(.*)$': '<rootDir>/src/helpers$1',
        '^@i18n(.*)$': '<rootDir>/src/i18n$1',
        '^@store(.*)$': '<rootDir>/src/store$1',
        '^@shared-types(.*)$': '<rootDir>/../types$1',
        '^@theme(.*)$': '<rootDir>/src/theme$1',
        '^@pages(.*)$': '<rootDir>/src/pages$1',
        '^@utility(.*)$': '<rootDir>/src/utility$1',
        "^.+\\.(css|scss)$": "identity-obj-proxy",
        '^@veritone/glc-react': '@veritone/glc-react/dist/bundle-cjs',
        '^@veritone/glc-redux$': '@veritone/glc-redux/dist/bundle-cjs',
        '\\.svg\\?react$': '<rootDir>/test/mockSvg.js',
        '^@shared-assets(.*)$': '<rootDir>/../assets$1',
    },
    setupFilesAfterEnv: ['./test/jest.setup.ts'],
};
