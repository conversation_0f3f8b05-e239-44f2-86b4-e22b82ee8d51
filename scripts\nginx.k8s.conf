# Remove "index.html" from the URI
map $uri $stripped_uri {
    ~*(.*)index.html$ $1;
    default         "";
}

# Determine the real scheme based on $http_x_forwarded_proto
map $http_x_forwarded_proto $real_scheme {
    ""      $scheme;
    default $http_x_forwarded_proto;
}

# Create the full URL using the real scheme
map "$real_scheme://$http_host$stripped_uri" $full_url {
    ~^(.*)(.*)(.*)$ $1$2$3;
    default          "";
}

server {
    listen       9000;
    server_name  localhost;

    # Set the nonce variable using the request ID
    set $${empty_for_envsubst}cspNonce $${empty_for_envsubst}request_id;

    # Inject the nonce into the response
    sub_filter_once off;
    sub_filter_types *;
    sub_filter NGINX_CSP_NONCE $${empty_for_envsubst}cspNonce;
    sub_filter AIWARE_DOMAIN_TO_REPLACE $api_host_without_protocol;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    server_tokens off;
    # can't run this under we work out how to build nginx
    # more_clear_headers Server;

    location / {
        location = /index.html {
            add_header Cache-Control "must-revalidate";
            internal;  # This prevents direct access from clients
        }

        # zip files to transmit faster
        gzip_static on;
        gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/vnd.ms-fontobject application/x-font-ttf font/opentype image/svg+xml image/x-icon application/javascript;

        # Where nginx should look for files
        root /usr/share/nginx/html;

        # Which files to try serving in order
        index index.html index.htm;

        # Try looking for a file, or directory, or default to index.html
        try_files $uri $uri/ /index.html;

        # Replacing HTML tags with dynamic values
        sub_filter 'aiware.run' '$api_host_without_protocol';
        sub_filter '<base href="/" />' '<base href="$full_url" />';
        sub_filter '<head>' '<head><script>window["aiwareConfig"]=$CONFIG</script>';

        # Making sure sub_filter only runs once
        sub_filter_once off;
    }

    location /api {     
        proxy_pass http://localhost:3002;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location ~ ^/api-docs$|/api-docs/ {
            proxy_pass http://localhost:3002;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #error_page 404 and 403 to 200 and index.html
    error_page 404 =200 /index.html;
    error_page 403 =200 /index.html;
    location = /index.html {
        root /usr/share/nginx/html;
        internal;

        add_header Content-Security-Policy "font-src 'self' data: fonts.googleapis.com cdn.jsdelivr.net fonts.gstatic.com static.veritone.com *.$api_host_without_protocol; object-src 'none'; script-src 'self' 'unsafe-eval' get.aiware.com veritone.my.site.com cdn.jsdelivr.net cdnjs.cloudflare.com 'nonce-$${empty_for_envsubst}cspNonce' *.$api_host_without_protocol; style-src 'self' fonts.googleapis.com 'unsafe-inline' cdn.jsdelivr.net static.veritone.com veritone.my.site.com *.$api_host_without_protocol; frame-src 'self' *.$api_host_without_protocol; base-uri 'self'; frame-ancestors 'self' support.veritone.com *.$api_host_without_protocol; worker-src 'self' blob: *.$api_host_without_protocol;";
        add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options nosniff;
        add_header Referrer-Policy "strict-origin";
        add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
        add_header Cache-Control "must-revalidate";
    }

    location ~* \.(js|css)$ {
        root /usr/share/nginx/html;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
