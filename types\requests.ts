import { MatchGroup } from './tracker';

/* GET */
export interface GetEventPayload {
  eventId: string;
}

export interface GetEventsParams {
  pageSize: string | number;
  currentPage: string | number;
  dir?: string;
  sort?: string;
}

export interface SearchEventsQueryParams {
  pageSize?: string | number;
  currentPage?: string | number;
  sortDirection?: string;
  sortBy?: string;
  event?: string;
  tag?: string;
}

export interface SearchMatchGroupsQueryParams {
  pageSize?: number;
  currentPage?: number;
  eventId?: string;
  thumbnails?: boolean;
}

export interface GetFilePayload {
  fileId: string;
}

export interface GetMatchGroupPayload {
  matchGroupId: string;
}

export interface GetFilesParams {
  pageSize: string | number;
  currentPage: string | number;
  dir?: string;
  sort?: string;
}

export interface SearchFilesQueryParams {
  pageSize?: string | number;
  currentPage?: string | number;
  sortDirection?: string;
  sortBy?: string;
  eventId?: string;
  file?: string;
}

export interface GetMatchGroupsPayload {
  matchGroups: {
    pageSize: string;
    currentPage: string;
    eventId: string;
    sortType: string;
    sortDirection: string;
  };
}

/* PATCH */
export interface UpdateEventPayload {
  name?: string;
  description?: string;
  tags?: string[];
  eventStartDate?: string;
  eventEndDate?: string;
}

export interface UpdateFilePayload {
  name?: string;
}

export type UpdateMatchGroupPayload = Partial<Omit<MatchGroup, "id">>;

/* POST */
export interface CreateEventPayload {
  name: string;
  description: string;
  eventStartDate: string;
  eventEndDate: string;
}

export interface UploadFilePayload {
  eventId: string;
  fileName: string;
  fileType: string;
}

export interface IngestFilePayload {
  fileType: string;
  getUrl: string;
}

export type CreateMatchGroupPayload = MatchGroup;

export type GenerateTimeLine = Pick<MatchGroup, "timelineProject"> & {
  generatedTimelineName?: string;
};

/* DELETE */
export interface DeleteEventPayload {
  eventId: string;
}

export interface DeleteFilePayload {
  fileId: string;
}

export interface DeleteMatchGroupPayload {
  matchGroupId: string;
}

export interface DeleteMatchGroupSearchPayload {
  matchGroupId: string;
  searchId: string;
}

export interface DeleteTrackletsPayload {
  matchGroupId: string;
  trackletIds: string[];
}

export interface DeleteGeneratedTimelinePayload {
  matchGroup: MatchGroup;
  generatedTimelineId: string;
}

export interface GetThumbnailsResponse {
  tracklets: {
    trackletId: string;
    orgId: number;
    fileId: number;
  }[];
}
