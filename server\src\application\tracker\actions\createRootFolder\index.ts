import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';

async function createRootFolder<ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
) {
  const { log, req } = context;
  const headers = { Authorization: req.headers.authorization };

  try {
    const resp = await callGQL<responses.createRootFolder, ReqPayload, Data>(
      context,
      headers,
      queries.createRootFolder,
      {}
    );
    const newNullOwnerIdRootFolder = resp.createRootFolders.find(
      (folder) => folder.ownerId === null
    );

    if (!newNullOwnerIdRootFolder) {
      throw new ActionError('no root folder is created');
    }

    return newNullOwnerIdRootFolder;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
}

export default createRootFolder;
