import { Link, Breadcrumbs as MuiB<PERSON>crumbs, Skeleton } from '@mui/material';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import './Breadcrumbs.scss';
import { Event, File, MatchGroup } from '@shared-types/tracker';
import cn from 'classnames';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';

interface BreadcrumbsProps {
  event?: Event;
  file?: File;
  matchGroup?: MatchGroup;
  searchName?: string;
  loading?: boolean;
}

export const Breadcrumbs = ({ event, file, matchGroup, searchName, loading }: BreadcrumbsProps) => {
  const intl = useIntl();
  loading = loading ?? false;
  const { pathname } = useLocation();
  const { eventId, fileId, matchGroupId, searchId } = useParams();
  const navigate = useNavigate();

  return <div className="breadcrumbs">
    <MuiBreadcrumbs aria-label="breadcrumb" separator="|">
      <Link underline="hover" color="inherit" href="/">
        <div className="material-symbols-outlined">folder</div>
        <div className="breadcrumbs__text">
          {I18nTranslate.TranslateMessage('allEvents')}
        </div>
      </Link>
      {pathname.includes('/search') && (
        <Link
          underline="hover"
          color="inherit"
          onClick={() => navigate('/search')}
        >
          <div className="material-icons">search</div>
          {loading && <Skeleton width={40} />}
          {!loading && (
            <div className="breadcrumbs__text">
              {I18nTranslate.TranslateMessage('search')}
            </div>
          )}
        </Link>
      )}
      {eventId && <Link
        underline="hover"
        color="inherit"
        data-testid="event-breadcrumb"
        className={cn({ disabled: loading })}
        onClick={() => navigate(`/event/${event?.id || ''}`)}
        href={`/event/${event?.id || ''}`}
      >
        <div className="material-symbols-outlined">folder_open</div>
        {loading && <Skeleton width={40} />}
        {!loading && <div className="breadcrumbs__text">{event?.name || 'Event'}</div>}
      </Link>}
      {fileId && <Link
        underline="hover"
        color="inherit"
        data-testid="file-breadcrumb"
        className={cn({ disabled: loading })}
        onClick={_e => navigate(`/event/${event?.id || ''}/file/${file?.id || ''}`)}
        href={`/event/${event?.id || ''}/file/${file?.id || ''}`}
      >
        {loading && <Skeleton width={40} />}
        {!loading && <div className="breadcrumbs__text">{file?.fileName || 'File'}</div>}
      </Link>}
      {matchGroupId && <Link
        underline="hover"
        color="inherit"
        data-testid="match-group-breadcrumb"
        className={cn({ disabled: loading })}
        onClick={() => navigate(`/event/${event?.id || ''}/match-group/${matchGroup?.id || ''}`)}
        href={`/event/${event?.id || ''}/match-group/${matchGroup?.id || ''}`}
      >
        {loading && <Skeleton width={40} />}
        {!loading && <div className="breadcrumbs__text">{matchGroup?.name || 'Match Group'}</div>}
      </Link>}
      {searchId && <Link
        underline="hover"
        color="inherit"
        data-testid="potential-match-search-breadcrumb"
        className={cn({ disabled: loading })}
        onClick={() => navigate(`/event/${event?.id || ''}/match-group/${matchGroup?.id || ''}/potential-match-search/${searchId}`)}
        href={`/event/${event?.id || ''}/match-group/${matchGroup?.id || ''}/potential-match-search/${searchId}`}
      >
        {loading && <Skeleton width={40} />}
        {!loading && <div className={"breadcrumbs__text"}>{searchName || intl.formatMessage({ id: 'potentialMatchSearch', defaultMessage: 'Potential Match Search' })}</div>}
      </Link>}
    </MuiBreadcrumbs>
  </div >;
};

export default Breadcrumbs;
