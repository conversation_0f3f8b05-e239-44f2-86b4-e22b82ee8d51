import en from './en-US';
import fr from './fr-CA';

export type Messages = Record<
  | 'actions'
  | 'addTag'
  | 'aFile'
  | 'all'
  | 'allEvents'
  | 'anEvent'
  | 'attributes'
  | 'browse'
  | 'cancel'
  | 'clearSelections'
  | 'complete'
  | 'confirm'
  | 'confirmMessage'
  | 'confirmDeleteEvent'
  | 'confirmDeleteMatchGroup'
  | 'continue'
  | 'createdBy'
  | 'create'
  | 'createdDate'
  | 'createFirstMatchGroup'
  | 'createFirstTimelineGeneratedVideo'
  | 'date'
  | 'delete'
  | 'deleteEvent'
  | 'deleteFile'
  | 'deleteFileMessage'
  | 'deleteMatchGroupSearchMessage'
  | 'deleteSelectedTrackletsMessage'
  | 'deleteSelections'
  | 'deleteVideo'
  | 'detections'
  | 'downloadVideo'
  | 'dragAndDropFiles'
  | 'enterDescriptionOrNotes'
  | 'error'
  | 'events'
  | 'eventCreator'
  | 'eventName'
  | 'eventTime'
  | 'existingMatchGroup'
  | 'failed'
  | 'fewerMatches'
  | 'file'
  | 'files'
  | 'fileGPSLocation'
  | 'fileMetadata'
  | 'fileName'
  | 'fileSize'
  | 'fileStatus'
  | 'fileType'
  | 'fileUploadedBy'
  | 'filter'
  | 'findMatches'
  | 'items'
  | 'loading'
  | 'matchGroup'
  | 'matchGroups'
  | 'matchGroupName'
  | 'matchGroupSearchResults'
  | 'searchResults'
  | 'markAsVerified'
  | 'Mb'
  | 'moreMatches'
  | 'new'
  | 'newEvent'
  | 'newEventName'
  | 'newMatchGroup'
  | 'noEventFound'
  | 'noEvents'
  | 'noFilesAvailable'
  | 'noFilesFound'
  | 'noMatchGroupsFound'
  | 'noSearches'
  | 'none'
  | 'noTimelineGeneratedVideosFound'
  | 'noTrackletsDetected'
  | 'of'
  | 'pending'
  | 'pendingDeletion'
  | 'people'
  | 'potentialMatchSearch'
  | 'potentialMatchSearches'
  | 'processing'
  | 'selectPotentialMatchSearch'
  | 'selectTrackletToViewDetails'
  | 'processed'
  | 'running'
  | 'resultsPerPage'
  | 'retryUpload'
  | 'search'
  | 'searchEvents'
  | 'searchEventsTagsAndFiles'
  | 'searchAllFile'
  | 'save'
  | 'select'
  | 'selected'
  | 'selectAFile'
  | 'selectATrackletToViewDetails'
  | 'thumbnailScale'
  | 'timelineEditor'
  | 'timelineGeneratedVideos'
  | 'timelineVideoCreatedBy'
  | 'timelineVideoInfoLocation'
  | 'tracklets'
  | 'typeNewTag'
  | 'unavailable'
  | 'unknown'
  | 'upload'
  | 'uploaded'
  | 'uploading'
  | 'uploadDate'
  | 'uploadFile'
  | 'uploadFirstFile'
  | 'uploadToWhichEvent'
  | 'videoLength'
  | 'vehicles'
  | 'vehicleAndPersonDetection'
  | 'verifiedMatches'
  | 'viewEvent'
  | 'viewFile'
  | 'viewMatchGroup'
  | 'viewPotentialMatchSearches'
  | 'viewSearch'
  | 'Backpack'
  | 'Footwear'
  | 'Female'
  | 'Male'
  | 'Lower'
  | 'Upper'
  | 'Body'
  | 'Face'
  | 'FootwearColor'
  | 'FootwearType'
  | 'Gender'
  | 'HairColor'
  | 'HairType'
  | 'LowerColor'
  | 'LowerType'
  | 'UpperColor'
  | 'UpperSleeve'
  | 'UpperType'
  | 'Color'
  | 'Make'
  | 'Model'
  | 'Type'
  | 'Hair'
  | 'Sleeve'
  | 'Accessory'
  | 'showMatchScore'
  | 'clearFilter'
  | 'snackSelectMatchGroup'
  | 'snackSelectAttribute'
  | 'findAttributeMatch'
  | 'addResultsToMatchGroup'
  | 'addFirstTracklet'
  | 'noSearchResultsFound'
  | 'attributeSearch'
  | 'viewModifyAttributeSearch'
  | 'disableConfidence'
  | 'noTrackletsFound'
  | 'selectMatchGroup'
  | 'searchPeople'
  | 'searchVehicles'
  | 'chooseAttributes'
  | 'resetSelection'
  | 'removeBoundingBox'
  | 'filterTrackletsByRegion',
  string
>;

export default {
  ...en,
  ...fr,
};
