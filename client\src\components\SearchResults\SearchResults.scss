.search-results {
    .search-results__header {
        display: flex;
        justify-content: space-between;
        background-color: var(--header-background);
    }

    .search-results__main-content-tabs {
        display: flex;
        justify-content: center;
        background-color: var(--header-background);
    }

    .search-results__main-content {
        display: flex;
        justify-content: left;
        gap: 20px;
        padding: 25px 20px 0;
        height: calc(100vh - 220px);
        background-color: var(--main-content-background);

        .search-results__video_and_attributes {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-grow: 1;
            width: 525px;

            .search-results__video {
                flex: 0 0 auto;
            }

            .search-results__attributes {
                flex: 1 1 auto;
                overflow-y: auto;
            }
        }

        .search-results__detail {
            width: 60%;
            display: block;

            .search-results__header-container {
                display: flex;
                margin-bottom: 6px;
                justify-content: space-between;

                .search-results__header-container-footer-confidence {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .material-symbols-outlined {
                        margin-right: 10px;
                        font-size: 18px;
                        color: var(--help-icon);
                    }

                    .search-results__header-container-footer-confidence-slider {
                        margin: 0 20px;

                        &.Mui-disabled {
                            color: var(--button-disabled);
                        }
                    }

                    .search-results__header-container-footer-confidence-text {
                        @include size-0;

                        color: var(--text-secondary);
                        width: 146px;
                        white-space: nowrap;

                        &.disabled {
                            color: var(--disabled);
                        }
                    }

                    .search-results__header-container-footer-confidence-checkbox {
                        margin-left: 40px;

                        .MuiCheckbox-root.Mui-disabled {
                            color: var(--button-disabled);
                        }

                        .MuiFormControlLabel-label {
                            @include size-0;
                            
                            margin-left: 8px;
                            width: 86px;
                            color: var(--text-secondary);
                            white-space: nowrap;
                        }

                        .MuiFormControlLabel-label.Mui-disabled {
                            color: var(--disabled);
                        }
                    }
                }
            }
        }
    }

    .search-results__potential-match-search {
        .search-results__potential-match-search-label {
            @include size-1-bold;

            color: var(--text-secondary);
            margin-bottom: 10px;
            padding-left: 10px;
        }

        .MuiFormControl-root {
            background-color: var(--select-background);
            width: 100%;
        }
    }
    .search-results__view-modify-attribute-button-container {
        .search-results__view-modify-attribute-button {
            color: #1565C0;
            display: flex;
            align-items: center;
            text-transform: none;
            background-color: transparent;
            padding: 0;
            margin-left: 10px;

            @include size-1;
        }
    }

    .search-results__person-matched-detection-tab-tracklets {
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: left;
        align-items: center;

        .search-results__person-matched-detection-tab-tracklet {
            height: 155px;
            width: 80px;
            object-fit: cover;
            border: 4px solid transparent;
            border-radius: 10px;
            cursor: pointer;
        }

        .search-results__person-matched-detection-tab-tracklet-loading {
            height: 155px;
            width: 80px;
        }

        .search-results__person-matched-detection-tab-tracklet.selected {
            border: 4px solid var(--selected-outline);
        }
    }

    .search-results__vehicle-matched-detection-tab-tracklets {
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: left;
        align-items: center;

        .search-results__vehicle-matched-detection-tab-tracklet {
            height: 155px;
            width: 80px;
            object-fit: cover;
            border: 4px solid transparent;
            border-radius: 10px;
            cursor: pointer;
        }

        .search-results__vehicle-matched-detection-tab-tracklet.selected {
            border: 4px solid var(--selected-outline);
        }
    }

    .search-results__tabbed-detections-main-container-label {
        @include size-1-bold;

        padding-left: 15px;
        margin-bottom: 10px;
        color: var(--text-secondary);
    }

    .search-results__tabbed-detections-main-container {
        border: solid 1px var(--divider);
        border-radius: 7px;
        height: calc(100% - 24px);
        width: 100%;
        display: flex;
        flex-direction: column;
        background-color: var(--white-background);

        .search-results__tabbed-detections-tabs-container {
            margin-top: 25px;
            padding: 0 25px;
        }

        .search-results__tabbed-detections-tab-panels {
            overflow-y: auto;
            flex-grow: 1;
            margin-bottom: 16px;
        }

        .search-results__tabbed-detections-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 23px;
            height: 50px;
        }
    }

    .search-results__tracklet-detail {
        .MuiPaper-root {
            margin-bottom: 3px;
            background-color: initial;
        }

        .MuiAccordionSummary-content {
            @include size-1-bold;
        }

        .MuiAccordionSummary-root {
            border-radius: 6px 6px 0 0;
            background-color: var(--accordion-background);

            &[aria-expanded="false"] {
                border-radius: 6px;
            }
        }

        .MuiCollapse-root {
            background-color: var(--accordion-background);
            border-radius: 0 0 6px 6px;
            padding-left: 7.5px;
        }

        .search-results__tracklet-detail-loading {
            margin-bottom: 3px;
            border-radius: 6px;
        }

        .search-results__tracklet-detail-no-file {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: var(--text-tertiary);
            margin-top: 30px;
        }

        .search-results__accordion-ai-engines {
            display: none;
        }
    }
    
    .search-results__no-search-found,
    .search-results__no-search-results-found {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: var(--disabled);
        margin: 30px auto;

        @include size-3;
    }

    .search-results__media-container {
        background-color: var(--accordion-background);
    }

    .search-results__media-container-showing {
        background-color: var(--main-content-background);
    }
}
