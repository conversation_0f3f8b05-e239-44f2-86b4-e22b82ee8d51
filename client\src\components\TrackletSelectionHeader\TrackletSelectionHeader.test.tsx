import { render, fireEvent, screen, within } from '@testing-library/react';
import TrackletSelectionHeader from './TrackletSelectionHeader';
import { TrackletAttributes } from '../../../test/testConstants';
import { I18nProvider, LOCALES } from '@i18n';

describe('TrackletSelectionHeader', () => {
  const mockSelectAllTracklets = jest.fn();
  const mockUnselectAllTracklets = jest.fn();

  const defaultProps = {
    tracklets: [
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'fileName 1',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
      {
        orgId: '1',
        trackletId: '22',
        fileId: '2',
        fileName: 'fileName 2',
        startTimeMs: 10000,
        stopTimeMs: 13000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.8,
      },
      {
        orgId: '1',
        trackletId: '33',
        fileId: '3',
        fileName: 'fileName 3',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.6,
      },
    ],
    selectAllTracklets: mockSelectAllTracklets,
    unselectAllTracklets: mockUnselectAllTracklets,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call selectAllTracklets when no tracklets are selected', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TrackletSelectionHeader {...defaultProps} />
      </I18nProvider>
    );
    fireEvent.click(
      within(
        screen.getByTestId('tracklet-selection-header-select-all')
      ).getByRole('checkbox')
    );
    expect(mockSelectAllTracklets).toHaveBeenCalled();
  });

  it('should call unselectAllTracklets when all tracklets are selected', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TrackletSelectionHeader
          {...defaultProps}
          userSelectedTracklets={defaultProps.tracklets}
        />
      </I18nProvider>
    );
    fireEvent.click(
      within(
        screen.getByTestId('tracklet-selection-header-select-all')
      ).getByRole('checkbox')
    );
    expect(mockUnselectAllTracklets).toHaveBeenCalled();
  });

  it('should call selectAllTracklets when some tracklets are selected', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TrackletSelectionHeader
          {...defaultProps}
          userSelectedTracklets={[defaultProps.tracklets[0]]}
        />
      </I18nProvider>
    );
    fireEvent.click(
      within(
        screen.getByTestId('tracklet-selection-header-select-all')
      ).getByRole('checkbox')
    );
    expect(mockSelectAllTracklets).toHaveBeenCalled();
  });

  it('should open the menu and select the "all" option', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TrackletSelectionHeader {...defaultProps} />
      </I18nProvider>
    );

    fireEvent.click(
      screen.getByTestId('tracklet-selection-header-select-all-option')
    );
    expect(mockSelectAllTracklets).toHaveBeenCalled();
  });

  it('should open the menu and select the "none" option', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TrackletSelectionHeader {...defaultProps} />
      </I18nProvider>
    );

    fireEvent.click(
      screen.getByTestId('tracklet-selection-header-select-none-option')
    );
    expect(mockUnselectAllTracklets).toHaveBeenCalled();
  });
});
