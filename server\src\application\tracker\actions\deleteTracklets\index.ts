import { Context } from '../../../types';
import { responses } from '@tracker/graphQL';
import { ActionError, ActionValidationError } from '@common/errors';
import getMatchGroups from '../getMatchGroups';
import { MatchGroup } from '../../../../../../types/tracker';
import updateMatchGroupStructuredData from '../updateMatchGroupStructuredData';

const deleteTracklets = async <
  ReqPayload,
  Data extends
    | ((responses.searchFileTracklets | responses.searchMatchTracklets) &
        responses.getFile)
    | (responses.getMatchGroup & { selectedTrackletIds: string[] }),
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { cache, data } = context;

  if ('matchGroup' in data) {
    const { matchGroup, selectedTrackletIds } = data;
    await removeTracklets(context, {
      matchGroup,
      trackletIds: selectedTrackletIds,
    });
  } else if ('file' in data) {
    const schemaId = cache.get('matchGroupsSchemaId');
    if (!schemaId) {
      throw new ActionValidationError('schemaId not found');
    }

    if (!('fileSearch' in data) || data.fileSearch === undefined) {
      throw new ActionError('No tracklets found');
    }
    const { file, fileSearch } = data;
    const trackletIds = fileSearch.results.map(({ trackletId }) => trackletId);

    /* Delete tracklets from match groups */
    await getMatchGroups({ ...context, data: { eventId: file.eventId } }).then(
      async (cxt) => {
        const { matchGroups } = cxt.data;
        for (const matchGroup of matchGroups.searchResults) {
          await removeTracklets(cxt, { matchGroup, trackletIds });
        }
      }
    );
  }

  return context;
};

const removeTracklets = async <ReqPayload, Data = object>(
  cxt: Context<ReqPayload, Data>,
  {
    matchGroup,
    trackletIds,
  }: {
    matchGroup: MatchGroup;
    trackletIds: string[];
  }
) => {
  const { selectedTracklets: selectedTracklets_old, timelineProject } =
    matchGroup;

  const selectedTracklets =
    selectedTracklets_old?.filter((id) => !trackletIds.includes(id)) ?? [];

  if (timelineProject !== undefined && timelineProject.groups) {
    for (const group of timelineProject.groups) {
      group.tracklets = group.tracklets.filter(
        ({ trackletId }) => !trackletIds.includes(trackletId)
      );
    }
  }

  await updateMatchGroupStructuredData({
    ...cxt,
    data: { matchGroup, selectedTracklets, timelineProject },
  });
};

export default deleteTracklets;
