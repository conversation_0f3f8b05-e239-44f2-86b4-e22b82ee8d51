import { render } from '@testing-library/react';
import AppContainer from '@components/common/AppContainer';

describe('AppContainer tests', () => {
  it('renders correctly AppContainer with sideBarOffset false', () => {
    const { asFragment } = render(<AppContainer sideBarOffset={false} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly AppContainer with sideBarOffset true', () => {
    const { asFragment } = render(<AppContainer sideBarOffset />);
    expect(asFragment()).toMatchSnapshot();
  });
});
