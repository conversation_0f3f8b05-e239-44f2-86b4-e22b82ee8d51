import React from 'react';
import { render, waitFor, fireEvent, screen } from '@testing-library/react';
import Snackbar, { createSnackNotification, AlertLevel } from './Snackbar';

describe('Snackbar common component', () => {
  it('renders ', () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');
    expect(screen.getByTestId('snackbar')).toBeInTheDocument();
  });

  it('renders a success snack', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');

    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-success')).toBeInTheDocument();
    });
  });

  it('renders a error snack', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Error, 'title', 'body');

    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-error')).toBeInTheDocument();
    });
  });

  it('renders a info snack', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Info, 'title', 'body');

    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-info')).toBeInTheDocument();
    });
  });

  it('renders a warning snack', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Warning, 'title', 'body');

    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-warning')).toBeInTheDocument();
    });
  });

  it('closes the snack on x button click ', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Warning, 'title', 'body');

    // The snack is rendered in Alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Click the x
    fireEvent.click(screen.getByTestId('snackbar-box-close'));

    // The snack is rendered in dead state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });
  });

  it('closes the snack on x button enter key up', async () => {
    render(<Snackbar className='' timeout={200}/>);

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Warning, 'title', 'body');

    // The snack is rendered in Alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Click the x
    fireEvent.keyUp(screen.getByTestId('snackbar-box-close'), { keyCode: 13 });

    // The snack is rendered in dead state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });
  });

  it('adds a snack and the snack goes through the lifecycle', async () => {
    render(<Snackbar timeout={200} tickTime={25} fadeTime={200} className=''/>);

    expect(screen.getByTestId('snackbar')).toBeInTheDocument();

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');

    // The snack is rendered in Init state (0)
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-0-0')).toBeInTheDocument();
    });

    // The snack is rendered in Alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // The snack is rendered in Fadeout state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-2')).toBeInTheDocument();
    });

    // The snack is rendered in Dead state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });
  });

  it('adds two snacks and the snacks goes through the lifecycle', async () => {
    render(<Snackbar timeout={200} tickTime={25} fadeTime={200} className=''/>);

    expect(screen.getByTestId('snackbar')).toBeInTheDocument();

    // Add a snacks to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');

    // The first snack is rendered in Init state (0) in position 0
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-0-0')).toBeInTheDocument();
    });

    // The first snack is rendered in Alive state (1) in position 1
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    createSnackNotification(AlertLevel.Success, 'title2', 'body2');

    // The second snack goes into the init position while the first is still in the first position
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-0-0')).toBeInTheDocument();
    });

    // The second snack goes into the alive state and the snacks stack
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-2-1')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Eventually the first goes into fadeout state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-2-2')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Eventually the first snack dies and the second snack goes into fadeout state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-2')).toBeInTheDocument();
    });

    // Eventually all snacks are dead
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });
  });

  it('resets the timer when hovering and goes into resurrected state', async () => {
    render(<Snackbar timeout={200} tickTime={25} fadeTime={200} className=''/>);

    expect(screen.getByTestId('snackbar')).toBeInTheDocument();

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');

    // The snack is rendered in Init state (0)
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-0-0')).toBeInTheDocument();
    });

    // The snack is rendered in Alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Hover over snack
    fireEvent.mouseEnter(screen.getByTestId('snackbar-box-1-1'));

    // The snack is rendered in resurrected state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-3')).toBeInTheDocument();
    });

    // Leave snack
    fireEvent.mouseLeave(screen.getByTestId('snackbar-box-1-3'));

    // The snack is rendered in alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // The snack is rendered in fadeout state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-2')).toBeInTheDocument();
    });

    // The snack is rendered in dead state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });
  });

  it('resets the timer when focused and goes into resurrected state', async () => {
    render(<Snackbar timeout={200} tickTime={25} fadeTime={200} className=''/>);

    expect(screen.getByTestId('snackbar')).toBeInTheDocument();

    // Add a snack to the snackbar
    createSnackNotification(AlertLevel.Success, 'title', 'body');

    // The snack is rendered in Init state (0)
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-0-0')).toBeInTheDocument();
    });

    // The snack is rendered in Alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // Focus on snack
    fireEvent.focus(screen.getByTestId('snackbar-box-1-1'));

    // The snack is rendered in resurrected state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-3')).toBeInTheDocument();
    });

    // Wait a tick
    await new Promise((resolve) => setTimeout(resolve, 25));

    // Leave snack
    fireEvent.blur(screen.getByTestId('snackbar-box-1-3'));

    // The snack is rendered in alive state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-1')).toBeInTheDocument();
    });

    // The snack is rendered in fadeout state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-2')).toBeInTheDocument();
    });

    // The snack is rendered in dead state
    await waitFor(() => {
      expect(screen.getByTestId('snackbar-box-1-4')).toBeInTheDocument();
    });

    // Wait a tick
    await new Promise((resolve) => setTimeout(resolve, 25));

    // Wait a tick
    await new Promise((resolve) => setTimeout(resolve, 25));

    // Dead snack gone
    await waitFor(() => {
      expect(screen.queryByTestId('snackbar-box-1-4')).not.toBeInTheDocument();
    });
  });
});
