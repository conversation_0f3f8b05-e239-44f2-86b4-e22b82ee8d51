import cn from 'classnames';
import { Tracklet as TrackletType } from '@shared-types/tracker';
import { Checkbox, FormGroup, Tooltip } from '@mui/material';
import { Check } from '@mui/icons-material';
import { millisToTimeFormatted } from '@utility/convert';

interface CheckboxInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  'data-testid'?: string;
}

interface Props {
  selected: boolean;
  playing?: boolean;
  checked?: boolean;
  isReference?: boolean;
  isInMatchGroup?: boolean;
  showConfidenceScore?: boolean;
  thumbnailScale: number;
  showCheck?: boolean;
  confidenceScore?: number;
  index: number;
  tracklet: TrackletType;
  thumbnailUrl: string;
  handleTrackletClick?: (t: TrackletType) => void;
  handleTrackletCheck?: (t: TrackletType, event?:React.MouseEvent) => void;
  onTrackletStartTimeClick?: (n: number) => void;
  onTrackletStopTimeClick?: (n: number) => void;
  onDrag?: (e: React.DragEvent<HTMLDivElement>) => void;
}

const Tracklet = (props: Props) => {
  const { selected, checked, showCheck, isReference, isInMatchGroup, showConfidenceScore, playing, confidenceScore, thumbnailScale, index, tracklet, thumbnailUrl, onDrag, onTrackletStartTimeClick, onTrackletStopTimeClick, handleTrackletClick, handleTrackletCheck } = props;

  const scalePercent = thumbnailScale / 100;
  const checkboxScaledMargin = 7 * scalePercent * scalePercent;

  return <div className={cn("main__tracklet_thumbnails-tracklet-border", { selected, playing, checked })}>
    <div
      data-testid={`Tracklet-${index}`}
      key={`Tracklet-${index}`}
      className="main__tracklet_thumbnails-tracklet-container"
      onClick={() => handleTrackletClick?.(tracklet)}
      style={{
        width: `${113 * scalePercent}px`,
        height: `${113 * scalePercent}px`,
        fontSize: `${12 * scalePercent}px`
      }}
      onDrag={onDrag}
    >
      <div className="main__tracklet_thumbnails-tracklet-container-border">
        <img
          data-testid={`Tracklet-${index}-img`}
          data-tracklet-id={tracklet.trackletId}
          src={thumbnailUrl}
          data-file-id={tracklet.fileId}
          data-index={index}
          className="main__tracklet_thumbnails-tracklet"
        />
        {showCheck && <FormGroup>
          <Checkbox
            style={{
              left: `${checkboxScaledMargin}px`,
              top: `${checkboxScaledMargin}px`,
              transform: `scale(${scalePercent})`
            }}
            className="main__tracklet_thumbnails-tracklet-checkbox"
            color="primary"
            checked={checked}
            onClick={(event) => {
              event.stopPropagation();
              handleTrackletCheck?.(tracklet, event);
            }}
            icon={<div className="main__tracklet_thumbnails-tracklet-checkbox-icon" />}
            checkedIcon={<Check />}
            inputProps={{
              'data-testid': `Tracklet-${index}-checkbox`
            } as CheckboxInputProps}
          />
        </FormGroup>}
      </div>
      {selected &&
        <svg
          style={{
            transform: `scale(${.7 * scalePercent})`,
            left: `${5 * scalePercent}px`,
            bottom: `${7 * scalePercent}px`,
          }}
          className="main__tracklet_playing" xmlns="http://www.w3.org/2000/svg" width="30" height="24" viewBox="0 0 24 24">
          <rect style={{ animationPlayState: playing ? 'running' : 'paused' }} className="eq-bar eq-bar--1" x="3" y="4" width="3" height="8" />
          <rect style={{ animationPlayState: playing ? 'running' : 'paused' }} className="eq-bar eq-bar--2" x="8" y="4" width="3" height="16" />
          <rect style={{ animationPlayState: playing ? 'running' : 'paused' }} className="eq-bar eq-bar--3" x="13" y="4" width="3" height="11" />
          <rect style={{ animationPlayState: playing ? 'running' : 'paused' }} className="eq-bar eq-bar--4" x="18" y="4" width="3" height="8" />
        </svg>}
    </div>
    <div
      className="main__tracklet_thumbnails-tracklet-timestamp"
      style={{
        fontSize: `${12 * scalePercent}px`,
        lineHeight: `${12 * scalePercent}px`,
      }}
    >
      <a onClick={() => onTrackletStartTimeClick?.(tracklet.startTimeMs)}>
        {millisToTimeFormatted(tracklet.startTimeMs, { shortenIfPossible: true, render0asMMSS: tracklet.stopTimeMs >= 1000 })}
      </a>
      {` `}
      <span>-</span>
      {` `}
      <a onClick={() => onTrackletStopTimeClick?.(tracklet.stopTimeMs)}>
        {millisToTimeFormatted(tracklet.stopTimeMs, { shortenIfPossible: true })}
      </a>
    </div>
    <div className="main__tracklet_thumbnails-tracklet-details"
      style={{
        marginTop: `${5 * scalePercent}px`,
        gap: `${3 * scalePercent * scalePercent}px`
      }}
    >
      {isReference && <div className="main__tracklet_thumbnails-tracklet-reference" style={{
        height: `${18 * scalePercent}px`,
        width: `${18 * scalePercent}px`
      }}>
        <Tooltip title="Search Reference" arrow>
          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 18 18" fill="none">
            <path d="M7.47126 12.5625H10.3126V11.4375H7.47126V12.5625ZM7.47126 9.5625H12.5626V8.4375H7.47126V9.5625ZM5.43763 6.5625H12.5626V5.4375H5.43763V6.5625ZM5.19238 16.125C4.25788 16.125 3.46176 15.7974 2.80401 15.1421C2.14626 14.4868 1.81738 13.6918 1.81738 12.7571C1.81738 12.0591 2.00538 11.4361 2.38138 10.8879C2.75738 10.3399 3.24538 9.93656 3.84538 9.67781H1.87513V8.553H5.82707V12.5048H4.70207V10.5563C4.19145 10.6659 3.77051 10.925 3.43926 11.3336C3.10801 11.7423 2.94238 12.2144 2.94238 12.75C2.94238 13.3779 3.16238 13.9099 3.60238 14.346C4.04226 14.782 4.57226 15 5.19238 15V16.125ZM7.47126 15.375V14.25H14.0193C14.0771 14.25 14.1299 14.2259 14.1779 14.1778C14.2261 14.1298 14.2501 14.0769 14.2501 14.0192V3.98081C14.2501 3.92306 14.2261 3.87019 14.1779 3.82219C14.1299 3.77406 14.0771 3.75 14.0193 3.75H3.98095C3.9232 3.75 3.87032 3.77406 3.82232 3.82219C3.7742 3.87019 3.75013 3.92306 3.75013 3.98081V6.90863H2.62513V3.98081C2.62513 3.60194 2.75638 3.28125 3.01888 3.01875C3.28138 2.75625 3.60207 2.625 3.98095 2.625H14.0193C14.3982 2.625 14.7189 2.75625 14.9814 3.01875C15.2439 3.28125 15.3751 3.60194 15.3751 3.98081V14.0192C15.3751 14.3981 15.2439 14.7188 14.9814 14.9813C14.7189 15.2438 14.3982 15.375 14.0193 15.375H7.47126Z" fill="#212121" />
          </svg>
        </Tooltip>
      </div>}
      {isInMatchGroup && <div className="main__tracklet_thumbnails-tracklet-in-match-group" style={{
        height: `${18 * scalePercent}px`,
        width: `${18 * scalePercent}px`
      }}>
        <Tooltip title="Verified" arrow>
          <svg width="100%" height="100%" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="Verified">
              <path id="Vector" d="M8.2125 11.4027L12.1903 7.42493L11.3884 6.623L8.2125 9.79906L6.62306 8.20962L5.82112 9.01156L8.2125 11.4027ZM9 16.1105C7.37787 15.6681 6.03487 14.7133 4.971 13.2461C3.907 11.7788 3.375 10.1384 3.375 8.32493V4.00962L9 1.90381L14.625 4.00962V8.32493C14.625 10.1384 14.093 11.7788 13.029 13.2461C11.9651 14.7133 10.6221 15.6681 9 16.1105ZM9 14.9249C10.3 14.5124 11.375 13.6874 12.225 12.4499C13.075 11.2124 13.5 9.83743 13.5 8.32493V4.78118L9 3.10081L4.5 4.78118V8.32493C4.5 9.83743 4.925 11.2124 5.775 12.4499C6.625 13.6874 7.7 14.5124 9 14.9249Z" fill="#212121" />
            </g>
          </svg>
        </Tooltip>
      </div>}
      {showConfidenceScore && confidenceScore !== undefined &&
        <Tooltip title="Score" arrow>
          < div
            data-testid={`Tracklet-${index}-confidence`}
            className={cn(
              'main__tracklet_thumbnails-tracklet-confidence',
              {
                green: confidenceScore >= 80,
                yellow: confidenceScore >= 60 && confidenceScore < 80,
                red: confidenceScore < 60
              }
            )}
            style={{
              fontSize: `${12 * scalePercent}px`,
              lineHeight: `${12 * scalePercent}px`
            }}
          >
            {confidenceScore}
          </div>
        </Tooltip>
      }
    </div>
  </div >;
};

export default Tracklet;
