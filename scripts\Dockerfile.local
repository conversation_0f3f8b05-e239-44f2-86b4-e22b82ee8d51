FROM node:22 AS frontend

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app
ADD . /app
WORKDIR /app/client

RUN cp /app/scripts/index.local.html /app/client/index.html

RUN ls -a && \
    chmod +x /app/scripts/*.sh && \
    yarn && \
    yarn build

RUN echo '### /app/scripts/buildinfo.sh...' && /app/scripts/buildinfo.sh

FROM node:22 AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

RUN mkdir -p /app/server
WORKDIR /app/server

COPY types/ ../types
COPY assets/ ../assets

RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .

RUN yarn
RUN yarn build

FROM nginx:stable-alpine

RUN apk update
RUN apk add jq curl bash nodejs npm
RUN apk add --upgrade pcre libjpeg-turbo libxml2 ncurses curl

ENV NGINX_PORT=443

EXPOSE ${NGINX_PORT}/tcp

COPY --from=frontend /app/scripts/getconfig-dynamicConfig.sh /getconfig-dynamicConfig.sh
COPY --from=frontend /app/scripts/dynamicConfig-index-html.sh /dynamicConfig-index-html.sh
COPY --from=frontend /app/scripts/entrypoint.sh /entrypoint.sh
COPY --from=frontend /app/scripts/nginx.local.conf /etc/nginx/conf.d/default.conf
COPY --from=frontend /app/client/configWhitelist.json /configWhitelist.json
COPY --from=frontend /app/client/dist /usr/share/nginx/html

COPY --from=frontend /app/client/build-manifest.yml /build-manifest.yml
COPY --from=frontend /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

COPY --from=backend /app/server/apiConfigWhitelist.json /apiConfigWhitelist.json
COPY --from=backend /app/server/ /server/

COPY --from=backend /app/server/local.veritone.com-key.pem /etc/nginx/
COPY --from=backend /app/server/local.veritone.com.pem /etc/nginx/

RUN ls -la /usr/share/nginx/html
RUN ls -la /usr/share/nginx/html
RUN ls -la /usr/share/nginx/html


ENTRYPOINT '/entrypoint.sh'