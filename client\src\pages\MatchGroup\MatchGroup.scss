.match-group {
  .match-group__header {
    display: flex;
    justify-content: space-between;
    background-color: var(--header-background);

    .match-group__header-actions {
      margin: 16px 30px 0 0;
      display: flex;
      gap: 10px;
      align-items: center;
      background-color: var(--header-background);

      .match-group-export-button {
        padding: 6px 16px;
        text-transform: none;
        border-radius: 30px;
        border: 1.5px solid var(--primary);
        margin-left: 10px;

        @include size-1-bold;
      }

      .match-group__save-button {
        padding: 6px 16px;
        text-transform: none;
        border-radius: 30px;
        margin-left: 10px;

        @include size-1-bold;
      }

      .match-group__save-generate-button {
        padding: 6px 16px;
        text-transform: none;
        border-radius: 30px;
        margin-left: 10px;

        @include size-1-bold;
      }

      .match-group__file-filter {
        .MuiFormLabel-root {
          background-color: var(--select-label-background);
          padding-right: 3px;
        }
      }

      .match-group__clear_file-filter {
        text-transform: none;
    
        @include size-2;
      }

      .match-group-export-button-container {
        display: none;
      }
    }
  }

  .match-group__main-content-tabs {
    display: flex;
    justify-content: center;
    background-color: var(--header-background);
  }

  .match-group__main-content {
    display: flex;
    gap: 20px;
    padding: 25px 20px 0;
    height: calc(100vh - 190px);
    background-color: var(--main-content-background);
    border-top: 2px solid var(--main-content-border);

    .match-group__search {
      width: 100%
    }

    .match-group__video_and_attributes {
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex-grow: 1;
      width: 525px;

      .match-group__view-potential-match-searches {
        min-width: 200px;
        max-width: 250px;
        padding: 6px 10px;
        text-align: left;
        justify-content: left;
        text-transform: capitalize;

        @include size-1;
      }

      .match-group__video {
        flex: 0 0 auto;
      }

      .match-group__attributes {
        flex: 1 1 auto;
        overflow-y: auto;
      }
    }

    .match-group__detail {
      width: 60%;

      .match-group__detections-footer {
        display: flex;
        justify-content: right;
        align-items: center;
        padding: 20px 30px;
        border-top: solid 1px var(--divider);
      }
    }
  }

  .match-group__potential-match-search {
    .match-group__potential-match-search-label {
      @include size-1-bold;

      color: var(--text-secondary);
      margin-bottom: 10px;
      padding-left: 10px;
    }

    .MuiFormControl-root {
      background-color: var(--select-background);
      width: 100%;
    }
  }

  .match-group__person-matched-detection-tab-tracklets {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: left;
    align-items: center;

    .match-group__person-matched-detection-tab-tracklet {
      height: 155px;
      width: 80px;
      object-fit: cover;
      border: 4px solid transparent;
      border-radius: 10px;
      cursor: pointer;
    }

    .match-group__person-matched-detection-tab-tracklet-loading {
      height: 155px;
      width: 80px;
    }

    .match-group__person-matched-detection-tab-tracklet.selected {
      border: 4px solid var(--selected-outline);
    }
  }

  .match-group__vehicle-matched-detection-tab-tracklets {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: left;
    align-items: center;

    .match-group__vehicle-matched-detection-tab-tracklet {
      height: 155px;
      width: 80px;
      object-fit: cover;
      border: 4px solid transparent;
      border-radius: 10px;
      cursor: pointer;
    }

    .match-group__vehicle-matched-detection-tab-tracklet.selected {
      border: 4px solid var(--selected-outline);
    }
  }

  .match-group__tabbed-detections-main-container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;

    .match-group__tabbed-detections-main-container-actions {
      display: flex;
      gap: 12px;
      padding-right: 15px;

      .match-group__tabbed-detections-main-container-action {
        display: flex;
        justify-content: space-between;
        gap: 6px;
        align-items: center;
        font-size: 11px;
        text-transform: capitalize;
        padding: 2px 8px;

        @include size-1;

        .material-symbols-outlined {
          font-size: 20px;
        }
      }
    }
  }

  .match-group__tabbed-detections-main-container {
    border: solid 1px var(--divider);
    border-radius: 7px;
    height: calc(100% - 24px);
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--white-background);

    .match-group__tabbed-detections-tabs-container {
      margin-top: 25px;
      padding: 0 25px;
    }

    .match-group__tabbed-detections-tab-panels {
      overflow-y: auto;
      flex-grow: 1;
      margin-bottom: 16px;

      .match-group__no-tracklets-found {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: var(--disabled);
        margin: 30px auto;

        @include size-3;
      }
    }

    .match-group__tabbed-detections-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 30px;

      .match-group__tabbed-detections-footer-confidence {
        display: flex;
        justify-content: center;
        align-items: center;

        .material-symbols-outlined {
          margin-right: 10px;
          font-size: 18px;
          color: var(--help-icon);
        }

        .match-group__tabbed-detections-footer-confidence-slider {
          margin: 0 20px;
        }

        .match-group__tabbed-detections-footer-confidence-text {
          @include size-0;

          color: var(--text-secondary);
          width: 145px;
        }
      }
    }
  }

  .match-group__tracklet-detail {
    .MuiPaper-root {
      margin-bottom: 3px;
      background-color: initial;
    }

    .MuiAccordionSummary-content {
      @include size-1-bold;
    }

    .MuiAccordionSummary-root {
      border-radius: 6px 6px 0 0;
      background-color: var(--accordion-background);

      &[aria-expanded='false'] {
        border-radius: 6px;
      }
    }

    .MuiCollapse-root {
      background-color: var(--accordion-background);
      border-radius: 0 0 6px 6px;
      padding-left: 7.5px;
    }

    .match-group__tracklet-detail-loading {
      margin-bottom: 3px;
      border-radius: 6px;
    }

    .match-group__tracklet-detail-no-file {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: var(--text-tertiary);
      margin-top: 30px;
    }

    .match-group__accordion-ai-engines {
      display: none;
    }
  }
}