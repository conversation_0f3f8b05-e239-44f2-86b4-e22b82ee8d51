import getEvent from '../getEvent';
import NodeCache from 'node-cache';
import { Variables } from 'graphql-request';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { createRequest, createResponse } from 'node-mocks-http';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { responses } from '@tracker/graphQL';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

let cxt: Context<object, responses.getEvent>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({ folder: { contentTemplates: [{ sdo: { data: {} } }] } })
  ),
}));

describe('Get Event', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    cxt = {
      data: {
        event: {
          eventEndDate: '2024-04-10T17:02:10Z',
          createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
          createdByName: 'Test User',
          name: 'Event Test 1',
          description: 'New description',
          createdDateTime: '2024-04-11T00:10:13.876Z',
          modifiedDateTime: '2024-04-11T00:10:24.681Z',
          id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
          eventStartDate: '2024-04-10T17:02:10Z',
          tags: ['Tag 1', 'Tag 2'],
          matchGroupsCount: 14,
          filesCount: 13,
        },
        folder: {
          id: '',
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          contentTemplates: [],
          treeObjectId: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Gets event from valid folder', async () => {
    cxt.req.params.eventId = 'eventId';

    await getEvent(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(cxt.data.event).not.toBeNull();
    expect(cxt.data.event).not.toBe({});
  });

  it('Throws an error if invalid folder', async () => {
    expect(async () => await getEvent(cxt)).rejects.toThrowError();
    expect(callGQL).not.toHaveBeenCalled();
  });
});
