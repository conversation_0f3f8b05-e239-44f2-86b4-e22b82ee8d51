{"apiRoot": "https://api.stage.us-1.veritone.com", "credentialApi": "https://tracker2.stage.us-1.veritone.com/api/v1/credential", "veritoneAppId": "0021f79f-f29d-45b9-b94d-7b98c933df4b", "graphQLEndpoint": "v3/graphQL", "nodeEnv": "production", "port": 3002, "serviceName": "tracker-server", "startApi": "true", "useRedis": false, "cloud": "aws", "blob": {"endpointSuffix": "core.usgovcloudapi.net", "account": "vtstorcoredev", "key": "ENV_AZURE_BLOB_ACCOUNT_KEY", "container": "tracker2", "expireSecs": 3600}, "s3": {"bucket": "tracker2-dev", "accessKey": "replace with accessKey for local dev, no need for ec2", "secretKey": "replace with secretKey for local dev, no need for ec2", "roleArn": "arn:aws:iam::************:role/VeritoneGLCAssumeRole", "region": "us-east-1", "expireSecs": 3600}, "glcIngestorEngineId": "da093aca-2a6b-4577-8bfe-2b19a2f2faea", "defaultClusterId": "rt-9d7a5d1b-ffe0-4d71-a982-190522cdf272", "trackerEngineId": "d77d6133-a801-472c-bc7e-48ddafec8590", "outputWriterEngineId": "8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3", "registryIds": {"eventsRegistryId": "ab2a98f0-dc3d-486d-8d9a-c2147aeb43bc", "matchGroupsRegistryId": "793f0f3a-ed2e-45b4-a8c8-c455e6dc18a9"}, "redis": {"host": "localhost", "port": 6379}}