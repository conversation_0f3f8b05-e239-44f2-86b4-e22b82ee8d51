import { createAppSlice } from '../../createAppSlice';
import { Attributes, Tracklet } from '@shared-types/tracker';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  BoundingBoxesState,
  CreateMatchGroup,
  EventState,
  FileFilterState,
  FileState,
  MatchGroupState,
  MatchGroupsState,
  SearchResultsState,
  UpdateMatchGroup,
} from './types';
import { RootState } from '@store/store';
import HttpClient from '@store/dependencies/httpClient';
import getApiAuthToken from '@utility/getApiAuthToken';
import {
  AlertLevel,
  createSnackNotification,
} from '@components/common/Snackbar/Snackbar';
import {
  CreateMatchGroupResponse,
  GetEventResponse,
  GetMatchGroupResponse,
  GetSearchResultsResponse,
  GetMatchGroupsResponse,
  GetFileResponse,
  GetBoundingBoxesResponse,
  GetThumbnailsResponse,
} from '@shared-types/responses';
import qs from 'qs';
import { GetMatchGroupsParams } from '../event/types';
import { SearchMatchGroupsQueryParams } from '@shared-types/requests';
import { map, maxBy, merge, uniq, get } from 'lodash';
import ls from 'localstorage-slim';
import { getMatchGroupSelectedTracklets } from '../matchGroup/slice';
import { updateMatchGroupAttributeSearch } from '../event/slice';
import attributesJson from '@shared-assets/attributes.json';
import { convertJsonAttributes } from '@utility/convertJsonAttributes';
import { getAttributesJson } from '@utility/getAttributesJson';
import { shiftClickTracklet } from '@utility/shiftClickTracklet';

export interface SearchResultsSliceState {
  fileFilter: FileFilterState;
  matchGroup: MatchGroupState;
  matchGroups: MatchGroupsState;
  createMatchGroup: CreateMatchGroup;
  updateMatchGroup: UpdateMatchGroup;
  event: EventState;
  searchResults: SearchResultsState;
  selectedTracklet?: Tracklet[];
  selectedTrackletFile: FileState;
  confidenceThreshold: number;
  boundingBoxes: BoundingBoxesState;
  thumbnailUrls: Record<
    string,
    {
      thumbnailUrls: {
        best: string;
      };
      expiresDateTime: string;
    }
  >;
  attributes: {
    person: Attributes;
    vehicle: Attributes;
  };
  lastSelectedIndex: number | null;
}

const initialState: SearchResultsSliceState = {
  searchResults: {
    results: [],
    referenceTrackletId: '',
    searchId: '',
    searchName: '',
    matchGroupId: '',
    matchGroupName: '',
    eventId: '',
    type: 'person',
    currentPage: 1,
    pageSize: 100,
    totalCount: 0,
    totalPages: 0,
    apiStatus: 'idle',
    error: '',
    fileNames: [],
    allFiles: [],
  },
  matchGroup: {
    data: undefined,
    apiStatus: 'idle',
    error: '',
  },
  matchGroups: {
    eventId: '',
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
    sortType: '',
    sortDirection: '',
  },
  createMatchGroup: {
    status: 'idle',
    error: '',
    id: '',
  },
  updateMatchGroup: {
    status: 'idle',
    error: '',
  },
  event: {
    data: undefined,
    apiStatus: 'idle',
    error: '',
  },
  fileFilter: {
    fileNames: [],
    selectedFileNames: [],
    displayString: '',
    fileIds: [],
    apiStatus: 'idle',
  },
  selectedTracklet: undefined,
  selectedTrackletFile: {
    file: undefined,
    apiStatus: 'idle',
    error: '',
  },
  confidenceThreshold: 0.8,
  boundingBoxes: {
    data: [],
    apiStatus: 'idle',
    error: '',
  },
  thumbnailUrls: ls.get('thumbnailUrls') || {},
  attributes: {
    person: {},
    vehicle: {},
  },
  lastSelectedIndex: null,
};

export const searchResultsSlice = createAppSlice({
  name: 'searchResults',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>(); // TODO: Fix getState type
    return {
      addFileNameFilter: create.reducer(
        (
          state,
          action: PayloadAction<{
            selectedFileNames: string[];
            displayString: string;
            selectedFileIds: string[];
          }>
        ) => {
          const { selectedFileNames, displayString, selectedFileIds } =
            action.payload;
          state.fileFilter.selectedFileNames = selectedFileNames;
          state.fileFilter.displayString = displayString;
          state.fileFilter.fileIds = selectedFileIds;
          state.selectedTracklet = undefined;
          state.selectedTrackletFile = {
            file: undefined,
            apiStatus: 'idle',
            error: '',
          };
        }
      ),
      getBoundingBoxes: createHttpThunk(
        async (
          {
            fileId,
            trackletId,
            startTimeMs,
            stopTimeMs,
            type,
          }: {
            fileId?: string;
            trackletId?: string;
            startTimeMs?: number;
            stopTimeMs?: number;
            type?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetBoundingBoxesResponse>(signal)(
            `bounding-boxes${qs.stringify(
              { fileId, trackletId, startTimeMs, stopTimeMs, type },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.boundingBoxes.apiStatus = 'loading';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = '';
          },
          fulfilled: (state, action) => {
            state.boundingBoxes.apiStatus = 'idle';
            state.boundingBoxes.data = action.payload.results;
            state.boundingBoxes.error = '';
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Bounding Boxes failed',
              action.error.message
            );
            state.boundingBoxes.apiStatus = 'failure';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = action.error.message;
          },
        }
      ),
      setConfidenceThreshold: create.reducer(
        (state, action: PayloadAction<number>) => {
          state.confidenceThreshold = action.payload;
        }
      ),
      setSearchResultsPagination: create.reducer(
        (
          state,
          action: PayloadAction<{ currentPage: number; pageSize: number }>
        ) => {
          state.searchResults.currentPage = action.payload.currentPage;
          state.searchResults.pageSize = action.payload.pageSize;
        }
      ),
      setSelectedTracklet: create.reducer(
        (state, action: PayloadAction<{ tracklet: Tracklet; shiftKey?: boolean }>) => {
          const { tracklet, shiftKey } = action.payload;    
          const allTracklets = state.searchResults.results ?? [];
          const currentIndex = allTracklets.findIndex((item) => item.trackletId === tracklet.trackletId);
      
          if (!shiftKey) {
            const found = state.selectedTracklet?.find(
              (item) => item.trackletId === tracklet?.trackletId
            );
            if (found) {
              state.selectedTracklet = state.selectedTracklet?.filter(
                (item) => item.trackletId !== tracklet?.trackletId
              );
              state.lastSelectedIndex = null;
            } else {
              state.selectedTracklet = (state.selectedTracklet ?? []).concat(tracklet);
              state.lastSelectedIndex = currentIndex;
            }
            return;
          }
          const { updatedTracklets, lastSelectedIndex } = shiftClickTracklet(
            allTracklets,
            state.selectedTracklet,
            tracklet,
            state.lastSelectedIndex
          );
          state.selectedTracklet = updatedTracklets;
          state.lastSelectedIndex = lastSelectedIndex;
        }
      ),
      selectAllTracklets: create.reducer((state, action: PayloadAction<{ tracklets: Tracklet[] }>) => {
        state.selectedTracklet = action.payload.tracklets;
      }),
      unselectAllTracklets: create.reducer((state) => {
        state.selectedTracklet = [];
        state.lastSelectedIndex = null;
      }),
      getEvent: createHttpThunk(
        async (
          {
            eventId,
          }: {
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetEventResponse>(signal)(
            `/event/${eventId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.event.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.event = { data: action.payload.event, apiStatus: 'idle' };
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Get event failed',
                action.error.message
              );
              state.event.apiStatus = 'failure';
            } else {
              state.event.apiStatus = 'idle';
            }
          },
        }
      ),
      getSearchResultsById: createHttpThunk(
        async (
          {
            matchGroupId,
            searchId,
            fileIds,
            page: currentPage,
            limit: pageSize,
          }: {
            matchGroupId?: string;
            searchId?: string;
            trackletType?: string;
            fileIds?: string[];
            page?: number;
            limit?: number;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const threshold = (getState() as RootState).searchResults
            .confidenceThreshold;
          const response = await http.get<GetSearchResultsResponse>(signal)(
            `match-groups/${matchGroupId}/search/${searchId}/${qs.stringify(
              { pageSize, currentPage, fileIds, times: true, threshold },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.searchResults.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.searchResults = {
              ...action.payload,
              apiStatus: 'idle',
              allFiles:
                maxBy(
                  [state.searchResults.allFiles, action.payload.allFiles],
                  'length'
                ) ?? [],
            };
            state.fileFilter.fileNames =
              maxBy(
                [state.fileFilter.fileNames, action.payload.fileNames],
                'length'
              ) ?? [];
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            state.searchResults.apiStatus = 'failure';
          },
        }
      ),
      getMatchGroup: createHttpThunk(
        async ({ matchGroupId }: { matchGroupId: string }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroup.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchGroup = {
              data: action.payload.matchGroup,
              apiStatus: 'idle',
            };
          },
          rejected: (state) => {
            state.matchGroup.apiStatus = 'failure';
          },
        }
      ),
      getMatchGroups: createHttpThunk(
        async ({ eventId }: GetMatchGroupsParams, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const searchEventsQuery: SearchMatchGroupsQueryParams = {
            eventId: eventId,
            pageSize: 10000,
            currentPage: 1,
          };
          const response = await http.get<GetMatchGroupsResponse>(signal)(
            `/match-groups/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroups.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchGroups = { ...action.payload, status: 'idle' };
          },
          rejected: (state) => {
            state.matchGroups.status = 'failure';
          },
        }
      ),
      getFile: createHttpThunk(
        async ({ tracklet }: { tracklet: Tracklet }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetFileResponse>(signal)(
            `/file/${tracklet.fileId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.selectedTrackletFile.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.selectedTrackletFile = {
              file: action.payload.file,
              selectedTracklet: action.meta.arg.tracklet,
              apiStatus: 'idle',
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            state.selectedTrackletFile.apiStatus = 'failure';
          },
        }
      ),
      createMatchGroup: createHttpThunk(
        async (
          {
            name,
            eventId,
          }: {
            name: string;
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<CreateMatchGroupResponse>(signal)(
            '/match-groups',
            {
              name,
              eventId,
            },
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.createMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.createMatchGroup = {
              status: 'idle',
              id: action.payload.matchGroup?.id ?? '',
            };
            createSnackNotification(
              AlertLevel.Success,
              'Create Match Group',
              `Match Group ${action.payload.matchGroup?.name} was created successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Create Match Group failed',
              action.error.message
            );
            state.createMatchGroup.status = 'failure';
          },
        }
      ),
      updateMatchGroup: createHttpThunk(
        async (
          {
            matchGroupId,
            trackletId,
            referenceTrackletId,
            searchName,
          }: {
            matchGroupId: string;
            trackletId: string;
            referenceTrackletId: string;
            searchName: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const newSearch = {
            referenceTrackletId,
            id: trackletId,
            searchName,
            searchTime: new Date().toISOString(),
          };

          const existingSearches =
            (getState() as RootState).searchResults.matchGroups.results.find(
              (matchGroup) => matchGroup.id === matchGroupId
            )?.searches ?? [];

          if (
            existingSearches.some(
              (search) =>
                search.referenceTrackletId === newSearch.referenceTrackletId
            )
          ) {
            const searchWithSameReferenceTracklet = existingSearches.find(
              (search) =>
                search.referenceTrackletId === newSearch.referenceTrackletId
            );
            createSnackNotification(
              AlertLevel.Error,
              'This tracklet already has a search',
              `See ${searchWithSameReferenceTracklet?.searchName}`
            );
            throw new Error('This tracklet already has a search');
          }

          const searches = [...existingSearches, newSearch];
          const response = await http.patch<CreateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            {
              searches,
            },
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = { status: 'idle' };
            state.matchGroups.results = map(
              state.matchGroups.results,
              (matchGroup) =>
                matchGroup.id === action.payload.matchGroup?.id
                  ? merge({}, matchGroup, action.payload.matchGroup)
                  : matchGroup
            );
            state.matchGroup.data = action.payload.matchGroup;
            createSnackNotification(
              AlertLevel.Success,
              'Update Match Group',
              `Match Group ${action.payload?.matchGroup?.name} was updated successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      updateSelectedTracklets: createHttpThunk(
        async (
          {
            matchGroupId,
            selectedTracklets,
          }: { matchGroupId: string; selectedTracklets: Tracklet[] },
          thunkAPI
        ) => {
          const {
            getState,
            dispatch,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.patch<CreateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            {
              selectedTracklets: uniq([
                ...((getState() as RootState).searchResults.matchGroup.data
                  ?.selectedTracklets ?? []),
                ...selectedTracklets.map((tracklet) => tracklet.trackletId),
              ]),
            },
            // Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          if (response.data.matchGroup) {
            dispatch(getMatchGroup({ matchGroupId }));
            dispatch(
              getMatchGroups({ eventId: response.data.matchGroup.eventId })
            );
            dispatch(getMatchGroupSelectedTracklets({ matchGroupId }));
          }
          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = { status: 'idle' };
            createSnackNotification(
              AlertLevel.Success,
              'Update Selected Detection',
              `Match Group ${action.payload.matchGroup?.name} was updated successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Update Selected Detection failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),

      getSearchResultsByIdAll: createHttpThunk(
        async (
          {
            matchGroupId,
            searchId,
          }: {
            matchGroupId?: string;
            searchId?: string;
            trackletType?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          let currentPage = 1;
          const pageSize = 100;
          const allResults: { fileId: string; fileName: string }[] = [];
          let count = 0;
          const threshold = (getState() as RootState).searchResults
            .confidenceThreshold;
          do {
            const response = await http.get<GetSearchResultsResponse>(signal)(
              `match-groups/${matchGroupId}/search/${searchId}/${qs.stringify(
                { pageSize, currentPage, threshold },
                { addQueryPrefix: true }
              )}`,
              {
                // TODO: Fix getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              }
            );

            const results = response.data.results ?? [];
            allResults.push(
              ...results.map((result) => ({
                fileId: result.fileId,
                fileName: result.fileName,
              }))
            );
            currentPage += 1;
            count = results.length;
          } while (pageSize === count);
          return allResults;
        },
        {
          pending: (state) => {
            state.fileFilter.apiStatus = 'loading';
            state.fileFilter.selectedFileNames = [];
            state.fileFilter.displayString = '';
            state.fileFilter.fileIds = [];
          },
          fulfilled: (state, action) => {
            const results = action.payload ?? [];
            state.fileFilter.fileNames = results.map(
              (result) => result.fileName
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'SearchResults failed',
              action.error.message
            );
            state.fileFilter.apiStatus = 'failure';
          },
        }
      ),
      getThumbnails: createHttpThunk(
        async (
          tracklets: Array<{
            trackletId: string;
            orgId: string;
            fileId: string;
          }>,
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<GetThumbnailsResponse>(signal)(
            '/thumbnails',
            { tracklets },
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          fulfilled: (state, action) => {
            Object.assign(state.thumbnailUrls, action.payload.thumbnails);
            state.thumbnailUrls = Object.entries(state.thumbnailUrls).reduce(
              (acc, [key, value]) =>
                new Date(value.expiresDateTime) <= new Date()
                  ? acc
                  : { ...acc, [key]: value },
              {}
            );
            ls.set('thumbnailUrls', state.thumbnailUrls);
          },
          rejected: (_state, _action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Get Thumbnails Failed'
            );
          },
        }
      ),
      setThumbnails: create.reducer(
        (
          state,
          action: PayloadAction<
            Record<
              string,
              {
                thumbnailUrls: {
                  best: string;
                };
                expiresDateTime: string;
              }
            >
          >
        ) => {
          state.thumbnailUrls = action.payload;
          ls.set('thumbnailUrls', state.thumbnailUrls);
        }
      ),
      setAttributes: create.reducer(
        (state, action: { payload?: { searchId?: string } }) => {
          let personJsonAttributes = getAttributesJson('person');
          let vehicleJsonAttributes = getAttributesJson('vehicle');

          const searchId = action.payload?.searchId;
          if (searchId) {
            const engineId = state.matchGroup.data?.searches?.find(
              (search) => search.id === searchId
            )?.engineId;
            if (engineId) {
              const personKey = `${engineId}_attr`;
              const vehicleKey = `${engineId}_attr_vehicle`;

              if (personKey in attributesJson) {
                personJsonAttributes = get(attributesJson, personKey);
              }
              if (vehicleKey in attributesJson) {
                vehicleJsonAttributes = get(attributesJson, vehicleKey);
              }
            }
          }

          const personAttributes = convertJsonAttributes(personJsonAttributes);
          const vehicleAttributes = convertJsonAttributes(
            vehicleJsonAttributes
          );

          state.attributes = {
            person: personAttributes,
            vehicle: vehicleAttributes,
          };
        }
      ),
    };
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateMatchGroupAttributeSearch.pending, (state) => {
        state.updateMatchGroup.status = 'loading';
        state.matchGroups.status = 'loading';
        state.matchGroup.apiStatus = 'loading';
      })
      .addCase(updateMatchGroupAttributeSearch.fulfilled, (state, action) => {
        state.updateMatchGroup.status = 'idle';
        state.matchGroups.status = 'idle';
        state.matchGroup.apiStatus = 'idle';

        state.matchGroups.results = map(
          state.matchGroups.results,
          (matchGroup) =>
            matchGroup.id === action.payload.matchGroup?.id
              ? merge({}, matchGroup, action.payload.matchGroup)
              : matchGroup
        );
        state.matchGroup.data = action.payload.matchGroup;
      })
      .addCase(updateMatchGroupAttributeSearch.rejected, (state) => {
        state.updateMatchGroup.status = 'failure';
        state.matchGroups.status = 'failure';
        state.matchGroup.apiStatus = 'failure';
      });
  },
  selectors: {
    selectBoundingBoxes: (state) => state.boundingBoxes.data,
    selectSelectedTrackletFile: (state) => state.selectedTrackletFile,
    selectSearchResults: (state) => state.searchResults,
    selectSelectedTracklet: (state) => state.selectedTracklet,
    selectFileFilter: (state) => state.fileFilter,
    selectMatchGroup: (state) => state.matchGroup,
    selectMatchGroups: (state) => state.matchGroups,
    selectNewMatchGroupId: (state) => state.createMatchGroup.id,
    selectEvent: (state) => state.event,
    selectConfidenceThreshold: (state) => state.confidenceThreshold,
    selectThumbnails: (state) => state.thumbnailUrls,
    selectAttributes: (state) => state.attributes,
  },
});

export const {
  setSearchResultsPagination,
  setSelectedTracklet,
  setConfidenceThreshold,
  selectAllTracklets,
  unselectAllTracklets,
  getBoundingBoxes,
  getSearchResultsById,
  addFileNameFilter,
  getFile,
  getMatchGroup,
  getMatchGroups,
  createMatchGroup,
  updateMatchGroup,
  updateSelectedTracklets,
  getEvent,
  getSearchResultsByIdAll,
  getThumbnails,
  setThumbnails,
  setAttributes,
} = searchResultsSlice.actions;

export const {
  selectBoundingBoxes,
  selectSelectedTrackletFile,
  selectSelectedTracklet,
  selectFileFilter,
  selectSearchResults,
  selectMatchGroup,
  selectMatchGroups,
  selectNewMatchGroupId,
  selectEvent,
  selectConfidenceThreshold,
  selectThumbnails,
  selectAttributes,
} = searchResultsSlice.selectors;
