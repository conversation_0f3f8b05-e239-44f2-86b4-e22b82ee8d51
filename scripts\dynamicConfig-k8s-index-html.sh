#!/bin/bash

CONFIG_JSON=`cat /usr/share/nginx/html/aiware-config.final.json | jq -c`
GIT_COMMIT=$(cat /build-manifest.yml | grep  git_commit: )
VERSION=$(cat /build-manifest.yml | grep  version: )

echo $CONFIG_JSON
echo $GIT_COMMIT
echo $VERSION

sed -i "s|window\['config'\] = {}|window\['config'\] = ${CONFIG_JSON}|g" /usr/share/nginx/html/index.html
sed -i "s|window\['build'\] = 'GIT_COMMIT'|window\['build'\] = '${GIT_COMMIT}'|g" /usr/share/nginx/html/index.html
sed -i "s|window\['version'\] = 'VERSION'|window\['version'\] = '${VERSION}'|g" /usr/share/nginx/html/index.html

cat  /etc/nginx/conf.d/default.conf