import { AxiosError, isAxiosError } from 'axios';

class ApiError extends Error {
  constructor(error: AxiosError | Error | string | { message: string }) {
    let errorMessage: string;
    if (isAxiosError(error)) {
      errorMessage = JSON.stringify(error.response?.data) || error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error instanceof Error || (error && error.message)) {
      errorMessage = error.message;
    } else {
      errorMessage = 'An unknown error has occurred';
    }

    super(errorMessage);
    Error.captureStackTrace(this, this.constructor);
    this.message = errorMessage;
    this.name = 'ApiError';
  }
}

export default ApiError;
