import getFolder from '../getFolder';
import { Context } from '../../../types';
import { NotFoundError } from '@common/errors';
import { responses } from '@tracker/graphQL';

const getEvent = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & Partial<responses.getEvent>>> => {
  const getFolderData = await getFolder(context);
  if (!getFolderData) {
    throw new NotFoundError('No folder found');
  }

  const { data } = getFolderData;
  const { folder } = data;
  const event = folder?.contentTemplates?.[0]?.sdo?.data;
  if (!event) {
    const eventId = context.req.params?.eventId;
    throw new NotFoundError(`No event found for ${eventId}`);
  }

  const new_data = Object.assign({}, data, {
    event,
  });
  const new_context = Object.assign({}, context, { data: new_data });
  return new_context;
};

export default getEvent;
