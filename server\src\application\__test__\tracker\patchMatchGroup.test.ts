import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'searchId'),
}));

jest.useFakeTimers().setSystemTime(new Date('2024-04-01T00:00:00.000Z'));
jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getMatchGroup)) {
        return Promise.resolve({
          structuredDataObject: {
            id: 'matchGroupId',
            data: {
              id: 'matchGroupId',
              name: 'name',
              eventId: 'eventId',
              searches: [
                {
                  id: 'search-id-1',
                  searchName: 'searchName-1',
                  referenceTrackletId: 'referenceTrackletId',
                },
              ],
              selectedTracklets: ['123'],
            },
          },
        });
      }
      if (query.includes(queries.createStructuredData)) {
        return Promise.resolve({
          createStructuredData: {
            id: 'matchGroupId',
            schemaId: 'schemaId',
            data: {
              id: 'matchGroupId',
              name: 'newName',
              eventId: 'eventId',
              searches: [
                {
                  id: 'search-id-1',
                  searchName: 'searchName-1',
                  referenceTrackletId: 'referenceTrackletId',
                },
              ],
              selectedTracklets: ['7777'],
              modifiedDateTime: '2024-04-01T00:00:00.000Z',
            },
          },
        });
      }
    }
  ),
}));

describe('patch event', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('patch event with new search', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const newSearch = [
      {
        id: 'search-id-2',
        searchName: 'searchName-2',
        referenceTrackletId: 'referenceTrackletId',
      },
    ];

    const resp = await supertest(expressApp)
      .patch('/api/v1/match-groups/matchGroupId')
      .set('Authorization', 'Bearer validToken')
      .send({
        name: 'newName',
        searches: newSearch,
        selectedTracklets: ['123'],
      })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'matchGroupId',
        schemaId: 'schemaId',
      }
    );

    expect(callGQL).toHaveBeenNthCalledWith(
      4,
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'matchGroupId',
        schemaId: 'schemaId',
        data: {
          id: 'matchGroupId',
          name: 'newName',
          eventId: 'eventId',
          searches: [
            {
              id: 'search-id-2',
              searchName: 'searchName-2',
              referenceTrackletId: 'referenceTrackletId',
            },
          ],
          selectedTracklets: ['123'],
          modifiedDateTime: '2024-04-01T00:00:00.000Z',
        },
      }
    );
  });

  it('patch event with new selectedTracklet', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .patch('/api/v1/match-groups/matchGroupId')
      .set('Authorization', 'Bearer validToken')
      .send({
        name: 'newName',
        selectedTracklets: ['777'],
      })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'matchGroupId',
        schemaId: 'schemaId',
      }
    );

    expect(callGQL).toHaveBeenNthCalledWith(
      4,
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'matchGroupId',
        schemaId: 'schemaId',
        data: {
          id: 'matchGroupId',
          name: 'newName',
          eventId: 'eventId',
          searches: [
            {
              id: 'search-id-1',
              searchName: 'searchName-1',
              referenceTrackletId: 'referenceTrackletId',
            },
          ],
          selectedTracklets: ['777'],
          modifiedDateTime: '2024-04-01T00:00:00.000Z',
        },
      }
    );
  });

  it('cannot patch event w/o eventId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .patch('/api/v1/match-groups')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
