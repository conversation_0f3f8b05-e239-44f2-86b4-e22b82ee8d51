import { onSetConfig, selectConfig } from '@store/modules/config/slice';
import { configureAppStore } from '@store/store';

const initialStateForMock = {};

describe('configSlice', () => {
  it('should handle onSetConfig', () => {
    const newConfig = { nodeEnv: 'test' };

    const mockedStore = configureAppStore(initialStateForMock);

    // create a store with the initial state
    mockedStore.dispatch(onSetConfig(newConfig));

    // expect config to be in the state
    const state = mockedStore.getState();
    expect(state.config).toEqual(newConfig);

    // expect selectConfig to return the config
    const result = selectConfig(state);
    expect(result).toBe(newConfig);
  });
});
