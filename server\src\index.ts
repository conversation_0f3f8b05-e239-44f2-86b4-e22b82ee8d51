import fs from 'fs';
import https from 'https';
import parseArgs from 'minimist';
import { createExpressApp } from './application';
import createConfig from './config';
import env from './env';

export async function start() {
  const config = await createConfig({ env });
  const expressApp = await createExpressApp({ config });

  config.log.info(`Starting server ${env.serviceName}`, { port: env.port });

  config.log.info(`Node version ${process.versions.node}`);

  if (env.nodeEnv === 'development') {
    const args = parseArgs(process.argv.slice(2));
    if (args.key && args.cert) {
      let privateKey = '';
      let certificate = '';

      try {
        privateKey = fs.readFileSync(args.key, 'utf8');
        certificate = fs.readFileSync(args.cert, 'utf8');
      } catch (_err) {
        console.error(`\nMissing certs. Run 'yarn init:env'\n`);
        process.exit(1);
      }

      https
        .createServer({ key: privateKey, cert: certificate }, expressApp)
        .listen(env.port, () => {
          config.log.info(
            `${env.serviceName} accepting connections over ${env.port}`
          );
          config.log.debug(
            `\nAPI Health ----> https://local.veritone.com:${env.port}/api/health\n`
          );
          config.log.debug(
            `\nEvents Endpoint ----> https://local.veritone.com:${env.port}/api/v1/events\n`
          );
          config.log.debug(
            `\nCredential Endpoint ----> https://local.veritone.com:${env.port}/api/v1/credential\n`
          );
          config.log.debug(
            `\nMatch Groups Endpoint ----> https://local.veritone.com:${env.port}/api/v1/match-groups\n`
          );
        });
    } else {
      console.error(`\nMissing cert args. Run with 'yarn start:dev'?\n`);
      process.exit(1);
    }
  } else {
    expressApp.listen(env.port, () => {
      config.log.info(
        `${env.serviceName} accepting connections over ${env.port}`
      );
    });
  }
}
