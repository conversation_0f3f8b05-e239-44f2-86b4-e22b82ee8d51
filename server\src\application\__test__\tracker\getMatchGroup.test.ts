import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { JobStatus, VideoSpliceDetails } from '../../../../../types/tracker';

const mockStructuredDataObject = {
  id: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
  data: {
    id: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
    name: 'Test Match Group',
    eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
    searches: [
      {
        id: '12345678-b1cb-4b93-bfea-0401861d8d40',
        searchName: 'testSearchName',
        referenceTrackletId: '7777',
      },
      {
        id: 'ABCDEFGH-b1cb-4b93-bfea-0401861d8d40',
        searchName: 'testSearchName',
        referenceTrackletId: '7777',
      },
    ],
    selectedTracklets: ['7777'],
  },
  createdDateTime: '2024-05-03T21:14:26.769Z',
  modifiedDateTime: '2024-05-03T21:57:48.320Z',
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            contentTemplates: [{ id: 'id', sdo: { data: {} } }],
          },
        });
      }
      if (
        query.includes(queries.getMatchGroup) &&
        variables?.id === 'a-matchgroup-with-generated-timeline-id'
      ) {
        return Promise.resolve({
          structuredDataObject: {
            ...mockStructuredDataObject,
            data: {
              ...mockStructuredDataObject.data,
              generatedTimelines: [
                {
                  id: 'timeline1',
                  name: 'Timeline Project 1',
                  createdDateTime: '2022-01-01T00:00:00Z',
                  createdUserId: 'user1',
                  tdoId: 'tdo1',
                  resolution: '1920x1080',
                  outputFormat: 'mp4',
                  videoLengthMs: 60000,
                  videoSizeBytes: 5000000,
                },
              ],
            },
          },
        });
      }
      if (query.includes(queries.getMatchGroup)) {
        return Promise.resolve({
          structuredDataObject: mockStructuredDataObject,
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
    }
  ),
}));

jest.mock('@tracker/actions/getSplicingTdoDetails', () => ({
  getSplicingTdoDetails: jest
    .fn()
    .mockImplementation((): { [key: string]: VideoSpliceDetails } => ({
      tdo1: {
        downloadUrl: 'mock-download-url',
        status: JobStatus.Complete,
      },
    })),
}));

describe('get match group', () => {
  it('get match group', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/match-groups/a-matchgroup-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'a-matchgroup-id',
        schemaId: 'schemaId',
      }
    );
  });

  it('get match group with generated timeline ', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/match-groups/a-matchgroup-with-generated-timeline-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'a-matchgroup-with-generated-timeline-id',
        schemaId: 'schemaId',
      }
    );
  });
});
