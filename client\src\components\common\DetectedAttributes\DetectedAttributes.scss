.detected-attributes__main-container {
  display: flex;
  flex-direction: column;
  border-radius: 0 0 7px 7px;
  height: 100%;
  width: 100%;
  background-color: var(--accordion-background);

  .detected-attributes__header-text {
    padding: 0 20px 10px;

    @include size-4;
  }

  .detected-attributes__scrollable-div {
    flex-grow: 1;
    padding: 0 6px;
    overflow: auto hidden;
    display: flex;
    flex-wrap: wrap;

    .detected-attributes__accordion-container {
      display: inline-block;
      margin-right: 15px;
      margin-bottom: 14px;

      .detected-attributes__name {
        @include size-0-bold;

        color: var(--text-secondary);
        text-transform: capitalize;
        margin: 0 0 14px;
      }

      .detected-attributes__chips {

        .detected-attributes__chip {
          text-align: center;
          line-height: 24px;
          background-color: var(--disabled-background);
          margin-right: 5px;

          @include size-1-bold;

          .MuiChip-label {
            padding: 10px 20px;
            text-transform: capitalize;
          }
        }
      }
    }
  }
}