import getFileLocation from './getFileLocation';
import { TemporalData } from '../../../../../../types/tracker';

jest.useFakeTimers();

describe('getFileLocation', () => {
  it('returns the correct location after a delay', async () => {
    const mockTDO: TemporalData = {
      id: '123',
      name: 'Test Temporal Data', // Ensure name is defined
      status: 'active', // Ensure status is defined
      details: {
        tags: [],
        veritoneProgram: {
          programLiveImage: 'image_url',
        },
        veritoneFile: {
          fileName: 'test.mp4',
          duration: 1000,
          fileSize: 1000,
          fileType: 'video',
          mimetype: 'video/mp4',
          createdByName: 'user',
          videoFrameRate: 30,
        },
        veritonePermissions: {
          isPublic: true,
          acls: [],
        },
        addToIndex: true,
        startDateTime: 1622520000000,
        stopDateTime: 1622523600000,
        veritoneAppId: 'veritoneAppId123',
        status: 'completed',
        createdDateTime: 1622520000000,
        modifiedDateTime: 1622523600000,
        recordingId: 1,
      },
      createdDateTime: '2021-06-01T00:00:00Z',
      modifiedDateTime: '2021-06-01T01:00:00Z',
      thumbnailUrl: 'thumbnail_url',
      createdBy: 'user',
      primaryAsset: {
        signedUri: 'signed_uri',
      },
      streams: [
        {
          uri: 'stream_uri',
          protocol: 'rtsp',
        },
      ],
    };
    const locationPromise = getFileLocation(mockTDO);

    jest.advanceTimersByTime(100);

    const location = await locationPromise;

    expect(location).toBe('(90.0000, -180.0000)');
  });
});
