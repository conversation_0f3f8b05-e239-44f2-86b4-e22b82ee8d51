.search-and-upload__detail-search-and-upload {
  height: 88px;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 23px 30px;
  justify-content: right;

  .search-and-upload__detail-search {
    width: 400px;

    .MuiOutlinedInput-root {
      border-radius: 30px;
      padding: 2px 2px 2px 20px;
    }
  }

  .search-and-upload__circular-progress {
    margin-right: 15px;
    color: inherit;
  }

  .search-and-upload__detail-upload-button, .search-and-upload__detail-search-button {
    padding: 6px 16px;
    text-transform: none;
    border-radius: 30px;
    border: 1.5px solid var(--primary);

    @include size-1-bold;
  }

  .MuiInputBase-input {
    padding: 7.5px 14px;
  }
}

.search-and-upload__detail-upload-popover {
  width: 560px;
  margin: 12px -20px;
  padding: 30px;

  .search-and-upload__files {
    margin: 25px 0;
    max-height: 300px;
    overflow: hidden auto;

    .search-and-upload__files-file {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--selected-files);
      height: 50px;
      padding: 20px 0;
      border-bottom: solid 2px var(--border-area);

      .search-and-upload__files-file-size {
        &.uploading {
          margin-right: 15px;
        }
      }

      &:last-child {
        border: none;
      }

      span {
        display: flex;
        align-items: center;
        color: var(--text-tertiary);

        .material-symbols-outlined {
          margin: 0 15px;
          color: var(--text-primary);
        }

        &.disabled {
          opacity: 0;
        }
      }

      .search-and-upload__files-file-name {
        width: 235px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .search-and-upload__files-file-progress {
        width: 100px;
      }

      .search-and-upload__files-file-progress-text {
        @include size-1;

        margin: 0 12px;
        text-align: center;
      }
    }
  }

  .search-and-upload__detail-upload-title {
    @include size-4;
  }

  .search-and-upload__detail-upload-desc {
    @include size-2;

    margin-top: 10px;
  }

  .search-and-upload__detail-upload-question {
    @include size-1;

    margin-top: 23px;
  }

  .search-and-upload__detail-upload-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;

    .search-and-upload__cancel-new-event {
      line-height: 40px;
      margin-left: 5px;
      color: var(--primary);
      cursor: pointer;

      &.disabled {
        color: var(--disabled);
        cursor: default;
        pointer-events: none;
      }
    }

    .search-and-upload__detail-upload-browse-container {
      display: flex;
      justify-content: flex-end;

      button {
        margin-left: 10px;
        width: 100px;

        .material-icons {
          margin-right: 5px;
        }
      }

      .search-and-upload__detail-upload-browse-confirm {
        width: 110px;
      }

      .search-and-upload__detail-upload-browse-add {
        width: initial;
      }
    }

    .search-and-upload__detail-upload-select-event {
      width: 200px;
      height: 38px;

      .MuiOutlinedInput-root {
        padding: 0;

        .material-icons {
          padding-left: 10px;
        }
      }

      .MuiSelect-select {
        padding: 8px 30px 8px 10px;
      }
    }

    .MuiButtonBase-root {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 15px;

      @include size-2-bold;

      text-transform: none;
      line-height: 24px;

      .material-icons {
        margin-right: 13px;
        font-size: 18px;
      }
    }
  }

  .search-and-upload__detail-upload-browse-new-event-field {
    input {
      padding: 9.5px 14px;
    }
  }

  .search-and-upload__detail-upload-browse {
    border: 2px dashed var(--border-area);
    border-radius: 4px;
    padding: 16px;
    position: relative;

    input {
      opacity: 0;
      width: 100%;
      height: 100%;
      position: absolute;
      cursor: pointer;
    }

    .MuiButtonBase-root {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 12px;

      @include size-2-bold;

      text-transform: none;
      line-height: 24px;

      .material-icons {
        margin-right: 10px;
        font-size: 18px;
      }
    }
  }

  .search-and-upload__detail-upload-img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 61px 0;
  }

  .search-and-upload__detail-upload-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-top: 20px;

    @include size-2;

    >* {
      text-transform: none;
    }
  }
}
