.match-group-row {
  display: block;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  &.selected {
    background-color: var(--row-active);
  }

  .match-group-row__row-details {
    margin-top: -10px;
    margin-bottom: 10px;
    transition: height 300ms;
    overflow: hidden;

    .match-group-row__row-details-line {
      opacity: 0.5;
      background-color: var(--text-primary);
      width: 3px;
      height: 131px;
      margin-left: 70px;
      margin-right: 27px;
      transition: height 300ms;
      float: left;
    }
  }

  .match-group-row__row {
    position: relative;
    display: flex;

    .MuiPaper-root {
      width: 100%;
      box-shadow: none;

      .MuiAccordionDetails-root {
        padding: 0 16px 25px;
        background-color: var(--row-active);

        .table__column-header {
          padding-right: 75px;
        }
      }
    }
    .match-group-row__searches {
      position: relative;

      .match-group-row__searches-action {
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        right: 20px;
        display: flex;
        align-items: center;
        gap: 15px;

        .MuiButton-root {
          text-transform: capitalize;
          background-color: var(--view-match-group-background);
          border-radius: 4px;
  
          @include size-1-bold;
        }

      }
    }

    .match-group-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .match-group-row__cell-select {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .match-group-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 23px 16px 23px 0;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;

        &.pending {
          font-style: italic;
        }
      }

    }
  }
}