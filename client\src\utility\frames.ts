/* find nearest frame index whose time is <= given time */
export const findFrameIndex = (timeMs: number, fps: number): number =>
    Math.floor((timeMs / 1000.0) * fps);
  
  /* convert frame index to startTimeMs of frame */
  export const convertFrameIndexToTimeMs = (
    frameIndex: number,
    fps: number
  ): number => (1000.0 * frameIndex) / fps;
  
  /* align startTimeMs with nearest frame start time whose millisecond floor is <= to given time */
  export const frameAlignStartTimeMs = (
    startTimeMs: number,
    fps: number
  ): number => {
    const frameIndex = findFrameIndex(Math.ceil(startTimeMs), fps);
    let alignedTime = convertFrameIndexToTimeMs(frameIndex, fps);
  
    // handle cases where input startTimeMs that was rounded down or floored
    if (
      Math.ceil(startTimeMs) - Math.floor(startTimeMs) === 0 &&
      startTimeMs - alignedTime > 1
    ) {
      const correctedAlignedTime = convertFrameIndexToTimeMs(frameIndex + 1, fps);
  
      // check if the floor equal the input startTimeMs
      if (Math.floor(correctedAlignedTime) - Math.floor(startTimeMs) === 0) {
        alignedTime = correctedAlignedTime;
      }
    }
  
    return alignedTime;
  };
  
  /* align stopTimeMs with nearest frame whose millisecond ceil is >= to current stopTimeMs
  if stopTimeMs is already on a frame boundary will return current value
  otherwise it will return a larger value
  */
  export const frameAlignStopTimeMs = (
    stopTimeMs: number,
    fps: number
  ): number => {
    const nextFrameIndex = Math.ceil((Math.floor(stopTimeMs) / 1000.0) * fps);
    let alignedTime = convertFrameIndexToTimeMs(nextFrameIndex, fps);
  
    // handle cases where input stopTimeMs that was rounded up or ceiled
    if (
      Math.ceil(stopTimeMs) - Math.floor(stopTimeMs) === 0 &&
      alignedTime - stopTimeMs > 1
    ) {
      const correctedAlignedTime = convertFrameIndexToTimeMs(
        nextFrameIndex - 1,
        fps
      );
      // check if the floor equal the input startTimeMs
      if (Math.ceil(correctedAlignedTime) - Math.ceil(stopTimeMs) === 0) {
        alignedTime = correctedAlignedTime;
      }
    }
  
    return alignedTime;
  };
