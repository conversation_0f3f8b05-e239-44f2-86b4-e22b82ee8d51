import { render } from '@testing-library/react';
import Redirect from '.';
import { useNavigate } from 'react-router-dom';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
}));

describe('Redirect', () => {
    it('navigates to the specified path when rendered', () => {
        const navigate = jest.fn();
        (useNavigate as jest.Mock).mockReturnValue(navigate);
        render(<Redirect to="/event-test" />);
        expect(navigate).toHaveBeenCalledWith('/event-test');
    });
});
