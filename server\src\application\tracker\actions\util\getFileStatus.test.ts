import getFileStatus from './getFileStatus';
import { TemporalData } from '../../../../../../types/tracker';

jest.useFakeTimers();

describe('getFileStatus', () => {
  it('returns the correct status for pending', async () => {
    let mockTDO: {
      id: string;
      jobs?: { records: { name: string; status: string }[] };
    } = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'pending',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('pending');

    mockTDO = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'waiting',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('pending');

    mockTDO = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'queued',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('pending');
  });

  it('returns the correct status for job failed', async () => {
    let mockTDO: {
      id: string;
      jobs?: { records: { name: string; status: string }[] };
    } = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'aborted',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('error');

    mockTDO = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'failed',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('error');

    mockTDO = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'cancelled',
          },
        ],
      },
    };

    expect(getFileStatus(mockTDO)).toBe('error');
  });

  it('returns the correct status for job complete', async () => {
    const mockTDO: {
      id: string;
      jobs?: { records: { name: string; status: string }[] };
    } = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'complete',
          },
        ],
      },
    };

    const status = getFileStatus(mockTDO);

    expect(status).toBe('processed');
  });

  it('returns the correct status for job processing', async () => {
    const mockTDO: {
      id: string;
      jobs?: { records: { name: string; status: string }[] };
    } = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'Track Ingest Job',
            status: 'running',
          },
        ],
      },
    };

    const status = getFileStatus(mockTDO);

    expect(status).toBe('processing');
  });

  it('returns the correct status for job unknown', async () => {
    const mockTDO: {
      id: string;
      jobs?: { records: { name: string; status: string }[] };
    } = {
      id: '123',
      jobs: {
        records: [
          {
            name: 'NOT Track Ingest Job',
            status: 'running',
          },
        ],
      },
    };

    const status = getFileStatus(mockTDO);

    expect(status).toBe('unknown');
  });
});
