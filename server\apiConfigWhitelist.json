["apiRoot", "veritoneAppId", "graphQLEndpoint", "nodeEnv", "port", "serviceName", "startApi", "cloud", "blob", "blob.endpointSuffix", "blob.account", "blob.key", "blob.container", "blob.expireSecs", "registryIds", "registryIds.eventsRegistryId", "registryIds.matchGroupsRegistryId", "defaultClusterId", "gpuClusterId", "cpuClusterId", "trackerEngineId", "validWritableCredentialEngineIds", "glcIngestorEngineId", "outputWriterEngineId", "aws", "s3", "s3.bucket", "s3.accessKey", "s3.secret<PERSON>ey", "s3.roleArn", "s3.region", "s3.expireSecs", "useRedis", "videoSliceEngineId", "credentialApi", "redis", "redis.host", "redis.port", "adaptiveBoxPoolingThreshold"]