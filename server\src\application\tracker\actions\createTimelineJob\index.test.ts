import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import createTimelineJob from '.';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import * as ResTypes from '../../../../../../types/responses';
import { TimelineProject } from '../../../../../../types/tracker';

const timelineProject: TimelineProject = {
  groups: [
    {
      id: 'ec8f33b4-2e69-4d0e-a533-39e1f7ba2cc8',
      name: 'Default Group',
      tracklets: [
        {
          orgId: '1',
          type: 'person',
          fileId: '3150000006',
          fileName: 'La_Cienega_Clifton.mp4',
          attributes: {
            Body: [
              {
                key: 'Body',
                value: 'Average',
                label: 'BodyAverage',
              },
            ],
            Face: [
              {
                key: 'Face',
                value: 'Back',
                label: 'FaceBack',
              },
            ],
            HairColor: [
              {
                key: 'HairColor',
                value: 'Black',
                label: 'HairColorBlack',
              },
            ],
            HairType: [
              {
                key: 'HairType',
                value: 'Long',
                label: 'typeLong',
              },
            ],
            LowerType: [
              {
                key: 'LowerType',
                value: 'Trousers',
                label: 'LowerTypeTrousers',
              },
            ],
            Gender: [
              {
                key: 'Gender',
                value: 'Femald',
                label: 'GenderFemale',
              },
            ],
          },
          confidence: 0.2,
          stopTimeMs: 880,
          trackletId: '6aa9239c-480e-4dca-96ab-22dd89a34327',
          startTimeMs: 320,
          thumbnailUrls: {
            best: '',
          },
        },
      ],
    },
    {
      id: 'f7ad58e1-7502-4715-b8ce-52d2175e3e53',
      name: 'Default Group',
      tracklets: [
        {
          orgId: '1',
          type: 'person',
          fileId: '3150000006',
          fileName: 'La_Cienega_Clifton.mp4',
          attributes: {
            Body: [
              {
                key: 'Body',
                value: 'Average',
                label: 'BodyAverage',
              },
              {
                key: 'Body',
                value: 'Overweight',
                label: 'BodyOverweight',
              },
            ],
            Face: [
              {
                key: 'Face',
                value: 'Back',
                label: 'FaceBack',
              },
            ],
            HairColor: [
              {
                key: 'HairColor',
                value: 'Black',
                label: 'HairColorBlack',
              },
              {
                key: 'HairColor',
                value: 'Brown',
                label: 'HairColorBrown',
              },
            ],
            lowerColor: [
              {
                key: 'lowerColor',
                value: 'White',
                label: 'lowerColorWhite',
              },
            ],
            Gender: [
              {
                key: 'Gender',
                value: 'Male',
                label: 'GenderMale',
              },
            ],
            footwear: [
              {
                key: 'FootwearColor',
                value: 'Black',
                label: 'FootwearColorBlack',
              },
            ],
          },
          confidence: 0.21,
          stopTimeMs: 1920,
          trackletId: '0e45b7ad-9ca6-4420-8a9b-9a8410c40e35',
          startTimeMs: 1600,
          thumbnailUrls: {
            best: '',
          },
        },
      ],
    },
  ],
};

const createCxt = () => {
  const cxt: Context<object, ResTypes.CreateTimelineJobPayloadResponse> = {
    data: {
      currentTime: 'modifiedDateTime',
      timelineProject,
      generatedTimelineName: 'generatedTimelineName.mp4',
    },
    req: createRequest({
      headers: {
        authorization: 'Bearer validToken',
      },
    }),
    log: consoleLogger(),
    res: createResponse(),
    cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    queries: {},
    gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
  };
  cxt.cache.set('videoSliceEngineId', 'videoSliceEngineId');
  return cxt;
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        createJob: { targetId: '1', id: '2', status: 'pending' },
      })
  ),
}));

describe('Create Splice Job', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Creates splice job', async () => {
    const cxt = createCxt();
    const newCxt = await createTimelineJob(cxt);

    expect(newCxt?.data.spliceJob).toBeDefined();
    expect(newCxt?.data.spliceJob.tdoId).toBe('1');
    expect(newCxt?.data.spliceJob.jobId).toBe('2');
    expect(newCxt?.data.spliceJob.status).toBe('pending');
    expect(newCxt?.data.generatedTimelineName).toBe(
      'generatedTimelineName.mp4'
    );
  });

  it('Throws error if timelineProject is missing', async () => {
    const cxt = createCxt();
    cxt.data.timelineProject = undefined;
    await expect(createTimelineJob(cxt)).rejects.toThrow(
      'Missing required data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
