import NodeCache from 'node-cache';
import { responses } from '../../graphQL';
import { Variables } from 'graphql-request';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import deleteMatchGroup from '../deleteMatchGroup';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, responses.getMatchGroup>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

const matchGroupId = 'matchGroupId';
const schemaId = 'schemaId';

describe('Delete Match Group', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        matchGroupId,
        matchGroup: {
          id: matchGroupId,
          name: 'name',
          eventId: 'eventId',
          searches: [],
          selectedTracklets: [],
          modifiedDateTime: 'modifiedDateTime',
          timelineProject: {},
          generatedTimelines: [],
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', schemaId);
  });

  it('Successfully deletes match group', async () => {
    const response = await deleteMatchGroup(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { schemaId, sdoId: matchGroupId }
    );
    expect(response).not.toBeNull();
  });

  it('Throws error if there is no match-group id', async () => {
    cxt.data.matchGroupId = '';
    await expect(async () => await deleteMatchGroup(cxt)).rejects.toThrow(
      'No matchGroup provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if there is no match-group', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.matchGroup = undefined;
    await expect(async () => await deleteMatchGroup(cxt)).rejects.toThrow(
      'No matchGroup provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if schemaId is undefined', async () => {
    cxt.cache.del('matchGroupsSchemaId');
    await expect(async () => await deleteMatchGroup(cxt)).rejects.toThrow(
      'schemaId not found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
