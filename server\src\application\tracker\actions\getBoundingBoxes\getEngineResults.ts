import { Context, RequestHeader } from '@application/types';
import { queries } from '@tracker/graphQL';
import {
  engineResults,
  trackerEngineResultJsonData,
} from '@tracker/graphQL/responses';
import { callGQL } from '@util/api/graphQL/callGraphQL';

export async function getEngineResults<ReqPayload, Data>({
  fileId,
  engineIds,
  startOffsetMs,
  stopOffsetMs,
  context,
  headers,
}: {
  fileId: string;
  engineIds: string[];
  startOffsetMs: number;
  stopOffsetMs: number;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const response = await callGQL<
    engineResults<trackerEngineResultJsonData>,
    ReqPayload,
    Data
  >(context, headers, queries.engineResults, {
    tdoId: fileId,
    engineIds,
    startOffsetMs,
    stopOffsetMs,
  });

  return response.engineResults.records;
}
