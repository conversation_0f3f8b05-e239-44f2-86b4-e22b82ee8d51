import { reduce } from 'p-iteration';
import { Context } from '@application/types';
import { generateThumbnailUrl } from './generateThumbnailUrl';

const EXPIRE_IN_MS = 3600 * 6 * 1000;

const getThumbnails = async <
  ReqPayload,
  Data extends Partial<{
    tracklets: Array<{ trackletId: string; orgId: string; fileId: string }>;
  }> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    Data &
      Partial<{
        thumbnails: Record<
          string,
          {
            thumbnailUrls: { best: string };
            expiresDateTime: string;
          }
        >;
      }>
  >
> => {
  const { data, cache } = context;

  const thumbnails = await reduce(
    data?.tracklets ?? [],
    async (arr, { trackletId, orgId, fileId }) => {
      const expiresDateTime = new Date(Date.now() + EXPIRE_IN_MS - 3600000); // 1 hour before expiration

      // The best thumbnail is used,
      const thumbnailUrlBest = await generateThumbnailUrl({
        orgId,
        tdoId: fileId,
        trackletId,
        type: 'best',
        cache,
      });

      return {
        ...arr,
        [trackletId]: {
          thumbnailUrls: {
            best: thumbnailUrlBest,
          },
          expiresDateTime,
        },
      };
    },
    {}
  );

  Object.assign(data, { thumbnails });
  return context;
};

export default getThumbnails;
