import GQLApi from '.';

describe('verifyJWT', () => {
  // it is a driver program to troubleshoot gql api call
  // it is not a unit test and disable for this purpose
  // to troubleshoot blob api, enable this test (remove x) and provide account token
  xit('verifyJWT correctly', async () => {
    const endpoint = 'https://api.stage.us-gov-2.veritone.com/v3/graphql';
    const token = '';
    const veritoneAppId = 'veritoneAppId123';
    const gqlApi = new GQLApi(endpoint, token, veritoneAppId);
    const result = await gqlApi.verifyJWT(token);
    expect(result?.data?.verifyJWT?.payload.engineId).toHaveProperty(
      'engineId'
    );
  }, 60000);
});
