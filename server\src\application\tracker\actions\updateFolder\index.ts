import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { UpdateEventPayloadResponse } from '../../../../../../types/responses';

const updateFolder = async <
  ReqPayload,
  Data extends Partial<
    responses.getEvent & UpdateEventPayloadResponse
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data & Partial<UpdateEventPayloadResponse & responses.getEvent>
    >
  | undefined
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };
  const { folder, name } = data;
  if (!name) {
    return;
  }

  if (!folder) {
    throw new ActionError('Missing folder');
  }

  try {
    await callGQL<responses.updateFolder, ReqPayload, Data>(
      context,
      headers,
      queries.updateFolder,
      { folderId: folder.id, name }
    );
    return context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default updateFolder;
