RC_URL=registry.central.aiware.com

# If git commit is not set, get it from FS
ifeq ($(GIT_COMMIT),)
	GIT_COMMIT=$(shell git rev-parse HEAD)
endif

build: build-track build-k8s

build-track:
	docker build -t tracker-app:${GIT_COMMIT} -f ./Dockerfile --build-arg K8S_BUILD=FALSE --build-arg APPLICATION=tracker-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag tracker-app:${GIT_COMMIT} tracker-app:latest

run:
	docker run -p 9000:9000 -e ENVIRONMENT=stage -e APPLICATION=tracker-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} tracker-app 

build-local:
	docker build -t tracker-app-local -f ./scripts/Dockerfile.local --progress plain --build-arg ENVIRONMENT=stage --build-arg APPLICATION=tracker-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .

publish: publish-track publish-k8s

run-local:
	docker run -p 443:443 -e ENVIRONMENT=stage -e APPLICATION=tracker-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} tracker-app-local

build-k8s:
	docker build -t tracker-k8s-app:${GIT_COMMIT} -f ./Dockerfile --build-arg K8S_BUILD=TRUE --build-arg APPLICATION=tracker-k8s-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag tracker-k8s-app:${GIT_COMMIT} tracker-k8s-app:latest
	docker tag tracker-k8s-app:${GIT_COMMIT} tracker-app:k8s

run-k8s:
	docker run -p 9000:9000 -e ENVIRONMENT=dev -e APPLICATION=tracker-k8s-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} tracker-k8s-app:latest

publish-track:
	$(MAKE) docker-tag SERVICE=tracker-app
	$(MAKE) docker-push SERVICE=tracker-app

publish-k8s:
	$(MAKE) docker-tag SERVICE=tracker-k8s-app
	$(MAKE) docker-push SERVICE=tracker-k8s-app

docker-tag:
	echo "Tagging ${SERVICE} with Registry Central registry ${RC_URL}"
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:latest
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:dev

docker-push:
	echo "Pushing ${SERVICE} to ${RC_URL}"
	docker push ${RC_URL}/${SERVICE}:latest
	docker push ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker push ${RC_URL}/${SERVICE}:dev