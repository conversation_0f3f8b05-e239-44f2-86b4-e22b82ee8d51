import { AlertLevel, createSnackNotification } from '@components/common';
import { createAppSlice } from '../../createAppSlice';
import { CreateEventState, EventsState, FileToUpload } from './types';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  IngestFilePayload,
  SearchFilesQueryParams,
  SearchEventsQueryParams,
} from '@shared-types/requests';
import {
  CreateEventResponse,
  SearchEventsResponse,
  SearchFilesResponse,
  UploadFileResponse,
} from '@shared-types/responses';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import qs from 'qs';
import { DateTime } from 'luxon';
import { some } from 'lodash';
import type { Event, File } from '@shared-types/tracker';
import { FilesState } from '../home/<USER>';
import {
  getLocalStorage,
  setLocalStorage,
  getUpdatedEventsLocalStorage,
  getUpdatedFilesLocalStorage
} from '@utility/localStorage';
import { AxiosError } from 'axios';

export interface PendingUploadFile {
  fileName: string;
  uploadDate: string;
  eventId: string;
  status: string;
  id: string;
  expirationDate: string;
}

export interface UploadSliceState {
  filesToUpload: FileToUpload[];
  events: EventsState;
  allEvents: EventsState;
  createEvent: CreateEventState;
  selectedEvent?: Event | null;
  files: FilesState;
}

const initialState: UploadSliceState = {
  filesToUpload: [],
  events: {
    results: [],
    currentPage: 1,
    pageSize: 100,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  allEvents: {
    results: [],
    currentPage: 1,
    pageSize: 100,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  files: {
    results: [],
    currentPage: 1,
    pageSize: 100,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  createEvent: {
    status: 'idle',
    event: undefined,
  },
  selectedEvent: undefined,
};

export const uploadSlice = createAppSlice({
  name: 'upload',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>(); // TODO: Fix getState type

    return {
      setCreatedEvent: create.reducer(
        (state, action: PayloadAction<{ event?: Event }>) => {
          state.createEvent.event = action.payload.event;
        }
      ),
      setSelectedEvent: create.reducer(
        (state, action: PayloadAction<{ event?: Event | null }>) => {
          state.selectedEvent = action.payload.event;
        }
      ),
      startUpload: create.reducer((state) => {
        state.filesToUpload = state.filesToUpload.map((f) => {
          if (f.status === 'idle' && f.complete) {
            f.complete = false;
          }
          return f;
        });
      }),
      setUploadStatusError: create.reducer(
        (
          state,
          action: PayloadAction<{ fileIndex: number; error: string }>
        ) => {
          const { fileIndex, error } = action.payload;
          state.filesToUpload[fileIndex].error = error;
        }
      ),
      setUploadStatusComplete: create.reducer(
        (
          state,
          action: PayloadAction<{ fileIndex: number; complete: boolean }>
        ) => {
          const { fileIndex, complete } = action.payload;

          state.filesToUpload[fileIndex].complete = complete;
          state.filesToUpload[fileIndex].progress = complete
            ? 1
            : state.filesToUpload[fileIndex].progress;
        }
      ),
      setProgress: create.reducer(
        (
          state,
          action: PayloadAction<{ fileIndex: number; progress: number }>
        ) => {
          state.filesToUpload[action.payload.fileIndex].progress =
            action.payload.progress;
        }
      ),
      addFilesToUpload: create.reducer(
        (state, action: PayloadAction<{ filesToUpload: FileToUpload[] }>) => {
          const files = action.payload.filesToUpload;

          for (let i = 0; i < files.length; i++) {
            if (!some(state.filesToUpload, { path: files[i].path })) {
              state.filesToUpload.push(files[i]);
            }
          }
        }
      ),
      removeFileToUpload: create.reducer(
        (state, action: PayloadAction<{ path: string }>) => {
          const index = state.filesToUpload.findIndex(
            (f) => f.path === action.payload.path
          );
          if (index > -1) {
            state.filesToUpload.splice(index, 1);
          }
        }
      ),
      updateEvent: create.reducer((state, action: PayloadAction<Event>) => {
        for (let i = 0; i < state.events.results.length; i++) {
          if (state.events.results[i].id === action.payload.id) {
            state.events.results[i] = {
              ...state.events.results[i],
              ...action.payload,
            };
            break;
          }
        }
        for (let i = 0; i < state.allEvents.results.length; i++) {
          if (state.allEvents.results[i].id === action.payload.id) {
            state.allEvents.results[i] = {
              ...state.allEvents.results[i],
              ...action.payload,
            };
            break;
          }
        }
      }),
      updateFile: create.reducer((state, action: PayloadAction<File>) => {
        const index = state.files.results.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.files.results[index] = {
            ...state.files.results[index],
            ...action.payload,
          };
        }
      }),
      deleteEvent: create.reducer((state, action: PayloadAction<{ id: string }>) => {
        state.events.results = state.events.results.filter(
          (e) => e.id !== action.payload.id
        );
        state.allEvents.results = state.allEvents.results.filter(
          (e) => e.id !== action.payload.id
        );
      }),
      createEvent: createHttpThunk(
        async (
          {
            name,
          }: {
            name: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
            rejectWithValue,
          } = thunkAPI;
          const {
            home: { sort },
          } = getState() as RootState;
          try {
            const response = await http.post<CreateEventResponse>(signal)(
              '/event',
              {
                name,
                description: '',
                eventStartDate: DateTime.now().toISO(),
                eventEndDate: DateTime.now().toISO(),
              },
              // TODO: Fix the getState type
              { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
            );

            return { event: response.data.event, sort };
          } catch (error) {
            const { response } = error as AxiosError;
            return rejectWithValue(response?.data);
          }
        },
        {
          pending: (state) => {
            state.createEvent.status = 'loading';
          },
          fulfilled: (state, action) => {
            const event = action.payload?.event;
            state.createEvent.status = 'idle';

            if (event) {
              createSnackNotification(AlertLevel.Success, 'Created Event', event.name);
              state.createEvent.event = event;
            }
          },
          rejected: (state, action) => {
            const isString = (data: object): data is { message: string } => 'message' in data && typeof data.message === 'string';

            state.createEvent.status = 'failure';
            if (action.payload && isString(action.payload)) {
              state.createEvent.error = action.payload.message;
            } else {
              state.createEvent.error = action.error.message;
            }

            createSnackNotification(
              AlertLevel.Error,
              'Create Event Failed',
              state.createEvent.error
            );
          },
        }
      ),
      searchEvents: createHttpThunk(
        async (
          {
            searchTerm,
            pageSize,
            currentPage,
            dir,
            sort,
          }: {
            isPoll?: boolean;
            searchTerm: string;
            pageSize?: number;
            currentPage?: number;
            dir: string;
            sort: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const searchEventsQuery: SearchEventsQueryParams = {
            pageSize: pageSize ?? 10000,
            currentPage: currentPage ?? 1,
            ...(searchTerm
              ? {
                event: searchTerm,
                tag: searchTerm,
              }
              : {}),
            sortDirection: dir,
            sortBy: sort,
          };
          const response = await http.get<SearchEventsResponse>(signal)(
            `/events/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.events.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const { results } = action.payload;
            const resultsIds = results.map((event) => event.id);
            const { value: localStoragePendingCreateEvents } = getLocalStorage(
              'pendingCreateEvents'
            ) || { value: [] };
            const remainPendingEvents = localStoragePendingCreateEvents.filter(
              (event: Event) => !resultsIds.includes(event.id)
            );
            setLocalStorage('pendingCreateEvents', remainPendingEvents, 1);

            const updatedEventsLocalStorage = getUpdatedEventsLocalStorage(action.payload.results);

            state.events = {
              ...action.payload,
              // Use the updated event name from local storage, if available, instead of querying Elasticsearch
              results: action.payload.results.map(
                (event) => {
                  const updatedEvent = updatedEventsLocalStorage?.find((e) => e.value.eventId === event.id);
                  if (updatedEvent) {
                    return { ...event, name: updatedEvent.value.eventName };
                  }
                  return event;
                }
              ),
              pageSize: action.payload.pageSize ?? 10000,
              currentPage: action.payload.currentPage ?? 1,
              totalCount: action.payload.totalCount ?? 10,
              totalPages: action.payload.totalPages ?? 10,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              createSnackNotification(
                AlertLevel.Error,
                'Get events failed',
                action.error.message
              );
              state.events.status = 'failure';
            }
          },
        }
      ),
      searchAllEvents: createHttpThunk(
        async (
          {
            pageSize = 10000,
            currentPage = 1,
            dir = 'asc',
            sort = 'name',
          }: {
            pageSize?: number;
            currentPage?: number;
            dir?: string;
            sort?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const searchEventsQuery: SearchEventsQueryParams = {
            pageSize,
            currentPage,
            sortDirection: dir,
            sortBy: sort,
          };
          const response = await http.get<SearchEventsResponse>(signal)(
            `/events/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.allEvents.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.allEvents = {
              ...action.payload,
              pageSize: action.payload.pageSize ?? 10000,
              currentPage: action.payload.currentPage ?? 1,
              totalCount: action.payload.totalCount ?? 10,
              totalPages: action.payload.totalPages ?? 10,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get events failed',
              action.error.message
            );
            state.allEvents.status = 'failure';
          },
        }
      ),
      searchFiles: createHttpThunk(
        async (
          {
            searchTerm,
            pageSize,
            currentPage,
            dir,
            sort,
          }: {
            isPoll?: boolean;
            searchTerm: string;
            pageSize?: number;
            currentPage?: number;
            dir: string;
            sort: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const sortColumnMapping: { [key: string]: string } = {
            fileName: 'veritone-file.filename',
            uploadDate: 'createdTime',
          };
          const sortByColumn =
            sortColumnMapping[sort] || (sort ? sort : 'createdTime');

          const searchFilesQuery: SearchFilesQueryParams = {
            pageSize: pageSize ?? 10000,
            currentPage: currentPage ?? 1,
            sortDirection: dir ? dir : 'desc',
            sortBy: sortByColumn,
            file: searchTerm,
          };
          const response = await http.get<SearchFilesResponse>(signal)(
            `/files/${qs.stringify(searchFilesQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.files.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const updatedFilesLocalStorage = getUpdatedFilesLocalStorage(action.payload.results);

            state.files = {
              ...action.payload,
              // Use the updated fileName from local storage, if available, instead of querying Elasticsearch
              results: action.payload.results.map(
                (file) => {
                  const updatedFile = updatedFilesLocalStorage?.find((f) => f.value.fileId === file.id);
                  if (updatedFile) {
                    return { ...file, fileName: updatedFile.value.fileName };
                  }
                  return file;
                }
              ),
              pageSize: action.payload.pageSize ?? 10000,
              currentPage: action.payload.currentPage ?? 1,
              totalCount: action.payload.totalCount ?? 10,
              totalPages: action.payload.totalPages ?? 10,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              createSnackNotification(
                AlertLevel.Error,
                'Get file failed',
                action.error.message
              );
              state.files.status = 'failure';
            }
          },
        }
      ),
      poll: createHttpThunk(
        async (
          {
            type,
            searchTerm,
            pageSize,
            currentPage,
            dir,
            sort,
          }: {
            type: 'search-events' | 'search-files';
            searchTerm: string;
            pageSize?: number;
            currentPage?: number;
            dir: string;
            sort: string;
          },
          thunkAPI
        ) => {
          const { signal, dispatch } = thunkAPI;

          let dispatchSearchEvents: DispatchPromise;
          let dispatchSearchFiles: DispatchPromise;
          const pollInterval = setInterval(() => {
            if (type === 'search-events') {
              dispatchSearchEvents = dispatch(
                searchEvents({
                  searchTerm,
                  pageSize,
                  currentPage,
                  dir,
                  sort,
                  isPoll: true,
                })
              );
            }
            if (type === 'search-files') {
              dispatchSearchFiles = dispatch(
                searchFiles({
                  searchTerm,
                  pageSize,
                  currentPage,
                  dir,
                  sort,
                  isPoll: true,
                })
              );
            }
          }, 7500);

          signal.addEventListener('abort', () => {
            clearInterval(pollInterval);
            dispatchSearchEvents?.abort();
            dispatchSearchFiles?.abort();
          });
        }
      ),
      uploadFile: createHttpThunk(
        async (
          {
            fileIndex,
          }: {
            fileIndex: number;
          },
          thunkAPI
        ) => {
          const {
            getState,
            dispatch,
            signal,
            extra: { http },
          } = thunkAPI;
          const { filesToUpload, selectedEvent, createEvent } = (
            getState() as RootState
          ).upload;
          const fileToUpload = filesToUpload[fileIndex];
          const uploadToEvent = createEvent.event ?? selectedEvent;

          const deleteFile = async (fileId: string) => {
            try {
              await http.delete(signal)(`/file/${fileId}`, undefined, {
                // TODO: Fix the getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              });
            } catch (error) {
              console.error(error);
            }
          };

          if (!uploadToEvent) {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Please select an event'
            );
            return { status: 'idle' };
          }

          let uploadUrl = '';
          let fileId = '';
          let getUrl = '';

          try {
            const { data } = await http.post<UploadFileResponse>(signal)(
              '/file',
              {
                eventId: uploadToEvent.id,
                fileName: fileToUpload.name,
                fileType: fileToUpload.file.type,
              },
              // TODO: Fix the getState type
              { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
            );

            uploadUrl = data.uploadUrl;
            fileId = data.fileId;
            getUrl = data.getUrl;

          } catch (e) {
            throw new Error((e as AxiosError<{ message: string }>)?.response?.data?.message);
          }

          try {
            await http.raw({
              url: uploadUrl,
              method: 'put',
              data: fileToUpload.file,
              onUploadProgress: (progressEvent) => {
                dispatch({
                  type: 'upload/setProgress',
                  payload: { progress: progressEvent.progress, fileIndex },
                });
              },
              signal,
            });
          } catch (error) {
            console.error(error);
            await deleteFile(fileId);
            throw new Error(`Upload failed for ${fileToUpload.name}`);
          }

          try {
            await http.post<IngestFilePayload>(signal)(
              `/file/${fileId}/ingest`,
              {
                fileType: fileToUpload.file.type,
                getUrl
              },
              // TODO: Fix the getState type
              { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
            );

            // Only needed until fix is implemented on VE-5434
            // Add to local storage here ? Not sure how to get tdoId below in fulfilled area
            const fileName = fileToUpload.file.name;
            const uploadDate = DateTime.now().toISO();
            const eventId = uploadToEvent.id;
            const status = 'new';
            const id = fileId;
            const expirationDate = DateTime.now().plus({ hours: 1 }).toISO();

            const fileData: PendingUploadFile = {
              fileName,
              uploadDate,
              eventId,
              status,
              id,
              expirationDate,
            };

            // Retrieve existing pendingUploadFiles from localStorage
            const existingPendingUploads =
              localStorage.getItem('pendingUploadFiles');
            const pendingUploadFiles = existingPendingUploads
              ? JSON.parse(existingPendingUploads)
              : [];

            // Add new file data to the list
            pendingUploadFiles.push(fileData);

            // Store updated list back in localStorage
            localStorage.setItem(
              'pendingUploadFiles',
              JSON.stringify(pendingUploadFiles)
            );
            window.dispatchEvent(new Event('pendingUploadFiles'));
          } catch (e) {
            console.error(e);
            await deleteFile(fileId);
            throw new Error(
              `Ingestion failed to start for ${fileToUpload.name}`
            );
          }
        },
        {
          pending: (state, action) => {
            state.filesToUpload[action.meta.arg.fileIndex].status = 'loading';
          },
          fulfilled: (state, action) => {
            if (action.payload?.status && action.payload?.status === 'idle') {
              state.filesToUpload[action.meta.arg.fileIndex].status = 'idle';
              state.filesToUpload[action.meta.arg.fileIndex].complete = true;
            } else {
              state.filesToUpload[action.meta.arg.fileIndex].status =
                'complete';
              createSnackNotification(
                AlertLevel.Success,
                'Upload Complete',
                'File uploaded successfully'
              );
            }
          },
          rejected: (state, action) => {
            state.filesToUpload[action.meta.arg.fileIndex].status = 'failure';
            state.filesToUpload[action.meta.arg.fileIndex].error =
              action.error.message;
            createSnackNotification(
              AlertLevel.Error,
              'Upload Error',
              action.error.message
            );
          },
        }
      ),
    };
  },
  selectors: {
    selectEvents: (state) => state.events,
    selectAllEvents: (state) => state.allEvents,
    selectCreateEvent: (state) => state.createEvent,
    selectFilesToUpload: (state) => state.filesToUpload,
    selectSelectedEvent: (state) => state.selectedEvent,
    selectFiles: (state) => state.files,
  },
});

export const {
  searchEvents,
  searchAllEvents,
  searchFiles,
  createEvent,
  addFilesToUpload,
  setSelectedEvent,
  setCreatedEvent,
  removeFileToUpload,
  startUpload,
  uploadFile,
  setProgress,
  updateEvent,
  deleteEvent,
  updateFile,
  poll
} = uploadSlice.actions;

export const {
  selectEvents,
  selectSelectedEvent,
  selectAllEvents,
  selectFiles,
  selectCreateEvent,
  selectFilesToUpload,
} = uploadSlice.selectors;

export const { actions: uploadActions, reducer: uploadReducer } = uploadSlice;
