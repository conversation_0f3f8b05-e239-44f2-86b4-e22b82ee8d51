import { Context } from '../../../types';
import env from '../../../../env';
import { has } from 'lodash';
import { MatchGroup } from '../../../../../../types/tracker';
import { ActionError } from '@common/errors';

const updateLastSearchWithEngineId = async <
  ReqPayload,
  Data extends Partial<Pick<MatchGroup, 'searches'>> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<Pick<MatchGroup, 'searches'>>>
> => {
  const { data } = context;
  const { searches } = data;
  const { trackerEngineId } = env;

  if (!trackerEngineId) {
    throw new ActionError('Missing tracker engine id');
  }

  if (searches && searches.length > 0) {
    const lastSearch = searches[searches.length - 1];
    if (!has(lastSearch, 'engineId') && has(lastSearch, 'attributes')) {
      searches[searches.length - 1].engineId = trackerEngineId;
    }
  }

  const new_data = Object.assign({}, data, { searches });
  const new_context = Object.assign({}, context, { data: new_data });

  return new_context;
};

export default updateLastSearchWithEngineId;
