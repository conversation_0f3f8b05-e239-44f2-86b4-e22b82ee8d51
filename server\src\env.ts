import colors from 'colors/safe';
import { get, has } from 'lodash';
import fs from 'fs';
import path from 'path';

export type CloudType = 'azure' | 'aws';

interface RegistryIds {
  eventsRegistryId: string;
  matchGroupsRegistryId: string;
}

export interface Env {
  // TODO: [index: string]: any overrides the types here  -- find a better way
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [index: string]: any;
  apiRoot: string;
  credentialApi?: string;
  graphQLEndpoint: string;
  nodeEnv: string;
  port: number;
  serviceName: string;
  veritoneAppId: string;
  cloud: CloudType;
  blob?: {
    endpointSuffix: string;
    account: string;
    key: string;
    container: string;
    maxRetry?: number;
    expireSecs?: number;
  };
  s3?: {
    bucket: string;
    accessKey?: string;
    secretKey?: string;
    region: string;
    maxRetry?: number;
    expireSecs?: number;
  };
  trackerEngineId: string;
  validWritableCredentialEngineIds?: string[];
  glcIngestorEngineId: string;
  outputWriterEngineId: string;
  cpuClusterId?: string;
  gpuClusterId?: string;
  registryIds: RegistryIds;
  redis?: {
    host: string;
    port: number;
  };
  videoSliceEngineId: string;
  approxParamN?: number;
  approxParamNC?: number;
  adaptiveBoxPoolingThreshold?: number;
}

function DO_NOT_RequireFromEnv(
  config: unknown,
  path: string,
  defaultValue?: string | number
) {
  if (!has(config, path) && defaultValue === undefined) {
    console.info(`${colors.red('[WARNING] Missing env variable:')} ${path}`);
    return;
  }
  return has(config, path) ? config[path] : defaultValue;
}

function requireFromEnv(
  config: Env | undefined,
  path: string,
  defaultValue?: string | number
) {
  if (!has(config, path) && defaultValue === undefined) {
    console.error(`${colors.red('[ERROR] Missing env variable:')} ${path}`);
    return process.exit(1);
  }
  return has(config, path) ? config[path] : defaultValue;
}

let cachedConfig: Env;

function loadApiConfig(): Env | undefined {
  try {
    const configPath = path.join(__dirname, '../apiConfig.json');
    const configFile = fs.readFileSync(configPath, 'utf8');
    cachedConfig = JSON.parse(configFile);
    return cachedConfig;
  } catch (error) {
    console.error(`Failed to read or parse the configuration file: ${error}`);
  }
}

function getApiConfig(): Env | undefined {
  if (!cachedConfig) {
    return loadApiConfig();
  }
  return cachedConfig;
}

function buildEnvironment(config?: Env) {
  const env: Env = {
    apiRoot: requireFromEnv(config, 'apiRoot'),
    graphQLEndpoint: requireFromEnv(config, 'graphQLEndpoint'),
    nodeEnv: requireFromEnv(config, 'nodeEnv'),
    serviceName: requireFromEnv(config, 'serviceName'),
    // port is currently hardcoded in the nginx config so only makes
    // sense to be configurable when doing local dev
    port: requireFromEnv(config, 'port', 3002),
    cloud: requireFromEnv(config, 'cloud'),
    trackerEngineId: requireFromEnv(config, 'trackerEngineId'),
    validWritableCredentialEngineIds: get(
      config,
      'validWritableCredentialEngineIds'
    ),
    registryIds: requireFromEnv(config, 'registryIds'),
    veritoneAppId: requireFromEnv(config, 'veritoneAppId'),
    cpuClusterId:
      requireFromEnv(config, 'cpuClusterId', '') ||
      DO_NOT_RequireFromEnv(config, 'defaultClusterId'),
    gpuClusterId:
      requireFromEnv(config, 'gpuClusterId', '') ||
      DO_NOT_RequireFromEnv(config, 'defaultClusterId'),
    glcIngestorEngineId: requireFromEnv(config, 'glcIngestorEngineId'),
    outputWriterEngineId: requireFromEnv(config, 'outputWriterEngineId'),
    redis: get(config, 'redis'),
    videoSliceEngineId: requireFromEnv(config, 'videoSliceEngineId'),
    credentialApi: get(config, 'credentialApi'),
    useRedis: requireFromEnv(config, 'useRedis'),
    approxParamK: get(config, 'approxParamK'),
    approxParamNC: get(config, 'approxParamNC'),
    adaptiveBoxPoolingThreshold: get(config, 'adaptiveBoxPoolingThreshold'),
  };
  if (env.cloud === 'azure') {
    requireFromEnv(config, 'blob.endpointSuffix');
    requireFromEnv(config, 'blob.account');
    requireFromEnv(config, 'blob.key');
    requireFromEnv(config, 'blob.container');
    env.blob = requireFromEnv(config, 'blob');
  } else if (env.cloud === 'aws') {
    requireFromEnv(config, 's3.bucket');
    requireFromEnv(config, 's3.region');
    env.s3 = requireFromEnv(config, 's3');
  }
  return env;
}

export function loadEnvironment() {
  return buildEnvironment(loadApiConfig());
}

const apiConfig = getApiConfig();

const environment = buildEnvironment(apiConfig);

export default environment;
