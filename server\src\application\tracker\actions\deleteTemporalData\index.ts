import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';

const deleteTemporalData = async <
  ReqPayload,
  Data extends Partial<responses.getFile> & { fileId?: string; orgId?: string },
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { req, log, data, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };

  const fileId = req.params?.fileId ?? data?.fileId;
  if (!fileId) {
    throw new ActionError('No fileId provided');
  }

  try {
    await callGQL<responses.deleteTemporalData, ReqPayload, Data>(
      context,
      headers,
      queries.deleteTemporalData,
      { tdoId: fileId }
    );
    if (redisWrapper && data.file && data.orgId) {
      await redisWrapper.event.del(data.file.eventId, data.orgId);
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }

  return context;
};

export default deleteTemporalData;
