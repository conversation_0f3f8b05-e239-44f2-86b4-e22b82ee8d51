import {
    findFrameIndex,
    convertFrameIndexToTimeMs,
    frameAlignStartTimeMs,
    frameAlignStopTimeMs,
  } from './frames';
  
  describe('findFrameIndex', () => {
    it('should return the correct frame index', () => {
      expect(findFrameIndex(0, 30)).toBe(0);
      expect(findFrameIndex(1000, 30)).toBe(30);
      expect(findFrameIndex(2000, 30)).toBe(60);
      expect(findFrameIndex(333, 24)).toBe(7);
      expect(findFrameIndex(10000, 60)).toBe(600);
    });
  });
  
  describe('convertFrameIndexToTimeMs', () => {
    it('should convert frame index to time in milliseconds', () => {
      expect(convertFrameIndexToTimeMs(0, 30)).toBe(0);
      expect(convertFrameIndexToTimeMs(30, 30)).toBe(1000);
      expect(convertFrameIndexToTimeMs(60, 30)).toBe(2000);
      expect(convertFrameIndexToTimeMs(8, 24)).toBeCloseTo(333.333, 3);
      expect(convertFrameIndexToTimeMs(600, 60)).toBe(10000);
    });
  });
  
  describe('frameAlignStartTimeMs', () => {
    it('should align start time with nearest frame start time', () => {
      // simple tests
      expect(frameAlignStartTimeMs(0, 30)).toBe(0);
      expect(frameAlignStartTimeMs(1000, 30)).toBe(1000);
      expect(frameAlignStartTimeMs(2000, 30)).toBe(2000);
      expect(frameAlignStartTimeMs(1000, 24)).toBe(1000);
      expect(frameAlignStartTimeMs(10000, 60)).toBe(10000);
  
      // test multiple frame index positions at various frame rates
      const frameRates = [1, 3, 10, 15, 24, 25, 30, 60];
      frameRates.forEach((fps) => {
        const frameDuration = 1000 / fps;
        // test first 100 frames
        for (let frameIndex = 0; frameIndex < 100; frameIndex++) {
          const frameStartTimeMs = (1000 * frameIndex) / fps;
  
          expect(frameAlignStartTimeMs(frameStartTimeMs, fps)).toBeCloseTo(
            frameStartTimeMs,
            3
          ); // should map back to self
          expect(
            frameAlignStartTimeMs(Math.round(frameStartTimeMs), fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // rounded to nearest ms
          expect(
            frameAlignStartTimeMs(Math.round(frameStartTimeMs * 10) / 10.0, fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // 1 significant digit ms precision
          expect(
            frameAlignStartTimeMs(Math.ceil(frameStartTimeMs), fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // ceil to ms
          expect(
            frameAlignStartTimeMs(Math.floor(frameStartTimeMs), fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // floor to ms
          expect(
            frameAlignStartTimeMs(frameStartTimeMs + 0.5 * frameDuration, fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // still in frame
          expect(
            frameAlignStartTimeMs(frameStartTimeMs + 0.1 * frameDuration, fps)
          ).toBeCloseTo(frameStartTimeMs, 3); // still in frame
          if (frameIndex > 0) {
            expect(frameAlignStartTimeMs(frameStartTimeMs - 1, fps)).toBeCloseTo(
              frameStartTimeMs - frameDuration,
              3
            ); // now in previous
          }
        }
      });
    });
  });
  
  describe('frameAlignStopTimeMs', () => {
    it('should align stop time with nearest frame start time', () => {
      // simple tests
      expect(frameAlignStopTimeMs(0, 30)).toBeCloseTo(0, 3); // somehow this returns as -0 ?
      expect(frameAlignStopTimeMs(1000, 30)).toBe(1000);
      expect(frameAlignStopTimeMs(2000, 30)).toBe(2000);
      expect(frameAlignStopTimeMs(333, 24)).toBeCloseTo(333.333, 3);
      expect(frameAlignStopTimeMs(10000, 60)).toBe(10000);
  
      // test multiple frame index positions at various frame rates
      const frameRates = [1, 3, 10, 15, 24, 25, 30, 60];
      frameRates.forEach((fps) => {
        const frameDuration = 1000 / fps;
        // test first 100 frames
        for (let frameIndex = 0; frameIndex < 100; frameIndex++) {
          const frameStopTimeMs = (1000 * (frameIndex + 1)) / fps;
  
          expect(frameAlignStopTimeMs(frameStopTimeMs, fps)).toBeCloseTo(
            frameStopTimeMs,
            3
          ); // should map back to self
          expect(
            frameAlignStopTimeMs(Math.round(frameStopTimeMs), fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // rounded to nearest ms
          expect(
            frameAlignStopTimeMs(Math.round(frameStopTimeMs * 10) / 10.0, fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // 1 significant digit ms precision
          expect(
            frameAlignStopTimeMs(Math.ceil(frameStopTimeMs), fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // ceil to ms
          expect(
            frameAlignStopTimeMs(Math.floor(frameStopTimeMs), fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // floor to ms
          expect(
            frameAlignStopTimeMs(frameStopTimeMs - 0.5 * frameDuration, fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // still in frame
          expect(
            frameAlignStopTimeMs(frameStopTimeMs - 0.1 * frameDuration, fps)
          ).toBeCloseTo(frameStopTimeMs, 3); // still in frame
          expect(frameAlignStopTimeMs(frameStopTimeMs + 1, fps)).toBeCloseTo(
            frameStopTimeMs + frameDuration,
            3
          ); // now in previous
        }
      });
    });
  });
  
