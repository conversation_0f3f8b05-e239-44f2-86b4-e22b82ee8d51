.breadcrumbs {
  padding: 20px 30px;

  .MuiBreadcrumbs-ol {
    .MuiBreadcrumbs-li {
      &:last-child {
        pointer-events: none;

        a, .material-symbols-outlined {
          color: var(--text-primary);
        }
      }
    }
  }

  .MuiBreadcrumbs-separator {
    margin-left: 10px;
    margin-right: 10px;
  }

  .MuiBreadcrumbs-li {
    a {
      display: flex;
      justify-content: center;
      align-items: center;
      user-select: none;
      color: var(--link);

      .material-symbols-outlined,
      .material-icons {
        font-size: 18px;
        margin-right: 5px;
        line-height: 24px;
        margin-bottom: 2px;
        color: var(--link);

        &.disabled {
          color: var(--text-primary);
        }
      }

      &.MuiLink-underlineHover {
        &:hover {
          text-decoration: none;
        }
      }

      &.disabled {
        pointer-events: none;
      }
    }
  }

  .breadcrumbs__text {
    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }

    &.disabled {
      color: var(--text-primary);
      user-select: none;

      &:hover {
        text-decoration: none;
        cursor: initial;
      }
    }
  }

  .breadcrumbs__separator {
    font-weight: 900;
    font-size: 16px;
  }

  .breadcrumbs__padding {
    margin-left: 8px;
    margin-right: 8px;
  }

  .breadcrumbs__link-container {
    display: flex;
    align-items: center;
  }

  .breadcrumbs-last {
    display: flex;
    align-items: center;

    .material-symbols-outlined,
    .material-icons {
      font-size: 18px;
      margin-right: 5px;
      line-height: 24px;
      margin-bottom: 2px;
      color: var(--text-primary);
      cursor: default;
    }

    .breadcrumbs-last__text {
      color: var(--text-primary);

      &:hover {
        text-decoration: none;
        cursor: default;
      }
    }
  }
}