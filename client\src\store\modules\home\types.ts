import { ApiStatus } from '@store/types';
import {
  SearchEventsResponse,
  SearchFilesResponse,
} from '@shared-types/responses';

import { Event, File, } from '@shared-types/tracker';
export interface EventsApi {
  data: {
    id: number;
    name: string;
    date: string;
  }[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

// export interface FilesApi {
//   data: {
//     id: number;
//     name: string;
//     status: FileStatus;
//   }[];
//   currentPage: number;
//   pageSize: number;
//   totalCount: number;
//   totalPages: number;
// }

export interface EventsState extends SearchEventsResponse {
  status: ApiStatus;
  error?: string;
}

export interface EventState {
  event?: Event;
  status: ApiStatus;
  error?: string;
}

export interface FilesState extends SearchFilesResponse {
  status: ApiStatus;
  error?: string;
}

export interface FileState {
  file?: File;
  status: ApiStatus;
  error?: string;
}
