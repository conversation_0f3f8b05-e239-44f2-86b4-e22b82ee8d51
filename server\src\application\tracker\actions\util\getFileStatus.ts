import { FileStatus } from '../../../../../../types/tracker';

const getFileStatus = (tdoJobs: {
  id: string;
  jobs?: { records: { name: string; status: string }[] };
}): FileStatus => {
  const ingestJobStatus =
    tdoJobs?.jobs?.records?.find((j) => j.name === 'Track Ingest Job')
      ?.status ?? 'no status';
  switch (ingestJobStatus) {
    case 'no status':
      return 'unknown';
    case 'running':
      return 'processing';
    case 'complete':
      return 'processed';
    case 'failed':
    case 'cancelled':
    case 'aborted':
      return 'error';
    case 'pending':
    case 'queued':
    case 'accepted':
    case 'standby_pending':
    case 'waiting':
    case 'resuming':
    case 'paused':
    default:
      return 'pending';
  }
};

export default getFileStatus;
