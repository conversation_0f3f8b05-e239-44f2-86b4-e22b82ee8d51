import {
  Alert<PERSON>evel,
  Breadcrumbs,
  Column,
  NoSearchResults,
  SearchAndUpload,
  Table,
  createSnackNotification,
} from '@components/common';
import { FileRow, MatchGroupRow } from '@components/index';
import {
  Autocomplete,
  Button,
  Chip,
  CircularProgress,
  SvgIcon,
  Tab,
  Tabs,
  TextField,
  Skeleton,
} from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker';
import {
  File,
  GeneratedTimelineWithMatchGroup,
  MatchGroup,
} from '@shared-types/tracker';
import { useAppDispatch } from '@store/hooks';
import {
  getEventById,
  getFiles,
  getGeneratedTimeline,
  getMatchGroups,
  getTags,
  selectEvent,
  selectFiles,
  selectGeneratedTimelines,
  selectMatchGroups,
  selectTags,
  setFilesPagination,
  updateEventById,
  poll,
  setAttributes,
  selectAttributes,
  selectFileSelected,
  updateFileSelected,
  updateFileById,
} from '@store/modules/event/slice';
import { EventState } from '@store/modules/event/types';
import cn from 'classnames';
import { isEmpty, isEqual, isNil, keys } from 'lodash';
import { DateTime } from 'luxon';
import {
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  MouseEvent,
  useRef,
  useState,
} from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import CancelIcon from './cancel.svg?react';
import EditIcon from './edit.svg?react';
import SaveIcon from './save.svg?react';
import './Event.scss';

import TimelineDetail from '@components/TimelineGeneratedVideos/TimelineDetail';
import TimelineGeneratedVideos from '@components/TimelineGeneratedVideos/TimelineGeneratedVideos';

import { bytesToMb, toLocalTime, millisToTimeFormatted } from '@utility/convert';
import { MediaPlayer } from '@veritone/glc-react';
import { PlayerReference } from 'video-react';
import ConfirmDialog from '@components/common/ConfirmDialog';
import { deleteFile, selectFileDeletion } from '@store/modules/file/slice';
import {
  PendingUploadFile,
  selectSelectedEvent,
  updateEvent as updateEventSearch,
} from '@store/modules/upload/slice';
import { getEventById as getEventByIdHome, selectEventSelected, updateEvent as updateEventHome } from '@store/modules/home/<USER>';
import { ExpiringString, isExpiringString } from '@utility/expiringString';
import PendingUploadFileRow from '@components/rows/PendingUploadFileRow/PendingUploadFileRow';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';

const inputStyle = {
  '& .MuiOutlinedInput-root': {
    height: '32px',
    marginRight: '4px',
    marginTop: '4px',
    borderRadius: '6px',
  },
};

const Event = () => {
  const intl = useIntl();
  const { eventId } = useParams();
  const [tab, setTab] = useState('files');
  const [isEditing, setIsEditing] = useState(false);
  const [open, setOpen] = useState(false);
  const [tagOptions, setTagOptions] = useState<string[]>([]);
  const [sort, setSort] = useState<{ sort: string; dir: string }>();
  const [selectedTimeline, setSelectedTimeline] =
    useState<GeneratedTimelineWithMatchGroup | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const playerRef = useRef<PlayerReference | null>(null);

  const [editableFile, setEditableFile] = useState<File | undefined>();

  const navigate = useNavigate();
  const { search } = useLocation();
  const dispatch = useAppDispatch();
  const files = useSelector(selectFiles);
  const fileSelected = useSelector(selectFileSelected);
  const tags = useSelector(selectTags);
  const matchGroups = useSelector(selectMatchGroups);
  const event: EventState = useSelector(selectEvent);
  const generatedTimelines = useSelector(selectGeneratedTimelines);
  // const eventDeleted = useSelector(selectEventDeletion);
  const eventSelected = useSelector(selectEventSelected);
  const selectedEvent = useSelector(selectSelectedEvent);
  const fileDeletion = useSelector(selectFileDeletion);
  const attributes = useSelector(selectAttributes);
  const [editableEvent, setEditableEvent] = useState(event);
  const [deletedFileIds, setDeletedFileIds] = useState<string[]>([]);
  const [pendingUploadFiles, setPendingUploadFiles] = useState<
    PendingUploadFile[]
  >([]);

  const hasMatchGroupSearchResults =
    matchGroups?.results?.length > 0 && matchGroups.status !== 'loading';
  const streams = fileSelected.file?.streams;
  const thumbnailAssets = fileSelected.file?.thumbnailAssets;
  const frameRate = fileSelected.file?.frameRate;

  const searchParams = useMemo(
    () => Object.fromEntries(new URLSearchParams(search)),
    [search]
  );
  const { activeTab, page, limit } = searchParams;

  const pollPromiseRef = useRef<{ [pollKey: string]: DispatchPromise }>({});

  const doPoll = useCallback(
    (
      pollType: 'event' | 'files' | 'match-groups' | 'generated-timeline',
      dispatchPromise: DispatchPromise
    ) => {
      const { current: pollPromise } = pollPromiseRef;
      pollPromise?.[pollType]?.abort();
      if (
        ['event', 'files', 'match-groups', 'generated-timeline'].includes(
          pollType
        )
      ) {
        pollPromise[pollType] = dispatch(
          // @ts-expect-error TODO: can we type dispatch promise better to make this work?
          poll({ type: pollType, ...dispatchPromise?.arg })
        );
      }
      return dispatchPromise;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [pollPromiseRef.current]
  );

  useEffect(() => {
    if (tags?.status !== 'loading' && tags?.results.length > 0) {
      setTagOptions(tags.results);
      return;
    }
    setTagOptions([]);
  }, [tags]);

  useEffect(() => {
    const tabs = ['files', 'matchGroups', 'timelineGeneratedVideos'];
    if (activeTab && tabs.includes(activeTab)) {
      setTab(activeTab);
    } else {
      navigate(
        '?' +
        new URLSearchParams({ ...searchParams, activeTab: tab }).toString(),
        { replace: true }
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, eventId, dispatch]);

  useEffect(() => {
    const { current: pollPromise } = pollPromiseRef;

    // Disable event polling.
    // 1. The polling will override the event editing. If we disable polling when
    // editing starts, and enable it after saving or canceling, there will be
    // issue that the event polling read old before the saving is completed.
    // 2. The event polling is unnecessary. The event data does not change unless it is edited. So,
    // the editing will take care of event data.
    // eventId && doPoll('event', dispatch(getEventById({ eventId })));

    if (eventId) {
      dispatch(getEventById({ eventId }));
    }
    dispatch(getTags());

    return () => {
      Object.values(pollPromise).forEach((dp) => dp?.abort());
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (event.status !== 'loading') {
      setEditableEvent(event);
    }
  }, [event]);

  useEffect(() => {
    if (tab === 'files') {
      doPoll(
        'files',
        dispatch(
          getFiles({
            page: page ? Number(page) : 1,
            limit: limit ? Number(limit) : 10,
            eventId,
            ...(sort ? sort : {}),
          })
        )
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sort, page, limit]);

  useEffect(() => {
    const deletedFileIdsStr = localStorage.getItem('deletedFileIds');
    if (!deletedFileIdsStr) {
      return;
    }
    try {
      const parsedDeletedFileIds = JSON.parse(deletedFileIdsStr);
      const deletedFileIds = Array.isArray(parsedDeletedFileIds)
        ? parsedDeletedFileIds
        : [];

      const now = new Date().getTime();

      // Filter out expired items
      const validFileIds = deletedFileIds.filter(
        (item): item is ExpiringString =>
          isExpiringString(item) && now < item.expiry
      );

      // Update localStorage and state
      localStorage.setItem('deletedFileIds', JSON.stringify(validFileIds));
      setDeletedFileIds(validFileIds.map((item) => item.value));
    } catch (e) {
      console.error(e);
    }
  }, [files, fileDeletion]);

  useEffect(() => {
    if (selectedTimeline && generatedTimelines) {
      const foundTimeline = generatedTimelines.results.find(
        (timeline) => timeline.id === selectedTimeline.id
      );


      if (foundTimeline && foundTimeline?.status !== selectedTimeline.status) {
        setSelectedTimeline(foundTimeline);
      }
    }
  }, [selectedTimeline, generatedTimelines]);

  // Only needed until fix is implemented on VE-5434
  useEffect(() => {
    const handlePendingUploadFiles = () => {
      const pendingUploadFilesStr = localStorage.getItem('pendingUploadFiles');
      if (!pendingUploadFilesStr) {
        return;
      }
      try {
        const parsedPendingUploadFiles = JSON.parse(pendingUploadFilesStr);
        const pendingUploadFiles: PendingUploadFile[] = Array.isArray(
          parsedPendingUploadFiles
        )
          ? parsedPendingUploadFiles
          : [];

        const now = new Date().getTime();

        // Filter out files that are found in the current files list
        const updatedPendingUploadFiles: PendingUploadFile[] =
          pendingUploadFiles.filter(
            (file) =>
              !files.results.some((f) => f.id === file.id) &&
              new Date(file.expirationDate).getTime() > now
          );

        // Update localStorage and state
        localStorage.setItem(
          'pendingUploadFiles',
          JSON.stringify(updatedPendingUploadFiles)
        );

        const pendingUploadFilesByEventId = updatedPendingUploadFiles.filter(
          (file) => file.eventId === eventId
        );

        setPendingUploadFiles(pendingUploadFilesByEventId);
      } catch (e) {
        console.error(e);
      }
    };

    handlePendingUploadFiles();

    window.addEventListener('pendingUploadFiles', handlePendingUploadFiles);

    return () => {
      window.removeEventListener(
        'pendingUploadFiles',
        handlePendingUploadFiles
      );
    };
  }, [eventId, files]);

  useEffect(() => {
    if (keys(attributes.person).length === 0 && keys(attributes.vehicle).length === 0) {
      dispatch(setAttributes());
    }
  }, [dispatch, attributes]);

  const handleFileClick = (rowData: File) => {
    dispatch(updateFileSelected({ ...rowData }));
  };
  const handleFileDoubleClick = (rowData: File) => {
    if (rowData.status === 'processed') {
      navigate(`/event/${rowData.eventId}/file/${rowData.id}`);
    } else {
      const notificationText =
        rowData.status === 'processing' || rowData.status === 'pending'
          ? 'File has not finished processing'
          : 'File failed to process';
      createSnackNotification(
        rowData.status === 'processing' || rowData.status === 'pending'
          ? AlertLevel.Warning
          : AlertLevel.Error,
        rowData.status === 'processing' || rowData.status === 'pending'
          ? 'Warning'
          : 'Error',
        notificationText
      );
    }
  };
  const handleTimelineClick = (rowData: GeneratedTimelineWithMatchGroup) => {
    setSelectedTimeline({ ...rowData });
  };

  const handleTabChange = (_: SyntheticEvent, newTab: string) => {
    navigate(
      `?` +
      new URLSearchParams({
        ...searchParams,
        activeTab: newTab,
      }).toString()
    );
    setTab(newTab);
    dispatch(updateFileSelected(undefined));
    setSelectedTimeline(null);
  };

  const saveEditedEvent = () => {
    if (!isEqual(event, editableEvent)) {
      if (selectedEvent) {
        editableEvent.name = editableEvent.name.trim();
        const { error, status, ...rest } = editableEvent;
        const updatedEvent = { ...selectedEvent, ...rest };
        dispatch(updateEventHome(updatedEvent));
        dispatch(updateEventSearch(updatedEvent));
      }
      dispatch(
        updateEventById({ eventId: editableEvent.id, ...editableEvent })
      );
    }
    setIsEditing(false);
  };

  const cancelEditedEvent = () => {
    setEditableEvent(event);
    setIsEditing(false);
  };

  const handleFileNameSave = () => {
    if (!editableFile) {
      return;
    }

    const fileData = files.results;
    const isNameChanged = fileData.some(file => file.id === editableFile.id && file.fileName !== editableFile.fileName);
    if (!isNameChanged) {
      return;
    }

    dispatch(
      updateFileById({
        fileId: editableFile.id,
        fileName: editableFile.fileName.trim()
      })
    );
  };

  const setSortSearchParameters = ({
    column: sort,
    direction: dir,
  }: {
    column?: string;
    direction: string;
  }) => {
    navigate(
      `?` +
      new URLSearchParams({
        ...searchParams,
        page: '1',
        limit: '10',
      }).toString()
    );
    setSort(sort ? { sort, dir } : undefined);
  };

  const renderDetailContainer = () => {
    if (fileSelected.file) {
      return (
        <>
          <div
            className="event__detail-content"
            data-testid="event-detail-content"
          >
            <div
              className="event__detail-preview-container"
              data-testid="event__detail-preview-container"
            >
              {streams && frameRate ? (
                <MediaPlayer
                  ref={playerRef}
                  uri=""
                  streams={streams}
                  frameRate={frameRate}
                  maxHeight={500}
                  thumbnailAssets={thumbnailAssets}
                />
              ) : null}
            </div>
            <div className="event__detail-info">
              <div
                className="event__detail-name"
                data-testid="event-detail-name"
              >
                {editableFile && (
                  <div className='event__detail-file-name-container'>
                    <TextField
                      hiddenLabel
                      defaultValue="Normal"
                      variant="filled"
                      size="small"
                      value={editableFile.fileName}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleFileNameSave();
                          setEditableFile(undefined);
                        }
                      }}
                      onChange={(e) => {
                        setEditableFile({
                          ...editableFile,
                          fileName: e.target.value,
                        });
                      }}
                      slotProps={{
                        htmlInput: {
                          className: 'event__detail-file-name-textfield',
                          'data-testid': 'event-detail-name-textfield',
                        },
                        input: {
                          disableUnderline: true,
                        },
                      }}
                    />
                    <div
                      className="material-icons"
                      onClick={() => {
                        setEditableFile(undefined);
                      }}
                    >
                      cancel
                    </div>
                    <div
                      className="material-icons"
                      onClick={() => {
                        handleFileNameSave();
                        setEditableFile(undefined);
                      }}
                    >
                      save
                    </div>
                  </div>)}
                {!editableFile && (
                  <>
                    {files.status === 'loading' || fileSelected.status === 'loading' ? (
                      <div>
                        <Skeleton
                          className="event__detail-file-name"
                          variant="rectangular"
                          width={150}
                          height={15}
                        />
                      </div>
                    ) : (
                      <div className="event__detail-file-name-container">
                        <Tooltip title={fileSelected.file.fileName}>
                          <div
                            className="event__detail-file-name"
                            data-testid="event-detail-file-name"
                            onClick={(e: MouseEvent<HTMLDivElement>) => {
                              if (e.detail === 2) {
                                setEditableFile(fileSelected.file);
                              }
                            }}
                          >
                            {fileSelected.file.fileName}
                          </div>
                        </Tooltip>
                        <div
                          className="material-icons"
                          data-testid="event-detail-file-name-edit"
                          onClick={() => {
                            setEditableFile(fileSelected.file);
                          }}
                        >
                          edit
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div
                className="event__detail-info-text"
                data-testid="event-detail-date"
              >
                <span>
                  {`${intl.formatMessage({ id: 'uploaded', defaultMessage: 'Uploaded' })}:`}
                </span>
                <span>
                  {I18nTranslate.TranslateDate(toLocalTime(fileSelected.file?.uploadDate))}
                </span>
              </div>
              {fileSelected.file.status === 'processed' &&
                <div
                  className="event__detail-info-text"
                  data-testid="event-detail-location"
                >
                  <span>
                    {`${intl.formatMessage({ id: 'fileGPSLocation', defaultMessage: 'File GPS Location' })}:`}
                  </span>
                  <span>
                    {
                      isNil(fileSelected.file?.location)
                        || fileSelected.file.location === 'unavailable'
                        ? intl.formatMessage({ id: 'unavailable', defaultMessage: 'Unavailable' })
                        : fileSelected.file.location
                    }
                  </span>
                </div>
              }
              <div
                className="event__detail-info-text"
                data-testid="event-detail-type"
              >
                <span>
                  {`${intl.formatMessage({ id: 'fileType', defaultMessage: 'File Type' })}:`}
                </span>
                <span>{fileSelected.file.fileType}</span>
              </div>
              {fileSelected.file.status === 'processed' &&
                <>
                  <div
                    className="event__detail-info-text"
                    data-testid="event-detail-size"
                  >
                    <span>
                      {`${intl.formatMessage({ id: 'fileSize', defaultMessage: 'File Size' })}:`}
                    </span>
                    <span>
                      {`${bytesToMb(fileSelected.file.fileSize ?? 0)} ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`}
                    </span>
                  </div>
                  <div
                    className="event__detail-info-text"
                    data-testid="event-detail-length"
                  >
                    <span>
                      {`${intl.formatMessage({ id: 'videoLength', defaultMessage: 'Video Length' })}:`}
                    </span>
                    <span>{millisToTimeFormatted(fileSelected.file.length * 1000)}</span>
                  </div>
                </>
              }
            </div>
          </div>
          <div className="event__detail-actions">
            <Button
              data-testid="event-view-file-button"
              className={cn('event__file-view', {
                disabled: fileSelected.file.status !== 'processed',
              })}
              onClick={() => {
                if (
                  fileSelected.file &&
                  fileSelected.file.status &&
                  fileSelected.file.status !== 'processed'
                ) {
                  const notificationText =
                    fileSelected.file.status === 'processing' ||
                      fileSelected.file.status === 'pending'
                      ? 'File has not finished processing'
                      : 'File failed to process';
                  createSnackNotification(
                    fileSelected.file.status === 'processing' ||
                      fileSelected.file.status === 'pending'
                      ? AlertLevel.Warning
                      : AlertLevel.Error,
                    fileSelected.file.status === 'processing' ||
                      fileSelected.file.status === 'pending'
                      ? 'Warning'
                      : 'Error',
                    notificationText
                  );
                  return;
                }
                navigate(`/event/${eventId}/file/${fileSelected.file?.id}`);
              }}
              onAuxClick={() => {
                if (
                  fileSelected.file &&
                  fileSelected.file.status &&
                  fileSelected.file.status === 'processed'
                ) {
                  window.open(
                    `/event/${eventId}/file/${fileSelected.file.id}`,
                    '_blank'
                  );
                }
              }}
            >
              <img src="/check-circle.svg" alt="check" />
              <span>
                {I18nTranslate.TranslateMessage('viewFile')}
              </span>
            </Button>
            <Button
              data-testid="event-delete-file-button"
              onClick={() => setDeleteDialogOpen(true)}
            >
              <img src="/x-circle.svg" alt="check" />
              <span>
                {I18nTranslate.TranslateMessage('deleteFile')}
              </span>
            </Button>
            {deleteDialogOpen && (
              <ConfirmDialog
                title={intl.formatMessage({ id: 'deleteFile', defaultMessage: 'Delete File' })}
                open={deleteDialogOpen}
                content={intl.formatMessage({ id: 'deleteFileMessage', defaultMessage: 'You are about to delete {fileName}. Are you sure you want to delete it?' }, { fileName: fileSelected.file.fileName })}
                confirmText={intl.formatMessage({ id: 'delete', defaultMessage: 'Delete' })}
                cancelText={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
                onConfirm={() => {
                  if (fileSelected.file) {
                    dispatch(deleteFile(fileSelected.file.id)).then(() => {
                      if (eventSelected.event) {
                        dispatch(getEventByIdHome({ eventId: eventSelected.event.id }));
                      }
                    });
                  }
                  dispatch(updateFileSelected(undefined));
                  setDeleteDialogOpen(false);
                }}
                onClose={() => setDeleteDialogOpen(false)}
              />
            )}
          </div>
        </>
      );
    }
    if (selectedTimeline) {
      return (
        <TimelineDetail
          timeline={selectedTimeline}
          setSelectedTimeline={setSelectedTimeline}
        />
      );
    }
  };

  return (
    <div className="event" data-testid="event-container">
      <div className="event__main-content">
        <div className="event__tables" data-testid="event-tables">
          <Breadcrumbs event={event} loading={event.status === 'loading'} />
          <div className="event__heading-container">
            {isEditing && (
              <>
                <div className="event__heading-name-date">
                  <div
                    className="event__heading-name"
                  >
                    <TextField
                      hiddenLabel
                      className="event__tables-name-textfield"
                      variant="outlined"
                      size="small"
                      value={editableEvent.name}
                      onChange={(e) => {
                        setEditableEvent({
                          ...editableEvent,
                          name: e.target.value,
                        });
                      }}
                      inputProps={{
                        'data-testid': 'event-change-name',
                      }}
                      sx={inputStyle}
                    />
                  </div>
                  <div className="event__tables-date-and-created-by-container">
                    <LocalizationProvider dateAdapter={AdapterLuxon}>
                      <MobileDateTimePicker
                        value={DateTime.fromISO(editableEvent.eventStartDate)}
                        onChange={(
                          e: DateTime<true> | DateTime<false> | null
                        ) => {
                          const startDate = e?.toUTC().toISO();
                          if (startDate) {
                            setEditableEvent({
                              ...editableEvent,
                              eventStartDate: startDate,
                            });
                          }
                        }}
                        maxDateTime={DateTime.fromISO(
                          editableEvent.eventEndDate
                        )}
                        sx={inputStyle}
                      />
                      <MobileDateTimePicker
                        value={DateTime.fromISO(editableEvent.eventEndDate)}
                        onChange={(
                          e: DateTime<true> | DateTime<false> | null
                        ) => {
                          const endDate = e?.toUTC().toISO();
                          if (endDate) {
                            setEditableEvent({
                              ...editableEvent,
                              eventEndDate: endDate,
                            });
                          }
                        }}
                        minDateTime={DateTime.fromISO(
                          editableEvent.eventStartDate
                        )}
                        sx={inputStyle}
                      />
                    </LocalizationProvider>
                    <div className="event__tables-actions">
                      <Tooltip title={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })} arrow>
                        <SvgIcon
                          className="event-icon"
                          data-testid="event-cancel-event"
                          onClick={cancelEditedEvent}
                        >
                          <CancelIcon className="svg-icon" />
                        </SvgIcon>
                      </Tooltip>
                      <Tooltip title={intl.formatMessage({ id: 'save', defaultMessage: 'Save' })} arrow>
                        <SvgIcon
                          className="event-icon"
                          data-testid="event-save-event"
                          onClick={saveEditedEvent}
                        >
                          <SaveIcon className="svg-icon" />
                        </SvgIcon>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className="event__tables-desc-container">
                  <TextField
                    hiddenLabel
                    className="event__tables-desc-textfield"
                    variant="outlined"
                    size="small"
                    placeholder={intl.formatMessage({ id: 'enterDescriptionOrNotes', defaultMessage: 'Enter description or notes...' })}
                    value={editableEvent.description}
                    onChange={(e) => {
                      setEditableEvent({
                        ...editableEvent,
                        description: e.target.value,
                      });
                    }}
                    inputProps={{
                      'data-testid': 'event-change-description',
                    }}
                    sx={inputStyle}
                  />
                </div>
                <div className="event__tables-tags-and-actions">
                  <Autocomplete
                    className="event__heading-tags"
                    data-testid="event__tags-autocomplete"
                    multiple
                    open={open}
                    onOpen={() => {
                      setOpen(true);
                    }}
                    onClose={() => {
                      setOpen(false);
                    }}
                    loading={tags?.status === 'loading'}
                    options={tagOptions}
                    noOptionsText={intl.formatMessage({ id: 'typeNewTag', defaultMessage: 'Type to add new tag' })}
                    freeSolo
                    filterOptions={(options, params) => {
                      const filtered = options.filter((option: string) =>
                        option
                          .toLowerCase()
                          .includes(params.inputValue.toLowerCase())
                      );
                      const { inputValue } = params;
                      if (inputValue !== '') {
                        filtered.push(`Add as "${inputValue}"`);
                      }
                      return filtered;
                    }}
                    value={editableEvent.tags}
                    onChange={(_event, newTags) => {
                      const cleanedTags = newTags.map((tag) =>
                        tag.startsWith('Add as "') ? tag.slice(8, -1) : tag
                      );
                      setEditableEvent({ ...editableEvent, tags: cleanedTags });
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        data-testid="event-tags-autocomplete-field"
                        variant="outlined"
                        placeholder={open ? '' : intl.formatMessage({ id: 'addTag', defaultMessage: 'Add tag...' })}
                        InputProps={{
                          ...params.InputProps,
                          inputProps: {
                            ...params.inputProps,
                            'data-testid':
                              'event-tags-autocomplete-field-input',
                          },
                          endAdornment: (
                            <>
                              {tags?.status === 'loading' ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </div>
              </>
            )}
            {!isEditing && (
              <div className="event__heading">
                <div className="event__heading-name-date">
                  {event.status === 'loading' ? (
                    <Skeleton
                      className="event__heading-name skeleton"
                      variant="rectangular"
                      width={150}
                    />
                  ) : (
                    <div
                      className="event__heading-name"
                      data-testid="event-heading-name"
                    >
                      {event.name}
                    </div>
                  )}
                  <div className="event__tables-date-and-created-by-container">
                    {event.status === 'loading' ? (
                      <Skeleton
                        className="event__heading-date skeleton"
                        variant="rectangular"
                        width={260}
                      />
                    ) : (
                      <div className="event__heading-date" data-testid="event-date">
                        {event.eventStartDate && !isNaN(new Date(event.eventStartDate).getTime()) ? I18nTranslate.TranslateDate(toLocalTime(event.eventStartDate)) : 'N/A'}
                        {' - '}
                        {event.eventEndDate && !isNaN(new Date(event.eventEndDate).getTime()) ? I18nTranslate.TranslateDate(toLocalTime(event.eventEndDate)) : 'N/A'}
                      </div>
                    )}
                    {event.status === 'loading' ? (
                      <Skeleton
                        className="event__heading-created-by skeleton"
                        variant="rectangular"
                        width={150}
                      />
                    ) : (
                      <div
                        className="event__heading-created-by"
                        data-testid="event-date"
                      >
                        {`${intl.formatMessage({ id: 'createdBy', defaultMessage: 'Created by' })}: `}
                        <span className="event__heading-created-by-name">
                          {event.createdByName ?? 'Officer Name'}
                        </span>
                      </div>
                    )}
                    {event.status === 'loading' ? (
                      <Skeleton
                        className="event__tables-actions skeleton"
                        variant="rectangular"
                        width={25}
                      />
                    ) : (
                      <div className="event__tables-actions">
                        <Tooltip title="Edit Event" arrow>
                          <SvgIcon
                            className="event-icon"
                            data-testid="event-edit-event"
                            onClick={() => setIsEditing(true)}
                          >
                            <EditIcon className="svg-icon" />
                          </SvgIcon>
                        </Tooltip>
                      </div>
                    )}
                  </div>
                </div>
                <div className="event__tables-desc-container">
                  {event.status === 'loading' ? (
                    <Skeleton
                      className="event__tables-desc skeleton"
                      variant="rectangular"
                      width={200}
                    />
                  ) : (
                    <div
                      className="event__tables-desc"
                      data-testid="event-description"
                    >
                      {event.description}
                    </div>
                  )}
                </div>
                <div className="event__tables-tags-and-actions">
                  <div className="event__tables-tags">
                    {event.status === 'loading' ? (
                      <div style={{ display: 'flex', gap: '10px' }}>
                        {Array.from({
                          length:
                            event.tags.length && event.tags.length > 0
                              ? event.tags.length
                              : 3,
                        }).map((_, index) => (
                          <Skeleton
                            key={index}
                            className="event__tables-desc skeleton"
                            variant="rectangular"
                            width={50}
                          />
                        ))}
                      </div>
                    ) : (
                      <>
                        {event.tags.map((tag, index) => (
                          <Chip
                            className="event__tables-tag"
                            data-testid={`event-tag-${index}`}
                            key={index}
                            label={tag}
                          />
                        ))}
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          <>
            <Tabs
              value={tab}
              onChange={handleTabChange}
              data-testid="event-search-tabs"
            >
              <Tab
                value="files"
                label={intl.formatMessage({ id: 'files', defaultMessage: 'Files' })}
                iconPosition="start"
                data-testid="event-files-tab"
              />
              <Tab
                value="matchGroups"
                label={intl.formatMessage({ id: 'matchGroups', defaultMessage: 'Match Groups' })}
                iconPosition="start"
                data-testid="event-match-groups-tab"
              />
              <Tab
                value="timelineGeneratedVideos"
                label={intl.formatMessage({ id: 'timelineGeneratedVideos', defaultMessage: 'Timeline Generated Videos' })}
                iconPosition="start"
                data-testid="event-generated-timeline-videos-tab"
              />
            </Tabs>
            <div className="divider" />
            <div className="event__tab-container">
              {tab === 'files' && (
                <>
                  {files.status !== 'loading' &&
                    pendingUploadFiles.length > 0 && (
                      <Table<PendingUploadFile>
                        RowComponent={PendingUploadFileRow}
                        className="event__pending-upload-files main__scrollbar"
                        rowData={pendingUploadFiles}
                        disablePagination
                      >
                        <Column
                          title={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
                          dataKey="fileName"
                          grow={1}
                        />
                        <Column
                          title={intl.formatMessage({ id: 'fileStatus', defaultMessage: 'File Status' })}
                          dataKey="status"
                          grow={0}
                          width={150}
                        />
                        <Column
                          title={intl.formatMessage({ id: 'uploadDate', defaultMessage: 'Upload Date' })}
                          dataKey="uploadDate"
                          grow={0}
                          width={170}
                        />
                      </Table>
                    )}
                  <Table<
                    File,
                    {
                      isPendingDeletion: (id: string) => boolean;
                    }
                  >
                    RowComponent={FileRow}
                    rowData={files.results}
                    onRowClick={handleFileClick}
                    onDoubleRowClick={handleFileDoubleClick}
                    onDataQuery={(params) => {
                      navigate(
                        `?` +
                        new URLSearchParams({
                          ...searchParams,
                          page: params.page.toString() ?? '1',
                          limit: params.limit.toString() ?? '10',
                        }).toString()
                      );
                    }}
                    loading={files.status === 'loading'}
                    pagination={files}
                    setSort={setSortSearchParameters}
                    setPagination={setFilesPagination}
                    emptyComponent={
                      pendingUploadFiles.length === 0 ? (
                        <div className="event__tab-container-no-results" data-testid="event-tab-container-no-results">
                          <NoSearchResults
                            hasSearchResults={false}
                            title={intl.formatMessage({ id: 'noFilesFound', defaultMessage: 'No Files Found' })}
                            description={intl.formatMessage({ id: 'uploadFirstFile', defaultMessage: 'Please upload your first file for {eventName}.' }, { eventName: event.name })}
                          />
                        </div>
                      ) : undefined
                    }
                    data-testid="event-files-table"
                    selectedId={fileSelected.file?.id}
                    additionalProps={{
                      isPendingDeletion: (id: string) =>
                        (fileDeletion.status === 'loading' &&
                          id === fileDeletion.id) ||
                        deletedFileIds.includes(id),
                    }}
                  >
                    <Column
                      title={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
                      dataKey="fileName"
                      grow={1}
                      sortable
                    />
                    <Column
                      title={intl.formatMessage({ id: 'fileStatus', defaultMessage: 'File Status' })}
                      dataKey="status"
                      grow={0}
                      width={150}
                    />
                    <Column
                      title={intl.formatMessage({ id: 'uploadDate', defaultMessage: 'Upload Date' })}
                      dataKey="uploadDate"
                      grow={0}
                      width={170}
                      sortable
                    />
                  </Table>
                </>
              )}
              {tab === 'matchGroups' && (
                <Table<MatchGroup>
                  className="event__match-groups-table"
                  RowComponent={MatchGroupRow}
                  rowData={matchGroups.results}
                  loading={matchGroups.status === 'loading'}
                  dataQuery={(params) =>
                    doPoll(
                      'match-groups',
                      dispatch(
                        getMatchGroups({ eventId: eventId || '', ...params })
                      )
                    )
                  }
                  queryOnMount
                  emptyComponent={
                    <div className="event__tab-container-no-results">
                      <NoSearchResults
                        hasSearchResults={hasMatchGroupSearchResults}
                        title={intl.formatMessage({ id: 'noMatchGroupsFound', defaultMessage: 'No Match Groups Found' })}
                        description={intl.formatMessage({ id: 'createFirstMatchGroup', defaultMessage: 'Please open a file and create your first match group with \"Find Matches\".' })}
                      />
                    </div>
                  }
                  disablePagination
                >
                  <Column
                    title={intl.formatMessage({ id: 'matchGroupName', defaultMessage: 'Match Group Name' })}
                    dataKey="name"
                    grow={1}
                    minWidth={200}
                  />
                  <Column
                    title={intl.formatMessage({ id: 'potentialMatchSearches', defaultMessage: 'Potential Match Searches' })}
                    dataKey="numSearches"
                    grow={1}
                    minWidth={100}
                  />
                </Table>
              )}
              {tab === 'timelineGeneratedVideos' && (
                <TimelineGeneratedVideos
                  onRowClick={handleTimelineClick}
                  generatedTimelines={generatedTimelines}
                  getGeneratedTimeline={(params) =>
                    doPoll(
                      'generated-timeline',
                      dispatch(
                        getGeneratedTimeline({
                          eventId: eventId || '',
                          ...params,
                        })
                      )
                    )
                  }
                  tableProps={{
                    disablePagination: true,
                  }}
                />
              )}
            </div>
          </>
        </div>
        <div className="event__detail">
          <SearchAndUpload
            searchAllFiles
            disableSearch
            disableButton={!(event.id && selectedEvent?.id && !isEmpty(attributes.person) && !isEmpty(attributes.vehicle))}
          />
          <div
            data-testid="event-detail-container"
            className={cn('event__detail-container', {
              hide: !(fileSelected.file || selectedTimeline || tab === 'files'),
            })}
          >
            {fileSelected.file || selectedTimeline ? (
              renderDetailContainer()
            ) : (
              <>
                {tab === 'files' && (
                  <div
                    className="event__detail-container-empty"
                    data-testid="event-delete-container-empty"
                  >
                    {intl.formatMessage({ id: 'selectAFile', defaultMessage: 'Select a file' })}.
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Event;
