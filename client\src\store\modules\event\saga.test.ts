import { takeEvery, put, select } from 'redux-saga/effects';
import { deleteEvent, selectEventDeletion } from './slice';
import { getEvents, selectEvents } from '../home/<USER>';
import { watchDeleteEvent, watchEventSagas } from './saga';

const mockGetEventsResponse = {
  "results": [
    {
      "eventEndDate": "2024-04-10T17:02:10Z",
      "createdBy": "c38b16a7-f623-4dd4-9847-0f135bef9dc5",
      "createdByName": "Test User",
      "name": "New event name",
      "description": "New description",
      "createdDateTime": "2024-04-11T21:44:35.441Z",
      "modifiedDateTime": "2024-04-11T21:44:52.430Z",
      "id": "b17597d1-56a3-4c2e-a7c5-6c094e5fa25b",
      "eventStartDate": "2024-04-10T17:02:10Z",
      "tags": [
        "Tag 1",
        "Tag 2"
      ]
    },
    {
      "eventEndDate": "2024-04-10T17:02:10Z",
      "createdBy": "c38b16a7-f623-4dd4-9847-0f135bef9dc5",
      "createdByName": "Test User",
      "name": "Event Test 1",
      "description": "New description",
      "createdDateTime": "2024-04-11T00:10:13.876Z",
      "modifiedDateTime": "2024-04-11T00:10:24.681Z",
      "id": "947db3be-91ec-4e4b-a00f-6ad2ae06e25d",
      "eventStartDate": "2024-04-10T17:02:10Z",
      "tags": [
        "Tag 1",
        "Tag 2"
      ]
    }
  ],
  "currentPage": 1,
  "pageSize": 10000,
  "totalCount": 2,
  "totalPages": 1,
  "status": "idle"
};

describe('event saga', () => {
  it('handles delete event', () => {
    const generator = watchDeleteEvent();
    expect(generator.next().value).toEqual(select(selectEvents));
    const mockEvents = { ...mockGetEventsResponse, status: 'error', error: false };
    expect(generator.next(mockEvents).value).toEqual(select(selectEventDeletion));
    const mockEventDeleted = { ...mockGetEventsResponse, status: 'error', error: false };
    const received = generator.next(mockEventDeleted).value;
    expect(JSON.stringify(received)).toEqual(JSON.stringify(put(getEvents(mockEvents))));
    expect(generator.next().done).toBeTruthy();
  });

  it('watches event sagas', () => {
    const generator = watchEventSagas();
    expect(generator.next().value).toEqual(takeEvery(deleteEvent.fulfilled, watchDeleteEvent));
    expect(generator.next().done).toBeTruthy();
  });
});
