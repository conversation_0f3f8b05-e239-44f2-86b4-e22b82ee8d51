import { createBrowserRouter } from "react-router-dom";
import { Home, Event, NotFound, FileViewer, MatchGroup } from "@pages/index";
import useRouteChanged from "./useRouteChanged";
import { ReactNode } from "react";
import Redirect from "../Redirect";
import PageContainer from "../PageContainer/PageContainer";

interface RouteWrapperProps {
  children: ReactNode;
}

export const RouteWrapper = ({ children }: RouteWrapperProps) => {
  useRouteChanged();
  // Wait for the app to finish booting before rendering the children
  return (
    <PageContainer>
      {children}
    </PageContainer>
  );
};

export const routes = [
  {
    path: "/",
    element: <Home />,
  },
  {
    path: "/home",
    element: <Home />,
  },
  {
    path: "/event",
    element: <Redirect to="/home" />,
  },
  {
    path: "/event/:eventId",
    element: <Event />,
  },
  {
    path: "/event/search",
    element: <Event />,
  },
  {
    path: "/event/:eventId/file/:fileId",
    element: <FileViewer />,
  },
  {
    path: "/event/:eventId/match-group/:matchGroupId/potential-match-search/:searchId",
    element: <MatchGroup />,
  },
  {
    path: "/event/:eventId/match-group/:matchGroupId",
    element: <MatchGroup />,
  },
  {
    path: "*",
    element: <NotFound />,
  },
];

const wrappedRoutes = routes.map((route) => ({
  ...route,
  element: <RouteWrapper>{route.element}</RouteWrapper>,
}));

const router = createBrowserRouter(wrappedRoutes);

export default router;
