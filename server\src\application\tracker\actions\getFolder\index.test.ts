import NodeCache from 'node-cache';
import getFolder from '../getFolder';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, { eventId?: string }>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({ folder: { contentTemplates: [{ sdo: { data: {} } }] } })
  ),
}));

describe('Get Folder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Query w/ eventId in params', async () => {
    cxt.req.params.eventId = 'eventId';

    const response = await getFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { folderId: 'eventId' }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/ eventId in body', async () => {
    cxt.data.eventId = 'eventId';

    const response = await getFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { folderId: 'eventId' }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/o eventId', async () => {
    expect(async () => await getFolder(cxt)).rejects.toThrowError(
      'No eventId provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
