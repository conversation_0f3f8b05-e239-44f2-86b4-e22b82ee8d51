import { useEffect, useRef } from 'react';
import { isHmrReload } from '@utility/hmrUpdate';

const AppBar = () => {
  // aiware mount properties
  const appName = 'APP_BAR';
  const elementId = 'tracker-app-bar';
  const config = {
    title: 'Track',
    backgroundColor: '#1a3d77',
    borderBottom: '1px solid #1a3d77',
    help: true,
    zIndex: 1000,
    notification: false,
  };
  const widgetIdRef = useRef<string>();

  // Wait till the aiware.js has loaded to the window object
  useEffect(() => {
    function setupAppbar(timeoutId: NodeJS.Timeout) {
      if (window.aiware) {
        clearInterval(timeoutId);
        if (!isAiwareLoaded()) {
          aiwareInit();
        } else {
          mountWidgetSDK();
        }
      }
    }

    const intervalId = setInterval(() => {
      setupAppbar(intervalId);
    }, 250);
    setupAppbar(intervalId);
    return () => {
      // Note: Appbar won't load correctly on HMR reload if AppBar file was edited
      clearInterval(intervalId);
      if (!isHmrReload && widgetIdRef.current) {
        unmountWidgetSDK();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isAiwareLoaded = () => window?.isAiwareInitialized || false;

  const aiwareInit = () => {
    window.isAiwareInitialized = true;
    // init properties
    const apiRoot = window.config?.apiRoot || '';
    const graphQLEndpoint = window.config?.graphQLEndpoint || '';
    const veritoneAppId = window.config?.veritoneAppId || '0021f79f-f29d-45b9-b94d-7b98c933df4b';

    window.aiware?.init(
      {
        applicationId: veritoneAppId,
        baseUrl: `${apiRoot}/${graphQLEndpoint}`,
        withAuth: true,
        betaFeatures: false,
      },
      () => {
        window.aiware?.on('openSupport', async () => {
          try {
            const { initChatWithSupport, chatWithSupport } = await import(
              // eslint-disable-next-line import/no-unresolved
              'https://get.aiware.com/veritone-support/latest/2/index.js'
            );
            await initChatWithSupport();
            await chatWithSupport();
          } catch (error) {
            console.error('Failed to load chat support:', error);
          }
          // window.openSalesforceChatWindow?.();
        });
        mountWidgetSDK();
      }
    );
  };

  const mountWidgetSDK = () => {
    widgetIdRef.current = window.aiware?.mountWidget(
      {
        name: appName,
        elementId: elementId,
        config,
      },
      () => {
        // mountWidget callback
      }
    );
  };

  const unmountWidgetSDK = () => {
    if (widgetIdRef.current) {
      window.aiware?.unmountWidget(widgetIdRef.current);
      widgetIdRef.current = '';
    }
  };

  return <div id="aiWareAppBar" data-testid="aiWareAppBar" />;
};

export default AppBar;
