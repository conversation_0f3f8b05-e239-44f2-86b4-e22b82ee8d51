## Open Questions 

### Design
  1. ~~Need an export page for downloading and viewing progress on tracklet compilation video~~
     * Ticket created
  2. ~~Does a event have a location as well?~~
     * Events do not have a location at this time
  3. ~~What is a file location? GPS? Is it a relative location of a camera? e.g. Office, file room, bank vault~~
    + GPS coordinates 
  4. ~~Do Match Groups have pagination? Are they searchable?~~
     * Have pagination, not searchable
  5. ~~What are the vehicle attributes?~~
     * There is no vehicle attributes, but there will be in the future
  6. ~~Do bounding boxes need to be rendered into the final tracklet compilation video? Is it optional?~~
     * Bounding boxes are optional on export
     * Note that FFMPEG may have support for drawing boxes per frame 
       + https://ffmpeg.org/ffmpeg-filters.html#drawbox
  7. ~~Will we show multiple tracklets at the same time in the timeline export?~~
   * Not an MVP feature, include in data model because it is a likely future feature
   * Note that FFMPEG has support for making mosaic videos specifically suggested for surveillance systems
      + https://trac.ffmpeg.org/wiki/Create%20a%20mosaic%20out%20of%20several%20input%20video
      + What would happen when we want to show a mosaic of tracklets with different video lengths?

### AIware capability
  1. ~~Is user identity recorded on folder when its created?~~
    * Owner is not set when folder is created
    * Need to add to content template
  2. What metadata is on TDO after ingestion? Check if GLC Ingestor or SI2 (default aiware ingestor) does metadata extraction. (time was created, GPS, bitrate, camera information, etc.)
  3. Is user identity recorded on TDO creation?
    * Yes for TDO
  4. ~~Validate s3/blob-storage credentials can be generated for the engine in AIware, otherwise credentials need to be passed in through engine payload~~
    * Via Brian Noecker - Hmmm, so typically I think the general way it works, assuming the S3 bucket is in account (or a trust likely needs set between accounts) an EC2 instance can be launched with a role attached that allows it inherited access.   If you want to isolate the request to a service (container) ON the EC2 instances, an IAM cred could be used, but that's not great.  I think a IAM STS API call for temp credential would be the way to go, and again I think it more the EC2 instance that has the originating Identity with STS-API permissions + S3 perms.   Brett Chain(AWS TAM) would be able to guide this pretty well in best practice if you want to engage him.

  5. Need to validate the posting of AION format to engine results, and that fingerprints and other searchable data is indexed
    * ES8 eod 2/22/24
  6. Document what the engine payload for VSL v17 engine would be

### VSL
  1. What is full list of available attributes for person ~~and vehicle?~~
     * There is no vehicle attributes currently, but there will be 

## Data Domains

### Event 
* Folder
  - Name
  - User identity of creation (?) 
* Content template
  - TDO IDs (Files)
  - TDO IDs (Output Videos)
  - Description
  - Date Range, user defined when you create
  - Location (?)
  - Tags

### File
* TDO
  - Event ID
  - Metadata (?)
    + type
    + size
    + length
* Location (?)
* User identity of upload (?)
* Date of upload (createdAt exists)
* Engine Job(s) (VSL Processing, Export)
  + Job ID

### Match group
* SDO
* Event ID
* Name of match group
* REID Search Results
  + Likely an async api
  + Reference Tracklet
  + File IDs
  + Search results { Tracklet, Confidence }
* Timeline
  + [ trackletData, trackletData[], trackletData, trackletData,... ]
  + Tracklet data includes...
    - TDO id 
    - Time ranges (output video + tracklet)
    - Bounding box data (?) (if necessary, could be optional export config)
  + User/session/date lock

### Tracklets
* When the VSL Processing Engine completes:
  1. Engine results contain Tracklet data. Engine results are posted on TDO
    + Fingerprint vectors
    + Timestamped bounding boxes
    + A thumbnail GUID
    + Attributes (person)
      - Accessories
      - Body
      - Face
      - Footwear
      - Gender
      - Hair
      - Lower
      - Upper
    + Attributes (vehicle)
      - (?)
  2. Fingerprints are indexed for REID search


### Thumbnail
* E.G. https://tracker.us-gov-2.veritone.com/api/v1/thumbnail/a-universally-unique-id
* VSL Engine will...  
  1. Accept a thumbnail selection parameter (first frame or VSL confidence)
  2. Accept storage location (bucket name) for thumbnails
  3. Write thumbnail to s3/blob storage (orgId/a-universally-unique-id.png) 
  4. Post thumbnail UUID as engine result

* Client
  1. Gets Thumbnail URI from engine result (on TDO)
  2. Requests signed urls from backend given thumbnail URI.
  
  or

  2. Request is routed through NGINX with [security control](https://www.nginx.com/blog/using-nginx-as-object-storage-gateway/)
    + Thumbnail access by orgId folders

### ~~Application state (per org)~~
* ~~SDO~~
* ~~Backend can handle application locking~~
* Added data to match group regarding Timelines edits and locks

## Event Page E2E

### Request Events
1. Client - GET /events?sort=(name|date)&dir=asc&limit=(1-10k)&page=(1-*)?search=....
2. Server - GraphQL Search (Event SDO), client token

### Request Files
1. Client - GET /files?sort=(name|date)&dir=asc&limit=(1-10,000)&page=(1-*)?search=....
2. Server - GraphQL Search (File SDO), client token

### Upload 

* Client 
  1. POST /upload, multipart/form-data video
* Application Backend
  1. Create TDO
  3. Set TDO on event content template
  4. Request upload url
  5. Forward request stream to upload url (blocking)
  6. Start VSL engine for video processing (async)
    + Engine can will update job status
  7. Set job information on File
* Client polls for processing job status, or server sends status updates via socket
* For bulk upload, we can setup a job to check and S3 bucket similar to v1, and just run the above server actions

...more Event page E2E

## Potential Matches Page E2E

### Request Match Group 
1. Client - GET /match-group/{id}
  + Match Group

### Find Matches
 
* Client 
  1. POST /reid
    + Reference Tracklet ID
    + Files
    + Case(s)
* Application Backend
  1. Fingerprint search
  2. Set search results to Match Group SDO

...more Potential Matches Page E2E

## Timeline Page E2E

### Edit timeline

### Set edit lock
  1. Client
    + POST match-group/{id}/timeline/lock

  2. Application Backend 
    + GraphQL Set user/session lock on Match Group SDO

### Edit timeline
  1. Client
   + User drags tracklet into timeline
   + Tracklets appear in timeline in their dragged position
      ```js
      [ 
        { tdoId, videoTimeRange: 00:00-00:02, trackletTimeRange: 01:00-01:02, boundingBoxData }, 
        { tdoId, videoTimeRange: 00:02-00:05, trackletTimeRange: 00:00-00:02, boundingBoxData } 
      ]
      ```
   + If user drags over existing timeline tracklet, add a sub-array to timeline edit data structure
      ```js
      [ 
        { tdoId, videoTimeRange: 00:00-00:02, trackletTimeRange: 01:00-01:02, boundingBoxData }, 
        [ 
          { tdoId, videoTimeRange: 00:02-00:05, trackletTimeRange: 01:00-01:09, boundingBoxData }, 
          { tdoId, videoTimeRange: 00:02-00:05, trackletTimeRange: 01:50-01:55, boundingBoxData }  
        ],
        { tdoId, videoTimeRange: 00:05-00:14, trackletTimeRange: 12:07-13:10, boundingBoxData }, 
      ]
      ```
     - Would likely result in an export mosaic for that time period
     - What would happen when we want to show a mosaic with different lengths (?)
  2. Application Backend
   + PUT match-group/{id}/timeline/
    

## VSL as a service

### Tracker v2 without VSL as an engine

* Client and Application API discussed above remain the same
* Data Models
  - Event
    + Additional field for VSL video ID (AddVideoRequest, AddVideoResponse) ( could be on TDO as well )
    + Additional field for VSL Add Video status (AddVideoResponse, VideoProcessedResponse) ( could be on TDO as well )

  - Files
    + TDOs will not reference VSL Engine Processing jobs

  - Match group
    + Search (REID) results
      * Run existing REID engine, Store engine job on Match group SDO
        + May be better to just manually request matches, do similar logic to the engine in the application backend.
      * Store response data on Match Group SDO, RankedMatchesResponse { TrackId[], VideoId, TrackletScores: { TrackletInfo, score } }

  - Tracklets
    + Running a VSL Processing Engine replaced with AddVideoRequest
    + Poll AddVideoResponse, VideoProcessedResponse for processing status
    + On processing completion    
      - TrackletsRequest { VideoId }
      - TrackletsResponse { TrackletInfo[] { TrackId, StartMs, EndMs, ThumbnailPath } }
      - TrackletAttributesRequest { TrackletId[] }
      - TrackletAttributesResponse { FullTrackletInfo: { BoundingBoxes[], Attributes[] } }
    * Check on how this is done now

  - Thumbnails
    + Have VSL modify s3 path similar to what is planned for v2 (orgId/id.png)
    + Modify v1 thumbnail server nginx to serve only authorized thumbnails by orgId
    * Check on how this is done now

## Links
* [VSL Chunk Engine](https://github.com/veritone/engines/tree/master/engines/tracker/fingerprint/tracker-chunk)
* [Next Gen Tracker AION format wiki](https://veritone.atlassian.net/wiki/spaces/APT/pages/2859532299/Next+generation+tracker+AION+format)
* [Content Template usage in other apps](https://veritone.atlassian.net/wiki/spaces/VT/pages/2658992147/create+new+case)
* [Figma mocks](https://www.figma.com/file/4Zbn9gjrPY2r3tiizfa3eR/Tracker?type=design&node-id=904-600&mode=design&t=fzTMsSldtxvvcnuT-0)