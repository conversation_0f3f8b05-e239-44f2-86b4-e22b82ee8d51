import { range } from 'lodash';
import { File } from '@shared-types/tracker';

const filesMocks: File[] = range(1, 10).map((i) => ({
  id: `${i}`,
  fileName: `file-${i}.mp4`,
  status: 'processed',
  length: i * 1000,
  uploadDate: ' 2021-01-01T00:00:00.000Z',
  location: '',
  fileType: 'mp4',
  fileSize: i * 2048,
  eventId: 'eventId',
  eventName: 'eventName',
  createdByName: 'createdByName',
  thumbnailUrl: '',
  primaryAsset: {
    signedUri: '',
  },
  streams: [],
  frameRate: 30
}));

// REMOVE THIS FILE WHEN THE API IS INTEGRATED
export const fetchFile = (fileId: string) =>
  new Promise<{ data: File }>((resolve) =>
    setTimeout(
      () =>
        resolve({
          data: filesMocks.find((f) => f.id === fileId) ?? filesMocks[0],
        }),
      1000
    )
  );
