import {
  render,
  screen,
  fireEvent,
  waitFor,
  within,
  act,
} from '@testing-library/react';
import { <PERSON>rowserRouter, useLocation } from 'react-router-dom';
import Home from '@pages/Home/Home';
import Event from '@pages/Event/Event';
import FileViewer from '@pages/FileViewer/FileViewer';
import { Provider } from 'react-redux';
import { configureAppStore } from '@store/store';
import axios from 'axios';
import { DateTime, Settings } from 'luxon';
import {
  CreateEventResponse,
  SearchEventsResponse,
  SearchFilesResponse,
} from '@shared-types/responses';
import {
  AlertLevel,
  createSnackNotification,
} from '@components/common/Snackbar/Snackbar';
import { bytesToMb } from '@utility/convert';
import sleep from '@utility/sleep';
import { uploadSlice } from '@store/modules/upload/slice';
import ls from 'localstorage-slim';
import { I18nProvider, LOCALES } from '@i18n';
import { HomeSliceState } from '@store/modules/home/<USER>';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
  }),
}));
jest.mock('axios');

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: jest.fn(),
}));

jest.mock('@components/common/Snackbar/Snackbar', () => ({
  ...jest.requireActual('@components/common/Snackbar/Snackbar'),
  createSnackNotification: jest.fn(),
}));

Settings.defaultZone = 'utc';

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockNavigate = jest.fn();

const mockGetEventsResponse: SearchEventsResponse = {
  results: [
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b32137d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 6,
      filesCount: 4,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name 1',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 6,
      filesCount: 3,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 1',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 2,
      filesCount: 2,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 2',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '9dr3b3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 3',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '547db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 4',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '542ss3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 5',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '511dd4be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 10,
      filesCount: 120,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 6',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'aaad4be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 30,
      filesCount: 34,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 7',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'bbb444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 8',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'zzz444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 9',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'ccc444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 3'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 10',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'ooo444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 3'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
  ],
  currentPage: 1,
  pageSize: 50,
  totalCount: 12,
  totalPages: 1,
};

const mockGetEventsResponse2: SearchEventsResponse = {
  results: [
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b32137d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 6,
      filesCount: 4,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name 1',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 6,
      filesCount: 3,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 1',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 2,
      filesCount: 2,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 2',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '9dr3b3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 3',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '547db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 4',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '542ss3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 5',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '511dd4be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 10,
      filesCount: 120,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 6',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'aaad4be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 30,
      filesCount: 34,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 7',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'bbb444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 8',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: 'zzz444be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
  ],
  currentPage: 1,
  pageSize: 10,
  totalCount: 12,
  totalPages: 2,
};

const mockGetFilesResponse: SearchFilesResponse = {
  results: [
    {
      id: '30510000',
      createdByName: 'Test User',
      fileName: 'test_file_1.mp4',
      status: 'processed',
      length: 256,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 3000,
      eventId: 'b32137d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: '30510001',
      createdByName: 'Test User',
      fileName: 'test_file_2.mp4',
      status: 'processing',
      length: 512,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 1024,
      eventId: 'b32137d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: '30510002',
      createdByName: 'Test User',
      fileName: 'test_file_3.mp4',
      status: 'error',
      length: 128,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 512,
      eventId: 'b32137d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
  ],
  currentPage: 1,
  pageSize: 100,
  totalCount: 3,
  totalPages: 1,
};

const mockPostEventResponse: CreateEventResponse = {
  event: {
    id: '3d155106-ac92-4f78-b9d4-dabe42b5fa80',
    name: 'test new event',
    tags: [],
    createdBy: '8e71aba4-205c-4029-b662-db37ccd3e08f',
    createdByName: 'Test User',
    description: 'Event Description',
    eventEndDate: '2024-04-17T13:23:19.512-06:00',
    eventStartDate: '2024-04-17T13:23:19.512-06:00',
    createdDateTime: '2024-04-17T19:23:19.545Z',
    modifiedDateTime: '2024-04-17T19:23:19.545Z',
    matchGroupsCount: 10,
    filesCount: 10,
  },
};

const initialStateForMock: {home: HomeSliceState} = {
  home: {
    events: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    files: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    eventSelected: {
      event: undefined,
      status: 'idle',
      error: '',
    },
    fileSelected: {
      file: undefined,
      status: 'idle',
      error: '',
    },
  },
};

async function switchToTab(dataTestId: string) {
  const desiredTab = dataTestId.split('-')[1];

  // Find specified tab
  const tab = screen.getByTestId(dataTestId);

  fireEvent.click(tab);

  await waitFor(() => {
    // is the tab selected?
    expect(tab).toHaveClass('Mui-selected');
  });
  await waitFor(() => {
    if (desiredTab !== 'events') {
      expect(screen.getByTestId('home-events-tab')).not.toHaveClass(
        'Mui-selected'
      );
    }
  });
  mockNavigate(`/?activeTab=${desiredTab}&page=1&limit=50`);
}

describe('Home on Events tab', () => {
  beforeAll(() => {
    (useLocation as jest.Mock).mockReturnValue({ search: '', pathname: '/' });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  beforeEach(() => {
    localStorage.clear();
  });
  it('renders Home component and polls for data', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    let callToEventsAndFiles = 0;

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        callToEventsAndFiles++;
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        callToEventsAndFiles++;
        return Promise.resolve({ data: mockGetFilesResponse });
      }

      if (url?.includes('/events') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    const { container: _container } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // check if the component is rendered
    expect(screen.getByTestId('home')).toBeInTheDocument();

    expect(callToEventsAndFiles).toBe(3);

    await sleep(7501);
    // Poll twice
    expect(callToEventsAndFiles).toBe(5);

    await sleep(7501);
    // Poll third
    expect(callToEventsAndFiles).toBe(7);
  }, 16000);

  it('click event within table and select view event', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/events') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
      if (url?.includes('/event/') && method === 'get') {
        return Promise.resolve({
          data: { event: mockGetEventsResponse.results[0] },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    fireEvent.click(
      screen.getByTestId(`event-row-0-${mockGetEventsResponse.results[0].id}`)
    );

    // expect the view event button to not be displayed
    expect(screen.queryByTestId('home-view-event-button')).not.toBeInTheDocument();

    expect(
      await screen.findByText(
        `Event Creator: ${mockGetEventsResponse.results[0].createdByName}`
      )
    ).toBeVisible();
    //  expect the view event button to be displayed when the event is loaded completely
    const viewEventButton = screen.getByTestId('home-view-event-button');
    expect(viewEventButton).toBeInTheDocument();
    fireEvent.click(viewEventButton);

    // check all content for the
    expect(screen.getByTestId('home-detail-created-date')).toHaveTextContent(
      `Date: 4/10/2024, 5:02 PM`
    );
    expect(screen.getByTestId('home-detail-creator-name')).toHaveTextContent(
      `Event Creator: ${mockGetEventsResponse.results[0].createdByName}`
    );
    expect(screen.getByTestId('home-detail-file-count')).toHaveTextContent(
      `${mockGetEventsResponse.results[0].filesCount} Files`
    );
    expect(
      screen.getByTestId('home-detail-matchgroup-count')
    ).toHaveTextContent(
      `${mockGetEventsResponse.results[0].matchGroupsCount} Match Groups`
    );
    expect(mockNavigate).toHaveBeenCalledWith(
      `/event/${mockGetEventsResponse.results[0].id}`
    );
  });

  it('click event within table and select delete event', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }

      if (url?.includes('/events') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }

      if (url?.includes('/event/') && method === 'get') {
        return Promise.resolve({
          data: { event: mockGetEventsResponse.results[0] },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // click on the second event and delete it
    fireEvent.click(
      screen.getByTestId(`event-row-1-${mockGetEventsResponse.results[1].id}`)
    );

    expect(
      await screen.findByText(
        `Event Creator: ${mockGetEventsResponse.results[0].createdByName}`
      )
    ).toBeVisible();

    // click on the delete button
    fireEvent.click(screen.getByTestId('home-delete-event-button'));
    expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument();
  });

  it('click event within table and update event name', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = '     Updated Event name  ';
    const nameUpdatedTrim = 'Updated Event name';

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/event/') && method === 'get') {
        return Promise.resolve({
          data: { event: mockGetEventsResponse.results[1] },
        });
      }
      if (url?.includes('/event') && method === 'patch') {
        return Promise.resolve({
          data: {
            event: {
              ...mockGetEventsResponse.results[1], // Update second file
              name: nameUpdated,
            },
          },
          status: 200,
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // click on the second event and delete it
    fireEvent.click(
      screen.getByTestId(`event-row-1-${mockGetEventsResponse.results[1].id}`)
    );

    expect(
      await screen.findByText(
        `Event Creator: ${mockGetEventsResponse.results[0].createdByName}`
      )
    ).toBeVisible();

    // check if the event name is present
    expect(screen.getByTestId('home-detail-name')).toHaveTextContent(
      mockGetEventsResponse.results[1].name
    );

    // click on the edit button
    fireEvent.click(screen.getByTestId('home-detail-event-name-edit'));

    // Update event name
    fireEvent.change(screen.getByTestId('home-detail-event-name-textfield'), {
      target: { value: nameUpdated },
    });

    // click enter to update the textfield
    fireEvent.keyDown(screen.getByTestId('home-detail-event-name-textfield'), {
      key: 'Enter',
    });

    // check if the event name is updated, trimmed
    await waitFor(() => {
      expect(
        screen.getByTestId(`event-row-1-${mockGetEventsResponse.results[1].id}`)
      ).toHaveTextContent(nameUpdatedTrim);
    });

    // check if the event name in the selected row is updated, trimmed
    await waitFor(() => {
      expect(screen.getByTestId('home-detail-name')).toHaveTextContent(
        nameUpdatedTrim
      );
    });
  });

  it('update search input, load search events, update page size', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    const { rerender: _render } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // search input is present
    expect(
      screen.getByTestId('search-and-upload-search_input')
    ).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByTestId('home-events-tab')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        within(screen.getByTestId('home-events-tab')).getByText('12')
      ).toBeInTheDocument();
    });

    // update search input
    fireEvent.change(screen.getByTestId('search-and-upload-search_input'), {
      target: { value: 'event' },
    });

    // url contains search parameter
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenLastCalledWith(
        '?terms=event&activeTab=events&page=1&limit=50',
        { replace: true }
      );
      mockNavigate.mockClear();
    });

    // check pagination
    expect(
      screen.getByTestId('table-pagination-page-size')
    ).toBeInTheDocument();

    await waitFor(() => {
      expect(
        within(screen.getByTestId('table-pagination-page-size')).getByText('50')
      ).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        screen.getByTestId('table-pagination-page-selector-text')
      ).toHaveTextContent('1-12 of 12');
    });

    // check number of event rows
    expect(within(screen.getByRole('table')).getAllByRole('row').length).toBe(
      12
    );
  });

  it('update search input, load search events with limit and search new term with same limit', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    const _useLocationMock = jest
      .spyOn(require('react-router-dom'), 'useLocation')
      .mockReturnValue({
        search: { limit: '10', activeTab: 'events', page: '1', terms: 'event' },
        pathname: '/',
      });

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/events') &&
        method === 'get' &&
        url?.includes('pageSize=10')
      ) {
        return Promise.resolve({ data: mockGetEventsResponse2 });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // search input is present
    await waitFor(() => {
      expect(
        screen.getByTestId('search-and-upload-search_input')
      ).toBeInTheDocument();
      expect(screen.getByTestId('home-events-tab')).toBeInTheDocument();
    });

    // check pagination
    await waitFor(() => {
      expect(
        screen.getByTestId('table-pagination-page-size')
      ).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        within(screen.getByTestId('table-pagination-page-size')).getByText('10')
      ).toBeInTheDocument();
    });

    fireEvent.change(screen.getByTestId('search-and-upload-search_input'), {
      target: { value: 'ev' },
    });

    // url contains search parameter
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });

    // page size limit remain the same with new search term
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenLastCalledWith(
        '?terms=ev&activeTab=events&page=1&limit=10',
        { replace: true }
      );
      mockNavigate.mockClear();
    });
  });

  it('create event and check if it is added to the list', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/event') && method === 'post') {
        return Promise.resolve({ data: mockPostEventResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // dispatch create event action
    await act(() => {
      mockedStore.dispatch(
        uploadSlice.actions.createEvent({ name: 'test new event' })
      );
    });

    // check if the event is added to the pending list
    await waitFor(() => {
      expect(screen.getByTestId('pending-event-row-name')).toHaveTextContent(
        'test new event'
      );
    });
  });
});

describe('Home on Files tab', () => {
  beforeAll(() => {
    (useLocation as jest.Mock).mockReturnValue({ search: '', pathname: '/' });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  beforeEach(() => {
    localStorage.clear();
  });
  it('switches tab to files tab', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }

      if (url?.includes('/events') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // Switch to files tab
    switchToTab('home-files-tab');
  });

  it('click file within table and select view file', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/files') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    const { container: _container } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // Switch to files tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetFilesResponse.results[0].fileName)
      ).toBeInTheDocument();
    });

    fireEvent.click(
      screen.getByTestId(`file-row-${mockGetFilesResponse.results[0].id}`)
    );

    expect(screen.getByTestId('home-detail-creator')).toHaveTextContent(
      `File Uploaded by ${mockGetFilesResponse.results[0].createdByName}`
    );
    expect(screen.getByTestId('home-detail-file-size')).toHaveTextContent(
      `File Size: ${bytesToMb(mockGetFilesResponse.results[0].fileSize)} Mb`
    );
    expect(screen.getByTestId('home-detail-file')).toHaveTextContent(
      `File GPS Location: ${mockGetFilesResponse.results[0].location}`
    );

    fireEvent.click(screen.getByTestId('home-view-file-button'));

    expect(mockNavigate).toHaveBeenCalledWith(
      `/event/${mockGetFilesResponse.results[0].eventId}/file/${mockGetFilesResponse.results[0].id}`
    );
  });

  it('does not show file metadata block for files that are not in processed state', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/files') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    const { container: _container } = render(
        <Provider store={mockedStore}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <BrowserRouter>
              <Home />
            </BrowserRouter>
          </I18nProvider>
        </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
          screen.getByText(mockGetEventsResponse.results[1].name)
      ).toBeInTheDocument();
    });

    // Switch to files tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
          screen.getByText(mockGetFilesResponse.results[1].fileName)
      ).toBeInTheDocument();
    });

    fireEvent.click(
        screen.getByTestId(`file-row-${mockGetFilesResponse.results[1].id}`)
    );

    expect(screen.getByTestId('home-detail-creator')).toHaveTextContent(
        `File Uploaded by ${mockGetFilesResponse.results[1].createdByName}`
    );
    expect(screen.queryByTestId('home-detail-file-size')).not.toBeInTheDocument();

    expect(screen.queryByTestId('home-detail-file')).not.toBeInTheDocument();
  });

  it('select a file within table and update its name', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = '  Updated File name          ';

    const _mockPatch = jest.fn();
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/file') && method === 'patch') {
        return Promise.resolve({
          data: {
            file: {
              ...mockGetFilesResponse.results[1], // Update second file
              fileName: nameUpdated.trim(),
            },
          },
          status: 200,
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // Switch to files tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetFilesResponse.results[0].fileName)
      ).toBeInTheDocument();
    });

    // click on the second file
    fireEvent.click(
      screen.getByTestId(`file-row-${mockGetFilesResponse.results[1].id}`)
    );

    // check if the file name is present
    expect(screen.getByTestId('home-detail-name')).toHaveTextContent(
      mockGetFilesResponse.results[1].fileName
    );

    // click on the edit button
    fireEvent.click(screen.getByTestId('home-detail-file-name-edit'));

    // Update file name
    fireEvent.change(screen.getByTestId('home-detail-file-name-textfield'), {
      target: { value: nameUpdated },
    });

    // enter the textfield
    fireEvent.keyDown(screen.getByTestId('home-detail-file-name-textfield'), {
      key: 'Enter',
    });

    // check if the file name is updated (trimmed)
    await waitFor(() => {
      expect(
        screen.getByTestId(`file-row-cell-1-${mockGetFilesResponse.results[1].id}-0`)
      ).toHaveTextContent(nameUpdated.trim());
    });

    // check if the file name in the home detail is updated
    expect(screen.getByTestId('home-detail-name')).toHaveTextContent(
      nameUpdated.trim()
    );
  });

  it('selects a file then deletes it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/files') && method === 'delete') {
        return Promise.resolve({ data: { message: 'success' } });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    // Switch to files tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetFilesResponse.results[0].fileName)
      ).toBeInTheDocument();
    });

    // click on the second event and delete it
    fireEvent.click(
      screen.getByTestId(`file-row-${mockGetFilesResponse.results[1].id}`)
    );

    // click on the delete button
    fireEvent.click(screen.getByTestId('home-delete-file-button'));

    // confirm the delete action
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    // wait for the event to be deleted
    await waitFor(() => {
      expect(screen.getByTestId('home-container-empty')).toBeInTheDocument();
    });
  });

  it('update search input, load search files, update page size', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    const { rerender } = render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // search input is present
    expect(
      screen.getByTestId('search-and-upload-search_input')
    ).toBeInTheDocument();

    // Switch to files tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        within(screen.getByTestId('home-files-tab')).getByText('3')
      ).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(
        '/?activeTab=files&page=1&limit=50'
      );
      mockNavigate.mockClear();
    });

    (useLocation as jest.Mock).mockReturnValue({
      search: `?activeTab=files&page=1&limit=50`,
      pathname: '/',
    });

    rerender(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // update search input
    fireEvent.change(screen.getByTestId('search-and-upload-search_input'), {
      target: { value: 'test' },
    });

    // url contains search parameter
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenLastCalledWith(
        '?terms=test&activeTab=files&page=1&limit=50',
        { replace: true }
      );
    });

    // check pagination
    expect(
      screen.getByTestId('table-pagination-page-size')
    ).toBeInTheDocument();

    await waitFor(() => {
      expect(
        within(screen.getByTestId('table-pagination-page-size')).getByText(
          '100'
        )
      ).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(
        within(
          screen.getByTestId('table-pagination-page-selector-text')
        ).getByText('1-3 of 3')
      ).toBeInTheDocument();
    });

    // check number of file rows
    expect(within(screen.getByRole('table')).getAllByRole('row').length).toBe(
      3
    );

    // Rerender component with search parameter in url
    (useLocation as jest.Mock).mockReturnValue({
      search: '?terms=test&activeTab=files&page=1&limit=10',
      pathname: '/',
    });
    mockNavigate('?terms=test&activeTab=files&page=1&limit=10');
    rerender(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // page size is updated
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(
        '?terms=test&activeTab=files&page=1&limit=10'
      );
    });

    await waitFor(() => {
      expect(
        screen
          .getByTestId('table-pagination-page-size')
          // eslint-disable-next-line testing-library/no-node-access
          .querySelector('div div')
      ).toHaveTextContent('10');
    });

    // search input is updated
    await waitFor(() => {
      expect(screen.getByTestId('search-and-upload-search_input')).toHaveValue(
        'test'
      );
    });
  });

  it('double click file that is not processed and display snack warning', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    // switch to file tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetFilesResponse.results[0].fileName)
      ).toBeInTheDocument();
    });

    // file is still processing
    const processingFile = mockGetFilesResponse.results[1];

    // double click file
    fireEvent.dblClick(screen.getByTestId(`file-row-${processingFile.id}`));

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not finished processing'
      );
    });

    // file has processing error
    const errorFile = mockGetFilesResponse.results[2];

    // double click file
    fireEvent.dblClick(screen.getByTestId(`file-row-${errorFile.id}`));

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Error,
        'Error',
        'File failed to process'
      );
    });
  });

  it('clicking a file (single/double) that is pending deletion displays snack warning', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // file is pending deletion
    const pendingDeletionFile = mockGetFilesResponse.results[2];

    // add deleted tdo id to localStorage deletedFileIds
    localStorage.setItem(
      'deletedFileIds',
      JSON.stringify([
        { value: pendingDeletionFile.id, expiry: new Date().getTime() + 10000 },
      ])
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    // switch to file tab
    switchToTab('home-files-tab');

    // wait for file from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetFilesResponse.results[0].fileName)
      ).toBeInTheDocument();
    });

    // single click file
    fireEvent.click(screen.getByTestId(`file-row-${pendingDeletionFile.id}`));

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'This file is pending deletion and cannot be selected.'
      );
    });

    // double click file
    fireEvent.dblClick(
      screen.getByTestId(`file-row-${pendingDeletionFile.id}`)
    );

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'This file is pending deletion and cannot be selected.'
      );
    });
  });

  it('Show empty message when there are no pending upload files or file results found', async () => {
    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', JSON.stringify([]));

    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({
          data: { ...mockGetFilesResponse, results: [] },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    // switch to file tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      expect(screen.getByText('There are no files.')).toBeInTheDocument();
    });
  });

  it('Show pending upload files when there are no file results found', async () => {
    const expirationDate = DateTime.now().plus({ days: 7 }).toISO();

    const pendingUploadFiles = [
      {
        fileName: 'test.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '111',
        status: 'new',
        id: '1',
        expirationDate,
      },
      {
        fileName: 'test1.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '123',
        status: 'new',
        id: '2',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '333',
        status: 'new',
        id: '3',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '12345678-1111-2222-3333-444444444444',
        status: 'new',
        id: '4',
        expirationDate,
      },
    ];

    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', pendingUploadFiles);

    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({
          data: { ...mockGetFilesResponse, results: [] },
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    // switch to file tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      expect(screen.queryByText('There are no files.')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.queryAllByTestId('pending-file-row').length).toBe(
        pendingUploadFiles.length
      );
    });
  });

  it('Show pending upload files but remove when found in file results or expired', async () => {
    jest.useFakeTimers();
    const expirationDate = DateTime.now().plus({ days: 7 }).toISO();
    const alreadyExpiredDate = DateTime.now().minus({ days: 7 }).toISO();

    const pendingUploadFiles = [
      {
        fileName: 'test.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '111',
        status: 'new',
        id: '30510000', // should be removed
        expirationDate,
      },
      {
        fileName: 'test1.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '123',
        status: 'new',
        id: '2',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '333',
        status: 'new',
        id: '3',
        expirationDate,
      },
      {
        fileName: 'test2.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '12345678-1111-2222-3333-444444444444',
        status: 'new',
        id: '4',
        expirationDate,
      },
      {
        fileName: 'test3.mp4',
        uploadDate: '2021-08-01T00:00:00.000Z',
        eventId: '12345678-1111-2222-3333-444444444444',
        status: 'new',
        id: '5',
        expirationDate: alreadyExpiredDate, // should be removed
      },
    ];

    // Set pending upload files to empty array
    ls.set('pendingUploadFiles', pendingUploadFiles);

    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );
    jest.advanceTimersByTime(60000);
    // wait for event from mock response to become available
    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toBeInTheDocument();
    });

    // switch to file tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      expect(screen.queryByText('There are no files.')).not.toBeInTheDocument();
    });

    const now = new Date().getTime();

    const filteredPendingUpload = pendingUploadFiles.filter(
      (file) =>
        !mockGetFilesResponse.results.some((f) => f.id === file.id) &&
        new Date(file.expirationDate).getTime() > now
    );

    await waitFor(() => {
      expect(screen.queryAllByTestId('pending-file-row').length).toBe(
        filteredPendingUpload.length
      );
    });

    const pendingFirstFile = screen.getByTestId(
      `pending-file-row-${filteredPendingUpload[0].id}`
    );

    // double click file
    fireEvent.dblClick(pendingFirstFile);

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not yet processed.'
      );
    });

    // double click file
    fireEvent.click(pendingFirstFile);

    // show snack warning
    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Warning,
        'Warning',
        'File has not yet processed.'
      );
    });

    jest.useRealTimers();
  });
});

describe('Home navigation', () => {
  beforeAll(() => {
    (useLocation as jest.Mock).mockReturnValue({ search: '', pathname: '/' });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  beforeEach(() => {
    localStorage.clear();
  });

  it('navigate to event details page and back', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
      if (url?.includes('/event/') && method === 'get') {
        return Promise.resolve({
          data: { event: mockGetEventsResponse.results[0] },
        });
      }
    });

    const historyBackSpy = jest.spyOn(window.history, 'back');
    const historyForwardSpy = jest.spyOn(window.history, 'forward');

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
            <Event />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    fireEvent.click(
      screen.getByTestId(`event-row-0-${mockGetEventsResponse.results[0].id}`)
    );
    expect(
      await screen.findByText(
        `Event Creator: ${mockGetEventsResponse.results[0].createdByName}`
      )
    ).toBeVisible();
    fireEvent.click(screen.getByTestId('home-view-event-button'));

    expect(mockNavigate).toHaveBeenLastCalledWith(
      `/event/${mockGetEventsResponse.results[0].id}`
    );

    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });

    window.history.back();
    expect(historyBackSpy).toHaveBeenCalled();

    await waitFor(() => {
      expect(screen.getByTestId('home-events-tab')).toHaveClass('Mui-selected');
    });

    window.history.forward();
    expect(historyForwardSpy).toHaveBeenCalled();

    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });
  });

  it('navigate to event details page with a double click', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
            <Event />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
    });

    fireEvent.dblClick(
      screen.getByTestId(`event-row-0-${mockGetEventsResponse.results[0].id}`)
    );

    expect(mockNavigate).toHaveBeenLastCalledWith(
      `/event/${mockGetEventsResponse.results[0].id}`
    );

    await waitFor(() => {
      expect(screen.getByTestId('event-heading-name')).toBeInTheDocument();
    });
  });

  it('navigate to file details page and back', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    const historyBackSpy = jest.spyOn(window.history, 'back');
    const historyForwardSpy = jest.spyOn(window.history, 'forward');

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
            <FileViewer />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
      expect(screen.getByTestId('home-events-tab')).toHaveClass('Mui-selected');
    });

    // switch to files tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      // check for file tabs to be selected
      expect(screen.getByTestId('home-files-tab')).toHaveClass('Mui-selected');
      expect(screen.getByText('Select a file.')).toBeInTheDocument();
    });

    // check last mockNavigate call
    expect(mockNavigate).toHaveBeenLastCalledWith(
      '/?activeTab=files&page=1&limit=100'
    );

    fireEvent.click(
      screen.getByTestId(`file-row-${mockGetFilesResponse.results[0].id}`)
    );
    fireEvent.click(screen.getByTestId('home-view-file-button'));

    expect(mockNavigate).toHaveBeenLastCalledWith(
      `/event/${mockGetFilesResponse.results[0].eventId}/file/${mockGetFilesResponse.results[0].id}`
    );

    await waitFor(() => {
      expect(screen.getByTestId('tabbed-detections')).toBeInTheDocument();
      expect(
        screen.getByTestId('file-viewer-tracklet-detail-no-file')
      ).toBeInTheDocument();
    });

    window.history.back();
    expect(historyBackSpy).toHaveBeenCalled();

    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toHaveClass('Mui-selected');
    });

    window.history.back();
    expect(historyBackSpy).toHaveBeenCalledTimes(2);

    window.history.forward();
    expect(historyForwardSpy).toHaveBeenCalled();

    await waitFor(() => {
      expect(screen.getByTestId('home-files-tab')).toHaveClass('Mui-selected');
    });

    window.history.forward();
    expect(historyForwardSpy).toHaveBeenCalledTimes(2);

    await waitFor(() => {
      expect(screen.getByTestId('tabbed-detections')).toBeInTheDocument();
      expect(
        screen.getByTestId('file-viewer-tracklet-detail-no-file')
      ).toBeInTheDocument();
    });
  });

  it('navigate to file details page and back', async () => {
    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <BrowserRouter>
            <Home />
            <FileViewer />
          </BrowserRouter>
        </I18nProvider>
      </Provider>
    );

    // wait for event from mock response to become available
    await waitFor(() => {
      expect(
        screen.getByText(mockGetEventsResponse.results[0].name)
      ).toBeInTheDocument();
      expect(screen.getByTestId('home-events-tab')).toHaveClass('Mui-selected');
    });

    // switch to files tab
    switchToTab('home-files-tab');

    await waitFor(() => {
      // check for file tabs to be selected
      expect(screen.getByTestId('home-files-tab')).toHaveClass('Mui-selected');
      expect(screen.getByText('Select a file.')).toBeInTheDocument();
    });

    // check last mockNavigate call
    expect(mockNavigate).toHaveBeenLastCalledWith(
      '/?activeTab=files&page=1&limit=100'
    );

    fireEvent.dblClick(
      screen.getByTestId(`file-row-${mockGetFilesResponse.results[0].id}`)
    );

    expect(mockNavigate).toHaveBeenLastCalledWith(
      `/event/${mockGetFilesResponse.results[0].eventId}/file/${mockGetFilesResponse.results[0].id}`
    );

    await waitFor(() => {
      expect(screen.getByTestId('tabbed-detections')).toBeInTheDocument();
      expect(
        screen.getByTestId('file-viewer-tracklet-detail-no-file')
      ).toBeInTheDocument();
    });
  });
});
