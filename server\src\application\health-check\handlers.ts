import { NextFunction, Response } from 'express';
import NodeCache from 'node-cache';
import { RequestWithMeta } from '../types';
import GQLApi from '@util/api/graphQL';
import { schemas } from './schemas';
import A from './actions';
// import E from './errors';

const createHandlers = ({
  log,
  gql,
  cache,
}: {
  log: Logger;
  gql: GQLApi;
  cache: NodeCache;
}) => ({
  get: {
    health: async (
      req: RequestWithMeta<void>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        cache,
        data: {},
        gql,
        validations: [
          { schema: schemas.get.query.health, validationData: req.query },
        ],
      };

      try {
        // Example actions
        // await A.validateToken(cxt);
        // await A.validateRequest(cxt);
        // await A.exampleAction(cxt);
        await A.send<{ message: string }, object>((_cxt) => ({
          message: 'I am alive!',
        }))(cxt);
      } catch (e) {
        switch (e.constructor) {
          // Example error handling
          // case E.ValidationError:
          //   return A.sendError(cxt)(400)(e);
          // case E.ForbiddenError:
          //   return A.sendError(cxt)(403)(e);
          // case E.UnauthorizedError:
          //   return A.sendError(cxt)(401)(e);
          // case E.ActionError:
          default:
            return A.sendError(cxt)(500)(e);
        }
      } finally {
        next();
      }
    },
  },
});

export default createHandlers;
