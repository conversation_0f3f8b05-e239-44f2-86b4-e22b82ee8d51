{"apiRoot": "https://api.stage.us-gov-2.veritone.com", "credentialApi": "https://tracker2.stage.us-gov-2.veritone.com/api/v1/credential", "veritoneAppId": "f6a2b4e5-79cc-46b6-91b7-4fb6decbacd0", "graphQLEndpoint": "v3/graphQL", "nodeEnv": "development", "port": 3002, "serviceName": "tracker-server", "startApi": "true", "useRedis": false, "cloud": "azure", "blob": {"endpointSuffix": "core.usgovcloudapi.net", "account": "vtstorcoredev", "key": "REPLACE_WITH_AZURE_BLOB_KEY", "container": "tracker2", "expireSecs": 3600}, "s3": {"bucket": "tracker2-dev", "accessKey": "replace with accessKey for local dev, no need for ec2", "secretKey": "replace with secretKey for local dev, no need for ec2", "roleArn": "arn:aws:iam::************:role/VeritoneGLCAssumeRole", "region": "us-east-1", "expireSecs": 3600}, "trackerEngineId": "f6634718-c2b7-40f5-9c2c-a606420104ac", "validWritableCredentialEngineIds": ["f6634718-c2b7-40f5-9c2c-a606420104ac", "d77d6133-a801-472c-bc7e-48ddafec8590"], "outputWriterEngineId": "8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3", "glcIngestorEngineId": "da093aca-2a6b-4577-8bfe-2b19a2f2faea", "defaultClusterId": "edge1-5d90f115-2a33-454c-a1cd-933a856d7862", "videoSliceEngineId": "3d9425b7-02be-44b0-9c5d-53e711449588", "registryIds": {"eventsRegistryId": "fee755bb-6561-431a-a58c-d1bea11a828c", "matchGroupsRegistryId": "38a6cfcc-e70d-4f25-999e-16dc95ee8568"}}