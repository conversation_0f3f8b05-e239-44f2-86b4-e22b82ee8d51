import { convertAttributes } from './convertAttributes';
import {
  Attributes,
  ConvertedAttributes,
} from '../../../../../../types/tracker';

describe('convertAttributes', () => {
  it('should return an empty object if no attributes are provided', () => {
    const result = convertAttributes();
    expect(result).toEqual(undefined);
  });

  it('should convert attributes correctly', () => {
    const attributes: Attributes = {
      person: [
        {
          label: 'AccessoryBagAny',
          value: 'BagAny',
          key: 'Accessory',
        },
        {
          label: 'AccessoryHandbag',
          value: 'Handbag',
          key: 'Accessory',
        },
      ],
      vehicle: [
        {
          label: 'Color_blue',
          value: 'blue',
          key: 'Color',
        },
        {
          label: 'Color_red',
          value: 'red',
          key: 'Color',
        },
      ],
    };

    const expected: ConvertedAttributes = {
      Accessory: ['BagAny', 'Handbag'],
      Color: ['blue', 'red'],
    };

    const result = convertAttributes(attributes);
    expect(result).toEqual(expected);
  });

  it('should handle attributes with the same key correctly', () => {
    const attributes: Attributes = {
      vehicle: [
        {
          label: 'Color_blue',
          value: 'blue',
          key: 'Color',
        },
        {
          label: 'Color_red',
          value: 'red',
          key: 'Color',
        },
      ],
    };

    const expected: ConvertedAttributes = {
      Color: ['blue', 'red'],
    };

    const result = convertAttributes(attributes);
    expect(result).toEqual(expected);
  });

  it('should handle empty attributes object correctly', () => {
    const attributes: Attributes = {};

    const result = convertAttributes(attributes);
    expect(result).toEqual(undefined);
  });
});
