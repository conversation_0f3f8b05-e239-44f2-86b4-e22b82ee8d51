import { isString } from 'lodash';
import * as QueryString from 'qs';

export function validateSortParams(
  sortBy: QueryString.ParsedQs['param'],
  sortDirection: QueryString.ParsedQs['param'],
  validSortByValues: string[],
  validSortDirectionValues: string[]
) {
  if (!isString(sortBy) || !isString(sortDirection)) {
    return null;
  }
  const isValidSortBy = validSortByValues.includes(sortBy);
  const isValidSortDirection = validSortDirectionValues.includes(sortDirection);

  if (!isValidSortBy || !isValidSortDirection) {
    return {
      error: `Invalid sortBy or sortDirection parameter. Received sortBy: ${sortBy}, sortDirection: ${sortDirection}`,
      details: {
        sortBy: sortBy,
        sortDirection: sortDirection,
      },
    };
  }

  return null;
}
