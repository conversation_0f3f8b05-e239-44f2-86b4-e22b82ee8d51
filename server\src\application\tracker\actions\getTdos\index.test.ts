import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { getTdos } from '../getTdos';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({})
  ),
}));

describe('getTdos', () => {
  let headers: RequestHeader = {
    authorization: 'Bearer validToken',
  };
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers,
        params: {},
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('throws ActionError when no tdoIds are provided', async () => {
    await expect(
      getTdos({
        tdoIds: [],
        context: cxt,
        headers: headers,
      })
    ).rejects.toThrowError('No tdoIds provided');
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Successfully queries TDO', async () => {
    const mockData = {
      temporalDataObject_123_0: {
        id: '123-123',
        name: 'test1',
      },
      temporalDataObject_123_1: {
        id: '123-122',
        name: 'test2',
      },
    };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve(mockData)
    );
    const tdoIds = ['123', '456'];
    const response = await getTdos({
      tdoIds,
      context: cxt,
      headers: headers,
    });

    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {},
      true
    );
    expect(response).not.toBeNull();
    expect(response).toEqual({
      '123-123': {
        id: '123-123',
        name: 'test1',
      },
      '123-122': {
        id: '123-122',
        name: 'test2',
      },
    });
  });

  it('throws GraphQLError when there is an error in the GraphQL query', async () => {
    const mockError = new Error('GraphQL query failed');

    (callGQL as jest.Mock).mockImplementationOnce(() => {
      throw mockError;
    });

    const tdoIds = ['123', '456'];
    await expect(
      getTdos({
        tdoIds,
        context: cxt,
        headers: headers,
      })
    ).rejects.toThrowError('GraphQL query failed');
    expect(callGQL).toHaveBeenCalledTimes(1);
  });
});
