import messages from './messages';
import { IntlProvider } from 'react-intl';
import { Fragment, ReactNode } from 'react';
import { LOCALES, LocaleTypes } from './locales';

const Provider = ({ children, locale }: Props) => (
  <IntlProvider
    locale={locale}
    defaultLocale={LOCALES.ENGLISH}
    textComponent={Fragment}
    messages={messages[locale]}
  >
    {children}
  </IntlProvider>
);

interface Props {
  readonly children: ReactNode | undefined;
  readonly locale: LocaleTypes;
}

export default Provider;
