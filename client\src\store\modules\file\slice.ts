import { createAppSlice } from '../../createAppSlice';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  FileDeletionState,
  FileState,
  TrackletsState,
  FileResultsState,
  EventState,
  TrackletFileState,
  BoundingBoxesState,
  SelectedAttributes,
  TrackletType,
} from './types';
import { RootState } from '@store/store';
import HttpClient from '@store/dependencies/httpClient';
import getApiAuthToken from '@utility/getApiAuthToken';
import { AlertLevel, createSnackNotification } from '@components/common';
import {
  DeleteFileResponse,
  GetBoundingBoxesResponse,
  GetEventResponse,
  GetFileResponse,
  GetFileSearchResultsResponse,
  GetThumbnailsResponse,
  SearchTrackletsWithIntersectingBoxResponse,
  UpdateFileResponse,
} from '@shared-types/responses';
import qs from 'qs';
import { Attributes, BoundingPoly, Tracklet } from '@shared-types/tracker';
import ls from 'localstorage-slim';

// Json file
import { isExpiringString } from '@utility/expiringString';
import { convertJsonAttributes } from '@utility/convertJsonAttributes';
import { getAttributesJson } from '@utility/getAttributesJson';
import { clamp, isNumber, isObject, isString } from 'lodash';
import {
  getBoundingBoxLocalStorage,
  UpdatedFileLocalStorage,
} from '@utility/localStorage';
import { AxiosError } from 'axios';

export interface FileSliceState {
  tracklets: {
    person: TrackletsState;
    vehicle: TrackletsState;
  };
  attributes: {
    person: Attributes;
    vehicle: Attributes;
  };
  event: EventState;
  file: FileState;
  fileDeletion: FileDeletionState;
  fileResults: {
    vehicle: FileResultsState;
    person: FileResultsState;
  };
  selectedTracklet: {
    vehicle?: Tracklet;
    person?: Tracklet;
  };
  selectedTrackletFile: {
    vehicle: TrackletFileState;
    person: TrackletFileState;
  };
  boundingBoxes: BoundingBoxesState;
  selectedAttributes: SelectedAttributes;
  thumbnailUrls: Record<
    string,
    {
      thumbnailUrls: {
        best: string;
      };
      expiresDateTime: string;
    }
  >;
  numberOfSourceBoundingBoxes: number;
  enableTrackletsIntersection: boolean;
}

const initialState: FileSliceState = {
  tracklets: {
    vehicle: {
      data: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    person: {
      data: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
  },
  event: {
    data: undefined,
    apiStatus: 'idle',
    error: '',
  },
  attributes: {
    person: {},
    vehicle: {},
  },
  file: {
    id: '',
    fileName: '',
    createdByName: '',
    status: 'processing',
    length: 0,
    uploadDate: '',
    location: '',
    fileType: '',
    fileSize: 0,
    apiStatus: 'idle',
    eventId: '',
    eventName: '',
    thumbnailUrl: '',
    primaryAsset: {
      signedUri: '',
    },
    streams: [],
    frameRate: 0,
    thumbnailAssets: undefined,
  },
  fileDeletion: {
    id: '',
    status: 'idle',
    message: '',
  },
  fileResults: {
    vehicle: {
      results: [],
      fileId: '',
      eventId: '',
      type: 'vehicle',
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      apiStatus: 'idle',
      error: '',
    },
    person: {
      results: [],
      fileId: '',
      eventId: '',
      type: 'person',
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      apiStatus: 'idle',
      error: '',
    },
  },
  selectedTracklet: {
    vehicle: undefined,
    person: undefined,
  },
  selectedTrackletFile: {
    vehicle: {
      file: undefined,
      apiStatus: 'idle',
      error: '',
    },
    person: {
      file: undefined,
      apiStatus: 'idle',
      error: '',
    },
  },
  boundingBoxes: {
    data: [],
    apiStatus: 'idle',
    error: '',
  },
  selectedAttributes: {
    person: {},
    vehicle: {},
  },
  thumbnailUrls: ls.get('thumbnailUrls') || {},
  numberOfSourceBoundingBoxes: 0,
  enableTrackletsIntersection: false,
};

const convertSelectedAttributes = (selectedAttributes: SelectedAttributes) => {
  const convertedAttributes: { [key: string]: string[] } = {};
  Object.keys(selectedAttributes).forEach((category) => {
    const attributes = selectedAttributes[category as keyof SelectedAttributes];
    if (attributes) {
      const trueAttributes = Object.keys(attributes).filter(
        (attribute) => attributes[attribute]
      );
      if (trueAttributes.length > 0) {
        convertedAttributes[category] = trueAttributes;
      }
    }
  });
  return convertedAttributes;
};

export const fileSlice = createAppSlice({
  name: 'file',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>(); // TODO: Fix getState type
    return {
      setThumbnails: create.reducer(
        (
          state,
          action: PayloadAction<
            Record<
              string,
              {
                thumbnailUrls: {
                  best: string;
                };
                expiresDateTime: string;
              }
            >
          >
        ) => {
          state.thumbnailUrls = action.payload;
          ls.set('thumbnailUrls', state.thumbnailUrls);
        }
      ),
      getBoundingBoxes: createHttpThunk(
        async (
          {
            fileId,
            trackletId,
            startTimeMs,
            stopTimeMs,
            type,
          }: {
            fileId?: string;
            trackletId?: string;
            startTimeMs?: number;
            stopTimeMs?: number;
            type: 'person' | 'vehicle';
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetBoundingBoxesResponse>(signal)(
            `bounding-boxes${qs.stringify(
              { fileId, trackletId, startTimeMs, stopTimeMs, type },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.boundingBoxes.apiStatus = 'loading';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = '';
          },
          fulfilled: (state, action) => {
            state.boundingBoxes.apiStatus = 'idle';
            state.boundingBoxes.data = action.payload.results;
            state.boundingBoxes.error = '';
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Bounding Boxes failed',
              action.error.message
            );
            state.boundingBoxes.apiStatus = 'failure';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = action.error.message;
          },
        }
      ),
      getFileResultsById: createHttpThunk(
        async (
          {
            trackletType,
            fileId,
            eventId,
            page: currentPage,
            limit: pageSize,
          }: {
            trackletType: TrackletType;
            fileId: string;
            eventId: string;
            page: number;
            limit: number;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          // TODO: Why does getState not have the correct type?
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const selectedAttributes = (getState() as any).file
            ?.selectedAttributes;

          const selectedAttributesByType: SelectedAttributes =
            trackletType === 'person'
              ? selectedAttributes.person
              : selectedAttributes.vehicle;

          const response = await http.get<GetFileSearchResultsResponse>(signal)(
            `event/${eventId}/file/${fileId}/results/${qs.stringify(
              {
                type: trackletType,
                pageSize,
                currentPage,
                fileId,
                attributes: convertSelectedAttributes(selectedAttributesByType),
                times: true,
              },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );

          return response.data;
        },
        {
          pending: (state, { meta: { arg } }) => {
            if (arg.trackletType === 'person') {
              state.fileResults.person.apiStatus = 'loading';
            }
            if (arg.trackletType === 'vehicle') {
              state.fileResults.vehicle.apiStatus = 'loading';
            }
          },
          fulfilled: (state, action) => {
            state.numberOfSourceBoundingBoxes =
              state.enableTrackletsIntersection
                ? getBoundingBoxLocalStorage(action.meta.arg.fileId).length
                : 0;

            if (action.meta.arg.trackletType === 'person') {
              state.fileResults.person = {
                ...action.payload,
                apiStatus: 'idle',
              };
            }
            if (action.meta.arg.trackletType === 'vehicle') {
              // TODO add vehicle to action payload mocked data
              state.fileResults.vehicle = {
                ...action.payload,
                results: action.payload.results.map((result) => ({
                  ...result,
                  type: 'vehicle',
                })),
                apiStatus: 'idle',
              };
            }
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Get File Results failed',
                action.error.message
              );
              if (action.meta.arg.trackletType === 'person') {
                state.fileResults.person.apiStatus = 'failure';
              } else if (action.meta.arg.trackletType === 'vehicle') {
                state.fileResults.vehicle.apiStatus = 'failure';
              }
            }
          },
        }
      ),
      getFileResultsByRegion: createHttpThunk(
        async (
          {
            trackletType,
            fileId,
            eventId,
            sourceBoundingBox,
            page: currentPage,
            limit: pageSize,
          }: {
            trackletType: TrackletType;
            fileId: string;
            eventId: string;
            sourceBoundingBox: BoundingPoly;
            page: number;
            limit: number;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const xValues = sourceBoundingBox.map((point) => point.x);
          const yValues = sourceBoundingBox.map((point) => point.y);

          const xMin = clamp(Math.min(...xValues), 0, 1).toPrecision(4);
          const xMax = clamp(Math.max(...xValues), 0, 1).toPrecision(4);
          const yMin = clamp(Math.min(...yValues), 0, 1).toPrecision(4);
          const yMax = clamp(Math.max(...yValues), 0, 1).toPrecision(4);

          const response =
            await http.get<SearchTrackletsWithIntersectingBoxResponse>(signal)(
              `event/${eventId}/file/${fileId}/results/intersection/${qs.stringify(
                {
                  type: trackletType,
                  pageSize,
                  currentPage,
                  xMin,
                  xMax,
                  yMin,
                  yMax,
                },
                { addQueryPrefix: true }
              )}`,
              {
                // TODO: Fix getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              }
            );

          return response.data;
        },
        {
          pending: (state, { meta: { arg } }) => {
            if (arg.trackletType === 'person') {
              state.fileResults.person.apiStatus = 'loading';
            }
            if (arg.trackletType === 'vehicle') {
              state.fileResults.vehicle.apiStatus = 'loading';
            }
          },
          fulfilled: (state, action) => {
            if (action.meta.arg.trackletType === 'person') {
              state.fileResults.person = {
                ...action.payload,
                eventId: action.meta.arg.eventId,
                apiStatus: 'idle',
              };
            }
            if (action.meta.arg.trackletType === 'vehicle') {
              state.fileResults.vehicle = {
                ...action.payload,
                eventId: action.meta.arg.eventId,
                results: action.payload.results.map((result) => ({
                  ...result,
                  type: 'vehicle',
                })),
                apiStatus: 'idle',
              };
            }
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Get File Results failed',
                action.error.message
              );
              if (action.meta.arg.trackletType === 'person') {
                state.fileResults.person.apiStatus = 'failure';
              } else if (action.meta.arg.trackletType === 'vehicle') {
                state.fileResults.vehicle.apiStatus = 'failure';
              }
            }
          },
        }
      ),
      setFileResultsPagination: create.reducer(
        (
          state,
          action: PayloadAction<{
            trackletType: TrackletType;
            currentPage: number;
            pageSize: number;
          }>
        ) => {
          if (action.payload.trackletType === 'vehicle') {
            state.fileResults.vehicle.currentPage = action.payload.currentPage;
            state.fileResults.vehicle.pageSize = action.payload.pageSize;
          }
          if (action.payload.trackletType === 'person') {
            state.fileResults.person.currentPage = action.payload.currentPage;
            state.fileResults.person.pageSize = action.payload.pageSize;
          }
        }
      ),
      setAttributes: create.reducer((state) => {
        const personJsonAttributes = getAttributesJson('person');
        const vehicleJsonAttributes = getAttributesJson('vehicle');

        const personAttributes = convertJsonAttributes(personJsonAttributes);
        const vehicleAttributes = convertJsonAttributes(vehicleJsonAttributes);

        state.attributes = {
          person: personAttributes,
          vehicle: vehicleAttributes,
        };
      }),
      setSelectedTracklet: create.reducer(
        (state, action: PayloadAction<{ tracklet: Tracklet }>) => {
          if (action.payload.tracklet.type === 'vehicle') {
            state.selectedTracklet.vehicle = action.payload.tracklet;
          }
          if (action.payload.tracklet.type === 'person') {
            state.selectedTracklet.person = action.payload.tracklet;
          }
        }
      ),
      getFileById: createHttpThunk(
        async (
          {
            fileId,
          }: {
            fileId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
            rejectWithValue,
          } = thunkAPI;
          try {
            const response = await http.get<GetFileResponse>(signal)(
              `/file/${fileId}`,
              {
                // TODO: Fix getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              }
            );
            return response.data.file;
          } catch (error) {
            const { response } = error as AxiosError;
            return rejectWithValue(response?.data);
          }
        },
        {
          pending: (state) => {
            state.file.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.file = { ...action.payload, apiStatus: 'idle' };
          },
          rejected: (state, action) => {
            const isErrorMessage = (data: unknown): data is { message: string } =>
              isObject(data) && "message" in data && typeof data.message === 'string';

            createSnackNotification(
              AlertLevel.Error,
              'Get file failed',
              isErrorMessage(action.payload) ? action.payload.message : action.error.message
            );
            state.file.apiStatus = 'failure';
          },
        }
      ),
      updateFileById: createHttpThunk(
        async (
          {
            fileId,
            fileName,
          }: {
            fileId: string;
            fileName: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.patch<UpdateFileResponse>(signal)(
            `/file/${fileId}`,
            {
              name: fileName,
            },
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );

          return response.data.file;
        },
        {
          pending: (state) => {
            state.file.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.file.apiStatus = 'idle';
            state.file.fileName = action.payload.fileName;
            createSnackNotification(
              AlertLevel.Success,
              'Success',
              'Updated file name successfully'
            );

            // Store updated fileId and fileName in localStorage
            const { fileId, fileName } = action.meta.arg;
            let updatedFiles: UpdatedFileLocalStorage[] | null =
              ls.get('updatedFiles');
            if (Array.isArray(updatedFiles)) {
              updatedFiles = updatedFiles.filter(
                (item) =>
                  isString(item.value.fileId) &&
                  isString(item.value.fileName) &&
                  isNumber(item.expiry)
              );
            } else {
              updatedFiles = [];
            }

            // Add the new updated file to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: {
                fileId,
                fileName,
              },
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };

            const existingUpdatedFile = updatedFiles.findIndex(
              (f) => f.value.fileId === item.value.fileId
            );
            if (existingUpdatedFile > -1) {
              updatedFiles.splice(existingUpdatedFile - 1, 1);
            }
            updatedFiles.push(item);
            // Store the updated array back in localStorage
            ls.set('updatedFiles', updatedFiles);
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Update file name failed',
                action.error.message
              );
              state.file.apiStatus = 'failure';
            } else {
              state.file.apiStatus = 'idle';
            }
          },
        }
      ),
      getFile: createHttpThunk(
        async ({ tracklet }: { tracklet: Tracklet }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetFileResponse>(signal)(
            `/file/${tracklet.fileId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state, { meta: { arg } }) => {
            if (arg.tracklet.type === 'person') {
              state.selectedTrackletFile.person.apiStatus = 'loading';
            }
            if (arg.tracklet.type === 'vehicle') {
              state.selectedTrackletFile.vehicle.apiStatus = 'loading';
            }
          },
          fulfilled: (state, action) => {
            if (action.meta.arg.tracklet.type === 'person') {
              state.selectedTrackletFile.person = {
                file: action.payload.file,
                apiStatus: 'idle',
              };
            }
            if (action.meta.arg.tracklet.type === 'vehicle') {
              state.selectedTrackletFile.vehicle = {
                file: action.payload.file,
                apiStatus: 'idle',
              };
            }
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            if (action.meta.arg.tracklet.type === 'person') {
              state.selectedTrackletFile.person.apiStatus = 'failure';
            }
            if (action.meta.arg.tracklet.type === 'vehicle') {
              state.selectedTrackletFile.vehicle.apiStatus = 'failure';
            }
          },
        }
      ),
      getEvent: createHttpThunk(
        async (
          {
            eventId,
          }: {
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
            rejectWithValue,
          } = thunkAPI;
          try {
            const response = await http.get<GetEventResponse>(signal)(
              `/event/${eventId}`,
              // TODO: Fix getState type
              { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
            );
            return response.data;
          } catch (error) {
            const { response } = error as AxiosError;
            return rejectWithValue(response?.data);
          }
        },
        {
          pending: (state) => {
            state.event.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.event = { data: action.payload.event, apiStatus: 'idle' };
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              const isErrorMessage = (data: unknown): data is { message: string } =>
                isObject(data) && "message" in data && typeof data.message === 'string';

              createSnackNotification(
                AlertLevel.Error,
                'Get event failed',
                isErrorMessage(action.payload) ? action.payload.message : action.error.message
              );
              state.event.apiStatus = 'failure';
            } else {
              state.event.apiStatus = 'idle';
            }
          },
        }
      ),
      deleteFile: createHttpThunk(
        async (fileId: string, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.delete<DeleteFileResponse>(signal)(
            `/file/${fileId}`,
            undefined,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state, action) => {
            state.fileDeletion.error = '';
            state.fileDeletion.status = 'loading';
            state.fileDeletion.id = action.meta.arg;
          },
          fulfilled: (state, action) => {
            const { code } = action.payload;
            if (code === 500) {
              state.fileDeletion.id = '';
              state.fileDeletion.status = 'failure';
              state.fileDeletion.error = 'Delete Event Failed';
              createSnackNotification(
                AlertLevel.Error,
                'Error',
                'Delete Event Failed'
              );
              return;
            }
            state.fileDeletion.id = '';
            state.fileDeletion.error = '';
            state.fileDeletion.status = 'idle';
            createSnackNotification(
              AlertLevel.Success,
              'Success',
              action.payload.message
            );

            // Store deleted file ID in localStorage
            const fileId = action.meta.arg;
            let deletedFileIds: Array<{
              value: string;
              expiry: number;
            }> | null = ls.get('deletedFileIds');
            if (Array.isArray(deletedFileIds)) {
              deletedFileIds = deletedFileIds.filter(isExpiringString);
            } else {
              deletedFileIds = [];
            }

            // Add the new deleted file ID to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: fileId,
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };
            deletedFileIds.push(item);

            // Store the updated array back in localStorage
            ls.set('deletedFileIds', deletedFileIds);
          },
          rejected: (state, action) => {
            state.fileDeletion.id = '';
            state.fileDeletion.status = 'failure';
            state.fileDeletion.error = action.error.message;
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Delete Event Failed'
            );
          },
        }
      ),
      setSelectedAttributes: create.reducer(
        (state, action: PayloadAction<SelectedAttributes>) => {
          state.selectedAttributes = action.payload;
          state.selectedTracklet = {
            vehicle: undefined,
            person: undefined,
          };
          state.selectedTrackletFile = {
            vehicle: {
              file: undefined,
              apiStatus: 'idle',
              error: '',
            },
            person: {
              file: undefined,
              apiStatus: 'idle',
              error: '',
            },
          };
          state.boundingBoxes = {
            data: [],
            apiStatus: 'idle',
            error: '',
          };
        }
      ),
      getThumbnails: createHttpThunk(
        async (
          tracklets: Array<{
            trackletId: string;
            orgId: string;
            fileId: string;
          }>,
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<GetThumbnailsResponse>(signal)(
            '/thumbnails',
            { tracklets },
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          fulfilled: (state, action) => {
            Object.assign(state.thumbnailUrls, action.payload.thumbnails);
            state.thumbnailUrls = Object.entries(state.thumbnailUrls).reduce(
              (acc, [key, value]) =>
                new Date(value.expiresDateTime) <= new Date()
                  ? acc
                  : { ...acc, [key]: value },
              {}
            );
            ls.set('thumbnailUrls', state.thumbnailUrls);
          },
          rejected: (_state, _action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Get Thumbnails Failed'
            );
          },
        }
      ),
      updateNumberOfSourceBoundingBoxes: create.reducer(
        (state, action: PayloadAction<number>) => {
          state.numberOfSourceBoundingBoxes = action.payload;
        }
      ),
      setEnableTrackletsIntersection: create.reducer(
        (state, action: PayloadAction<boolean>) => {
          state.enableTrackletsIntersection = action.payload;
        }
      )
    };
  },
  selectors: {
    selectBoundingBoxes: (state) => state.boundingBoxes.data,
    selectSelectedTracklet: (state) => state.selectedTracklet,
    selectAttributes: (state) => state.attributes,
    selectFile: (state) => state.file,
    selectEvent: (state) => state.event,
    selectFileResults: (state) => state.fileResults,
    selectFileDeletion: (state) => state.fileDeletion,
    selectSelectedTrackletFile: (state) => state.selectedTrackletFile,
    selectSelectedAttributes: (state) => state.selectedAttributes,
    selectThumbnails: (state) => state.thumbnailUrls,
    selectNumberOfSourceBoundingBoxes: (state) =>
      state.numberOfSourceBoundingBoxes,
    selectEnableTrackletsIntersection: (state) =>
      state.enableTrackletsIntersection,
  },
});

export const {
  getBoundingBoxes,
  getFileResultsById,
  getFileResultsByRegion,
  setSelectedTracklet,
  setFileResultsPagination,
  setAttributes,
  getEvent,
  getFileById,
  updateFileById,
  getFile,
  deleteFile,
  setSelectedAttributes,
  getThumbnails,
  setThumbnails,
  updateNumberOfSourceBoundingBoxes,
  setEnableTrackletsIntersection,
} = fileSlice.actions;

export const {
  selectBoundingBoxes,
  selectFileResults,
  selectAttributes,
  selectSelectedTracklet,
  selectFile,
  selectEvent,
  selectFileDeletion,
  selectSelectedTrackletFile,
  selectSelectedAttributes,
  selectThumbnails,
  selectNumberOfSourceBoundingBoxes,
  selectEnableTrackletsIntersection,
} = fileSlice.selectors;
