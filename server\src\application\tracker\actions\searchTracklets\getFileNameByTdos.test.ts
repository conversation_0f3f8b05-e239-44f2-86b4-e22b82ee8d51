import { getFileNameByTdos } from './getFileNameByTdos';
import { createRequest, createResponse } from 'node-mocks-http';
import { Context, RequestHeader } from '../../../types';
import GQLApi from '../../../../util/api/graphQL';
import consoleLogger from '../../../../logger';
import { callGQL } from '../../../../util/api/graphQL/callGraphQL';
import NodeCache from 'node-cache';

const logger = jest.fn();

const ctx: Context<object, object> = {
  req: createRequest({
    headers: {
      authorization: 'Bearer validToken',
    },
    query: {
      pageSize: 50,
      currentPage: 1,
      query: 'query',
    },
  }),
  log: {
    info: logger,
    error: logger,
    debug: logger,
  },
  res: createResponse(),
  queries: {},
  gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
  cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
  data: {},
};

const headers: RequestHeader = {
  authorization: 'Bearer validToken',
};

const mockCallGQL = callGQL as jest.MockedFunction<typeof callGQL>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(),
}));

const mockRecords = (count: number) =>
  Array.from({ length: count }, (_, i) => ({
    id: `file${i + 1}`,
  }));

describe('getFileNameByTdos', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns records when count is less than the limit (no loop)', async () => {
    mockCallGQL.mockResolvedValueOnce({
      temporalDataObjects: {
        count: 50,
        records: mockRecords(2),
      },
    });

    const results = await getFileNameByTdos({
      context: ctx,
      headers,
      tdoIds: ['file1', 'file2'],
    });

    expect(results).toEqual([{ id: 'file1' }, { id: 'file2' }]);
    expect(mockCallGQL).toHaveBeenCalledTimes(1);
  });

  it('iterates through the loop until the count is less than the limit', async () => {
    mockCallGQL
      .mockResolvedValueOnce({
        temporalDataObjects: {
          count: 100,
          records: mockRecords(100),
        },
      })
      .mockResolvedValueOnce({
        temporalDataObjects: {
          count: 100,
          records: mockRecords(100),
        },
      })
      .mockResolvedValueOnce({
        temporalDataObjects: {
          count: 20, // stop looping
          records: mockRecords(20),
        },
      });

    const results = await getFileNameByTdos({
      context: ctx,
      headers,
      tdoIds: ['file1', 'file2'],
    });

    expect(results.length).toEqual(220);
    expect(mockCallGQL).toHaveBeenCalledTimes(3); // 1 initial + 2 looped
    expect(ctx.log.error).not.toHaveBeenCalled();
  });

  it('stops looping and logs an error when the offset limit of 3000 is reached', async () => {
    mockCallGQL.mockResolvedValue({
      temporalDataObjects: {
        count: 100,
        records: mockRecords(100),
      },
    });

    const results = await getFileNameByTdos({
      context: ctx,
      headers,
      tdoIds: ['file1', 'file2'],
    });

    expect(mockCallGQL).toHaveBeenCalledTimes(31); // 31 calls to `callGQL` (1 initial + 30 looped) make offset greater than 3000
    // call error log
    expect(ctx.log.error).toHaveBeenCalledWith(
      'ERROR: Hit limit on trying to get file names'
    );
    expect(results.length).toEqual(3100); // 31 times call with 100 records each
  });
});
