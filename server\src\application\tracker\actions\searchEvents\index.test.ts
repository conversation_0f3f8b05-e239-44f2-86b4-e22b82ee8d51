import NodeCache from 'node-cache';
import searchEvents from '../searchEvents';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ searchMedia: { jsondata: { results: [] } } })
  ),
}));

describe('Search Events', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        query: {
          pageSize: 50,
          currentPage: 1,
          query: 'query',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('eventsSchemaId', 'eventsSchemaId');
  });

  it('Query w/ empty value searches all events', async () => {
    cxt.req.query.event = '';

    const response = await searchEvents(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [],
          },
          type: 'eventsSchemaId',
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('No params passed to search all events', async () => {
    cxt.req.query = {};

    const response = await searchEvents(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [],
          },
          type: 'eventsSchemaId',
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Inputed params to be handled correctly', async () => {
    cxt.req.query.pageSize = '10';
    cxt.req.query.currentPage = '2';
    cxt.req.query.event = ' event Search with Spaces  ';
    cxt.req.query.tag = ' tag Search with Spaces  ';

    const response = await searchEvents(cxt);

    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          type: 'eventsSchemaId',
          limit: 10,
          offset: 10,
          query: {
            operator: 'or',
            conditions: [
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'name.fulltext',
                    operator: 'query_string',
                    value: '*event Search with Spaces*',
                  },
                ],
              },
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'tags.fulltext',
                    operator: 'query_string',
                    value: '*tag Search with Spaces*',
                  },
                ],
              },
            ],
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  const sortingOptions = [
    {
      sortBy: 'name',
      sortDirection: 'asc',
    },
    {
      sortBy: 'eventStartDate',
      sortDirection: 'desc',
    },
    {
      sortBy: 'eventStartDate',
      sortDirection: 'asc',
    },
    {
      sortBy: 'eventEndDate',
      sortDirection: 'desc',
    },
    {
      sortBy: 'eventEndDate',
      sortDirection: 'asc',
    },
  ];

  for (const sort of sortingOptions) {
    it(`SearchEvents query are sorted by ${sort.sortBy} and ${sort.sortDirection} parameters`, async () => {
      cxt.req.query.event = '';
      cxt.req.query.sortBy = sort.sortBy;
      cxt.req.query.sortDirection = sort.sortDirection;
      const response = await searchEvents(cxt);

      expect(callGQL).toHaveBeenCalledTimes(1);
      expect(callGQL).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        {
          search: {
            index: ['mine'],
            limit: 50,
            offset: 0,
            sort: [{ field: sort.sortBy, order: sort.sortDirection }],
            query: {
              conditions: [],
              operator: 'or',
            },
            type: 'eventsSchemaId',
          },
        }
      );

      expect(response).not.toBeNull();
    });
  }
});
