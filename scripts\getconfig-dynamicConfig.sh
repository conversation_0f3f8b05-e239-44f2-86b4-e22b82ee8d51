#!/bin/bash

environment="${ENVIRONMENT}"
name="${APPLICATION}"
# AWS_STS_ACCOUNT_ID="${AWS_STS_ACCOUNT_ID}" # this one we will need to install aws cli to get info

configfile="/tmp/config-${environment}.json"
apiConfigfile="/tmp/appConfig-${environment}.json"

binaries_endpoint="binaries-lb.aws-${environment}.veritone.com"
vpc_dns_zone_name="aws-${environment}.veritone.com"

GLOBAL_BASE_URL="http://${binaries_endpoint}/conf/global/base.json"
GLOBAL_APP_URL="http://${binaries_endpoint}/conf/global/${name}.json"
ENV_BASE_URL="http://${binaries_endpoint}/conf/aws-${environment}/base.json"
ENV_APP_URL="http://${binaries_endpoint}/conf/aws-${environment}/${name}.json"

if echo "${environment}" | grep -E "^[A-Za-z]{3,4}[0-9]{1,2}-.*" &> /dev/null; then
    binaries_endpoint="binaries.${environment//-/.}.veritone.com"
    vpc_dns_zone_name="${environment//-/.}.veritone.com"

    GLOBAL_BASE_URL="http://${binaries_endpoint}/conf/global/base.json"
    GLOBAL_APP_URL="http://${binaries_endpoint}/conf/global/${name}.json"
    ENV_BASE_URL="http://${binaries_endpoint}/conf/${environment}/base.json"
    ENV_APP_URL="http://${binaries_endpoint}/conf/${environment}/${name}.json"
fi

echo "GLOBAL_BASE_URL=${GLOBAL_BASE_URL}"
echo "GLOBAL_APP_URL=${GLOBAL_APP_URL}"
echo "ENV_BASE_URL=${ENV_BASE_URL}"
echo "ENV_APP_URL=${ENV_APP_URL}"

# Download config files
# Only retry global base since it's guaranteed to be there
curl --retry 5 --retry-delay 2 -L ${GLOBAL_BASE_URL} --output /tmp/global_base.json || { echo 'Config retrieval failed' ; exit 1; }
cat /tmp/global_base.json
curl -L ${GLOBAL_APP_URL} --output /tmp/global_${name}.json
cat /tmp/global_${name}.json
curl -L ${ENV_BASE_URL} --output /tmp/${environment}_base.json
cat /tmp/${environment}_base.json
curl -L ${ENV_APP_URL} --output /tmp/${environment}_${name}.json
cat /tmp/${environment}_${name}.json

# Verify each config is valid json, if not replace with empty json {}
declare -a configFiles=(
    /tmp/global_base.json
    /tmp/global_${name}.json
    /tmp/${environment}_base.json
    /tmp/${environment}_${name}.json
)
for file in "${configFiles[@]}"; do
    echo "Checking if $file contains valid json."
    jq . ${file} >/dev/null 2>&1 || {
        echo "Invalid json found in $file, replacing with {}" ;
        echo "{}" >${file} ;
    }
done

echo "Retrieving publicDnsZoneName from environment base config"
public_dns_zone_name=$(cat /tmp/${environment}_base.json | jq '.publicDnsZoneName' | tr -d '" ')

echo "Combining config to ${configfile}"
# Combine configs, globalBase ---> globalApp ---> envBase ---> envApp
jq -s '.[0] * .[1] * .[2] * .[3]' \
    "${configFiles[@]}" > ${configfile}.in

template_file () {
    [[ -f $1 ]] || return 0
    # copy all permissions from template.in -> rendered_file:
    cp -p $1 ${1%%.in}

    # NOTE: quote styles here are mixed based on need:
    sed -e "s/@@INTERNAL_DNS_ZONE@@/${vpc_dns_zone_name}/" \
        -e "s/@@EXTERNAL_DNS_ZONE@@/${public_dns_zone_name}/" \
        -e "s/@@ENVIRONMENT@@/${environment}/" \
        -e "s/@@AWS_STS_ACCOUNT_ID@@/${AWS_STS_ACCOUNT_ID}/" \
	     $1 > ${1%%.in}
}

jq '. as $whitelist | input | with_entries(select([.key] | inside($whitelist)))' /configWhitelist.json ${configfile}.in  > ${configfile}_filtered.in

template_file ${configfile}_filtered.in

echo "Final ${configfile}"
cat ${configfile}_filtered

cp ${configfile}_filtered /config/config-${environment}.json
echo "Copied to config/config-${environment}.json"

echo "Combining config to ${apiConfigfile}"
# Combine configs, globalBase ---> globalApp ---> envBase ---> envApp
jq -s '.[0] * .[1] * .[2] * .[3]' \
    "${configFiles[@]}" > ${apiConfigfile}.in

getApiConfigFile () {
    [[ -f $1 ]] || return 0
    # copy all permissions from template.in -> rendered_file:
    cp -p $1 ${1%%.in}

    # NOTE: quote styles here are mixed based on need:
    sed -e '1s/^//' \
        -e "s/@@INTERNAL_DNS_ZONE@@/${vpc_dns_zone_name}/" \
        -e "s/@@EXTERNAL_DNS_ZONE@@/${public_dns_zone_name}/" \
        -e "s/@@ENVIRONMENT@@/${environment}/" \
        -e "s/@@AWS_STS_ACCOUNT_ID@@/${AWS_STS_ACCOUNT_ID}/" \
	     $1 > ${1%%.in}
}

jq '. as $whitelist | input | with_entries(select([.key] | inside($whitelist)))' /apiConfigWhitelist.json ${apiConfigfile}.in  > ${apiConfigfile}_filtered.in

getApiConfigFile ${apiConfigfile}_filtered.in

echo "Final ${apiConfigfile}"
cp ${apiConfigfile}_filtered /server/dist/server/apiConfig.json
echo "Copied to /server/dist/server/apiConfig.json"
cat ${apiConfigfile}_filtered
