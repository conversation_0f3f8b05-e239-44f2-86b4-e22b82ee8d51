import env from '../../../../env';
import {
  generateAzureReadOnlyCredential,
  generateAzureWritableCredential,
} from './azure';
import {
  generateS3ReadOnlyCredential,
  generateS3WritableCredential,
  signedUrl,
} from './aws';
import NodeCache from 'node-cache';

export async function buildReadOnlyUrl(path: string, cache?: NodeCache) {
  const cloud = env.cloud;
  const writable = false;
  if (cloud === 'azure') {
    return buildBlobUrl(path, writable, cache);
  } else if (cloud === 'aws') {
    return buildS3Url(path, writable, cache);
  } else {
    throw new Error('unsupported cloud provider');
  }
}

export async function buildWritableUrl(path: string, cache?: NodeCache) {
  const cloud = env.cloud;
  const writable = true;
  if (cloud === 'azure') {
    return buildBlobUrl(path, writable, cache);
  } else if (cloud === 'aws') {
    return buildS3Url(path, writable, cache);
  } else {
    throw new Error('unsupported cloud provider');
  }
}

export function buildBlobUrl(
  path: string,
  writable: boolean,
  cache?: NodeCache
) {
  let creds: {
    storage: string;
    storageUrl: string;
    credential: {
      sasToken: string;
    };
  };
  if (writable) {
    creds = generateAzureWritableCredential(cache);
  } else {
    creds = generateAzureReadOnlyCredential(cache);
  }
  const url = `${creds.storageUrl}/${path}?${creds.credential.sasToken}`;
  return url;
}

export async function buildS3Url(
  path: string,
  writable: boolean,
  cache?: NodeCache
) {
  let creds: {
    storage: string;
    bucket: string;
    region: string;
    credential: {
      accessKeyId: string;
      secretAccessKey: string;
      sessionToken: string;
    };
  };

  if (writable) {
    creds = await generateS3WritableCredential(cache);
  } else {
    creds = await generateS3ReadOnlyCredential(cache);
  }

  const url = await signedUrl({
    bucketName: creds.bucket,
    accessKey: creds.credential.accessKeyId,
    secretKey: creds.credential.secretAccessKey,
    sessionToken: creds.credential.sessionToken,
    region: creds.region,
    key: path,
    writable,
  });
  return url;
}
