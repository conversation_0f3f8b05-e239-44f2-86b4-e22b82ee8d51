import { Context } from '../../../types';
import { Variables } from 'graphql-request';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { GraphQLError, ActionValidationError } from '@common/errors';
import getMatchGroups from '../getMatchGroups';
import searchFiles from '../searchFiles';

interface SearchVars extends Variables {
  search: {
    index: string[];
    type: string;
    query: {
      operator: string;
      conditions: {
        operator: string;
        conditions: {
          operator: string;
          conditions: { field: string; operator: string; value: string }[];
        }[];
      }[];
    };
  };
}

const searchEventsByIds = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.searchEvents>> | undefined
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };
  const { pendingEventIds } = req.query;

  try {
    const schemaId = cache.get<string>('eventsSchemaId');
    if (!schemaId) {
      throw new ActionValidationError('schemaId not found');
    }

    const searchVars: SearchVars = {
      search: {
        index: ['mine'],
        type: schemaId,
        query: {
          operator: 'or',
          conditions: [],
        },
      },
    };

    const eventConditions =
      pendingEventIds &&
      Array.isArray(pendingEventIds) &&
      pendingEventIds.map((eventId) => ({
        operator: 'or',
        conditions: [
          {
            field: 'id',
            operator: 'term',
            value: eventId.toString(),
          },
        ],
      }));

    if (eventConditions && eventConditions.length > 0) {
      searchVars.search.query.conditions.push({
        operator: 'or',
        conditions: eventConditions,
      });
    }

    const { searchMedia } = await callGQL<
      responses.searchMedia<{
        id: string;
        name: string;
        tags: string[];
        createdBy: string;
        createdByName: string;
        description: string;
        eventStartDate: string;
        eventEndDate: string;
        createdDateTime: string;
        modifiedDateTime: string;
      }>,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, searchVars);
    if (searchMedia) {
      const searchEvents = {
        searchResults: await Promise.all(
          searchMedia.jsondata.results.map(async (result) => {
            req.query.eventId = result.id;
            const [matchGroupsContext, searchFilesContext] = await Promise.all([
              getMatchGroups(context),
              searchFiles(context),
            ]);
            return {
              ...result,
              matchGroupsCount: matchGroupsContext.data.matchGroupsCount,
              filesCount: searchFilesContext.data.filesCount,
            };
          })
        ),
        pageSize: searchMedia.jsondata.limit,
        currentPage: searchMedia.jsondata.from / searchMedia.jsondata.limit + 1,
        totalCount: Number(searchMedia.jsondata.totalResults),
        totalPages: Math.ceil(
          Number(searchMedia.jsondata.totalResults) / searchMedia.jsondata.limit
        ),
      };
      const new_data = Object.assign({}, data, {
        searchEvents: searchEvents,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e.message);
  }
};

export default searchEventsByIds;
