import { render, screen } from '@testing-library/react';
import { MemoryRouter, useRoutes } from 'react-router-dom';
import { RouteWrapper, routes } from '@components/common/Routes/Routes';

const TestRoutes = () => {
  const element = useRoutes(routes);
  return element;
};

jest.mock('./useRouteChanged', () => jest.fn());


jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useRoutes: jest.fn(),
  useLocation: jest.fn(() => ({ pathname: '/', search: '' })),
}));

jest.mock('react-redux', () => ({
  useDispatch: {
    __esModule: true,
    default: jest.fn(),
    withTypes: jest.fn(),
  },
  useSelector: jest.fn().mockReturnValue(true),
}));

describe('Routes', () => {
  it('sets path to / for Home component', () => {
    (useRoutes as jest.Mock).mockReturnValue(<div>/</div>);
    render(
      <MemoryRouter initialEntries={['/']}>
        <TestRoutes />
      </MemoryRouter>
    );
    expect(screen.getByText('/')).toBeInTheDocument();
  });

  it('sets path to /counter for Counter component', () => {
    (useRoutes as jest.Mock).mockReturnValue(<div>/counter</div>);
    render(
      <MemoryRouter initialEntries={['/counter']}>
        <TestRoutes />
      </MemoryRouter>
    );
    expect(screen.getByText('/counter')).toBeInTheDocument();
  });

  it('sets path to /not-found for NotFound component', () => {
    (useRoutes as jest.Mock).mockReturnValue(<div>/not-found</div>);
    render(
      <MemoryRouter initialEntries={['/not-found']}>
        <TestRoutes />
      </MemoryRouter>
    );
    expect(screen.getByText('/not-found')).toBeInTheDocument();
  });
});

describe('RouteWrapper', () => {
  it('renders its children', () => {
    const { asFragment } = render(
      <RouteWrapper>
        <div>Test child</div>
      </RouteWrapper>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(screen.getByText('Test child')).toBeInTheDocument();
  });
});
