import { GraphQLError } from '@common/errors';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { Context } from '../../../types';
import * as ResTypes from '../../../../../../types/responses';

const lookupLatestSchemaId = async <
  ReqPayload,
  Data extends ResTypes.LookupLatestSchemaId,
>(
  context: Context<ReqPayload, Data>
) => {
  const { data, req, log: _log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { registryId } = data;

  try {
    const { dataRegistry } = await callGQL<
      responses.lookupLatestSchemaId,
      ReqPayload,
      Data
    >(context, headers, queries.lookupLatestSchemaId, { registryId });

    if (dataRegistry?.publishedSchema?.id) {
      data.schemaId = dataRegistry.publishedSchema.id;
      return context;
    }
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
};

export default lookupLatestSchemaId;
