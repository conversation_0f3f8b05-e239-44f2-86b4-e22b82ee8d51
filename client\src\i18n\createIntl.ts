import messages from './messages';
import { LOCALES } from './locales';
import { createIntl } from 'react-intl';

const defaultLocale = LOCALES.ENGLISH;
export const LOCALSTORAGE_LANGUAGE_KEY = 'vtn_lang_preference';

export const sagaIntl = () => {
  const storeLanguage =
    window.localStorage?.getItem(LOCALSTORAGE_LANGUAGE_KEY) ??
    window.navigator.language;

  const [_, locale] = Object.entries({
    en: LOCALES.ENGLISH,
  }).find(([key]) => storeLanguage.includes(key)) || [undefined, defaultLocale];

  return createIntl({
    locale,
    messages: messages[locale],
  });
};
