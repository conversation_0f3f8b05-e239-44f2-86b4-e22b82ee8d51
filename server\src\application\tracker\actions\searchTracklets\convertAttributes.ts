import { isEmpty } from 'lodash';
import {
  Attributes,
  ConvertedAttributes,
} from '../../../../../../types/tracker';

export function convertAttributes(
  attributes?: Attributes
): ConvertedAttributes | undefined {
  const result: ConvertedAttributes = {};

  if (!attributes || isEmpty(attributes)) {
    return undefined;
  }

  for (const key in attributes) {
    // if (attributes.hasOwnProperty(key)) {
    if (Object.prototype.hasOwnProperty.call(attributes, key)) {
      attributes[key].forEach((attribute) => {
        if (!result[attribute.key]) {
          result[attribute.key] = [];
        }
        result[attribute.key].push(attribute.value);
      });
    }
  }

  return result;
}
