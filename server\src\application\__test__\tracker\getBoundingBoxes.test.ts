import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { callGQL } from '@util/api/graphQL/callGraphQL';

const mockedSearchResults = {
  data: {
    searchMedia: {
      jsondata: {
        results: [
          {
            hits: [
              {
                'object-recognition': {
                  series: [
                    {
                      start: 867,
                      end: 992,
                      boundingPoly: [
                        {
                          x: 0.871875,
                          y: 0.3111111,
                        },
                        {
                          x: 0.9992188,
                          y: 0.3111111,
                        },
                        {
                          x: 0.9992188,
                          y: 0.4777778,
                        },
                        {
                          x: 0.871875,
                          y: 0.4777778,
                        },
                      ],
                      referenceId: '7b2bc912-7197-409a-9f0f-750456a12667',
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
    },
  },
  trackletData: {
    searchMedia: {
      jsondata: {
        results: [
          {
            id: 'tdo123',
            label: 'person',
            referenceId: 'trackletId123',
          },
        ],
      },
    },
  },
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }

      if (query.includes('searchMedia')) {
        // TODO: why is this cast safe?
        const search = variables?.search as {
          index: string[];
          query: {
            conditions: {
              value?: string;
              query?: { conditions: { value: string }[] };
            }[];
          };
        };
        if (search.index[0] === 'fingerprint') {
          return Promise.resolve({
            searchMedia: mockedSearchResults.trackletData.searchMedia,
          });
        }
        if (
          search?.query?.conditions?.[0]?.query?.conditions[0].value ===
            'trackletId123' ||
          search.query.conditions[1].value === 'tdo123'
        ) {
          return Promise.resolve({
            searchMedia: mockedSearchResults.data.searchMedia,
          });
        }
        if (search.query.conditions[1].value === 'tdo-No-Results') {
          return Promise.resolve({
            searchMedia: {},
          });
        }
      }
    }
  ),
}));

describe('get bounding boxes', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('get bounding boxes by file', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    const resp = await supertest(expressApp)
      .get(
        '/api/v1/bounding-boxes?fileId=tdo123&startTimeMs=500&stopTimeMs=2000&type=car'
      )
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          seriesLimit: 10000,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'object-recognition.series',
                query: {
                  operator: 'and',
                  conditions: [
                    {
                      operator: 'range',
                      field: 'start',
                      gte: 500,
                    },
                    {
                      operator: 'range',
                      field: 'end',
                      lte: 2000,
                    },
                  ],
                },
                innerHits: {
                  sort: [
                    {
                      'object-recognition.series.start': { order: 'asc' },
                    },
                  ],
                },
              },
              {
                operator: 'term',
                field: 'recordingId',
                value: 'tdo123',
              },
            ],
          },
        },
      }
    );
    const want = {
      results: [
        {
          boundingPoly: [
            {
              x: 0.871875,
              y: 0.3111111,
            },
            {
              x: 0.9992188,
              y: 0.3111111,
            },
            {
              x: 0.9992188,
              y: 0.4777778,
            },
            {
              x: 0.871875,
              y: 0.4777778,
            },
          ],
          trackletId: '7b2bc912-7197-409a-9f0f-750456a12667',
          startTimeMs: 867,
          stopTimeMs: 992,
        },
      ],
    };
    expect(resp.body).toEqual(want);
  });

  it('get bounding boxes by tracklet', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    const resp = await supertest(expressApp)
      .get('/api/v1/bounding-boxes?trackletId=trackletId123')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);
    expect(callGQL).toHaveBeenLastCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          seriesLimit: 10000,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'object-recognition.series',
                query: {
                  operator: 'and',
                  conditions: [
                    {
                      operator: 'term',
                      field: 'referenceId',
                      value: 'trackletId123',
                    },
                  ],
                },
                innerHits: {
                  sort: [
                    {
                      'object-recognition.series.start': { order: 'asc' },
                    },
                  ],
                },
              },
            ],
          },
        },
      }
    );
    const want = {
      results: [
        {
          boundingPoly: [
            {
              x: 0.871875,
              y: 0.3111111,
            },
            {
              x: 0.9992188,
              y: 0.3111111,
            },
            {
              x: 0.9992188,
              y: 0.4777778,
            },
            {
              x: 0.871875,
              y: 0.4777778,
            },
          ],
          trackletId: '7b2bc912-7197-409a-9f0f-750456a12667',
          startTimeMs: 867,
          stopTimeMs: 992,
        },
      ],
    };
    expect(resp.body).toEqual(want);
  });

  it('returns no results in response when no result found', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/bounding-boxes?fileId=tdo-No-Results')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);
    expect(resp.body).toEqual({ results: [] });
  });

  it('gives 404 error when no file or trackletId provided', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1//api/v1/bounding-boxes')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
