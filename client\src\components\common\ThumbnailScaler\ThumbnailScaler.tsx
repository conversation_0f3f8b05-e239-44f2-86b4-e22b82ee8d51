import './ThumbnailScaler.scss';
import { useState, MouseEvent } from 'react';
import { Button, Menu, Slider, Skeleton } from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { I18nTranslate } from '@i18n';

const ThumbnailScaler = ({
  scale,
  setScale,
  loading,
}: Props) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const setThumbnailScale = (newScale: number) => {
    setScale(newScale);
    localStorage.setItem('thumbnailScale', newScale.toString());
  };

  return (
    <div className="thumbnail-scaler" data-testid="thumbnail-scaler">
      {loading &&
        <Skeleton
          className="pagination-page-size"
          variant="rectangular"
          width={135}
          height={22}
        />}
      {!loading && (
        <>
          <div className="thumbnail-scaler-text">
            {I18nTranslate.TranslateMessage('thumbnailScale')}
          </div>
          <Button
            data-testid="thumbnail-scaler-button"
            onClick={handleClick}
            style={{
              color: '#424242',
              fontWeight: 'normal',
              fontSize: 16,
              backgroundColor: 'transparent',
            }}
          >
            {`${scale}%`}
            <ArrowDropDownIcon />
          </Button>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <div
              style={{
                width: 250,
                padding: '5px 25px 0px 25px',
              }}
            >
              <Slider
                step={10}
                min={60}
                max={200}
                value={scale}
                valueLabelDisplay="off"
                data-testid={'thumbnail-scaler-slider'}
                onChange={(_e: unknown, newValue: number | number[]) =>
                  setThumbnailScale(Array.isArray(newValue) ? newValue[0] : newValue)
                }
              />
            </div>
          </Menu>
        </>
      )}
    </div>
  );
};

interface Props {
  scale: number;
  setScale: (scale: number) => void;
  loading: boolean;
}

export default ThumbnailScaler;
