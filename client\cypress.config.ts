import { defineConfig } from 'cypress';
import { addEnvToConfig } from './cypress/plugins';
import { addCucumberPreprocessorPlugin } from '@badeball/cypress-cucumber-preprocessor';
import webpackPreprocessor from '@cypress/webpack-preprocessor';

export default defineConfig({
  viewportWidth: 1920,
  viewportHeight: 1080,
  defaultCommandTimeout: 30000,
  execTimeout: 30000,
  taskTimeout: 30000,
  pageLoadTimeout: 30000,
  requestTimeout: 20000,
  responseTimeout: 30000,
  numTestsKeptInMemory: 20,
  chromeWebSecurity: false,
  e2e: {
    setupNodeEvents: async (
      on: Cypress.PluginEvents,
      config: Cypress.PluginConfigOptions
    ) => {
      let curConfig = config;

      await addCucumberPreprocessorPlugin(on, config);
      curConfig = {
        ...config,
        ...addEnvToConfig(on, {
          env: config.env || {},
          baseUrl: config.baseUrl || '',
        }),
      };
      const options = {
        webpackOptions: {
          resolve: {
            extensions: ['.ts', '.js'],
          },
          module: {
            rules: [
              {
                test: /\.ts$/,
                exclude: [/node_modules/],
                use: [
                  {
                    loader: 'ts-loader',
                    options: {
                      transpileOnly: true,
                    },
                  },
                ],
              },
              {
                test: /\.feature$/,
                use: [
                  {
                    loader: '@badeball/cypress-cucumber-preprocessor/webpack',
                    options: config,
                  },
                ],
              },
            ],
          },
        },
        watchOptions: {},
      };

      on('file:preprocessor', webpackPreprocessor(options));
      config.env.stepDefinitions = 'cypress/e2e/step_definitions/**/*.{js,ts}';
      return curConfig;
    },
    retries: {
      runMode: 1,
      openMode: 0
    },
    baseUrl: 'https://local.veritone.com:3004',
    specPattern: ['cypress/e2e/**/*.feature', 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}']
  },
});
