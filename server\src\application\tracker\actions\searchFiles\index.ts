import getFolder from '../getFolder';
import { Context } from '../../../types';
import getFileStatus from '../util/getFileStatus';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { GraphQLError, ValidationError } from '@common/errors';
import { validateSortParams } from '../../actions/util/validateSortParams';
import { isString, pick, isEmpty } from 'lodash';
import { TRACK_FILE_TAG } from '../createFileTemporalData';
import { TemporalData } from '../../../../../../types/tracker';
import { getTdos } from '../getTdos';
import { fetchJsonData } from '../getJsonData';
const SORT_FIELD = ['veritone-file.filename', 'recordingId', 'createdTime'];
const SORTDIRECTION = ['desc', 'asc'];
interface Condition {
  operator: string;
  field?: string;
  value?: string;
  values?: string[];
  not?: boolean;
  query?: {
    operator: string;
    field: string;
    value: string;
    dotNotation: boolean;
  };
  conditions?: Condition[];
}

// interface TdoIdsContext {
//   tdoIds: string[];
// }

const searchFiles = async <
  ReqPayload,
  Data extends Partial<responses.getEvent & { eventId?: string }> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    Data & Partial<responses.searchFiles & { filesCount: number }>
  >
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { pageSize, currentPage, file, sortBy, sortDirection } = req.query;

  const eventId = req.query?.eventId ?? req.params?.eventId ?? data?.eventId;

  let folder: responses.getFolder['folder'] | undefined;
  if (eventId && isString(eventId)) {
    try {
      data.eventId = eventId;
      const ctx = await getFolder<ReqPayload, Data>(context);
      folder = ctx?.data.folder;
    } catch (e) {
      log.error(e);
      return { ...context, data: { ...data, filesCount: 0 } };
    }
  }

  const validationError = validateSortParams(
    sortBy,
    sortDirection,
    SORT_FIELD,
    SORTDIRECTION
  );
  if (validationError) {
    throw new ValidationError(validationError.error);
  }

  const conditions: Condition[] = [
    {
      operator: 'query_object',
      field: 'tags',
      not: false,
      query: {
        operator: 'term',
        field: 'tags.value',
        value: TRACK_FILE_TAG,
        dotNotation: true,
      },
    },
  ];

  if (typeof file === 'string' && file) {
    conditions.push({
      operator: 'and',
      conditions: file.split(' ').map((value) => ({
        field: 'veritone-file.filename',
        operator: 'query_string',
        value: `*${value.trim()}*`,
      })),
    });
  }

  if (eventId && folder?.treeObjectId) {
    conditions.push({
      field: 'parentTreeObjectIds',
      operator: 'terms',
      values: [folder.treeObjectId],
    });
  }

  try {
    const searchQuery = {
      index: ['mine'],
      type: 'file',
      select: ['veritone-file'],
      limit: Number(pageSize ?? 50),
      offset: (Number(currentPage ?? 1) - 1) * Number(pageSize ?? 50),
      ...(sortBy &&
        sortDirection && {
          sort: [{ field: sortBy, order: sortDirection }],
        }),
      query:
        conditions.length > 0 ? { operator: 'and', conditions } : undefined,
    };
    let _start = Date.now();
    const { searchMedia } = await callGQL<
      responses.searchMedia<
        TemporalData & {
          context: {
            'veritone-file': {
              createdbyname: string;
              filename: string;
              duration: number;
              filetype: string;
              filesize: number;
              videoframerate: number;
            };
          }[];
          recording: {
            recordingId: string;
            createdTime: string;
          };
        }
      >,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, { search: searchQuery });

    let _end = Date.now();

    // log.debug(`Search files query took ${end - start}ms`);

    if (!searchMedia) {
      return context;
    }

    const tdoIds = searchMedia.jsondata.results.map(
      (result) => result.recording.recordingId
    );
    let tdos: {
      [key: string]: {
        id: string;
        name: string;
        assets?: { records: { assetType: string; signedUri: string }[] };
        eventId: string;
        eventName: string;
        thumbnailUrl: string;
        thumbnailAssets?: {
          records: Array<{
            id: string;
            name: string;
            contentType: string;
            signedUri: string;
            details: Record<string, unknown>;
          }>;
        };
        streams: {
          uri: string;
          protocol: string;
        }[];
        jobs: { records: { name: string; status: string }[] };
      };
    } = {};
    if (tdoIds.length > 0) {
      _start = Date.now();

      tdos = await getTdos({
        tdoIds,
        context,
        headers,
      });

      _end = Date.now();

      // log.debug(`Get TDOs took ${end - start}ms`);
    }

    const newData = Object.assign({}, data, {
      searchFiles: {
        searchResults: await Promise.all(
          searchMedia.jsondata.results.map(async (result) => {
            const tdo = tdos[result.recording.recordingId];

            const status = getFileStatus(tdo);

            const veritoneFile = result.context[0]?.['veritone-file'];
            const tdoId = result.recording.recordingId;
            const eventData = !folder
              ? pick(tdos[tdoId], ['eventId', 'eventName'])
              : {
                  eventId: folder?.id,
                  eventName: folder?.name,
                };
            const assets = tdos?.[tdoId]?.assets;
            const mediaDetailsAsset = assets?.records?.find(
              (asset) => asset.assetType === 'media-details'
            );

            const dataAsset = await fetchJsonData(mediaDetailsAsset?.signedUri);

            const thumbnailUrl = tdos?.[tdoId]?.thumbnailUrl;
            const streams = tdos?.[tdoId]?.streams;
            const thumbnailAssets = tdos?.[tdoId]?.thumbnailAssets;
            const location = !isEmpty(dataAsset?.summary?.location_exif)
              ? `(${dataAsset.summary.location_exif.GPSLatitude}, ${dataAsset.summary.location_exif.GPSLongitude})`
              : 'unavailable';
            return {
              id: tdoId,
              fileName: veritoneFile?.filename,
              status,
              createdByName: veritoneFile?.createdbyname,
              length: veritoneFile?.duration,
              uploadDate: result.recording.createdTime,
              location,
              fileType: veritoneFile?.filetype,
              fileSize: dataAsset?.summary?.size_bytes,
              thumbnailUrl,
              primaryAsset: {
                signedUri: result?.primaryAsset?.signedUri ?? '',
              },
              thumbnailAssets,
              streams,
              frameRate: veritoneFile?.videoframerate ?? 0,
              ...eventData,
            };
          })
        ),
        pageSize: searchMedia.jsondata.limit,
        currentPage: searchMedia.jsondata.from / searchMedia.jsondata.limit + 1,
        totalCount: Number(searchMedia.jsondata.totalResults.value),
        totalPages: Math.ceil(
          Number(searchMedia.jsondata.totalResults.value) /
            searchMedia.jsondata.limit
        ),
      },
      filesCount: Number(searchMedia.jsondata.totalResults.value),
    });
    return { ...context, data: newData };
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default searchFiles;
