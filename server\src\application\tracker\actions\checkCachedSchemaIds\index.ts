import env from '../../../../env';
import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';

export const checkCachedSchemaIdEvents = async <
  ReqPaylod,
  Data extends object = object,
>(
  context: Context<ReqPaylod, Data>
) => await checkCachedSchemaIds<ReqPaylod, Data>(context, 'eventsRegistryId');

export const checkCachedSchemaIdMatchGroups = async <
  ReqPaylod,
  Data extends object = object,
>(
  context: Context<ReqPaylod, Data>
) =>
  await checkCachedSchemaIds<ReqPaylod, Data>(context, 'matchGroupsRegistryId');

const checkCachedSchemaIds = async <ReqPaylod, Data extends object = object>(
  context: Context<ReqPaylod, Data>,
  registryId: keyof typeof env.registryIds
) => {
  const { cache, data: _data, req, log: _log } = context;
  const headers = { Authorization: req.headers.authorization };
  const schemaId = registryId.replace('Registry', 'Schema');
  const params = req.params;
  if (cache.get(schemaId)) {
    return context;
  }

  try {
    const { dataRegistry } = await callGQL<
      responses.lookupLatestSchemaId,
      ReqPaylod,
      Data
    >(context, headers, queries.lookupLatestSchemaId, {
      registryId: env.registryIds[registryId],
    });

    if (dataRegistry?.publishedSchema?.id) {
      cache.set(schemaId, dataRegistry.publishedSchema.id);
    }
    const new_req = Object.assign({}, req, {
      headers: req.headers,
      params: params,
    });
    const new_context = Object.assign({}, context, { req: new_req });
    return new_context;
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
};
