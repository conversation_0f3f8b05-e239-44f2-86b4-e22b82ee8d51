import { put, select, takeEvery } from 'typed-redux-saga';
import { deleteFile, selectFileDeletion } from './slice';
import { getFiles, selectFiles } from '../home/<USER>';
import { Action } from '@reduxjs/toolkit';

export function* watchDeleteFile() {
  const { currentPage, pageSize } = yield select(selectFiles);
  const { error } = yield select(selectFileDeletion);
  if (!error) {
    yield put(getFiles({ currentPage, pageSize }) as unknown as Action);
  }
}

export function* watchFileSagas() {
  yield takeEvery(deleteFile.fulfilled, watchDeleteFile);
}

const fileSagas = watchFileSagas;

export default fileSagas;
