import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import getFile from '../getFile';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import { fetchJsonData } from '../getJsonData';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

let cxt: Context<object, responses.getFile>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        temporalDataObject: {},
      })
  ),
}));

jest.mock('../getJsonData', () => ({
  fetchJsonData: jest.fn(),
}));

describe('Get Temporal Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    cxt = {
      data: {
        file: {
          id: 'id',
          fileName: 'fileName',
          status: 'processed',
          length: 0,
          createdByName: 'createdByName',
          uploadDate: 'uploadDate',
          location: 'location',
          fileType: 'fileType',
          fileSize: 0,
          eventId: 'eventId',
          eventName: 'eventName',
          thumbnailUrl: 'thumbnail',
          primaryAsset: { signedUri: 'signedUri' },
          streams: [],
          frameRate: 0,
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {
          fileId: 'fileId',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Successfully queries a TDO', async () => {
    const mockTDO = {
      id: 'fileId',
      jobs: {
        records: [
          {
            status: 'complete',
            name: 'Track Ingest Job',
          },
        ],
      },
      details: {
        veritoneFile: {
          createdByName: 'createdByName',
          fileName: 'fileName',
          duration: 100,
          fileType: 'fileType',
          fileSize: 1000,
          videoFrameRate: 25,
        },
        createdDateTime: 'createdDateTime',
      },
      folders: [{ id: 'eventId', name: 'eventName' }],
      assets: {
        records: [{ assetType: 'media-details' }],
      },
      thumbnailAssets: {
        records: [
          {
            id: 'id',
            name: 'id',
            contentType: 'contentType',
            signedUri: 'signedUri',
            details: {},
          },
        ],
      },
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [{ uri: 'uri', protocol: 'dash' }],
    };

    const mockDataAsset = { summary: { size_bytes: 1000 } };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        temporalDataObject: mockTDO,
      })
    );
    (fetchJsonData as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve(mockDataAsset)
    );
    const response = await getFile(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { tdoId: 'fileId' }
    );
    expect(response).not.toBeNull();
    expect(response?.data?.file).toStrictEqual({
      fileName: 'fileName',
      createdByName: 'createdByName',
      fileSize: 1000,
      fileType: 'fileType',
      id: 'fileId',
      length: 100,
      location: 'unavailable',
      status: 'processed',
      uploadDate: 'createdDateTime',
      eventId: 'eventId',
      eventName: 'eventName',
      thumbnailAssets: {
        records: [
          {
            id: 'id',
            name: 'id',
            contentType: 'contentType',
            signedUri: 'signedUri',
            details: {},
          },
        ],
      },
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [{ uri: 'uri', protocol: 'dash' }],
      frameRate: 25,
    });
  });

  it('Successfully queries and handles GPS data in location_exif', async () => {
    clientRedis.get = jest.fn().mockResolvedValue(null);
    const mockTDO = {
      id: 'fileId',
      jobs: {
        records: [
          {
            status: 'complete',
            name: 'Track Ingest Job',
          },
        ],
      },
      details: {
        veritoneFile: {
          createdByName: 'createdByName',
          fileName: 'fileName',
          duration: 100,
          fileType: 'fileType',
          fileSize: 1000,
          videoFrameRate: 25,
        },
        createdDateTime: 'createdDateTime',
      },
      folders: [{ id: 'eventId', name: 'eventName' }],
      assets: {
        records: [{ assetType: 'media-details' }],
      },
      thumbnailAssets: {
        records: [
          {
            id: 'id',
            name: 'id',
            contentType: 'contentType',
            signedUri: 'signedUri',
            details: {},
          },
        ],
      },
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [{ uri: 'uri', protocol: 'dash' }],
    };

    const mockDataAsset = {
      summary: {
        has_audio: true,
        has_video: true,
        mimetype: 'video/mp4',
        size_bytes: 1791488,
        duration: 3.535714,
        segment_duration: 5.0,
        audio_sample_rate: 48000,
        audio_num_channels: 2,
        video_frame_rate: 28,
        width: 1080,
        height: 1440,
        location_exif: {
          GPSAltitude: '0',
          GPSAltitudeRef: '0',
          GPSLatitude: '40.71249',
          GPSLongitude: '-111.86710',
        },
      },
    };

    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        temporalDataObject: mockTDO,
      })
    );
    (fetchJsonData as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve(mockDataAsset)
    );

    const response = await getFile(cxt);

    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { tdoId: 'fileId' }
    );
    expect(fetchJsonData).toHaveBeenCalledTimes(1);
    expect(response).not.toBeNull();
    expect(response?.data?.file).toStrictEqual({
      fileName: 'fileName',
      createdByName: 'createdByName',
      fileSize: 1791488,
      fileType: 'fileType',
      id: 'fileId',
      length: 100,
      location: '(40.71249, -111.86710)',
      status: 'processed',
      uploadDate: 'createdDateTime',
      eventId: 'eventId',
      eventName: 'eventName',
      thumbnailAssets: {
        records: [
          {
            id: 'id',
            name: 'id',
            contentType: 'contentType',
            signedUri: 'signedUri',
            details: {},
          },
        ],
      },
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [{ uri: 'uri', protocol: 'dash' }],
      frameRate: 25,
    });
  });

  it('Does not attempt to query w/o fileId', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.req.params.fileId = undefined;

    expect(async () => await getFile(cxt)).rejects.toThrowError(
      'No fileId provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
