import Redis from 'ioredis';
import { Env } from './env';

async function createRedisCache({
  env,
  log,
}: {
  env?: Env;
  log?: Logger;
}): Promise<Redis | undefined> {
  if (env?.useRedis) {
    const host =
      process?.env?.AIWARE_REDIS_HOST || env?.redis?.host || 'localhost';
    const port = env?.redis?.port || 6379;
    const username = process?.env?.AIWARE_REDIS_USERNAME;
    const password = process?.env?.AIWARE_REDIS_PASSWORD;
    const keyPrefix = 'track:redisTrack:';
    let client;
    if (username && password) {
      client = new Redis({
        host,
        port,
        username,
        password,
        keyPrefix,
        lazyConnect: true,
      });
    } else {
      client = new Redis({ host, port, keyPrefix, lazyConnect: true });
    }

    await client.connect();
    if (client.status === 'ready') {
      log?.info('Connected to Redis');
    }
    return client;
  }
}

export default createRedisCache;
