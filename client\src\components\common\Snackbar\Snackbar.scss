/* Should match the constants in code for snackbar */
$snackHeight: 96px;
$snack-z: 9999;

.snackbar {
  position: relative;
  width: 400px;
  height: 0;
}

.snackbar__mobile-clickoff {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.snackbar__box {
  position: absolute;
  width: 400px;
  height: $snackHeight;
  padding: 16px 23px 16px 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: var(--snack-bar-background);
  opacity: 1;
  transition: top 200ms, opacity 2000ms;
  z-index: $snack-z;

  &.success {
    color: var(--snack-bar-success);
    border-top: solid 3px var(--snack-bar-success-border);
    border-right: solid 1px var(--snack-bar-success-border);
    border-bottom: solid 1px var(--snack-bar-success-border);
    border-left: solid 1px var(--snack-bar-success-border);
  }

  &.error {
    color: var(--snack-bar-error);
    border-top: solid 3px var(--snack-bar-error-border);
    border-right: solid 1px var(--snack-bar-error-border);
    border-bottom: solid 1px var(--snack-bar-error-border);
    border-left: solid 1px var(--snack-bar-error-border);
  }

  &.warning {
    color: var(--snack-bar-warning);
    border-top: solid 3px var(--snack-bar-warning-border);
    border-right: solid 1px var(--snack-bar-warning-border);
    border-bottom: solid 1px var(--snack-bar-warning-border);
    border-left: solid 1px var(--snack-bar-warning-border);
  }

  &.info {
    color: var(--snack-bar-info);
    border-top: solid 3px var(--snack-bar-info-border);
    border-right: solid 1px var(--snack-bar-info-border);
    border-bottom: solid 1px var(--snack-bar-info-border);
    border-left: solid 1px var(--snack-bar-info-border);
  }

  &.fading {
    opacity: 0;
  }

  &.dead {
    opacity: 0;
  }

  .snackbar__box-close {
    position: absolute;
    top: 0;
    right: 0;
    margin: 6.5px;
    color: var(--disabled);
    cursor: pointer;
  }

  .snackbar__box-icon {
    position: absolute;
    top: 15px;
    left: 17px;
  }

  .snackbar__box-title {
    margin: 0;
    line-height: 22px;
  }

  .snackbar__box-body {
    margin-top: 4px;
    font-size: 15px;
    line-height: 16px;
    color: var(--text-primary);
    white-space: initial;
  }
}
