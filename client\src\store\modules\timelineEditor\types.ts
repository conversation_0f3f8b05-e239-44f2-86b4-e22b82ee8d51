import { File, MatchGroup, Tracklet, TrackletBoundingBox } from "@shared-types/tracker";
import { GetMatchGroupSelectedTrackletsResponse } from "@shared-types/responses";
import { ApiStatus } from "@store/types";

export interface FileState {
  file?: File;
  selectedTracklet?: Tracklet;
  apiStatus: ApiStatus;
  error?: string;
}

export interface SelectedTrackletsState extends GetMatchGroupSelectedTrackletsResponse {
  apiStatus: ApiStatus;
  error?: string;
}

export interface UpdateMatchGroup {
  matchGroup?: MatchGroup;
  status: ApiStatus;
  error?: string;
}

export interface BoundingBoxesState {
  data: TrackletBoundingBox[];
  apiStatus: ApiStatus;
  error?: string;
}
