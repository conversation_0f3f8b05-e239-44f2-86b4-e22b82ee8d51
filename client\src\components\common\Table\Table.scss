.table {
  height: 90%;

  .table__column-header {
    display: flex;
    justify-content: space-between;
    padding: 6px 0 0 18px;
  }

  .table__content-empty {
    padding: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--disabled);

    &.has-component {
      color: unset;
    }
  }

  .table__loading {
    padding-left: 18px;
    max-height: calc(100% - 50px);
    overflow: hidden;

    .MuiSkeleton-root {
      margin: 5px 0;
    }
  }

  .table__content {
    max-height: calc(100% - 50px);
    padding-right: 5px;
    margin-right: -10px;
    width: 100%;
    overflow: hidden auto;

    @supports (-moz-appearance:none) {
      scrollbar-width: thin;
      padding-right: 0;
    }

    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background: var(--scrollbar-thumb-track);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-thumb);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--scrollbar-thumb-hover);
    }
  }

  .table__pagination {
    display: flex;
    align-items: center;
    margin: 23px 0 40px;

    .table__pagination-page-size-text {
      @include size-2;

      color: var(--disabled);
    }

    .table__pagination-page-size {
      display: flex;
      margin-left: 10px;

      .table__pagination-page-size-select {
        color: var(--text-secondary);

        div {
          padding: 0;
          min-width: 48px;
        }

        svg {
          right: 2px;
          font-size: 1.2rem;
          fill: var(--icon);
        }
      }
    }

    .table__pagination-page-selector {
      display: inline-flex;
      justify-content: center;
      align-self: center;
      margin-left: 10px;

      .table__pagination-page-selector-next,
      .table__pagination-page-selector-back {
        margin-left: 10px;
        font-size: 20px;
        line-height: 23px;
        color: var(--icon-selector);
        user-select: none;
        pointer-events: none;

        &.enabled {
          color: var(--primary);
          cursor: pointer;
          pointer-events: initial;
        }
      }

      .table__pagination-page-selector-text {
        @include size-1;

        color: var(--text-secondary);
        line-height: 23px;
      }
    }
  }
}