Feature: Landing page

Background:
  Given The main page is loaded

# @e2e @landing-page
# Scenario: Check UI of event list
#   And The user should see the correct column headers for Event Name and Event Time
#   Then The user should see the application title Track

# @e2e @landing-page
# Scenario: Verify user can moving between Events and Files tab
#   When The user clicks on the "Files" tab
#   Then The "Files" tab should be active
#   When The user clicks on the "Events" tab
#   Then The "Events" tab should be active

# @e2e @landing-page
# Scenario: Verify when user clicks on the breadcrumb
#   When The user clicks the "All Events" breadcrumb
#   Then The page should not navigate away

# @e2e @landing-page
# Scenario: Verify user can see an event detail
#   When The user selects the event named "E2E-Test"
#   Then The following event details should be visible:
#       | Field               | Expected Value                |
#       | Event Name          | E2E-Test                      |
#       | Date                | Date: 5/29/2025, 11:34 AM     |
#       | Event Creator       | Event Creator: Yu<PERSON>    |
#       | File Count          | 1 File                        |
#       | Match Group Count   | 0 Match Group                 |
#       | View Event Button   | View Event                    |
#       | Delete Event Button | Delete Event                  |

# @e2e @landing-page
# Scenario: Verify format of the event time
#   Then Event Time has format: "M/D/YYYY, h:m AM/PM"

# @e2e @landing-page
# Scenario Outline: Verify user can edit event name
#   When The user selects the event named "<oldName>"
#   And The user clicks on event name on the right side
#   And The user changes the name to "<newName>"
#   Then The user can change name for event successfully

#   Examples:
#       | newName       | oldName       |
#       | Updated Event | E2E-Test      |
#       | E2E-Test      | Updated Event |

# @e2e @landing-page
# Scenario: Verify user can go into event
#   When The user selects the event named "E2E-Test"
#   And The user clicks on "View Event"
#   Then The user should navigate to event details page

# @e2e @landing-page
# Scenario: Verify user can delete an event successfully
#   When The user clicks upload files button
#   And The user clicks on new event button
#   Then The user enter "e2e-delete-test-event" in the event name textbox
#   And The user clicks on create event button
#   Then The user should see a success snackbar with message "e2e-delete-test-event"
#   And The user clicks on cancel upload button
#   Then The user should see "e2e-delete-test-event" in the event table
#   When The user selects the event named "e2e-delete-test-event"
#   And The user clicks on "Delete Event"
#   And The user enters wrong event name "e2e-delete-test-event"
#   Then The user verifies delete button is enabled and clicks it
#   Then The user should see a success snackbar with message "Successfully deleted event e2e-delete-test-event"

# @e2e @landing-page
# Scenario: Verify when user enters wrong event name
#   When The user selects the event named "E2E-Test"
#   And The user clicks on "Delete Event"
#   And The user enters wrong event name "Wrong Name"
#   Then The "Delete" button should still be disabled and textbox highlighted in red

# @e2e @landing-page
# Scenario Outline: Verify user can sort table columns by <ColumnName> <SortedBy>
#   When The user clicks on the "<ColumnName>" column header
#   Then "<ColumnName>" is sorted by "<SortedBy>"

#   Examples:
#     | ColumnName  | SortedBy |
#     | Event Name  | z-a      |
#     | Event Name  | a-z      |
#     | Event Time  | z-a      |
#     | Event Time  | a-z      |
  
# @e2e @landing-page @search
# Scenario Outline: Verify user can search for events
#   When The user enters "<keyword>" into the search bar
#   Then The displayed event results should contain "<keyword>"

#   Examples:
#     | keyword  |
#     | E2E-Test |

# @e2e @landing-page @search
# Scenario Outline: Verify user can search for files
#   When The user clicks on the "Files" tab
#   And The user enters "<keyword>" into the search bar
#   Then The displayed file results should contain "<keyword>"

#   Examples:
#     | keyword            |
#     | 4s testing tdo.mp4 |

# @e2e @landing-page
# Scenario: Verify results per page functionality
#   Then The user should see the "Results Per Page" label
#   When The user changes the results per page and verifies the following options:
#     | PerPage |
#     | 100     |
#     | 10      |
#     | 50      |

# @e2e @landing-page
# Scenario: Verify user can move between pages
#   Then The user should see the initial pagination state
#   When The user navigates to the "next" page
#   Then The pagination should update for the next page
#   When The user navigates to the "previous" page
#   Then The pagination should return to the initial state

@e2e @landing-page @file-upload
Scenario: Verify user can upload a file successfully
  When The user clicks upload files button
  And The user selects a file to upload "lucy.mp4"
  Then The file should be uploaded successfully
  Then The user click upload button
  And The upload should complete "100%"
