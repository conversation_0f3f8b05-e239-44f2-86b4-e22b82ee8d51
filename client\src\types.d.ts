
// eslint-disable-next-line spaced-comment
/// <reference types="vite/client" />
// eslint-disable-next-line spaced-comment
/// <reference types="vite-plugin-svgr/client" />

interface Window {
  config: {
    [key: string]: string | number | boolean | object | undefined;
    nodeEnv: string;
    sentry?: {
      tracing: string;
      DSN: string;
    };
    sentryDSN?: string;
  };
  buildDetails: {
    hash?: string;
    date?: string;
    message?: string;
  };

  aiware?: {
    init: (config: unknown, cb?: () => void) => void;
    mountWidget: (config: unknown, cb?: () => void) => string;
    unmountWidget: (id: string) => void;
    auth?: {
      reportAppActivity?: () => void;
    };
    on: (event: string, cb: () => void) => void;
    store: {
      getState: () => {
        auth?: {
          user?: {
            preferredLanguage?: string;
          }
        }
      }
    };
  };
  isAiwareInitialized?: boolean;
  intercomSettings?: unknown;
  Intercom?: (
    event: 'trackEvent',
    name: string,
    metadata?: Record<string, unknown>
  ) => void;
  chatWithSupport?: () => void;
  openSalesforceChatWindow?: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: any;
}

type DispatchPromise = (
  (Promise<
    | PayloadAction<
        unknown,
        string,
        { arg: unknown; requestId: string; requestStatus: "rejected"; aborted: boolean; condition: boolean } & ({ rejectedWithValue: true } | ({ rejectedWithValue: false } & {})), SerializedError
      >
    | PayloadAction<
        unknown,
        string,
        { arg: unknown; requestId: string; requestStatus: "fulfilled" }, never
      >
    > & {
      __linterBrands: "SafePromise"
    } & {
      abort: (reason?: string | undefined) => void;
      requestId: string;
      arg: object;
      unwrap: () => Promise<unkown>
    }
  ) | undefined);

declare module "*.module.scss";
