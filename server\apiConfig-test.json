{"apiRoot": "https://api.test.veritone.com", "credentialApi": "https://tracker2.test.veritone.com/api/v1/credential", "veritoneAppId": "********-9ABC-DEFG-1234-5678-********ABCD", "graphQLEndpoint": "v3/graphQL", "nodeEnv": "test", "port": 3002, "serviceName": "tracker-server", "startApi": "true", "useRedis": false, "cloud": "azure", "blob": {"endpointSuffix": "core.usgovcloudapi.net", "account": "vtstorcoredev", "key": "REPLACE_WITH_AZURE_BLOB_KEY", "container": "tracker2", "expireSecs": 3600}, "s3": {"bucket": "tracker2-test", "accessKey": "replace with accessKey for local dev, no need for ec2", "secretKey": "replace with secretKey for local dev, no need for ec2", "roleArn": "arn:aws:iam::********9ABC:role/VeritoneGLCAssumeRole", "region": "us-east-1", "expireSecs": 3600}, "trackerEngineId": "f6634718-c2b7-40f5-9c2c-a606420104ac", "validWritableCredentialEngineIds": ["f6634718-c2b7-40f5-9c2c-a606420104ac", "d77d6133-a801-472c-bc7e-48ddafec8590"], "glcIngestorEngineId": "fedff357-49e7-422b-9099-38e6c4abadbf", "outputWriterEngineId": "8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3", "defaultClusterId": "rt-9d7a5d1b-ffe0-4d71-a982-190522cdf272", "videoSliceEngineId": "3d9425b7-02be-44b0-9c5d-53e711449588", "registryIds": {"eventsRegistryId": "********-9ABC-DEFG-1234-5678-********ABCD", "matchGroupsRegistryId": "********-9ABC-DEFG-1234-5678-********ABCD"}}