import { renderHook } from "@testing-library/react";
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import useRouteChanged from '@components/Routes/useRouteChanged';
import { onRouteChanged } from '@store/modules/location/slice';

jest.mock('react-router-dom', () => ({
  useLocation: jest.fn(),
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

describe('useRouteChanged', () => {
  it('should dispatch onRouteChanged action when location changes', () => {
    const mockDispatch = jest.fn();
    (useDispatch as unknown as jest.Mock).mockReturnValue(mockDispatch);
    (useLocation as jest.Mock).mockReturnValue({ pathname: '/test', search: '', hash: '', key: 'someKey', payload: { data: 'test' } });

    const { rerender } = renderHook(() => useRouteChanged());

    // Simulate location change
    (useLocation as jest.Mock).mockReturnValue({ pathname: '/test2', search: '', hash: '', key: 'someKey', payload: { data: 'test2' } });
    rerender();

    expect(mockDispatch).toHaveBeenCalledWith(onRouteChanged({
      pathname: '/test2',
      search: '',
      hash: '',
      key: 'someKey',
      history: {
        hash: '',
        key: 'someKey',
        pathname: '/test',
        search: '',
        payload: { data: 'test' },
      },
      payload: { data: 'test2' },
    }));
  });
});
