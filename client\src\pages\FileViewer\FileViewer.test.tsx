import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react';
import <PERSON>Viewer from './FileViewer';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureAppStore } from '@store/store';
import axios from 'axios';
import {
  GetBoundingBoxesResponse,
  GetEventResponse,
  GetFileResponse,
  GetFileSearchResultsResponse,
} from '@shared-types/responses';
import { FileSliceState, setThumbnails } from '@store/modules/file/slice';
import {
  getAttributeCount,
  TrackletAttributes,
} from '../../../test/testConstants';
import { I18nProvider, LOCALES } from '@i18n';

jest.mock('axios');

const eventId = '947db3be-91ec-4e4b-a00f-6ad2ae06e25d';
const fileId = 'abcdefg-1234-4c2e-a7c5-6c094e5fa25b';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: jest.fn(() => ({
    pathname: '/event/1234/file/5678',
    search: '',
  })),
  useParams: () => ({
    eventId,
    fileId,
  }),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockNavigate = jest.fn();

const mockGetEventByIdResponse: GetEventResponse = {
  event: {
    eventEndDate: '2024-04-10T17:02:10Z',
    createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
    createdByName: 'Test User',
    name: 'Event Test 1',
    description: 'New description',
    createdDateTime: '2024-04-11T00:10:13.876Z',
    modifiedDateTime: '2024-04-11T00:10:24.681Z',
    id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
    eventStartDate: '2024-04-10T17:02:10Z',
    tags: ['Tag 1', 'Tag 2'],
    matchGroupsCount: 14,
    filesCount: 13,
  },
};

const mockGetFileByIdResponse: GetFileResponse = {
  file: {
    id: 'abcdefg-1234-4c2e-a7c5-6c094e5fa25b',
    createdByName: 'Test User',
    fileName: 'test_file_3.mp4',
    status: 'error',
    length: 128,
    uploadDate: '2024-04-10T17:02:10Z',
    location: 'GPS',
    fileType: 'video',
    fileSize: 512,
    eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
    eventName: 'Test Event',
    thumbnailUrl: 'thumbnailUrl',
    primaryAsset: {
      signedUri: 'signedUri',
    },
    streams: [
      {
        uri: 'uri',
        protocol: 'protocol',
      },
    ],
    frameRate: 25,
  },
};

const mockFileResultsResponse: GetFileSearchResultsResponse = {
  results: [
    {
      orgId: '1',
      trackletId: 'reference-tracketlet-id-mock-1',
      fileId: '3100000030',
      fileName: 'fileName 1',
      startTimeMs: 200,
      stopTimeMs: 640,
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      type: 'person',
      confidence: 0.95,
    },
    {
      orgId: '1',
      trackletId: 'reference-tracketlet-id-mock-2',
      fileId: '3100000031',
      fileName: 'fileName 2',
      startTimeMs: 800,
      stopTimeMs: 10360,
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      type: 'person',
      confidence: 0.7,
    },
    {
      orgId: '1',
      trackletId: 'reference-tracketlet-id-mock-3',
      fileId: '3100000032',
      fileName: 'fileName 3',
      startTimeMs: 10600,
      stopTimeMs: 14360,
      attributes: TrackletAttributes,
      thumbnailUrls: {
        best: '/tracklet-example-best.png',
      },
      type: 'person',
      confidence: 0.5,
    },
  ],
  type: 'person',
  currentPage: 1,
  pageSize: 100,
  totalCount: 3,
  totalPages: 1,
  fileId: 'abcdefg-1234-4c2e-a7c5-6c094e5fa25b',
  eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
};

const mockGetBoundingBoxes: GetBoundingBoxesResponse = {
  results: [
    {
      boundingPoly: [
        { x: 0, y: 0 },
        { x: 0, y: 0 },
        { x: 0, y: 0 },
        { x: 0, y: 0 },
      ],
      trackletId: 'trackletId',
      startTimeMs: 0,
      stopTimeMs: 1000,
    },
  ],
};

const initialStateForMock = {
  file: {
    tracklets: {
      vehicle: {
        data: [
          {
            trackletId: 'trackletId1',
            fileId: 'fileId1',
            fileName: 'fileName1',
            startTimeMs: 'startTimeMs',
            stopTimeMs: 'stopTimeMs',
            attributes: TrackletAttributes,
            thumbnailUrls: { best: 'best' },
            type: 'type',
            confidence: 1,
          },
        ],
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        status: 'idle',
        error: '',
      },
      person: {
        data: [
          {
            trackletId: 'trackletId1',
            fileId: 'fileId1',
            fileName: 'fileName1',
            startTimeMs: 'startTimeMs',
            stopTimeMs: 'stopTimeMs',
            attributes: TrackletAttributes,
            thumbnailUrls: { best: 'best' },
            type: 'type',
            confidence: 1,
          },
        ],
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        status: 'idle',
        error: '',
      },
    },
    attributes: {
      person: {},
      vehicle: {},
    },
    file: {
      id: '',
      fileName: '',
      createdByName: '',
      status: 'processing',
      length: 0,
      uploadDate: '',
      location: '',
      fileType: '',
      fileSize: 0,
      apiStatus: 'idle',
      eventId: '',
      eventName: '',
      thumbnailUrl: '',
      primaryAsset: {
        signedUri: '',
      },
      streams: [],
      frameRate: 0,
    },
    fileResults: {
      vehicle: {
        results: [],
        fileId: '',
        eventId: '',
        type: 'vehicle',
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        apiStatus: 'idle',
        error: '',
      },
      person: {
        results: [],
        fileId: '',
        eventId: '',
        type: 'person',
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        apiStatus: 'idle',
        error: '',
      },
    },
    event: {
      data: undefined,
      apiStatus: 'idle',
      error: '',
    },
    selectedTracklet: {
      vehicle: undefined,
      person: undefined,
    },
    selectedTrackletFile: {
      vehicle: {
        file: undefined,
        apiStatus: 'idle',
        error: '',
      },
      person: {
        file: undefined,
        apiStatus: 'idle',
        error: '',
      },
    },
    boundingBoxes: {
      data: [],
      apiStatus: 'idle',
      error: '',
    },
    selectedAttributes: {
      person: {},
      vehicle: {},
    },
    thumbnailUrls: {},
  } as unknown as FileSliceState,
};

describe('FileViewer', () => {
  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  test('renders FileViewer component', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
      if (url?.includes('/bounding-boxes') && method === 'get') {
        return Promise.resolve({ data: mockGetBoundingBoxes });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });
  });

  test('has mocked file information in FileViewer', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedStore.dispatch(
      setThumbnails({
        'reference-tracketlet-id-mock-1': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        'reference-tracketlet-id-mock-2': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        'reference-tracketlet-id-mock-3': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    expect(
      screen.queryByTestId('file-viewer-no-person-tracklets-found')
    ).not.toBeInTheDocument();

    // Click a tracklet
    await act(() => {
      screen.getByTestId('Tracklet-0').click();
    });

    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );
    });
  });

  test('FileViewer render best thumbnail', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        'reference-tracketlet-id-mock-1': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        'reference-tracketlet-id-mock-2': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        'reference-tracketlet-id-mock-3': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('file-viewer')).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
    expect(screen.getByTestId('Tracklet-0-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-1-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-2-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
  });

  test('has no tracklets found message when no tracklet results in fileviewer', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: { ...mockFileResultsResponse, results: [] },
        });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    // Should have no tracklets found message
    await waitFor(() => {
      expect(
        screen.getByTestId('file-viewer-no-person-tracklets-found')
      ).toBeInTheDocument();
    });
  });

  test('Should show skeleton but not show no tracklets found', async () => {
    jest.useFakeTimers();

    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return new Promise((resolve) => setTimeout(resolve, 3000)); // 3 seconds delay
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Let the API resolve after 3 seconds
    act(() => {
      jest.advanceTimersByTime(3000); // Resolve the mocked API after 3 seconds
    });

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    // Show skeleton but not show no tracklets found msg
    await waitFor(() => {
      expect(
        screen.getByTestId(
          'main__tracklet_thumbnails-tracklet-person-skeleton-0'
        )
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('file-viewer-no-person-tracklets-found')
      ).not.toBeInTheDocument();
    });

    jest.useRealTimers();
  });

  test('navigate to fileName', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    const fileBreadcrumb = screen.getByTestId('file-breadcrumb');

    expect(fileBreadcrumb).toBeInTheDocument();

    expect(fileBreadcrumb).toHaveTextContent(
      mockGetFileByIdResponse.file.fileName
    );

    // Click event breadcrumb
    fireEvent.click(screen.getByTestId('event-breadcrumb'));

    // Assert that file breadcrumb is clicked
    expect(mockNavigate).toHaveBeenCalledWith(
      `/event/${mockGetEventByIdResponse.event.id}`
    );
  });

  test('navigate to home if there is a failure while loading file', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.reject({
          response: {
            data: {
              message: "The event does not exist in current organization or belongs to another organization",
            }
          }
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    // Assert that mockNavigate was called
    expect(mockNavigate).toHaveBeenLastCalledWith(`/event/${eventId}`, {
      replace: true,
    });
  });

  test('Thumbnails scale people', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
      if (url?.includes('/bounding-boxes') && method === 'get') {
        return Promise.resolve({ data: mockGetBoundingBoxes });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    const tracklet = await screen.findByTestId('LoadingTracklet-0');
    expect(tracklet).toBeInTheDocument();
    expect(tracklet).toHaveStyle({ width: '113px' });

    const thumbnailScaler = screen.getByTestId('thumbnail-scaler');
    const thumbnailScalerButton = screen.getByTestId('thumbnail-scaler-button');
    expect(thumbnailScaler).toBeInTheDocument();
    expect(thumbnailScalerButton).toBeInTheDocument();

    // Show slider
    fireEvent.click(thumbnailScalerButton);
    const thumbnailScalerSlider = screen.getByTestId('thumbnail-scaler-slider');
    expect(thumbnailScalerSlider).toBeInTheDocument();

    // Increment scale to 200%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: 1 });
    expect(tracklet).toHaveStyle({ width: '226px' });

    // Decrement scale to 10%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: -1 });
    expect(tracklet).toHaveStyle({ width: '67.8px' });
  });

  test('Thumbnails scale vehicles', async () => {
    localStorage.clear();
    const mockedStore = configureAppStore(initialStateForMock);
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
      if (url?.includes('/bounding-boxes') && method === 'get') {
        return Promise.resolve({ data: mockGetBoundingBoxes });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });
    fireEvent.click(screen.getByTestId('vehicles-tab'));

    await waitFor(() => {
      expect(screen.getByTestId('LoadingTracklet-0')).toBeInTheDocument();
    });

    const tracklet = await screen.findByTestId('LoadingTracklet-0');
    expect(tracklet).toBeInTheDocument();
    expect(tracklet).toHaveStyle({ width: '113px' });

    const thumbnailScaler = screen.getByTestId('thumbnail-scaler');
    const thumbnailScalerButton = screen.getByTestId('thumbnail-scaler-button');
    expect(thumbnailScaler).toBeInTheDocument();
    expect(thumbnailScalerButton).toBeInTheDocument();

    // Show slider
    fireEvent.click(thumbnailScalerButton);
    const thumbnailScalerSlider = screen.getByTestId('thumbnail-scaler-slider');
    expect(thumbnailScalerSlider).toBeInTheDocument();

    // Increment scale to 200%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: 1 });
    expect(tracklet).toHaveStyle({ width: '226px' });

    // Decrement scale to 100%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: -1 });
    expect(tracklet).toHaveStyle({ width: '67.8px' });
  });

  test('Updates file name and saves it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    const nameUpdated = '     Updated File name    ';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
      if (url?.includes('/file') && method === 'patch') {
        return Promise.resolve({
          data: {
            file: {
              ...mockGetFileByIdResponse.file,
              fileName: nameUpdated.trim(),
            },
          },
          status: 200,
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-file-name-button'));

    // wait for the file name input to appear
    await waitFor(() => {
      expect(screen.getByTestId('file-change-name')).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update file name
    fireEvent.change(screen.getByTestId('file-change-name'), {
      target: { value: nameUpdated },
    });

    // Save the update
    fireEvent.click(screen.getByTestId('save-file-name-button'));

    // wait for the updated file name to appear
    await waitFor(() => {
      expect(screen.getByTestId('file-heading-name')).toHaveTextContent(
        nameUpdated.trim()
      );
    });
  });

  test('Updates file name and cancel it', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    const nameUpdated = 'Updated File name';

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/event') &&
        url?.includes('/results') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockFileResultsResponse });
      }
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileByIdResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <FileViewer />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('file-viewer')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-file-name-button'));

    // wait for the file name input to appear
    await waitFor(() => {
      expect(screen.getByTestId('file-change-name')).toBeInTheDocument();
    });

    mockedAxios.request.mockClear();

    // Update file name
    fireEvent.change(screen.getByTestId('file-change-name'), {
      target: { value: nameUpdated },
    });

    // Cancel the update
    fireEvent.click(screen.getByTestId('cancel-file-name-button'));

    // wait for the updated file name to appear
    await waitFor(() => {
      expect(screen.getByTestId('file-heading-name')).toHaveTextContent(
        mockGetFileByIdResponse.file.fileName
      );
    });
  });
});
