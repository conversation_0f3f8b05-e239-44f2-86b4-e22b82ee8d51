import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

const mockStructuredDataObject = {
  id: 'a-matchgroup-id',
  data: {
    name: 'Test Match Group',
    eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
    searches: [
      {
        id: 'a-search-id',
        searchName: 'testSearchName',
        referenceTrackletId: '7777',
      },
      {
        id: 'ABCDEFGH-b1cb-4b93-bfea-0401861d8d40',
        searchName: 'testSearchName',
        referenceTrackletId: '7777',
      },
    ],
    selectedTracklets: ['7777'],
  },
  createdDateTime: '2024-05-03T21:14:26.769Z',
  modifiedDateTime: '2024-05-03T21:57:48.320Z',
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            contentTemplates: [{ id: 'id', sdo: { data: {} } }],
          },
        });
      }
      if (query.includes(queries.getMatchGroup)) {
        return Promise.resolve({
          structuredDataObject: mockStructuredDataObject,
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
      if (query.includes(queries.createStructuredData)) {
        return Promise.resolve({
          createStructuredData: mockStructuredDataObject,
        });
      }
    }
  ),
}));

describe('delete match group', () => {
  it('deletes a match group', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/match-groups/a-matchgroup-id/search/a-search-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getMatchGroup,
      {
        id: 'a-matchgroup-id',
        schemaId: 'schemaId',
      }
    );

    expect(callGQL).toHaveBeenNthCalledWith(
      4,
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'a-matchgroup-id',
        schemaId: 'schemaId',
        data: {
          ...mockStructuredDataObject.data,
          searches: mockStructuredDataObject.data.searches.slice(1),
        },
      }
    );
  });
});
