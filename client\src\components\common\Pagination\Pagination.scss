.pagination {
  display: flex;
  align-items: center;
  min-width: 400px;

  .pagination-page-size-text {
    @include size-2;

    color: var(--disabled);
    line-height: 30px;
  }

  .pagination-page-size {
    display: flex;
    margin-left: 10px;

    .pagination-page-size-select {
      color: var(--text-secondary);
      width: 50px;
      line-height: 30px;

      div {
        padding: 0;
        min-width: 40px;
      }

      svg {
        right: 2px;
        font-size: 1.2rem;
        fill: var(--icon);
      }
    }
  }

  .pagination-page-select-drop-control-page {
    margin-right: 10px;
    margin-left: 10px;
    line-height: 30px;

    @include size-2;

    color: var(--disabled);
  }

  .pagination-page-select-drop {
    margin-top: 3px;
  }

  .pagination-page-selector {
    display: inline-flex;
    justify-content: center;
    align-self: center;
    margin-left: 10px;
    line-height: 30px;

    .pagination-page-selector-next,
    .pagination-page-selector-back {
      margin-left: 10px;
      font-size: 20px;
      line-height: 30px;
      color: var(--icon-selector);
      user-select: none;
      pointer-events: none;

      &.enabled {
        color: var(--primary);
        cursor: pointer;
        pointer-events: initial;
      }
    }

    .pagination-page-selector-text {
      @include size-1;

      color: var(--text-secondary);
      line-height: 30px;
    }
  }
}