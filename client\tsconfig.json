{
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "downlevelIteration": true,
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@common-modules/*": ["src/common-modules/*"],
      "@components/*": ["src/components/*"],
      "@helpers/*": ["src/helpers/*"],
      "@i18n": ["src/i18n"],
      "@store/*": ["src/store/*"],
      "@theme/*": ["src/theme/*"],
      "@pages/*": ["src/pages/*"],
      "@utility/*": ["src/utility/*"],
      "@shared-types/*": ["../types/*"],
      "@shared-assets/*": ["../assets/*"],
    },
    "types": ["jest", "node", "@testing-library/jest-dom"],
  },
  "include": ["../types/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.json", "test/**/*.ts", "../assets/*.json"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
