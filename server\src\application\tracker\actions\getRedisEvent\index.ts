import { responses } from '@tracker/graphQL';
import { Event } from '../../../../../../types/tracker';
import { Context } from '../../../types';

const getRedisEvent = async <ReqPayload, Data extends Partial<responses.getMe>>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & Partial<{ redisEvent: Event }>>> => {
  const { data, req, redisWrapper } = context;

  if (redisWrapper && data.userOrganizationId) {
    const redisEvent = await redisWrapper.event.get(
      req.params.eventId,
      String(data.userOrganizationId)
    );
    if (redisEvent) {
      const new_data = Object.assign({}, data, { redisEvent });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  }
  return context;
};

export default getRedisEvent;
