import cn from 'classnames';
import './PotentialMatchSearchRow.scss';
import { RowProps } from '@components/common/Table/Table';
import { Search } from '@shared-types/tracker';
import { Button, SvgIcon } from '@mui/material';
import EditIcon from '../../../pages/Event/edit.svg?react';
import { useState } from 'react';
import ConfirmDialog from '@components/common/ConfirmDialog';
import { useAppDispatch } from '@store/hooks';
import { deleteMatchGroupSearch, updateMatchGroupSearchName } from '@store/modules/event/slice';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';

export interface PotentialMatchSearchRowType extends Search {
  matchGroupRowIndex: number;
  matchGroupId: string;
  actions: string;
}

export function PotentialMatchSearchRow({
  colData,
  rowData,
  rowIndex,
  onRowClick,
  onAuxClick,
}: RowProps<PotentialMatchSearchRowType>) {
  const intl = useIntl();
  const { matchGroupRowIndex, matchGroupId } = rowData;
  const [deleteSearchDialogOpen, setDeleteSearchDialogOpen] = useState(false);
  const [renameSearchDialogOpen, setRenameSearchDialogOpen] = useState(false);
  const dispatch = useAppDispatch();

  const renderCell = ({
    dataKey,
    index: _index,
  }: {
    dataKey: string;
    index: number;
  }) => {
    switch (dataKey) {
      case 'searchName':
        return (
          <div role="cell" className="potential-match-search-row__cell">
            {rowData[dataKey]}
          </div>
        );
      case 'actions':
        return (
          <div role="cell" className="potential-match-search-row__cell-actions">
            {rowData[dataKey]}
            <Button variant="text">{I18nTranslate.TranslateMessage('viewSearch')}</Button>
            <SvgIcon
              className="edit-icon"
              data-testid={`matchgroup-edit-${rowData.id}`}
              onClick={(event) => {
                event.stopPropagation();
                setRenameSearchDialogOpen(true);
              }}
            >
              <EditIcon className="svg-icon" />
            </SvgIcon>
            <Button
              data-testid={`matchgroup-delete-${rowData.id}`}
              variant="text"
              onClick={(event) => {
                event.stopPropagation();
                setDeleteSearchDialogOpen(true);
              }}
            >
              <div className="material-symbols-outlined">delete</div>
            </Button>
          </div>
        );
    }
  };

  return (
    <div role="row" className={cn('potential-match-search-row')}>
      <div
        className={cn('potential-match-search-row__row')}
        onClick={() => onRowClick?.(rowData, rowIndex)}
        onAuxClick={() => onAuxClick?.(rowData, rowIndex)}
        data-testid={`potential-match-search-row-${matchGroupRowIndex}-${rowIndex}`}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="potential-match-search-row__cell"
            data-testid={`potential-match-search-row-cell-${rowIndex}-${index}`}
            key={`PotentialMatchSearchRowCell-${rowIndex}-${index}-${dataKey}`}
            style={{
              flexGrow: grow,
              maxWidth: width ?? 100,
              minWidth,
            }}
          >
            {renderCell({ dataKey, index })}
          </div>
        ))}
      </div>
      <ConfirmDialog
        open={deleteSearchDialogOpen}
        title="Delete Match Group Search"
        content={intl.formatMessage({ id: 'deleteMatchGroupSearchMessage', defaultMessage: 'You are about to delete {searchName}. Are you sure you want to delete it?' }, { searchName: rowData['searchName'] })}
        confirmText={intl.formatMessage({ id: 'delete', defaultMessage: 'Delete' })}
        cancelText={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
        onConfirm={() => {
          dispatch(deleteMatchGroupSearch({ matchGroupId, searchId: rowData.id }));
          setDeleteSearchDialogOpen(false);
        }}
        onClose={() => setDeleteSearchDialogOpen(false)}
      />
      <ConfirmDialog
        open={renameSearchDialogOpen}
        title="Rename Match Group Search"
        name={rowData['searchName']}
        editName
        confirmText={intl.formatMessage({ id: 'save', defaultMessage: 'Save' })}
        cancelText={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
        onConfirm={(input) => {
          if (input) {
            dispatch(updateMatchGroupSearchName({matchGroupId, searchId: rowData.id, searchName: input}));
          }
          setRenameSearchDialogOpen(false);
        }}
        onClose={() => setRenameSearchDialogOpen(false)}
      />
    </div>
  );
}

export default PotentialMatchSearchRow;
