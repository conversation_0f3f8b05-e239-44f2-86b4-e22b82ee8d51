import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import deleteTemporalData from '../deleteTemporalData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

describe('Delete Temporal Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {
          fileId: 'fileId',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      data: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully deletes temporal data', async () => {
    const response = await deleteTemporalData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { tdoId: 'fileId' }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no fileId', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.req.params.fileId = undefined;

    expect(async () => await deleteTemporalData(cxt)).rejects.toThrowError(
      'No fileId provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
