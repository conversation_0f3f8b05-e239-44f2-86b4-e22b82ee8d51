name: Run Tests and Linter

on:
  pull_request:
    branches:
      - 'feature/**'
  push:
    branches:
      - 'master'
      - 'feature/**'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '22.x'
      - run: yarn
      - run: yarn
        working-directory: client
      - name: Copy apiConfig-test.js into server
        run: cp apiConfig-test.json apiConfig.json
        working-directory: server
      - run: yarn
        working-directory: server
      - run: yarn run lint
        working-directory: client
        env:
          NODE_OPTIONS: --max_old_space_size=4096
      - run: yarn run lint
        working-directory: server
        env:
          NODE_OPTIONS: --max_old_space_size=4096
      - run: yarn test
        working-directory: client
      - run: yarn test
        working-directory: server
