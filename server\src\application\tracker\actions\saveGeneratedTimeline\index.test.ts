import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import saveGeneratedTimeline from '.';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import * as ResTypes from '../../../../../../types/responses';
import { responses, queries } from '@tracker/graphQL';
import { TimelineProject } from '../../../../../../types/tracker';

const timelineProject: TimelineProject = {
  groups: [
    {
      id: 'ec8f33b4-2e69-4d0e-a533-39e1f7ba2cc8',
      name: 'Default Group',
      tracklets: [
        {
          orgId: '1',
          type: 'person',
          fileId: '3150000006',
          fileName: 'La_Cienega_Clifton.mp4',
          attributes: {
            Body: [
              {
                key: 'Body',
                value: 'Average',
                label: 'BodyAverage',
              },
            ],
            Face: [
              {
                key: 'Face',
                value: 'Back',
                label: 'FaceBack',
              },
            ],
            HairColor: [
              {
                key: 'HairColor',
                value: 'Black',
                label: 'HairColorBlack',
              },
            ],
            HairType: [
              {
                key: 'HairType',
                value: 'Long',
                label: 'typeLong',
              },
              {
                key: 'HairType',
                value: 'Short',
                label: 'typeShort',
              },
            ],
            LowerType: [
              {
                key: 'LowerType',
                value: 'Trousers',
                label: 'LowerTypeTrousers',
              },
            ],
            UpperType: [
              {
                key: 'UpperType',
                value: 'Cotton',
                label: 'UpperTypeCotton',
              },
            ],
            Gender: [
              {
                key: 'Gender',
                value: 'Female',
                label: 'GenderFemale',
              },
            ],
            FootwearColor: [
              {
                key: 'FootwearColor',
                value: 'Black',
                label: 'FootwearColorBlack',
              },
              {
                key: 'FootwearColor',
                value: 'Gray',
                label: 'FootwearColorGray',
              },
            ],
            Accessories: [
              {
                key: 'Accessories',
                value: 'BagAny',
                label: 'AccessoryBagAny',
              },
              {
                key: 'Accessories',
                value: 'Handbag',
                label: 'AccessoryHandbag',
              },
              {
                key: 'Accessories',
                value: 'Shoulderbag',
                label: 'AccessoryShoulderbag',
              },
            ],
          },
          confidence: 0.2,
          stopTimeMs: 880,
          trackletId: '6aa9239c-480e-4dca-96ab-22dd89a34327',
          startTimeMs: 320,
          thumbnailUrls: {
            best: '',
          },
        },
      ],
    },
    {
      id: 'f7ad58e1-7502-4715-b8ce-52d2175e3e53',
      name: 'Default Group',
      tracklets: [
        {
          orgId: '1',
          type: 'person',
          fileId: '3150000006',
          fileName: 'La_Cienega_Clifton.mp4',
          attributes: {
            Body: [
              {
                key: 'Body',
                value: 'Average',
                label: 'BodyAverage',
              },
            ],
            Face: [
              {
                key: 'Face',
                value: 'Back',
                label: 'FaceBack',
              },
            ],
            HairColor: [
              {
                key: 'HairColor',
                value: 'Black',
                label: 'HairColorBlack',
              },
            ],
            HairType: [
              {
                key: 'HairType',
                value: 'Long',
                label: 'typeLong',
              },
              {
                key: 'HairType',
                value: 'Short',
                label: 'typeShort',
              },
            ],
            LowerType: [
              {
                key: 'LowerType',
                value: 'Trousers',
                label: 'LowerTypeTrousers',
              },
            ],
            UpperType: [
              {
                key: 'UpperType',
                value: 'Cotton',
                label: 'UpperTypeCotton',
              },
            ],
            Gender: [
              {
                key: 'Gender',
                value: 'Female',
                label: 'GenderFemale',
              },
            ],
            FootwearColor: [
              {
                key: 'FootwearColor',
                value: 'Black',
                label: 'FootwearColorBlack',
              },
              {
                key: 'FootwearColor',
                value: 'Gray',
                label: 'FootwearColorGray',
              },
            ],
            Accessories: [
              {
                key: 'Accessories',
                value: 'BagAny',
                label: 'AccessoryBagAny',
              },
              {
                key: 'Accessories',
                value: 'Handbag',
                label: 'AccessoryHandbag',
              },
              {
                key: 'Accessories',
                value: 'Shoulderbag',
                label: 'AccessoryShoulderbag',
              },
            ],
          },
          confidence: 0.21,
          stopTimeMs: 1920,
          trackletId: '0e45b7ad-9ca6-4420-8a9b-9a8410c40e35',
          startTimeMs: 1600,
          thumbnailUrls: {
            best: '',
          },
        },
      ],
    },
  ],
};

const createCxt = () => {
  const cxt: Context<
    object,
    ResTypes.CreateTimelineJobPayloadResponse & responses.createSpliceJob
  > = {
    data: {
      currentTime: 'modifiedDateTime',
      generatedTimelineName: 'name',
      spliceJob: {
        tdoId: 'tdoId',
        jobId: 'jobId',
        status: 'status',
      },
      timelineProject,
    },
    req: createRequest({
      headers: {
        authorization: 'Bearer validToken',
      },
    }),
    log: consoleLogger(),
    res: createResponse(),
    cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    queries: {},
    gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
  };
  return cxt;
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => {
      if (_query.includes(queries.getFileTemporalData)) {
        return Promise.resolve({
          temporalDataObject: {
            id: 'id',
            name: 'name',
            createdBy: 'mockUser',
            createdDateTime: 'createdDateTime',
          },
        });
      } else {
        return Promise.resolve({
          user_0: {
            id: 'id',
            firstName: 'firstName',
            lastName: 'lastName',
          },
        });
      }
    }
  ),
}));

describe('Generate Timeline', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Generates timeline', async () => {
    const cxt = createCxt();
    const new_context = await saveGeneratedTimeline(cxt);
    expect(new_context?.data?.generatedTimelines).toEqual([
      {
        id: 'id',
        name: 'name',
        tdoId: 'id',
        timeline: cxt.data.timelineProject,
        resolution: '',
        outputFormat: 'video/mp4',
        createdUserId: 'mockUser',
        createdUserName: 'firstName lastName',
        videoLengthMs: 0,
        videoSizeBytes: 0,
        createdDateTime: 'createdDateTime',
      },
    ]);
  });

  it('Throws error if spliceJob is missing', async () => {
    const cxt = createCxt();
    // @ts-expect-error TODO: Does this make sense with types?
    cxt.data.spliceJob = undefined;
    await expect(saveGeneratedTimeline(cxt)).rejects.toThrow(
      'Missing splice job data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if timelineProject is missing', async () => {
    const cxt = createCxt();
    cxt.data.timelineProject = undefined;
    await expect(saveGeneratedTimeline(cxt)).rejects.toThrow(
      'Missing timeline project data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
