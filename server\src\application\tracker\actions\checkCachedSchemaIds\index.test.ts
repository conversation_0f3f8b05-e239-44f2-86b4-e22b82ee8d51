import env from '../../../../env';
import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { checkCachedSchemaIdEvents } from '../checkCachedSchemaIds';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        dataRegistry: {
          publishedSchema: { id: env.registryIds.eventsRegistryId },
        },
      })
  ),
}));

describe('Check cached schema IDs', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Checks Events schemaId when it has not yet been set', async () => {
    expect(cxt.cache.get('eventsSchemaId')).toBeUndefined();

    const response = await checkCachedSchemaIdEvents(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { registryId: env.registryIds.eventsRegistryId }
    );
    expect(response).not.toBeNull();
    expect(cxt.cache.get('eventsSchemaId')).toEqual(
      env.registryIds.eventsRegistryId
    );
  });

  it('Does not check Events schemaId when it has already been set', async () => {
    cxt.cache.set('eventsSchemaId', env.registryIds.eventsRegistryId);

    expect(cxt.cache.get('eventsSchemaId')).not.toBeUndefined();
    expect(cxt.cache.get('eventsSchemaId')).toEqual(
      env.registryIds.eventsRegistryId
    );
    await checkCachedSchemaIdEvents(cxt);
    expect(callGQL).not.toHaveBeenCalled();
  });
});
