import { Context, RequestHeader } from '@application/types';
import { queries } from '@tracker/graphQL';
import { folderTDOs } from '@tracker/graphQL/responses';
import { callGQL } from '@util/api/graphQL/callGraphQL';

export async function getTdoByFolder<ReqPayload, Data>({
  folderId,
  limit,
  offset,
  context,
  headers,
}: {
  folderId: string;
  limit: number;
  offset: number;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const { folder } = await callGQL<folderTDOs, ReqPayload, Data>(
    context,
    headers,
    queries.getTdoByFolder,
    {
      id: folderId,
      limit,
      offset,
    }
  );
  return folder;
}

export async function getTdoByFolderAll<ReqPayload, Data>({
  folderId,
  context,
  headers,
}: {
  folderId: string;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const results: folderTDOs = {
    folder: {
      id: '',
      childTDOs: {
        records: [],
      },
    },
  };
  let offset = 0;
  const limit = 1000;
  let count = 0;
  do {
    const folder = await getTdoByFolder({
      folderId,
      limit,
      offset,
      context,
      headers,
    });
    offset += limit;
    count = folder.childTDOs.records.length;
    if (count > 0) {
      results.folder.childTDOs.records.push(...folder.childTDOs.records);
    }
    results.folder.id = folder.id;
  } while (count === limit);
  return results;
}
