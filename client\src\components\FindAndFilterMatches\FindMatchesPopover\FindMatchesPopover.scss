.find-matches-popover__container {
  width: 620px;
  padding: 30px;

  .find-matches-popover__header-text {
    padding: 6px 0;

    @include size-4;
  }

  .find-matches-popover__body-text {
    padding: 12px 0;
    line-height: 1.5;

    @include size-2-thin;
  }

  .find-matches-popover__existing-match,
  .find-matches-popover__new-match {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .find-matches-popover__select,
    .find-matches-popover__new-match_input {
      width: 200px;
    }

    .find-matches-popover__new-match-add,
    .find-matches-popover__new-match-button-container {
      width: 190px;
      display: flex;
      height: 40px;
      align-items: center;
      text-transform: none;
      line-height: 30px;
      gap: 5px;
      justify-content: space-between;

      @include size-2-thin;

      .material-icons {
        font-size: 16px;
      }
    }

    .find-matches-popover__new-match-add {
      justify-content: center;
    }

    .find-matches-popover__new-match-confirm,
    .find-matches-popover__new-match-cancel {
      width: 120px;
      margin-right: 5px;
      font-size: small;
      text-transform: none;
    
      @include size-2-thin;
    
      .material-icons {
        font-size: 14px;
        padding-right: 10px;
      }
    }

    .find-matches-popover__new-match-cancel-new-event {
      line-height: 40px;
      color: var(--primary);
      margin-left: -15px;
      cursor: pointer;
      font-size: 24px !important;
    }

    .find-matches-popover__new-match-cancel {
      margin-right: 0;
    }

    .find-matches-popover__input-text {
      width: 130px;

      @include size-2-thin;
    }
  }

  .find-matches-popover__footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;

    .find-matches-popover__footer-button {
      text-transform: none;

      @include size-2;
    }
  }
}
