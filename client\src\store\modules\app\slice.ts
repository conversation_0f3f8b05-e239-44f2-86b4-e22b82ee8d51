import { createAppSlice } from "../../createAppSlice";

export interface AppBarState {
  isBooting: boolean;
  bootDidFinish: boolean;
}

const initialState: AppBarState = {
  isBooting: false,
  bootDidFinish: false,
};

export const appSlice = createAppSlice({
  name: "app",
  initialState,
  reducers: create => ({
    booting: create.reducer(
      (state) => {
        state.isBooting = true;
        state.bootDidFinish = false;
      },
    ),
    bootFinished: create.reducer(
      (state) => {
        state.isBooting = false;
        state.bootDidFinish = true;
      },
    ),
  }),
  selectors: {
    local: state => state,
    isBooting: app => app.isBooting,
    bootDidFinish: app => app.bootDidFinish,
  },
});

export const { booting, bootFinished } = appSlice.actions;

export const { isBooting, bootDidFinish } = appSlice.selectors;

export const { actions: appActions, reducer: appReducer } = appSlice;
