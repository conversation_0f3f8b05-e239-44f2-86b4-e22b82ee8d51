.potential-match-search-row {
  display: block;
  cursor: pointer;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  &:hover {
    background-color: var(--row-hover);

    .material-symbols-outlined {
      opacity: 1;
    }

    .svg-icon {
      opacity: 1;
    }
  }

  &.selected {
    background-color: var(--row-active);
  }

  .potential-match-search-row__row-details {
    margin-top: -10px;
    margin-bottom: 10px;
    transition: height 300ms;
    overflow: hidden;

    .potential-match-search-row__row-details-line {
      opacity: 0.5;
      background-color: var(--text-primary);
      width: 3px;
      height: 131px;
      margin-left: 70px;
      margin-right: 27px;
      transition: height 300ms;
      float: left;
    }
  }

  .potential-match-search-row__row {
    position: relative;
    display: flex;
    padding: 11.5px 0 11.5px 18px;

    .potential-match-search-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .potential-match-search-row__cell-select {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .potential-match-search-row__cell-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .MuiButton-text {
          text-transform: capitalize;
          border-radius: 8px;
          padding: 0;
          min-width: 32px;
        }

        .material-symbols-outlined {
          opacity: 0;
        }
        &:last-child {
          margin-right: 0;
        }

        .edit-icon {
          font-size: 28px;
          cursor: pointer;
          margin-left: auto;
          margin-right: 15px;

          .svg-icon {
            opacity: 0;
            fill: var(--icon-selector);
          }

          &:hover {
            .svg-icon {
              fill: var(--text-tertiary);
            }
          }
        }
      }

      .potential-match-search-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;

        &.pending {
          font-style: italic;
        }
      }
    }

    &:hover {
      .potential-match-search-row__cell {
        .potential-match-search-row__cell-actions {
          .material-symbols-outlined {
            opacity: 0.9;
            cursor: pointer;
          }
          .svg-icon {
            opacity: 0.9;
            cursor: pointer;
          }
        }
      }
    }
  }
}
