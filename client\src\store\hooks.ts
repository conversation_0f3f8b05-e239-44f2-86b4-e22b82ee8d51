// This file serves as a central hub for re-exporting pre-typed Redux hooks.
// These imports are restricted elsewhere to ensure consistent
// usage of typed hooks throughout the application.
// We disable the ESLint rule here because this is the designated place
// for importing and re-exporting the typed versions of hooks.

import { useDispatch } from "react-redux";
import HttpClient from './dependencies/httpClient';
import { ThunkDispatch, Action } from "@reduxjs/toolkit";
import type { RootState } from "./store";

interface ExtraArg {
  http: HttpClient;
}

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = useDispatch.withTypes<ThunkDispatch<RootState, ExtraArg, Action>>();
