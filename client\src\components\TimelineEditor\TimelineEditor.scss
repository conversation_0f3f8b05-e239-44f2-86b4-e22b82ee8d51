.timeline-editor {
  width: 100%;

  .timeline-editor__top {
    display: flex;
    justify-content: left;
    gap: 20px;
    padding: 0 20px;
    height: calc(100% - 250px);
    background-color: var(--main-content-background);
  }

  .timeline-editor__no-tracklets-found {
    display: block;
    height: 100%;
    margin: 30px auto;
    color: var(--disabled);
    text-align: center;

    span {
      font-weight: 900;
    }
  }

  .timeline-editor__video_and_attributes {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex-grow: 1;
    width: 525px;
    overflow-y: auto;

    .timeline-editor__video {
      flex: 0 0 auto;
    }

    .timeline-editor__attributes {
      flex: 1 1 auto;
      overflow-y: auto;
    }
  }

  .timeline-editor__detail {
    width: 60%;
  }

  .timeline-editor__matched-detection-tab-tracklet-container {
    padding: 3px;
    border-radius: 10px;
    box-shadow: inset 0 0 0 3px white;
    border: 1px solid var(--tracklet-border);

    &:hover {
      border: 1px solid var(--tracklet-border-selected);
      box-shadow: 0 0 8px 0 var(--tracklet-border-selected);

      .main__tracklet_thumbnails-tracklet-checkbox {
        opacity: 1;
      }
    }

    &.selected {
      border: 4px solid var(--tracklet-border-selected);
      box-shadow: 0 0 8px 0 var(--tracklet-border-selected);
    }
  }

  .timeline-editor__matched-detection-tab-tracklets {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: left;
    align-items: center;

    .timeline-editor__matched-detection-tab-tracklet {
      width: 100%;
      height: 100%;
      cursor: pointer;
      border-radius: 6px;
      object-fit: contain;
      background: black;

      &.selected {
        border-radius: 4px;
      }

      &.skeleton {
        cursor: inherit;
        box-shadow: inset 0 0 0 3px white;
        border: 1px solid var(--tracklet-border);
        background-color: var(--skeleton-background);
      }

      .timeline-editor__matched-detection-tab-tracklet-loading {
        height: 155px;
        width: 80px;
      }
    }
  }

  .timeline-editor__tabbed-detections-main-container-label {
    @include size-2-bold;

    padding-left: 15px;
    margin-bottom: 10px;
    color: var(--text-secondary);
  }

  .timeline-editor__tabbed-detections-main-container {
    border: solid 1px var(--divider);
    border-radius: 7px;
    height: calc(100% - 24px);
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: white;

    .timeline-editor__tabbed-detections-tab-panels {
      overflow-y: auto;
      flex-grow: 1;
      margin-bottom: 16px;
    }

    .timeline-editor__tabbed-detections-footer {
      display: flex;
      justify-content: right;
      align-items: center;
      padding: 19px 13px;
      height: 50px;
      border-top: solid 1px var(--divider);
    }
  }

  .timeline-editor__tracklet-detail {
    .timeline-editor__tracklet-detail-loading {
      margin-bottom: 3px;
      border-radius: 6px;
    }

    .timeline-editor__tracklet-detail-no-file {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: var(--text-tertiary);
      margin-top: 30px;
    }

    .timeline-editor__tracklet-detail-video-information {
      background-color: var(--video-information-background);
      padding: 10px 10px 20px;
      border-radius: 6px;

      .timeline-editor__tracklet-detail-video-information-title {
        @include size-1-bold;

        color: var(--tex-primary);
      }

      .timeline-editor__tracklet-detail-video-information-content {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 5px 0;

        .timeline-editor__tracklet-detail-video-information-file-size {
          display: none;
        }

        .timeline-editor__tracklet-detail-video-information-video-length,
        .timeline-editor__tracklet-detail-video-information-file-size,
        .timeline-editor__tracklet-detail-video-information-tracklets-added {
          text-align: center;

          :nth-child(1) {
            @include size-6-bold;
          }

          :nth-child(2) {
            @include size-1;

            margin-top: 5px;
          }
        }
      }
    }
  }

  .timeline-editor__timeline {
    padding: 45px 0 50px;
    position: relative;
    height: 250px;
    z-index: 10;
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-top: 10px;

    .timeline-editor__timeline-current-time-container {
      z-index: 30;
      left: 10px;
      top: 30px;
      position: absolute;
      width: 20px;
      height: 192px;
    }

    .timeline-editor__timeline-current-time {
      position: absolute;
      height: 192px;
      width: 2px;
      z-index: 30;
      background-color: var(--timeline-scrubber-current-time);
      margin: 0 10px;

      &::before {
        content: '';
        position: absolute;
        left: -9px;
        top: 0;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 15px solid var(--timeline-scrubber-current-time);
        clear: both;
      }
    }

    .timeline-editor__timeline-scrubber {
      height: 90px;
      display: flex;
      position: relative;
      align-items: end;
      margin: 0 20px;
      top: -50px;
      z-index: 19;

      .timeline-editor__timeline-scrubber-timestamp {
        position: absolute;
        transform: translate(-50%, -25px);
        user-select: none;
        will-change: transform, left;

        @include size-1;
      }

      .timeline-editor__timeline-scrubber-tick {
        position: absolute;
        height: 9px;
        width: 1px;
        background-color: var(--tick-color);
        will-change: transform, left;
      }
    }

    .timeline-editor__timeline-container {
      height: 175px;
      top: -45px;
      position: relative;
      z-index: 20;

      .timeline-editor__timeline-container-left-border {
        position: absolute;
        height: 210px;
        width: 5px;
        left: -5px;
        top: -45px;
        background-color: var(--timeline-clips-background);
      }

      .timeline-editor__timeline-container-right-border {
        position: absolute;
        height: 210px;
        width: 5px;
        right: -5px;
        top: -45px;
        background-color: var(--timeline-clips-background);
      }

      .timeline-editor__timeline-clips {
        position: relative;
        background-color: var(--timeline-clips-background);
        height: 210px;
        top: -45px;

        .timeline-editor__timeline-clips-reorder {
          top: 40px;
          left: -2px;
          z-index: 50;
          width: 4px;
          height: 136px;
          position: absolute;
          background-color: gold;

          .material-symbols-outlined {
            font-size: 24px;
            background-color: gold;
            padding: 3px;
            border-radius: 4px;
            position: absolute;
            top: 0;
            left: 2px;
            border: solid 1px var(--timeline-icon-outline);
            transform: translate(-50%, -100%);
          }
        }

        .timeline-editor__timeline-clips-clip {
          top: 41px;
          width: 240px;
          height: 136px;
          position: absolute;
          background-color: var(--timeline-clip-background);
          align-items: center;
          padding: 0 70px;
          text-align: center;
          color: var(--text-tertiary);
          display: flex;
          user-select: none;
          justify-content: center;

          @include size-1;

          .timeline-editor__timeline-clips-clip-plus {
            font-size: 50px;
            text-align: center;
          }

          &.has-tracklets {
            padding: 0;
            border: solid 4px;
            border-left: none;
            justify-content: start;

            &:nth-child(2) {
              border-left: solid 4px;
              border-radius: 4px 0 0 4px;
            }

            &.is-last {
              border-radius: 0 4px 4px 0;
            }

            &.selected {
              border: 4px solid var(--selected-outline);
              border-left: none;

              &:nth-child(2) {
                border-left: solid 4px var(--selected-outline);
              }
            }

            &.next-selected {
              border-right: 4px solid var(--selected-outline);
            }
          }

          .timeline-editor__timeline-clips-clip-img {
            height: 100%;
          }

          .timeline-editor__timeline-clips-clip-img-container {
            height: 100%;
            text-align: center;
            color: var(--text-tertiary);
            overflow: hidden;
            display: flex;
            user-select: none;
            justify-content: start;
          }

          .timeline-editor__timeline-clips-clip-top {
            position: absolute;
            color: var(--text-primary);
            top: -27px;
            display: flex;
            justify-content: space-between;
            padding-right: 5px;
            padding-left: 5px;
            z-index: 21;
            align-items: center;

            @include size-0-bold;
          }

          .timeline-editor__timeline-clips-clip-right {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
          }

          .timeline-editor__timeline-clips-clip-title {
            color: var(--text-primary);
          }

          .timeline-editor__timeline-clips-clip-length {
            color: var(--text-primary);
          }

          .timeline-editor__timeline-clips-clip-delete {
            color: var(--text-primary);
            font-size: 16px;
            cursor: pointer;
          }
        }

        :first-child {
          &.first-tracklet {
            border: dashed 5px var(--text-tertiary);
          }
        }

        .timeline-editor__timeline-seconds-in-view-scaler-container {
          position: absolute;
          bottom: 0;
          width: 235px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px;

          .timeline-editor__timeline-seconds-in-view-scaler-start-handle {
            width: 10px;
            height: 10px;
            border-radius: 10px;
            background-color: var(--timeline-scrubber-track);
            border: solid 3px var(--timeline-scrubber-thumb);
            position: absolute;
            z-index: 10;
            left: 15px;
            top: 13px;
          }

          .material-icons {
            font-size: 14px;
          }
        }

        .timeline-editor__timeline-seconds-in-view-scaler {
          width: calc(100% - 40px);
          height: 10px;
          color: transparent;
          margin: 0 5px;
          opacity: 1;

          .MuiSlider-rail {
            background-color: var(--timeline-scrubber-track);
            opacity: 1;
          }

          .MuiSlider-track {
            background-color: var(--timeline-scrubber-rail);
            opacity: 1;
          }

          .MuiSlider-thumb {
            background-color: var(--timeline-scrubber-track);
            border: solid 3px var(--timeline-scrubber-thumb);
            height: 10px;
            width: 10px;
            opacity: 1;
            z-index: 10;

            &.Mui-disabled {
              display: none;
            }
          }

          &.Mui-disabled {
            .MuiSlider-track,
            .MuiSlider-thumb {
              display: none;
            }

            .MuiSlider-rail {
              background-color: var(--timeline-scrubber-disabled);
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
