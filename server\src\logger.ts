import chalk from 'chalk';
import coreLogger from '@veritone/core-logger';
import { Env } from './env';

const formatConsoleArgs = <T>(a: T) =>
  typeof a === 'object' ? JSON.stringify(a) : a;

export const consoleLogger = {
  error: function <T>(...args: T[]) {
    console.error(chalk.red(...args.map(formatConsoleArgs)));
  },
  info: function <T>(...args: T[]) {
    console.log(chalk.green(...args.map(formatConsoleArgs)));
  },
  debug: function <T>(...args: T[]) {
    console.log(chalk.blue(...args.map(formatConsoleArgs)));
  },
};

function createLogger(env?: Env): Logger {
  return env?.nodeEnv === 'development' ? consoleLogger : coreLogger(env);
}

export default createLogger;
