import NodeCache from 'node-cache';
import { Variables } from 'graphql-request';
import getThumbnails from '../getThumbnails';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { createRequest, createResponse } from 'node-mocks-http';
import * as buildUrl from '../../../credential/actions/util/buildUrl';

let cxt: Context<object, object>;

jest.spyOn(buildUrl, 'buildReadOnlyUrl').mockResolvedValue('http://test.com');

describe('getThumbnails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        tracklets: [
          {
            trackletId: 'trackletId',
            orgId: 123,
            fileId: 456,
          },
        ],
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {},
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully gets thumbnails', async () => {
    await expect(getThumbnails(cxt)).resolves.toMatchObject({
      data: {
        thumbnails: {
          trackletId: {
            thumbnailUrls: {
              best: 'http://test.com',
            },
            expiresDateTime: expect.anything(),
          },
        },
      },
    });
  });
});
