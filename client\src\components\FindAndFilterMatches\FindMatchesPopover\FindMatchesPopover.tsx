import { useEffect, useState } from 'react';
import { TextField, Button, Popover, Select, MenuItem, FormControl, InputLabel, SelectChangeEvent } from '@mui/material';
import './FindMatchesPopover.scss';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { createMatchGroup, getMatchGroups, selectMatchGroups, updateMatchGroup, selectNewMatchGroupId, selectSelectedTracklet } from '@store/modules/searchResults/slice';
import { selectSelectedTracklet as selectSelectedFileTracklet } from '@store/modules/file/slice';
import { useAppDispatch } from '@store/hooks';
import { merge } from 'lodash';
import { v4 as uuidV4 } from 'uuid';
import { AlertLevel, createSnackNotification } from '@components/common';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import { getLastSearchName } from '@utility/getLastSearchName';

interface Props {
  open: boolean;
  onClose: () => void;
  anchorEl: HTMLElement | null;
}

const FindMatchesPopover = ({ open, onClose, anchorEl }: Props) => {
  const intl = useIntl();
  const { eventId } = useParams();
  const [existingMatch, setExistingMatch] = useState('');
  const [newMatchGroup, setNewMatchGroup] = useState(false);
  const [newMatchGroupName, setNewMatchGroupName] = useState('');
  const [disableInput, setDisableInput] = useState(false);
  const matchGroups = useSelector(selectMatchGroups);
  const createdMatchGroupId = useSelector(selectNewMatchGroupId);
  const selectedTracklet = useSelector(selectSelectedTracklet);
  const selectedFileTracklet = useSelector(selectSelectedFileTracklet);

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (eventId && !newMatchGroup) {
      dispatch(getMatchGroups({ eventId }));
    }
  }, [eventId, dispatch, newMatchGroup]);

  const handleChangeExistingMatch = (event: SelectChangeEvent<string>) => {
    setExistingMatch(event.target.value);
  };

  const handleNewMatchGroup = () => {
    setDisableInput(false);
    setNewMatchGroupName('');
    setNewMatchGroup(true);
  };

  const handleNewMatchGroupInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewMatchGroupName(event.target.value);
  };

  const handleNewMatchGroupKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === ' ' || event.keyCode === 32 || event.keyCode === 37 || event.keyCode === 39) {
      event.stopPropagation();
    }
  };

  const handleCreateNewMatchGroup = () => {
    if (eventId && newMatchGroupName) {
      dispatch(createMatchGroup({ name: newMatchGroupName, eventId }));
      setDisableInput(true);
      dispatch(getMatchGroups({ eventId }));
    }
  };

  const handleCancelNewMatchGroup = () => {
    setExistingMatch('');
    setNewMatchGroup(false);
    setDisableInput(false);
    if (eventId) {
      dispatch(getMatchGroups({ eventId }));
    }
  };

  useEffect(() => {
    if (!open) {
      setNewMatchGroup(matchGroups.results?.length < 1);
      setDisableInput(false);
    }
  }, [matchGroups, open]);

  const handleConfirm = () => {
    const matchGroupId = newMatchGroup ? createdMatchGroupId : existingMatch;
    const tracklet = merge({}, selectedTracklet, [selectedFileTracklet.person], [selectedFileTracklet.vehicle]);
    const referenceTrackletId = tracklet[0].trackletId;
    const trackletId = uuidV4();
    const searchName = newMatchGroup
      ? `${intl.formatMessage({ id: 'potentialMatchSearch', defaultMessage: 'Potential Match Search' })} 1`
      : `${intl.formatMessage({ id: 'potentialMatchSearch', defaultMessage: 'Potential Match Search' })} ${getLastSearchName(matchGroups, matchGroupId, false)}`;

    if (matchGroupId && trackletId && (newMatchGroupName || existingMatch)) {
      dispatch(
        updateMatchGroup({
          matchGroupId,
          trackletId,
          referenceTrackletId,
          searchName,
        })
      );
      handleClose();
    } else {
      createSnackNotification(AlertLevel.Error, intl.formatMessage({ id: 'snackSelectMatchGroup', defaultMessage: 'Please select a match group or create a new one.' }));
    }
  };

  const handleClose = () => {
    onClose();
    setExistingMatch('');
    setNewMatchGroup(false);
  };

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >
      <div className="find-matches-popover__container" data-testid="find-matches-popover">
        <div className="find-matches-popover__header-text">
          {I18nTranslate.TranslateMessage('findMatches')}
        </div>
        <div className="find-matches-popover__body-text">
          System will now search this file and other files in this event for other objects that are similar to this object and store those matches. Please select an existing match group or create a
          new match group.
        </div>
        <div className="find-matches-popover__existing-match">
          {!newMatchGroup && (
            <>
              <div className="find-matches-popover__input-text">
                {I18nTranslate.TranslateMessage('existingMatchGroup')}
              </div>
              <FormControl size="small" className="find-matches-popover__select">
                <InputLabel className="file-and-filter-matches__detail-label" id="file-and-filter-matches__detail-label">
                  {I18nTranslate.TranslateMessage('matchGroups')}
                </InputLabel>
                <Select
                  label={matchGroups.results?.length > 0 ? 'Match Groups' : 'No match groups'}
                  size="small"
                  labelId="find-matches-popover__select"
                  id="find-matches-popover-select"
                  disabled={matchGroups.results?.length < 1}
                  data-testid="find-matches-popover-select"
                  value={existingMatch}
                  onChange={handleChangeExistingMatch}
                >
                  {matchGroups.results?.map((match, i) => (
                    <MenuItem
                      key={match.id}
                      value={match.id}
                      data-testid={`find-matches-popover-select-match-${i}`}
                    >
                      {match.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                className="find-matches-popover__new-match-add"
                data-testid="find-matches-popover__new-match-add"
                variant="outlined"
                size="small"
                onClick={handleNewMatchGroup}
              >
                <div className="material-icons">add</div>
                <span>
                  {I18nTranslate.TranslateMessage('newMatchGroup')}
                </span>
              </Button>
            </>
          )}
          {newMatchGroup && (
            <>
              <div className="find-matches-popover__input-text">
                {I18nTranslate.TranslateMessage('newMatchGroup')}
              </div>
              <TextField
                size="small"
                className="find-matches-popover__new-match_input"
                value={newMatchGroupName}
                disabled={disableInput}
                onChange={handleNewMatchGroupInput}
                onKeyDown={handleNewMatchGroupKeyDown}
              />
              <div className="find-matches-popover__new-match-button-container">
                {!disableInput && (
                  <Button
                    className="find-matches-popover__new-match-confirm"
                    data-testid="find-matches-popover__new-match-confirm"
                    variant="contained"
                    size="small"
                    onClick={handleCreateNewMatchGroup}
                  >
                    <div className="material-icons">done</div>
                    {I18nTranslate.TranslateMessage('create')}
                  </Button>
                )}
                {disableInput && (
                  <div
                    className="find-matches-popover__new-match-cancel-new-event material-icons"
                    data-testid="find-matches-popover__new-match-cancel-icon"
                    onClick={handleCancelNewMatchGroup}>
                    close
                  </div>
                )}
                {!disableInput && (
                  <Button
                    className="find-matches-popover__new-match-cancel"
                    data-testid="find-matches-popover__new-match-cancel"
                    variant="outlined"
                    size="small"
                    onClick={handleCancelNewMatchGroup}
                  >
                    <div className="material-icons">cancel</div>
                    {I18nTranslate.TranslateMessage('cancel')}
                  </Button>
                )}
                {disableInput && (
                  <Button
                    className="find-matches-popover__new-match-add"
                    data-testid="find-matches-popover__new-match-add"
                    variant="outlined"
                    size="small"
                    onClick={handleNewMatchGroup}
                  >
                    <div className="material-icons">add</div>
                    <span>
                      {I18nTranslate.TranslateMessage('newMatchGroup')}
                    </span>
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
        <div className="find-matches-popover__footer">
          <Button
            className="find-matches-popover__footer-button"
            variant="outlined"
            data-testid="find-matches-popover__close"
            onClick={handleClose}
          >
            {I18nTranslate.TranslateMessage('cancel')}
          </Button>
          <Button
            className="find-matches-popover__footer-button"
            variant="contained"
            data-testid="find-matches-popover__confirm"
            onClick={handleConfirm}
          >
            {I18nTranslate.TranslateMessage('continue')}
          </Button>
        </div>
      </div>
    </Popover>
  );
};

export default FindMatchesPopover;
