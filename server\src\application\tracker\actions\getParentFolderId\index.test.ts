import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import getParentFolderId from './';
import { ActionError } from '@common/errors';

let cxt: Context<object, object>;

const rootFolders = [
  {
    id: '36b57a3d-3b44-4328-adb0-49d78cd0b8db',
    name: 'Root Admin cms Root Folder',
    ownerId: null,
  },
];
jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        rootFolders: rootFolders,
      })
  ),
}));

describe('Get parentFolderId', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('return root folder when root folder available', async () => {
    const response = await getParentFolderId(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {}
    );
    expect(response).not.toBeNull();
    expect(response?.data?.parentFolderId).toEqual(
      '36b57a3d-3b44-4328-adb0-49d78cd0b8db'
    );
  });

  it('throw error when there is no root folder', async () => {
    rootFolders.length = 0;
    const t = async () => {
      const response = await getParentFolderId(cxt);
    };
    expect(t()).rejects.toThrow(ActionError);
    expect(callGQL).toHaveBeenCalledTimes(1);
  });
});
