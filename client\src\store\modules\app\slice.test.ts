import { appSlice, booting, bootFinished } from '@store/modules/app/slice';
import { configureAppStore } from '@store/store';

const initialStateForMock = {
  app: {
    isBooting: false,
    bootDidFinish: false,
  }
};

describe('appSlice', () => {
  it('should handle booting', () => {

    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // dispatch the booting action
    mockedStore.dispatch(booting());

    // expect isBooting to be true
    const state = mockedStore.getState();

    // expect app state after booting action
    expect(state.app).toEqual({
      isBooting: true,
      bootDidFinish: false,
    });

    // expect selectors after booting action
    expect(appSlice.selectors.isBooting(state)).toEqual(true);
    expect(appSlice.selectors.bootDidFinish(state)).toEqual(false);
    expect(state.app.isBooting).toBe(true);
  });

  it('should handle bootFinished', () => {

    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    mockedStore.dispatch(bootFinished());

    // expect bootDidFinish to be true
    const state = mockedStore.getState();
    expect(state.app).toEqual({
      isBooting: false,
      bootDidFinish: true,
    });

    // expect selectors after bootFinished action
    expect(appSlice.selectors.isBooting(state)).toEqual(false);
    expect(appSlice.selectors.bootDidFinish(state)).toEqual(true);
  });
});
