import { bytesToMb, millisToTimeFormatted } from './convert';

describe('Utility functions', () => {
  test('bytesToMb converts bytes to MB ', () => {
    expect(bytesToMb(1048576)).toBe('1.0');
    expect(bytesToMb(5242880)).toBe('5.0');
  });

  test('bytesToMb converts Nan to 0 ', () => {
    expect(bytesToMb(Number.NaN)).toBe('0');
  });

  // Disabled due to timezone issues
  // test('toLocalTime converts ISO datetime string to local time', () => {
  //   const dateTimeString = '2023-06-26T12:34:56.789Z';
  //   const localTime = DateTime.fromISO(dateTimeString)
  //     .toLocal()
  //     .toLocaleString(DateTime.DATETIME_SHORT);
  //   expect(toLocalTime(dateTimeString)).toBe(localTime);
  // });

  test('millisToTimeFormatted converts seconds to hh:mm:ss', () => {
    expect(millisToTimeFormatted(1000)).toBe('00:00:01');
    expect(millisToTimeFormatted(61000)).toBe('00:01:01');
    expect(millisToTimeFormatted(3661000)).toBe('01:01:01');
  });
});
