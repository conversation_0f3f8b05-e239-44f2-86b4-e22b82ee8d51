import {
  AlertLevel,
  createSnackNotification,
} from '@components/common/Snackbar/Snackbar';
import { PayloadAction } from '@reduxjs/toolkit';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import { createAppSlice } from '../../createAppSlice';
import {
  DeleteEventResponse,
  DeleteMatchGroupSearchResponse,
  GetEventResponse,
  GetMatchGroupsResponse,
  SearchFilesResponse,
  GetTagsResponse,
  UpdateEventResponse,
  DeleteGeneratedTimelineResponse,
  CreateMatchGroupResponse,
  UpdateFileResponse,
} from '@shared-types/responses';
import {
  DeleteGeneratedTimelineState,
  EventDeletionState,
  EventState,
  FilesState,
  FileState,
  GeneratedTimelineState,
  GetFilesParams,
  GetMatchGroupsParams,
  MatchGroupDeletionState,
  MatchGroupsState,
  TagsState,
  UpdateEventParams,
} from './types';
import type { File } from '@shared-types/tracker';
import { SearchMatchGroupsQueryParams } from '@shared-types/requests';
import qs from 'qs';
import { deleteEvent as deleteEventSearchAndUpload } from '../upload/slice';
import { compact, isNumber, isString, map, merge, isEqual, isObject } from 'lodash';
import { isExpiringString } from '@utility/expiringString';
import ls from 'localstorage-slim';
import { DeletedMatchGroupLocalStorage, getUpdatedFilesLocalStorage, removeDeletedMatchGroup, removeUpdatedMatchGroup, UpdatedEventLocalStorage, UpdatedFileLocalStorage, UpdatedMatchGroupLocalStorage } from '@utility/localStorage';
import { deleteMatchGroup, updateMatchGroup } from '../matchGroup/slice';

// Json file
import { convertJsonAttributes } from '@utility/convertJsonAttributes';
import { AttributeRecord, Attributes, AttributeType, Search } from '@shared-types/tracker';
import { CreateMatchGroup } from '../matchGroup/types';
import { UpdateMatchGroup } from '../searchResults/types';
import { getAttributesJson } from '@utility/getAttributesJson';
import { AxiosError } from 'axios';

export interface EventSliceState {
  files: FilesState;
  fileSelected: FileState;
  matchGroups: MatchGroupsState;
  event: EventState;
  eventDeletion: EventDeletionState;
  matchGroupSearchDeletion: MatchGroupDeletionState;
  tags: TagsState;
  generatedTimelines: GeneratedTimelineState;
  generatedTimelineDeletions: DeleteGeneratedTimelineState[];
  attributes: {
    person: Attributes;
    vehicle: Attributes;
  };
  createMatchGroup: CreateMatchGroup;
  updateMatchGroup: UpdateMatchGroup;
}

const initialState: EventSliceState = {
  files: {
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
  },
  fileSelected: {
    file: undefined,
    status: 'idle',
    error: '',
  },
  matchGroups: {
    eventId: '',
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
    sortType: '',
    sortDirection: '',
  },
  event: {
    id: '',
    name: '',
    tags: [],
    createdBy: '',
    createdByName: '',
    description: '',
    eventStartDate: '',
    eventEndDate: '',
    createdDateTime: '',
    modifiedDateTime: '',
    status: 'idle',
    matchGroupsCount: 0,
    filesCount: 0,
  },
  eventDeletion: {
    id: '',
    status: 'idle',
    message: '',
  },
  matchGroupSearchDeletion: {
    status: 'idle',
    message: '',
    matchGroupId: '',
    searchId: '',
  },
  tags: {
    results: [],
    status: 'idle',
    error: '',
  },
  generatedTimelines: {
    results: [],
    status: 'idle',
    error: '',
  },
  generatedTimelineDeletions: [],
  attributes: {
    person: {},
    vehicle: {},
  },
  createMatchGroup: {
    status: 'idle',
    error: '',
    id: '',
  },
  updateMatchGroup: {
    status: 'idle',
    error: '',
  },
};

export const eventSlice = createAppSlice({
  name: 'event',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();

    return {
      setFilesPagination: create.reducer(
        (
          state,
          action: PayloadAction<{ currentPage: number; pageSize: number }>
        ) => {
          state.files.currentPage = action.payload.currentPage;
          state.files.pageSize = action.payload.pageSize;
        }
      ),
      getFiles: createHttpThunk(
        async (
          {
            page,
            limit,
            eventId,
            query,
            sort,
            dir,
          }: GetFilesParams & { isPoll?: boolean },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          let sortBy = 'createdTime';
          switch (sort) {
            case 'fileName':
              sortBy = 'veritone-file.filename';
              break;
            case 'uploadDate':
              sortBy = 'createdTime';
              break;
            default:
              sortBy = sort ? sort : 'createdTime';
          }

          const sortDirection = dir ? dir : 'desc';
          const response = await http.get<SearchFilesResponse>(signal)(
            `/files/${qs.stringify(
              {
                currentPage: page,
                pageSize: limit,
                eventId,
                query,
                sortBy,
                sortDirection,
              },
              { addQueryPrefix: true }
            )}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.files.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const updatedFilesLocalStorage = getUpdatedFilesLocalStorage(action.payload.results);

            state.files = {
              ...action.payload,
              // Use the updated fileName from local storage, if available, instead of querying Elasticsearch
              results: action.payload.results.map(
                (file) => {
                  const updatedFile = updatedFilesLocalStorage?.find((f) => f.value.fileId === file.id);
                  if (updatedFile) {
                    return { ...file, fileName: updatedFile.value.fileName };
                  }
                  return file;
                }
              ),
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              if (action.error.message === 'Aborted') {
                state.files.status = 'idle';
              } else {
                createSnackNotification(
                  AlertLevel.Error,
                  'Get files failed',
                  action.error.message
                );
                state.files.status = 'failure';
              }
            }
          },
        }
      ),
      setMatchGroupsPagination: create.reducer(
        (
          state,
          action: PayloadAction<{ currentPage: number; pageSize: number }>
        ) => {
          state.matchGroups.currentPage = action.payload.currentPage;
          state.matchGroups.pageSize = action.payload.pageSize;
        }
      ),
      getMatchGroups: createHttpThunk(
        async (
          { eventId }: GetMatchGroupsParams & { isPoll?: boolean },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const searchEventsQuery: SearchMatchGroupsQueryParams = {
            eventId: eventId,
            pageSize: 10000,
            currentPage: 1,
          };
          const response = await http.get<GetMatchGroupsResponse>(signal)(
            `/match-groups/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.matchGroups.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            const { results } = action.payload;

            // remove edited match-groups from local storage and update match-groups state
            const updatedMatchGroups: UpdatedMatchGroupLocalStorage[] = ls.get('updatedMatchGroups') || [];
            const isUpdated = removeUpdatedMatchGroup(updatedMatchGroups, results);
            if (updatedMatchGroups.length === 0 || isUpdated) {
              state.matchGroups = { ...action.payload, status: 'idle' };
            }

            // remove deleted match-groups from local storage
            removeDeletedMatchGroup(results);
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.matchGroups.status = 'failure';
            }
          },
        }
      ),
      getEventById: createHttpThunk(
        async (
          {
            eventId,
          }: {
            eventId: string;
            isPoll?: boolean;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
            rejectWithValue,
          } = thunkAPI;
          try {
            const response = await http.get<GetEventResponse>(signal)(
              `/event/${eventId}`,
              {
                // TODO: Fix the getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              }
            );
            return response.data;
          } catch (error) {
            const { response } = error as AxiosError;
            return rejectWithValue(response?.data);
          }
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.event.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            state.event = { ...action.payload.event, status: 'idle' };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              if (action.error.message !== 'Aborted') {
                const isErrorMessage = (data: unknown): data is { message: string } =>
                  isObject(data) && "message" in data && typeof data.message === 'string';

                createSnackNotification(
                  AlertLevel.Error,
                  'Get event failed',
                  isErrorMessage(action.payload) ? action.payload.message : action.error.message
                );
                state.event.status = 'failure';
              } else {
                state.event.status = 'idle';
              }
            }
          },
        }
      ),
      updateEventById: createHttpThunk(
        async ({ eventId, ...data }: UpdateEventParams, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.patch<UpdateEventResponse>(signal)(
            `/event/${eventId}`,
            data,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data.event;
        },
        {
          pending: (state) => {
            state.event.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.event = { ...action.payload, status: 'idle' };
            createSnackNotification(
              AlertLevel.Success,
              'Success',
              'Updated event successfully'
            );

            // Store updated eventId and event name in localStorage
            const { eventId, name } = action.meta.arg;
            let updatedEvents: UpdatedEventLocalStorage[] | null = ls.get('updatedEvents');
            if (Array.isArray(updatedEvents)) {
              updatedEvents = updatedEvents.filter(
                (item) => isString(item.value.eventId) && isString(item.value.eventName) && isNumber(item.expiry)
              );
            } else {
              updatedEvents = [];
            }

            // Add the new updated event to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: {
                eventId,
                eventName: name || '',
              },
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };

            const existingUpdatedEvent = updatedEvents.findIndex((e) => e.value.eventId === item.value.eventId);
            if (existingUpdatedEvent > -1) {
              updatedEvents.splice(
                existingUpdatedEvent - 1,
                1
              );
            }

            updatedEvents.push(item);
            // Store the updated array back in localStorage
            ls.set('updatedEvents', updatedEvents);
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Get event failed',
                action.error.message
              );
              state.event.status = 'failure';
            } else {
              state.event.status = 'idle';
            }
          },
        }
      ),
      updateFileById: createHttpThunk(
        async (
          {
            fileId,
            fileName,
          }: {
            fileId: string;
            fileName: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.patch<UpdateFileResponse>(signal)(`/file/${fileId}`,
            {
              name: fileName
            },
            {
              // TODO: Fix the getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
          });

          return response.data.file;
        },
        {
          pending: (state) => {
            state.fileSelected.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.fileSelected.status = 'idle';

            if (state.fileSelected.file) {
              state.fileSelected.file.fileName = action.payload.fileName;
            }

            const index = state.files.results.findIndex(item => item.id === action.payload.id);
            if (index !== -1) {
              state.files.results[index] = {
                ...state.files.results[index],
                ...action.payload,
              };
            }

            createSnackNotification(
              AlertLevel.Success,
              'Success',
              'Updated file name successfully'
            );

            // Store updated fileId and fileName in localStorage
            const { fileId, fileName } = action.meta.arg;
            let updatedFiles: UpdatedFileLocalStorage[] | null = ls.get('updatedFiles');
            if (Array.isArray(updatedFiles)) {
              updatedFiles = updatedFiles.filter(
                (item) => isString(item.value.fileId) && isString(item.value.fileName) && isNumber(item.expiry)
              );
            } else {
              updatedFiles = [];
            }

            // Add the new updated file to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: {
                fileId,
                fileName
              },
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };

            const existingUpdatedFile = updatedFiles.findIndex((f) => f.value.fileId === item.value.fileId);
            if (existingUpdatedFile > -1) {
              updatedFiles.splice(
                existingUpdatedFile - 1,
                1
              );
            }
            updatedFiles.push(item);
            // Store the updated array back in localStorage
            ls.set('updatedFiles', updatedFiles);
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Update file name failed',
                action.error.message
              );
              state.fileSelected.status = 'failure';
            } else {
              state.fileSelected.status = 'idle';
            }
          },
        }
      ),
      deleteEvent: createHttpThunk(
        async (id: string, thunkAPI) => {
          const { getState, signal, extra: { http }, dispatch } = thunkAPI;
          // TODO: Fix the getState type
          const response = await http.delete<DeleteEventResponse>(signal)(`/event/${id}`, {}, { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` });

          const result = response.data;

          if (response.status === 200 && result.code !== 500) {
            // Delete the event from the Search and Upload File
            dispatch(deleteEventSearchAndUpload({ id }));
          }
          return result;
        },
        {
          pending: (state, action) => {
            state.eventDeletion.error = '';
            state.eventDeletion.status = 'loading';
            state.eventDeletion.id = action.meta.arg;
          },
          fulfilled: (state, action) => {
            const { code } = action.payload;
            if (code === 500) {
              state.eventDeletion.id = '';
              state.eventDeletion.status = 'failure';
              state.eventDeletion.error = 'Delete Event Failed';
              createSnackNotification(
                AlertLevel.Error,
                'Error',
                'Delete Event Failed'
              );
              return;
            }
            state.eventDeletion.id = '';
            state.eventDeletion.error = '';
            state.eventDeletion.status = 'idle';
            createSnackNotification(
              AlertLevel.Success,
              'Success',
              action.payload.message
            );

            // Store deleted event ID in localStorage
            const eventId = action.meta.arg;
            let deletedEventIds: Array<{
              value: string;
              expiry: number;
            }> | null = ls.get('deletedEventIds');
            if (Array.isArray(deletedEventIds)) {
              deletedEventIds = deletedEventIds.filter(isExpiringString);
            } else {
              deletedEventIds = [];
            }

            // Add the new deleted event ID to the array with an expiration time of 5 minutes
            const now = new Date();
            const item = {
              value: eventId,
              expiry: now.getTime() + 1000 * 60 * 5,
            };
            deletedEventIds.push(item);

            // Store the updated array back in localStorage
            ls.set('deletedEventIds', deletedEventIds);
          },
          rejected: (state, action) => {
            state.eventDeletion.id = '';
            state.eventDeletion.status = 'failure';
            state.eventDeletion.error = action.error.message;
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Delete Event Failed'
            );
          },
        }
      ),
      poll: createHttpThunk(
        async (
          {
            type,
            eventId,
            query,
            sort,
            dir,
            page,
            limit,
          }: {
            eventId: string;
            type: 'event' | 'files' | 'match-groups' | 'generated-timeline';
            query?: string;
            sort?: string;
            dir?: string;
            page: number;
            limit: number;
          },
          thunkAPI
        ) => {
          const { signal, dispatch } = thunkAPI;
          let dispatchGetEventById: DispatchPromise;
          let dispatchGetFiles: DispatchPromise;
          let dispatchGetMatchGroups: DispatchPromise;
          let dispatchGetGeneratedTimeline: DispatchPromise;
          const pollInterval = setInterval(() => {
            if (type === 'event') {
              dispatchGetEventById = dispatch(
                getEventById({ eventId, isPoll: true })
              );
            }
            if (type === 'files') {
              dispatchGetFiles = dispatch(
                getFiles({
                  page,
                  limit,
                  eventId,
                  query,
                  sort,
                  dir,
                  isPoll: true,
                })
              );
            }
            if (type === 'match-groups') {
              dispatchGetMatchGroups = dispatch(
                getMatchGroups({ eventId, isPoll: true })
              );
            }
            if (type === 'generated-timeline') {
              dispatchGetGeneratedTimeline = dispatch(
                getGeneratedTimeline({ eventId, isPoll: true })
              );
            }
          }, 7500);

          signal.addEventListener('abort', () => {
            clearInterval(pollInterval);
            dispatchGetEventById?.abort();
            dispatchGetFiles?.abort();
            dispatchGetMatchGroups?.abort();
            dispatchGetGeneratedTimeline?.abort();
          });
        }
      ),
      deleteMatchGroupSearch: createHttpThunk(
        async (
          {
            matchGroupId,
            searchId,
          }: { matchGroupId: string; searchId: string },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.delete<DeleteMatchGroupSearchResponse>(
            signal
          )(
            `/match-groups/${matchGroupId}/search/${searchId}`,
            {},
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroupSearchDeletion.status = 'loading';
          },
          fulfilled: (state, action) => {
            const { code } = action.payload;
            if (code === 500) {
              createSnackNotification(
                AlertLevel.Error,
                'Error',
                'Delete Match Group Search Failed'
              );
              state.matchGroupSearchDeletion.status = 'failure';
              state.matchGroupSearchDeletion.error =
                'Delete Match Group Search Failed';
              return;
            }
            const { message } = action.payload;
            createSnackNotification(AlertLevel.Success, 'Success', message);
            state.matchGroupSearchDeletion = {
              ...action.payload,
              status: 'idle',
            };

            // Store deleted file ID in localStorage
            const searchId = action.payload.searchId;
            let deletedSearchIds: Array<{
              value: string;
              expiry: number;
            }> | null = ls.get('deletedSearchIds');
            if (Array.isArray(deletedSearchIds)) {
              deletedSearchIds = deletedSearchIds.filter(isExpiringString);
            } else {
              deletedSearchIds = [];
            }

            // Add the new deleted Search ID to the array with an expiration time of 15 minutes
            const now = new Date();
            const item = {
              value: searchId,
              expiry: now.getTime() + 1000 * 60 * 15,
            };
            deletedSearchIds.push(item);

            // Store the updated array back in localStorage
            ls.set('deletedSearchIds', deletedSearchIds);
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Delete Match Group Search Failed'
            );
            state.matchGroupSearchDeletion.status = 'failure';
            state.matchGroupSearchDeletion.error = action.error.message;
          },
        }
      ),
      updateMatchGroupSearchName: createHttpThunk(
        async (
          {
            matchGroupId,
            searchId,
            searchName,
          }: {
            matchGroupId: string;
            searchId: string;
            searchName: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const existingMatchGroup = (getState() as RootState).event.matchGroups.results.find((matchGroup) => matchGroup.id === matchGroupId)?.searches ?? [];
          const updatedSearch = {...existingMatchGroup.find((search) => search.id === searchId), searchName};

          const searches = existingMatchGroup.map(search => search.id === updatedSearch?.id ? updatedSearch : search);

          const response = await http.patch<CreateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            {
              searches,
            },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroups.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchGroups.status = 'idle';
            state.matchGroups.results = map(state.matchGroups.results, (matchGroup) =>
              matchGroup.id === action.payload.matchGroup?.id ? merge({}, matchGroup, action.payload.matchGroup) : matchGroup
            );
            createSnackNotification(
              AlertLevel.Success,
              'Update Match Group Search Name',
              `Updated ${action.payload?.matchGroup?.name} search name successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group Search Name failed',
              action.error.message
            );
            state.matchGroups.status = 'idle';
          },
        }
      ),
      getTags: createHttpThunk(
        async (_, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetTagsResponse>(signal)(`/tags`, {
            // TODO: Fix the getState type
            Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
          });

          return response.data;
        },
        {
          pending: (state) => {
            state.tags.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.tags = {
              ...action.payload,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Tags failed',
              action.error.message
            );
            state.tags.status = 'failure';
          },
        }
      ),
      getGeneratedTimeline: createHttpThunk(
        async (
          { eventId }: GetMatchGroupsParams & { isPoll?: boolean },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const state = getState() as RootState;
          let matchGroupsResults = state.event.matchGroups.results?.slice();

          const searchEventsQuery: SearchMatchGroupsQueryParams = {
            eventId: eventId,
            pageSize: 10000,
            currentPage: 1,
          };
          const response = await http.get<GetMatchGroupsResponse>(signal)(
            `/match-groups/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            { Authorization: `Bearer ${getApiAuthToken(state)}` }
          );
          matchGroupsResults = response.data.results;

          const generatedTimeline = compact(
            matchGroupsResults.flatMap((group) => {
              if (group?.generatedTimelines) {
                return group.generatedTimelines.map((timeline) => ({
                  ...timeline,
                  matchGroup: {
                    id: group.id,
                    name: group.name,
                    eventId: group.eventId,
                  },
                }));
              } else {
                return null;
              }
            })
          );
          return generatedTimeline;
        },
        {
          pending: (state, action) => {
            if (!action.meta.arg.isPoll) {
              state.matchGroups.status = 'loading';
            }
          },
          fulfilled: (state, action) => {
            state.generatedTimelines = {
              results: action.payload,
              status: 'idle',
            };
          },
          rejected: (state, action) => {
            if (!action.meta.arg.isPoll) {
              createSnackNotification(
                AlertLevel.Error,
                'Get generated timeline failed',
                action.error.message
              );
              state.generatedTimelines.status = 'failure';
            }
          },
        }
      ),
      deleteGeneratedTimeline: createHttpThunk(
        async (
          {
            matchGroupId,
            generatedTimelineId,
          }: {
            matchGroupId: string;
            generatedTimelineId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.delete<DeleteGeneratedTimelineResponse>(
            signal
          )(
            `/match-groups/${matchGroupId}/timeline`,
            { generatedTimelineId },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state, action) => {
            const gTimelineDeletion: DeleteGeneratedTimelineState = {
              id: action.meta.arg.generatedTimelineId,
              status: 'loading',
              error: '',
            };
            state.generatedTimelineDeletions.push(gTimelineDeletion);
          },
          fulfilled: (state, action) => {
            //  Filter out the deleted generated timeline from the list - it will now go to local storage
            state.generatedTimelineDeletions = state.generatedTimelineDeletions.filter(
              (gTimeline) => gTimeline.id !== action.meta.arg.generatedTimelineId
            );

            createSnackNotification(
              AlertLevel.Success,
              'Deleted Timeline Generated Video',
              action.payload.message
            );

            // Store deleted generated timeline ID in localStorage
            const deletedGeneratedTimelineId =
              action.meta.arg.generatedTimelineId;
            let deletedGeneratedTimelineIds: Array<{
              value: string;
              expiry: number;
            }> | null = ls.get('deletedGeneratedTimelineIds');
            if (Array.isArray(deletedGeneratedTimelineIds)) {
              deletedGeneratedTimelineIds =
                deletedGeneratedTimelineIds.filter(isExpiringString);
            } else {
              deletedGeneratedTimelineIds = [];
            }

            // Add the new deleted generated timeline ID to the array with an expiration time of 1 day
            const now = new Date();
            const item = {
              value: deletedGeneratedTimelineId,
              expiry: now.getTime() + 1000 * 60 * 60 * 24,
            };
            deletedGeneratedTimelineIds.push(item);

            // Store the updated array back in localStorage
            ls.set('deletedGeneratedTimelineIds', deletedGeneratedTimelineIds);

            // We don't delete the video from the generatedTimelines state because it is in a pending deletion state,
            // and we want to show the user that the video is being deleted.   It will drop off the list upon
            // re-polling.
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Delete Timeline Generated Video failed',
              action.error.message
            );

            state.generatedTimelineDeletions = state.generatedTimelineDeletions.filter(
                (gTimeline) => gTimeline.id !== action.meta.arg.generatedTimelineId
            );
          },
        }
      ),
      setAttributes: create.reducer((state) => {
        const personJsonAttributes = getAttributesJson('person');
        const vehicleJsonAttributes = getAttributesJson('vehicle');

        const personAttributes = convertJsonAttributes(personJsonAttributes);
        const vehicleAttributes = convertJsonAttributes(vehicleJsonAttributes);

        state.attributes = {
          person: personAttributes,
          vehicle: vehicleAttributes,
        };
      }),
      createMatchGroup: createHttpThunk(
        async (
          {
            name,
            eventId,
          }: {
            name: string;
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<CreateMatchGroupResponse>(signal)(
            '/match-groups',
            {
              name,
              eventId,
            },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.createMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.createMatchGroup = {
              status: 'idle',
              id: action.payload.matchGroup?.id ?? '',
            };
            createSnackNotification(AlertLevel.Success, 'Create Match Group', `Match Group ${action.payload.matchGroup?.name} was created successfully`);
          },
          rejected: (state, action) => {
            createSnackNotification(AlertLevel.Error, 'Create Match Group failed', action.error.message);
            state.createMatchGroup.status = 'failure';
          },
        }
      ),
      updateMatchGroupAttributeSearch: createHttpThunk(
        async (
          {
            matchGroupId,
            trackletId,
            attributeType,
            attributes,
            searchName,
          }: {
            matchGroupId: string;
            trackletId: string;
            attributeType: AttributeType;
            attributes: AttributeRecord[];
            searchName?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const existingSearches = (getState() as RootState).event.matchGroups.results.find((matchGroup) => matchGroup.id === matchGroupId)?.searches ?? [];

          let searches: Search[];

          if (searchName) {
            const newSearch = {
              id: trackletId,
              searchName,
              attributes: {
                [attributeType]: attributes,
              },
              searchTime: new Date().toISOString(),
            };

            if (existingSearches.some((search) => isEqual(search.attributes?.[attributeType], attributes))) {
              createSnackNotification(AlertLevel.Error, 'These attributes already have a search', 'Please select different attributes');
              throw new Error('These attributes already have a search');
            }
            searches = [...existingSearches, newSearch];
          } else {
            searches = existingSearches.map((search) =>
              search.id === trackletId
                ? {
                    ...search,
                    attributes: {
                      [attributeType]: attributes,
                    },
                  }
                : search
            );
          }

          const response = await http.patch<CreateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            {
              searches,
            },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
            state.matchGroups.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup.status = 'idle';
            state.matchGroups.status = 'idle';
            state.matchGroups.results = map(state.matchGroups.results, (matchGroup) =>
              matchGroup.id === action.payload.matchGroup?.id ? merge({}, matchGroup, action.payload.matchGroup) : matchGroup
            );
            createSnackNotification(
              AlertLevel.Success,
              'Update Match Group',
              `Match Group ${action.payload?.matchGroup?.name} was updated successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
            state.matchGroups.status = 'failure';
          },
        }
      ),
      updateFileSelected: create.reducer(
        (state, action: PayloadAction<File | undefined>) => {
          state.fileSelected.file = action.payload;
        }
      ),
    };
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateMatchGroup.pending, (state) => {
        state.matchGroups.status = 'loading';
      })
      .addCase(updateMatchGroup.fulfilled, (state, action) => {
        const matchGroup = action.payload.matchGroup;
        if (matchGroup) {
          state.matchGroups.results = state.matchGroups.results.map((group) =>
            (group.id === matchGroup.id) ? matchGroup : group
          );
          const updatedMatchGroups: UpdatedMatchGroupLocalStorage[] = ls.get('updatedMatchGroups') || [];
          const newUpdatedMatchGroup: UpdatedMatchGroupLocalStorage = {
            value: {
              matchGroupId: matchGroup.id,
              matchGroupName: matchGroup.name
            },
            expiry: new Date().getTime() + 60*60*1000
          };
          updatedMatchGroups.push(newUpdatedMatchGroup);
          ls.set('updatedMatchGroups', updatedMatchGroups);
        }
        state.matchGroups.status = 'idle';
      })
      .addCase(deleteMatchGroup.pending, (state) => {
        state.matchGroups.status = 'loading';
      })
      .addCase(deleteMatchGroup.fulfilled, (state, action) => {
        const matchGroupId = action.payload.matchGroupId;
        if (matchGroupId) {
          const deletedMatchGroups: DeletedMatchGroupLocalStorage[] = ls.get('deletedMatchGroups') || [];
          const newDeletedMatchGroup: DeletedMatchGroupLocalStorage = {
            value: {
              matchGroupId
            },
            expiry: new Date().getTime() + 60*60*1000
          };
          deletedMatchGroups.push(newDeletedMatchGroup);
          ls.set('deletedMatchGroups', deletedMatchGroups);
        }
        state.matchGroups.status = 'idle';
      });
  },
  selectors: {
    selectFiles: (state) => state.files,
    selectMatchGroups: (state) => state.matchGroups,
    selectEvent: (state) => state.event,
    selectEventDeletion: (state) => state.eventDeletion,
    selectMatchGroupSearchDeleted: (state) => state.matchGroupSearchDeletion,
    selectTags: (state) => state.tags,
    selectGeneratedTimelines: (state) => state.generatedTimelines,
    selectAttributes: (state) => state.attributes,
    selectNewMatchGroupId: (state) => state.createMatchGroup.id,
    selectFileSelected: (state) => state.fileSelected,
    selectGeneratedTimelineDeletions: (state) => state.generatedTimelineDeletions,
  },
});

export const {
  getMatchGroups,
  setMatchGroupsPagination,
  getFiles,
  setFilesPagination,
  getEventById,
  updateEventById,
  updateFileById,
  deleteEvent,
  deleteMatchGroupSearch,
  getTags,
  getGeneratedTimeline,
  deleteGeneratedTimeline,
  poll,
  updateMatchGroupSearchName,
  setAttributes,
  createMatchGroup,
  updateMatchGroupAttributeSearch,
  updateFileSelected,
} = eventSlice.actions;

export const {
  selectMatchGroups,
  selectFiles,
  selectEvent,
  selectEventDeletion,
  selectMatchGroupSearchDeleted,
  selectTags,
  selectGeneratedTimelines,
  selectAttributes,
  selectNewMatchGroupId,
  selectFileSelected,
  selectGeneratedTimelineDeletions,
} = eventSlice.selectors;

export const { actions: eventActions, reducer: eventReducer } = eventSlice;
