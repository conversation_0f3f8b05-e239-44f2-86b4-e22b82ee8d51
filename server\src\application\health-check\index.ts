import NodeCache from 'node-cache';
import createHandlers from './handlers';
import actions from './actions';
import GQLApi from '@util/api/graphQL';

const createHealthCheckApp = ({
  log,
  gql,
  cache,
}: {
  log: Logger;
  gql: GQLApi;
  cache: NodeCache;
}) => {
  const handlers = createHandlers({ log, gql, cache });

  return {
    actions,
    handlers,
  };
};

export default createHealthCheckApp;
