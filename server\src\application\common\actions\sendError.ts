import { Context } from '../../types';
import { errorCode } from '@common/errors';

const sendError =
  <ReqPayload, Data>(context: Context<ReqPayload, Data>) =>
  (error: Error) => {
    const code = errorCode(error.constructor.name);
    context?.log?.error('Api request failed', {
      code,
      name: error?.name,
      stack: error?.stack,
      message: error?.message,
      error,
    });
    context.res.status(code).send({
      code,
      message: error?.message,
      name: error?.name,
      stack: error?.stack,
      requestIds: context?.requestIds ?? [],
    });
  };

export default sendError;
