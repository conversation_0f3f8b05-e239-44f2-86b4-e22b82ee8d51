import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import getFileStatus from '../util/getFileStatus';
import { fetchJsonData } from '../getJsonData';
import { isEmpty } from 'lodash';
import { File } from '../../../../../../types/tracker';

const getFile = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.getFile>> | undefined
> => {
  const { data, req, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };

  const { fileId } = req.params;
  if (!fileId) {
    throw new ActionError('No fileId provided');
  }

  try {
    const { temporalDataObject } = await callGQL<
      responses.getFileTemporalData,
      ReqPayload,
      Data
    >(context, headers, queries.getFileTemporalData, { tdoId: fileId });

    if (temporalDataObject) {
      const status = getFileStatus(temporalDataObject);

      const assets = temporalDataObject.assets;
      const mediaDetailsAsset = assets?.records?.find(
        (asset: { assetType: string }) => asset.assetType === 'media-details'
      );

      let file: File | undefined;
      let orgId: string | undefined;
      const getFile = async () => {
        const dataAsset = await fetchJsonData(mediaDetailsAsset?.signedUri);
        const locationResponse = !isEmpty(dataAsset?.summary?.location_exif)
          ? `(${dataAsset.summary.location_exif.GPSLatitude}, ${dataAsset.summary.location_exif.GPSLongitude})`
          : 'unavailable';
        file = {
          id: temporalDataObject.id,
          createdByName: temporalDataObject.details.veritoneFile.createdByName,
          fileName: temporalDataObject.details.veritoneFile.fileName,
          status,
          length: temporalDataObject.details.veritoneFile.duration,
          uploadDate: (
            temporalDataObject.details.createdDateTime ??
            temporalDataObject.createdDateTime
          )?.toString(),
          location: locationResponse,
          fileType:
            temporalDataObject.details.veritoneFile.fileType ??
            temporalDataObject.details.veritoneFile.mimetype,
          fileSize: dataAsset?.summary?.size_bytes,
          eventId: temporalDataObject.folders?.[0]?.id ?? '',
          eventName: temporalDataObject.folders?.[0]?.name ?? '',
          thumbnailUrl: temporalDataObject?.thumbnailUrl,
          primaryAsset: {
            signedUri: temporalDataObject?.primaryAsset?.signedUri ?? '',
          },
          streams: temporalDataObject.streams,
          frameRate:
            temporalDataObject.details.veritoneFile?.videoFrameRate ?? 0,
          thumbnailAssets: temporalDataObject?.thumbnailAssets,
        };
        orgId = temporalDataObject.folders?.[0]?.parent?.organizationId;
      };

      if (redisWrapper) {
        await redisWrapper.file.get(fileId).then(async (result) => {
          if (result === null) {
            await getFile();
            // Do not set until it has ingested
            if (file && file.streams?.length !== 0) {
              await redisWrapper.file.set(fileId, file);
            }
          } else {
            file = result;
          }
        });
      } else {
        await getFile();
      }
      const newData = Object.assign({}, data, {
        file,
        orgId,
      });
      return { ...context, data: newData };
    }
  } catch (e) {
    console.error(e);
    throw new GraphQLError(
      'The file does not exist in current organization or belongs to another organization'
    );
  }
};

export default getFile;
