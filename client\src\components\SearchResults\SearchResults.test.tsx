import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import SearchResults from './SearchResults';
import { Provider } from 'react-redux';
import { configureAppStore } from '@store/store';
import axios from 'axios';
import {
  CreateMatchGroupResponse,
  GetFileResponse,
  GetMatchGroupResponse,
  GetMatchGroupSearchResultsResponse,
  GetMatchGroupsResponse,
} from '@shared-types/responses';
import { ThemeProvider } from '@emotion/react';
import { createMaterialTheme } from '@theme/material';
import React from 'react';
import {
  getAttributeCount,
  TrackletAttributes,
} from '../../../test/testConstants';
import { getAttributeValue } from '@utility/getAttributeValue';
import { Attributes } from '@shared-types/tracker';
import { setThumbnails } from '@store/modules/searchResults/slice';
import { I18nProvider, LOCALES } from '@i18n';

jest.mock('axios');

jest.mock('@components/common', () => ({
  ...jest.requireActual('@components/common'),
  createSnackNotification: jest.fn(),
}));

jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  Slider: (props: {
    id: string;
    name: string;
    min: number;
    max: number;
    step: number;
    'data-testid': string;
    value: string;
    onChange: (ev: unknown, val: number) => void;
    onChangeCommitted: (ev: any) => void;
  }) => {
    const { id, name, min, max, onChange } = props;
    return (
      <input
        data-testid={props?.['data-testid']}
        type="range"
        id={id}
        value={props.value}
        name={name}
        min={min}
        max={max}
        onChange={(event) => onChange(event, Number(event.target.value))}
        onMouseUp={(event) => props.onChangeCommitted(event)}
      />
    );
  },
}));
const mockEventId = '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5';
const mockMatchGroupId = 'a85f9b41-f550-4754-8871-518266dbae49';
const mockSearchId = '16b5f647-7358-4812-87f4-93e0468f19a6';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({
    eventId: mockEventId,
    matchGroupId: mockMatchGroupId,
    searchId: mockSearchId,
  }),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockNavigate = jest.fn();

const mockGetMatchGroupSearchResultsResponse: GetMatchGroupSearchResultsResponse =
  {
    results: [
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'file-1.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
      {
        orgId: '1',
        trackletId: '22',
        fileId: '2',
        fileName: 'file-2.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.8,
      },
      {
        orgId: '1',
        trackletId: '33',
        fileId: '3',
        fileName: 'fileName 3',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.6,
      },
    ],
    type: 'person',
    referenceTrackletId: '99999',
    searchId: '16b5f647-7358-4812-87f4-93e0468f19a6',
    searchName: 'Potential Search 1',
    matchGroupId: 'a85f9b41-f550-4754-8871-518266dbae49',
    matchGroupName: 'John Doe',
    eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
    currentPage: 1,
    pageSize: 50,
    totalCount: 100,
    totalPages: 2,
  };

const mockGetMatchGroupsResponse: GetMatchGroupResponse = {
  matchGroup: {
    id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
    name: 'test match group',
    eventId: '1b9c7f1a-f759-4065-94c3-4dd48868fb06',
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Potential Match Search 1',
        referenceTrackletId: 'reference-tracketlet-id-mock-1',
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Potential Match Search 2',
        referenceTrackletId: 'reference-tracketlet-id-mock-2',
      },
    ],
    modifiedDateTime: '2024-05-16T22:50:21.111Z',
    selectedTracklets: [],
  },
};

const mockSelectedSearchAttributes: Attributes = {
  person: [
    {
      key: 'Accessory',
      label: 'AccessoryBackpack',
      value: 'Backpack',
    },
    {
      key: 'Accessory',
      label: 'AccessoryBagAny',
      value: 'BagAny',
    },
    {
      key: 'Accessory',
      label: 'AccessoryGlasses',
      value: 'Glasses',
    },
    {
      key: 'FootwearType',
      label: 'FootwearTypeBoots',
      value: 'Boots',
    },
    {
      key: 'LowerColor',
      label: 'LowerColorBlack',
      value: 'Black',
    },
  ],
};

const mockGetMatchGroupsAttributeSearchResponse: GetMatchGroupResponse = {
  matchGroup: {
    id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
    name: 'test match group',
    eventId: '1b9c7f1a-f759-4065-94c3-4dd48868fb06',
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Attribute Search 1',
        attributes: mockSelectedSearchAttributes,
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Attribute Search 2',
        attributes: mockSelectedSearchAttributes,
      },
    ],
    modifiedDateTime: '2024-05-16T22:50:21.111Z',
    selectedTracklets: [],
  },
};

const _mockGetMatchGroupsResponseWithEventId: GetMatchGroupsResponse = {
  eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
  results: [
    {
      id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
      name: 'test match group',
      eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
      searches: [
        {
          id: '16b5f647-7358-4812-87f4-93e0468f19a6',
          searchName: 'Potential Match Search 1',
          referenceTrackletId: 'reference-tracketlet-id-mock-1',
        },
        {
          id: '12345678-5150-4d56-90aa-6d15d3a82761',
          searchName: 'Potential Match Search 2',
          referenceTrackletId: 'reference-tracketlet-id-mock-2',
        },
      ],
      modifiedDateTime: '2024-05-16T22:50:21.111Z',
      selectedTracklets: [],
    },
  ],
  currentPage: 1,
  pageSize: 50,
  totalCount: 100,
  totalPages: 2,
  sortType: 'name',
  sortDirection: 'asc',
};

const _mockPatchGetMatchGroupsResponse: CreateMatchGroupResponse = {
  matchGroup: {
    eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
    id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
    modifiedDateTime: '2024-08-20T22:50:21.111Z',
    name: 'test match group',
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Potential Match Search 1',
        referenceTrackletId: 'reference-tracketlet-id-mock-1',
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Potential Match Search 2',
        referenceTrackletId: 'reference-tracketlet-id-mock-2',
      },
      {
        referenceTrackletId: '11',
        id: 'f2cf67a9-36d9-4122-bc17-d48c5c12799b',
        searchName: 'Potential Match Search 3',
      },
    ],
    selectedTracklets: [],
  },
};

const mockGetFileResponse: GetFileResponse = {
  file: {
    id: '1',
    createdByName: 'Test User',
    fileName: 'file-1.mp4',
    status: 'processed',
    length: 1000,
    uploadDate: '2021-01-01T00:00:00.000Z',
    location: '',
    fileType: 'mp4',
    fileSize: 2048,
    eventId: 'eventId',
    eventName: 'eventName',
    thumbnailUrl: '',
    primaryAsset: {
      signedUri: 'signedUri',
    },
    streams: [
      {
        uri: 'uri',
        protocol: 'protocol',
      },
    ],
    frameRate: 25,
  },
};

const mockGetFileResponse2: GetFileResponse = {
  file: {
    id: '2',
    createdByName: 'Test User',
    fileName: 'file-2.mp4',
    status: 'processed',
    length: 1000,
    uploadDate: '2021-01-01T00:00:00.000Z',
    location: '',
    fileType: 'mp4',
    fileSize: 2048,
    eventId: 'eventId',
    eventName: 'eventName',
    thumbnailUrl: '',
    primaryAsset: {
      signedUri: 'signedUri',
    },
    streams: [
      {
        uri: 'uri',
        protocol: 'protocol',
      },
    ],
    frameRate: 25,
  },
};

async function selectCheckboxById(dataTestId: string) {
  await waitFor(() => {
    expect(screen.getByTestId(dataTestId)).toBeInTheDocument();
  });

  const checkbox = within(screen.getByTestId(dataTestId)).getByRole('checkbox');
  await act(() => {
    checkbox.click();
  });
}

describe('Search Results page', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Search results page loads tracklets', async () => {
    const mockedStore = configureAppStore();

    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Tracklets load
    await waitFor(
      () => {
        expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
  });

  test('SearchResult render best thumbnail', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
    expect(screen.getByTestId('Tracklet-0-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-1-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-2-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
  });

  test('Potential match dropdown changes the route', async () => {
    const mockedStore = configureAppStore();

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <SearchResults
              eventId={mockEventId}
              matchGroupId={mockMatchGroupId}
              searchId={mockSearchId}
            />
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Find Potential Match Search drop
    const potentialMatchSearchDrop = screen.getByTestId(
      'potential-match-search-drop'
    );

    // Click Potential Match Search drop
    await waitFor(() => {
      expect(potentialMatchSearchDrop).toBeInTheDocument();
    });
    const element = within(potentialMatchSearchDrop).getByRole('combobox');
    fireEvent.mouseDown(element);

    // Wait for drop items to exist
    await waitFor(() => {
      expect(
        screen.getByTestId(
          'potential-match-search-drop-12345678-5150-4d56-90aa-6d15d3a82761'
        )
      ).toBeInTheDocument();
    });

    // Find second potential match search dropdown item
    const potentialMatchSearchDropOption2 = screen.getByTestId(
      'potential-match-search-drop-12345678-5150-4d56-90aa-6d15d3a82761'
    );

    // Click second potential match option
    await act(() => {
      potentialMatchSearchDropOption2.click();
    });

    // URL changes to new search
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(
        '/event/83b9fc86-df85-46b6-99fe-f4d04ef2bdd5/match-group/a85f9b41-f550-4754-8871-518266dbae49/potential-match-search/12345678-5150-4d56-90aa-6d15d3a82761'
      );
    });
  });

  test('Selecting a tracklet shows attributes', async () => {
    const mockedStore = configureAppStore();

    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <SearchResults
              eventId={mockEventId}
              matchGroupId={mockMatchGroupId}
              searchId={mockSearchId}
            />
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // Click a tracklet
    await act(() => {
      screen.getByTestId('Tracklet-0').click();
    });

    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );
    });

    Object.keys(TrackletAttributes).map((attribute, _i) => {
      const attributeKeyCount = screen.getByTestId(
        `detected-attributes__chips-${attribute}`
      ).childNodes.length;
      expect(
        TrackletAttributes[attribute as keyof typeof TrackletAttributes].length
      ).toEqual(attributeKeyCount);
      TrackletAttributes?.[attribute as keyof typeof TrackletAttributes]?.map(
        (attributeRecord, _j) => {
          const chipValue = getAttributeValue(attributeRecord.value);
          expect(
            screen.getByTestId(
              `detected-attributes__chip-label-${attribute}-${attributeRecord.value}`
            )
          ).toHaveTextContent(chipValue);
        }
      );
    });
  });

  test('Selecting tracklets and loads the video srcs', async () => {
    const mockedStore = configureAppStore();

    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file/1') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
      if (url?.includes('/file/2') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse2 });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <SearchResults
              eventId={mockEventId}
              matchGroupId={mockMatchGroupId}
              searchId={mockSearchId}
            />
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // click first tracklet
    await act(() => {
      screen.getByTestId('Tracklet-0').click();
    });

    // wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );

      // check if selected tracklet's video src is loaded
      expect(
        mockedStore.getState().searchResults.selectedTrackletFile.file.fileName
      ).toEqual(
        mockedStore.getState().searchResults.searchResults.results[0].fileName
      );
      expect(
        mockedStore.getState().searchResults.selectedTrackletFile.file.fileName
      ).toEqual('file-1.mp4');
    });

    // click second tracklet
    await act(() => {
      screen.getByTestId('Tracklet-1').click();
    });

    // wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );

      // check if selected tracklet's video src is loaded
      expect(
        mockedStore.getState().searchResults.selectedTrackletFile.file.fileName
      ).toEqual(
        mockedStore.getState().searchResults.searchResults.results[1].fileName
      );
      expect(
        mockedStore.getState().searchResults.selectedTrackletFile.file.fileName
      ).toEqual('file-2.mp4');
    });
  });

  test('Search results page loads tracklets', async () => {
    const mockedStore = configureAppStore();

    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });

    const thresholdVal = 0.5;

    // Change Slider Value
    const slider = screen.getByTestId('search-results-confidence-slider');
    fireEvent.change(slider, {
      target: { value: thresholdVal },
    });
    fireEvent.mouseUp(slider);

    // Check if last call from axios contains new threshold value
    await waitFor(
      () => {
        const calls = mockedAxios.request.mock.calls;

        expect(JSON.stringify(calls)).toContain(`threshold=${thresholdVal}`);
      },
      { timeout: 3000 }
    );
  });

  test('Loads thumbnails if missing from local storage', async () => {
    const mockedStore = configureAppStore();

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/thumbnails') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve();
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Check if last call from axios contains thumbnails
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      const lastUrl = lastCall[0].url;
      expect(lastUrl).toContain('thumbnails');
    });
  });

  test('Does not load thumbnails if not expired', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/thumbnails') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve();
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Check if last call from axios contains thumbnails
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      const lastUrl = lastCall[0].url;
      expect(lastUrl).not.toContain('thumbnails');
    });
  });

  test('Loads thumbnails if expired', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() - 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() - 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/thumbnails') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve();
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Check if last call from axios contains thumbnails
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      const lastUrl = lastCall[0].url;
      expect(lastUrl).toContain('thumbnails');
    });
  });

  test('Should show confidence score when the check box is checked', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 1000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // Click Confidence Level checkbox
    await selectCheckboxById('search-results-confidence-checkbox');

    // Show confidence score for each Tracklet
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0-confidence')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1-confidence')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2-confidence')).toBeInTheDocument();
    });

    // Click Confidence Level checkbox again
    await selectCheckboxById('search-results-confidence-checkbox');

    // Hide confidence score for each Tracklet
    await waitFor(() => {
      expect(
        screen.queryByTestId('Tracklet-0-confidence')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('Tracklet-1-confidence')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('Tracklet-2-confidence')
      ).not.toBeInTheDocument();
    });
  });

  test('Show no search message if no searchId is provided', async () => {
    const mockedStore = configureAppStore();

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
          />
        </I18nProvider>
      </Provider>
    );

    // page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // check if no search message is displayed with match group name
    await waitFor(() => {
      const noSearchMessage = screen.getByTestId(
        'search-results__no-search-found'
      );
      expect(noSearchMessage).toBeInTheDocument();
      expect(noSearchMessage).toHaveTextContent(
        'There are no searches yet for "test match group". To add a search, open a file, select a Detection, and click Find Matches.'
      );
    });
  });

  test('Should render View or modify search parameters button and open search parameters dialog', async () => {
    const mockedStore = configureAppStore();

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({
          data: mockGetMatchGroupsAttributeSearchResponse,
        });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchResults
            eventId={mockEventId}
            matchGroupId={mockMatchGroupId}
            searchId={mockSearchId}
          />
        </I18nProvider>
      </Provider>
    );

    // page renders
    await waitFor(() => {
      expect(screen.getByTestId('search-results')).toBeInTheDocument();
    });

    // check if button is displayed
    await waitFor(() => {
      const button = screen.getByTestId(
        'search-results__view-modify-attribute-button'
      );
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('View or modify search parameters');
    });

    // click button
    fireEvent.click(
      screen.getByTestId('search-results__view-modify-attribute-button')
    );

    // check if dialog is displayed with the correct attributes count selected
    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog')).toBeInTheDocument();
      expect(
        screen.getByTestId(
          'search-all-files-dialog__content-attribute-selected-count'
        )
      ).toBeInTheDocument();
      expect(
        screen.getByTestId(
          'search-all-files-dialog__content-attribute-selected-count'
        )
      ).toHaveTextContent('Choose Attributes: 5 Selected');
    });
  });
});
