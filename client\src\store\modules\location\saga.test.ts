import { select, take, takeLatest } from 'redux-saga/effects';
import {
  onRouteChanged,
  selectCurrentLocation,
  selectHistory,
} from "@store/modules/location/slice";
import routes, { watchRouteSagasHandle, watchRoutesSagas } from '@store/modules/location/saga';
import { bootDidFinish, bootFinished } from '@store/modules/app/slice';
import { modules } from '@veritone/glc-redux';

const {
    user: { userIsAuthenticated },
  } = modules;


describe('watchRouteSagasHandle', () => {
  it('should handle booting and user authentication when currentLocation equals history', () => {
    const generator = watchRouteSagasHandle();

    expect(generator.next().value).toEqual(select(bootDidFinish));
    expect(generator.next(true).value).toEqual(select(userIsAuthenticated));
    expect(generator.next(true).value).toEqual(select(selectCurrentLocation));
    const currentLocation = { pathname: '/counter', payload: { data: 'Counter' } };
    expect(generator.next(currentLocation).value).toEqual(select(selectHistory));
    const history = { pathname: '/counter', payload: { data: 'Counter' } };
    expect(generator.next(history).done).toBeTruthy();
  });

  it('should handle booting and user authentication when history pathName is undefined', () => {
    const generator = watchRouteSagasHandle();

    expect(generator.next().value).toEqual(select(bootDidFinish));
    expect(generator.next(true).value).toEqual(select(userIsAuthenticated));
    expect(generator.next(true).value).toEqual(select(selectCurrentLocation));
    const currentLocation = { pathname: '/test', payload: { data: 'test' } };
    expect(generator.next(currentLocation).value).toEqual(select(selectHistory));
    const history = undefined;
    expect(generator.next(history).done).toBeTruthy();
  });

  it('should handle user not authenticated', () => {
    const generator = watchRouteSagasHandle();

    expect(generator.next().value).toEqual(select(bootDidFinish));
    expect(generator.next(false).value).toEqual(take(bootFinished));
    expect(generator.next().value).toEqual(select(userIsAuthenticated));
    expect(generator.next(false).done).toBeTruthy();
  });

  it('should handle watchRoutesSagas', () => {
    const generator = watchRoutesSagas();
    expect(generator.next().value).toEqual(takeLatest(onRouteChanged, watchRouteSagasHandle));
  });

  it('should handle routes', () => {
    const generator = routes();
    const result = generator.next();
    expect(result.done).toBeFalsy();
  });
});
