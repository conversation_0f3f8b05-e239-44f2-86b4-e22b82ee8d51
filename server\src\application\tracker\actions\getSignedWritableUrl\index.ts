import { v4 as uuidv4 } from 'uuid';
import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import * as ResTypes from '../../../../../../types/responses';

const getSignedWritableUrl = async <
  ReqPayload,
  Data extends Partial<ResTypes.UploadFilePayloadResponse> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<ReqPayload, Data & Partial<responses.getSignedWritableUrl>>
  | undefined
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { fileName } = data;
  if (!fileName) {
    throw new ActionError('No fileName provided');
  }

  try {
    const { getSignedWritableUrl } = await callGQL<
      responses.getSignedWritableUrl,
      ReqPayload,
      Data
    >(context, headers, queries.getSignedWritableUrl, {
      key: `${fileName}-${uuidv4()}`,
      expiresInSeconds: 43200,
    });

    if (getSignedWritableUrl) {
      const new_data = Object.assign({}, data, { getSignedWritableUrl });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default getSignedWritableUrl;
