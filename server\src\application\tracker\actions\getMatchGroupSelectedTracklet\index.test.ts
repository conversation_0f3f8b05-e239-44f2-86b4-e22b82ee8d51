import NodeCache from 'node-cache';
import { Context } from '../../../types';
import { createRequest, createResponse } from 'node-mocks-http';
import consoleLogger from '../../../../logger';
import { fingerprintSearch } from '../searchTracklets/elasticSearch';
import getSelectedTracklets from '.';
import NotFoundError from '@common/errors/NotFoundError';

jest.mock('@application/credential/actions/util/azure', () => ({
  generateBlobSAS: jest.fn().mockImplementation(() => 'sasToken-mock'),
}));

jest.mock('../searchTracklets/elasticSearch', () => ({
  fingerprintSearch: jest.fn().mockImplementation(() => ({
    searchMedia: {
      jsondata: {
        results: [
          {
            organizationId: '1',
            referenceId: 'reference-tracklet-id-mock-1',
            id: '3100000030',
            score: 0.95,
            label: 'person',
            startTimeMs: 200,
            stopTimeMs: 640,
            tags: [
              {
                key: 'BodyAverage',
              },
              {
                key: 'FaceFront',
              },
              {
                key: 'FaceLeft',
              },
            ],
          },
        ],
        totalResults: 3,
        limit: 10,
        from: 0,
        to: 3,
      },
    },
  })),
}));

// jest.mock('../getTdos', () => ({
//   getTdos: jest.fn().mockImplementation(() => ({
//     '3100000030': {
//       id: '3100000030',
//       name: 'mock-file-name',
//     },
//   })),
// }));
describe('Get Selected Tracklets', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Successfully get the selected tracklets', async () => {
    const cxt = {
      data: {
        matchGroup: {
          name: 'test match group',
          eventId: 'event123',
          modifiedDateTime: '2024-05-03T21:57:48.320Z',
          selectedTracklets: ['tracklet123'],
          id: 'matchGroup123',
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    } as Context<object, object>;
    process.env.ENV_AZURE_BLOB_ACCOUNT_KEY = 'test blob key';
    const response = await getSelectedTracklets(cxt);
    expect(fingerprintSearch).toHaveBeenCalledTimes(1);
    expect(fingerprintSearch).toHaveBeenCalledWith(
      expect.objectContaining({
        referenceTrackletIds: ['tracklet123'],
      })
    );
  });

  it('return no results when no selected tracklets in match group', async () => {
    const cxt = {
      data: {
        matchGroup: {
          name: 'test match group',
          eventId: 'event123',
          modifiedDateTime: '2024-05-03T21:57:48.320Z',
          id: 'matchGroup123',
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    } as Context<object, object>;

    const response = await getSelectedTracklets(cxt);
    expect(fingerprintSearch).toHaveBeenCalledTimes(0);
    expect(response.data.selectedTracklets).toEqual({
      matchGroupId: 'matchGroup123',
      matchGroupName: 'test match group',
      eventId: 'event123',
    });
  });

  it('Throw NotFoundError when no match group found', async () => {
    const cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    } as Context<object, object>;
    async function getSelectedTrackletsNoMatchGroup() {
      await getSelectedTracklets(cxt);
    }
    expect(getSelectedTrackletsNoMatchGroup).rejects.toThrow(NotFoundError);
    expect(fingerprintSearch).toHaveBeenCalledTimes(0);
  });
});
