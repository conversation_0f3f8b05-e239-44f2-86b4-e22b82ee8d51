{"git": {"commitMessage": "Release ${version}", "tagName": "${version}", "requireCleanWorkingDir": true}, "github": {"tokenRef": "GITHUB_ACCESS_TOKEN"}, "npm": {"publish": false}, "hooks": {"before:init": [], "after:bump": "echo 'version=${version}' >> $GITHUB_ENV"}, "plugins": {"@release-it/bumper": {"in": "client/package.json", "out": "client/package.json"}, "@release-it/conventional-changelog": {"preset": "eslint"}, "@release-it/github": {"release": true, "draft": false, "prerelease": false, "assets": null}}}