import { AttributeRecord, Attributes } from '@shared-types/tracker';

export interface NestedAttributes {
  [key: string]: AttributeRecord[];
}

export interface AttributesWithCategories {
  [key: string]: AttributeRecord[] | NestedAttributes;
}

const CamelCaseRegex = /^[A-Z][a-z]+(?:[A-Z][a-z]+)*$/;
const FindAllKeysRegex = /[A-Z][a-z]+/g;

export const transformAttributesIntoCategories = (attributes: Attributes): AttributesWithCategories => {
  const result: AttributesWithCategories = {};
  Object.keys(attributes).forEach((key) => {
    if (CamelCaseRegex.test(key)) {
      const attributeKeys = key.match(FindAllKeysRegex) || [];
      let current = result;

      attributeKeys.forEach((attributeKey, index) => {
        if (index === attributeKeys.length - 1) {
          current[attributeKey] = attributes[key];
        } else {
          current[attributeKey] = current[attributeKey] || {};
          current = current[attributeKey] as NestedAttributes;
        }
      });
    } else {
      result[key] = attributes[key];
    }
  });

  return result;
};
