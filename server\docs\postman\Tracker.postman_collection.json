{"info": {"_postman_id": "e505cbab-93b5-49da-b48a-855465ed13a2", "name": "Tracker", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "7764720"}, "item": [{"name": "Events", "item": [{"name": "Create Event", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Event Test 1\",\n    \"description\": \"This is a description 1\",\n    \"eventStartDate\": \"2024-03-27T17:02:10Z\",\n    \"eventEndDate\": \"2024-03-27T17:02:10Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/event", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "event"]}}, "response": []}, {"name": "Delete Event", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer 852ff0fb-2dcb-4e0a-9152-dac3dac1af13", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/event/802ff21a-48c3-47be-9e77-898ef07b709e", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "event", "802ff21a-48c3-47be-9e77-898ef07b709e"]}}, "response": []}, {"name": "Fetch Event", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer acddb828-e4f9-4bea-afc4-12fe0c79c3fa", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/event/0eed0212-613e-4947-87e0-2712a1bbc4a1", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "event", "0eed0212-613e-4947-87e0-2712a1bbc4a1"]}}, "response": []}, {"name": "Update Event", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer 852ff0fb-2dcb-4e0a-9152-dac3dac1af13", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New event name\",\n    \"description\": \"New description\",\n    \"tags\": [\"Tag 1\", \"Tag 2\"],\n    \"eventStartDate\": \"2024-04-10T17:02:10Z\",\n    \"eventEndDate\": \"2024-04-10T17:02:10Z\",\n    \"matchGroups\": {}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/event/0eed0212-613e-4947-87e0-2712a1bbc4a1", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "event", "0eed0212-613e-4947-87e0-2712a1bbc4a1"]}}, "response": []}, {"name": "https://local.veritone.com:3002/app/node_modules/apollo-server-core/dist/requestPipeline.js:5:58)\\\\\\", "request": {"method": "GET", "header": [], "url": {"raw": "https://local.veritone.com:3002/app/node_modules/apollo-server-core/dist/requestPipeline.js:5:58)///", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["app", "node_modules", "apollo-server-core", "dist", "requestPipeline.js:5:58)", "", "", ""]}}, "response": []}]}, {"name": "Search", "item": [{"name": "Sorting", "item": [{"name": "Events", "item": [{"name": "Events sort asc by name", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/events?pageSize=50&currentPage=1&sortBy=name&sortDirection=asc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "events"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "name"}, {"key": "sortDirection", "value": "asc"}]}}, "response": []}, {"name": "Events sort asc by startDate", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/events?pageSize=50&currentPage=1&sortBy=eventStartDate&sortDirection=asc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "events"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "eventStartDate"}, {"key": "sortDirection", "value": "asc"}]}}, "response": []}, {"name": "Events sort desc by endDate", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/events?pageSize=50&currentPage=1&sortBy=eventEndDate&sortDirection=desc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "events"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "eventEndDate"}, {"key": "sortDirection", "value": "desc"}]}}, "response": []}]}, {"name": "Files", "item": [{"name": "Files sort asc by fileName", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?pageSize=50&currentPage=1&sortBy=veritone-file.filename&sortDirection=asc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "veritone-file.filename"}, {"key": "sortDirection", "value": "asc"}]}}, "response": []}, {"name": "Files sort asc by uploadDate", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?pageSize=50&currentPage=1&sortBy=createdTime&sortDirection=asc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "createdTime"}, {"key": "sortDirection", "value": "asc"}]}}, "response": []}, {"name": "Files by eventName sort desc by fileName", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 08250195-a8e9-448a-8434-18aada839ce8", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?eventId=863c1735-5abe-45c0-b57b-717f07203f37&pageSize=50&currentPage=1&sortBy=veritone-file.filename&sortDirection=desc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "eventId", "value": "863c1735-5abe-45c0-b57b-717f07203f37"}, {"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "sortBy", "value": "veritone-file.filename"}, {"key": "sortDirection", "value": "desc"}]}}, "response": []}]}]}, {"name": "Events", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer b18bb4aa-6019-4410-97d7-ed3dfe8bfeda", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/events?pageSize=100&currentPage=1&query=Tag", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "events"], "query": [{"key": "pageSize", "value": "100"}, {"key": "currentPage", "value": "1"}, {"key": "query", "value": "Tag"}]}}, "response": []}, {"name": "Files by eventId and fileName", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?eventId=863c1735-5abe-45c0-b57b-717f07203f37&pageSize=50&currentPage=1&query=Test_File", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "eventId", "value": "863c1735-5abe-45c0-b57b-717f07203f37"}, {"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "query", "value": "Test_File"}]}}, "response": []}, {"name": "Files by eventId", "request": {"method": "GET", "header": [], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?eventId=863c1735-5abe-45c0-b57b-717f07203f37&pageSize=50&currentPage=1", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "eventId", "value": "863c1735-5abe-45c0-b57b-717f07203f37"}, {"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}]}}, "response": []}, {"name": "Files by fileName", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?pageSize=50&currentPage=1&query=Test_File", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}, {"key": "query", "value": "Test_File"}]}}, "response": []}, {"name": "Files", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/files?pageSize=50&currentPage=1", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "files"], "query": [{"key": "pageSize", "value": "50"}, {"key": "currentPage", "value": "1"}]}}, "response": []}, {"name": "Match Groups", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 60dd3437-a79c-4160-b128-756cecf8e08a", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups?pageSize=30&currentPage=1&eventId=0eed0212-613e-4947-87e0-2712a1bbc4a1&sortType=modifiedDateTime&sortDirection=desc", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups"], "query": [{"key": "pageSize", "value": "30"}, {"key": "currentPage", "value": "1"}, {"key": "eventId", "value": "0eed0212-613e-4947-87e0-2712a1bbc4a1"}, {"key": "sortType", "value": "modifiedDateTime"}, {"key": "sortDirection", "value": "desc"}]}}, "response": []}, {"name": "Match Group Search", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 60dd3437-a79c-4160-b128-756cecf8e08a", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/44573a2a-fc44-45bb-ba03-4d6f25762f1e/search/1b9c7f1a-f759-4065-94c3-4dd48868fb06", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "44573a2a-fc44-45bb-ba03-4d6f25762f1e", "search", "1b9c7f1a-f759-4065-94c3-4dd48868fb06"]}}, "response": []}]}, {"name": "Files", "item": [{"name": "Generate File Upload Link", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}, {"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"eventId\": \"863c1735-5abe-45c0-b57b-717f07203f37\",\n    \"fileName\": \"Test_File.mp4\",\n    \"fileType\": \"video/mp4\",\n    \"fileSize\": 150\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/file", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "file"]}}, "response": []}, {"name": "Delete File", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/file/3050000172", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "file", "3050000172"]}}, "response": []}, {"name": "Fetch File", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/file/3050000172", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "file", "3050000172"]}}, "response": []}, {"name": "Upload File", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "video/mp4", "type": "text"}, {"key": "Authorization", "value": "Bearer 96b2f00d-da88-47f3-8cca-289eef4caa8d", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "postman-cloud:///1eeec5f1-2e50-4030-b659-ac67279da869"}]}, "url": {"raw": "https://api.stage.us-gov-2.veritone.com/v3/storage/ece23e54-58a3-43e9-bded-7a544f7a5000", "protocol": "https", "host": ["api", "stage", "us-gov-2", "veritone", "com"], "path": ["v3", "storage", "ece23e54-58a3-43e9-bded-7a544f7a5000"]}}, "response": []}]}, {"name": "Match Groups", "item": [{"name": "Delete Match Group Search", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer 60dd3437-a79c-4160-b128-756cecf8e08a", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/b24f0141-42b6-41d7-b80a-0590132868bb/search/12340141-42b6-41d7-b80a-0590132868aa", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "b24f0141-42b6-41d7-b80a-0590132868bb", "search", "12340141-42b6-41d7-b80a-0590132868aa"]}}, "response": []}, {"name": "Get Match Group", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer 60dd3437-a79c-4160-b128-756cecf8e08a", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/7eca2bb0-bad4-49a2-96a0-4d501fc827dd", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "7eca2bb0-bad4-49a2-96a0-4d501fc827dd"]}}, "response": []}, {"name": "Get Match Group Selected Tracklets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer 60dd3437-a79c-4160-b128-756cecf8e08a", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/44573a2a-fc44-45bb-ba03-4d6f25762f1e/selected-tracklets", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "44573a2a-fc44-45bb-ba03-4d6f25762f1e", "selected-tracklets"]}}, "response": []}, {"name": "Create Match Group", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer 3d1fa752-14df-4496-b873-1cae25f60f3f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"New match group 15\",\n\t\"eventId\": \"47c1eae3-0fff-4ca0-ad44-fcbe3b5d964a\",\n\t\"timelineProject\": {\n\t\t\"groups\": [\n\t\t\t{\n\t\t\t\t\"name\": \"group1\",\n\t\t\t\t\"id\": \"group1\",\n\t\t\t\t\"tracklets\": [\n\t\t\t\t\t{\n\t\t\t\t\t\t\"trackletId\": \"tracklet1\",\n\t\t\t\t\t\t\"tdoId\": \"tdo1\",\n\t\t\t\t\t\t\"startTimeMs\": 1000,\n\t\t\t\t\t\t\"stopTimeMs\": 2000\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t],\n\t\t\"modifiedDateTime\": \"2024-05-03T21:57:48.320Z\",\n\t\t\"modifiedUserId\": \"ece0ab78-89bb-43a5-bb2e-05f712415766\"\n\t},\n\t\"generatedTimelines\": [\n\t\t{\n\t\t\t\"id\": \"timeline1\",\n\t\t\t\"name\": \"Timeline Project 1\",\n\t\t\t\"createdDateTime\": \"2022-01-01T00:00:00Z\",\n\t\t\t\"createdUserId\": \"ece0ab78-89bb-43a5-bb2e-05f712415766\",\n\t\t\t\"tdoId\": \"tdo1\",\n\t\t\t\"resolution\": \"1920x1080\",\n\t\t\t\"outputFormat\": \"mp4\",\n\t\t\t\"videoLengthMs\": 60000,\n\t\t\t\"videoSizeBytes\": 5000000,\n\t\t\t\"timeline\": {\n\t\t\t\t\"groups\": [\n\t\t\t\t\t{\n\t\t\t\t\t\t\"name\": \"group1\",\n\t\t\t\t\t\t\"id\": \"group1\",\n\t\t\t\t\t\t\"tracklets\": [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\"trackletId\": \"tracklet1\",\n\t\t\t\t\t\t\t\t\"tdoId\": \"tdo1\",\n\t\t\t\t\t\t\t\t\"startTimeMs\": 1000,\n\t\t\t\t\t\t\t\t\"stopTimeMs\": 2000\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\t\"modifiedDateTime\": \"2024-05-03T21:57:48.320Z\",\n\t\t\t\t\"modifiedUserId\": \"ece0ab78-89bb-43a5-bb2e-05f712415766\"\n\t\t\t}\n\t\t}\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups"]}}, "response": []}, {"name": "Update Match Group - referenceTrackletId", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer 3d1fa752-14df-4496-b873-1cae25f60f3f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"searches\": [\n\t\t{\n\t\t\t\"id\": \"de1b86cb-5f91-46ff-abf7-48ff3c817685\",\n\t\t\t\"referenceTrackletId\": \"123\",\n\t\t\t\"searchName\": \"123\"\n\t\t}\n\t],\n\t\"selectedTracklets\": [\n\t\t\"7123\"\n\t],\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/5dabdef9-97ac-486b-82f9-178d7acc64af", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "5dabdef9-97ac-486b-82f9-178d7acc64af"]}}, "response": []}, {"name": "Update Match Group - attributes", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"searches\": [\n        {\n            \"id\": \"e0511ceb-f19a-4cc9-8f6d-e0f23d652e75\",\n            \"attributes\": {\n                \"person\": [\n                    {\n                        \"key\": \"LowerType\",\n                        \"label\": \"LowerTypeShorts\",\n                        \"value\": \"Shorts\"\n                    },\n                    {\n                        \"key\": \"LowerType\",\n                        \"label\": \"LowerTypeSkirt\",\n                        \"value\": \"Skirt\"\n                    }\n                ]\n            },\n            \"searchName\": \"Attribute Search 1\",\n            \"searchTime\": \"2024-11-01T00:56:52.525Z\"\n        },\n        {\n            \"id\": \"a7cd7d1f-a7cb-451a-900c-5ec7f7ac916b\",\n            \"searchName\": \"Attribute Search 2\",\n            \"attributes\": {\n                \"person\": [\n                    {\n                        \"label\": \"AccessoryBackpack\",\n                        \"value\": \"Backpack\",\n                        \"key\": \"Accessory\"\n                    }\n                ]\n            },\n            \"searchTime\": \"2024-11-01T01:16:57.778Z\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://local.veritone.com:3002/api/v1/match-groups/7eca2bb0-bad4-49a2-96a0-4d501fc827dd", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "match-groups", "7eca2bb0-bad4-49a2-96a0-4d501fc827dd"]}}, "response": []}]}, {"name": "Tags", "item": [{"name": "Get Tags", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer 27d0e87c-568d-4987-89ae-7f382314e5fb", "type": "text"}], "url": {"raw": "https://local.veritone.com:3002/api/v1/tags", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "tags"]}}, "response": []}]}, {"name": "Bounding Boxes", "item": [{"name": "Get Bounding Boxes", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "5bf57eff-9656-45df-8499-b52a97e65d2a", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://local.veritone.com:3002/api/v1/bounding-boxes?file=3150000006&startTimeMs=1734&stopTimeMs=1859&type=car", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "bounding-boxes"], "query": [{"key": "file", "value": "3150000006"}, {"key": "startTimeMs", "value": "1734"}, {"key": "stopTimeMs", "value": "1859"}, {"key": "type", "value": "car"}]}}, "response": []}]}, {"name": "Check Create Root Folder", "item": [{"name": "rootfolder", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "5bf57eff-9656-45df-8499-b52a97e65d2a", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "https://local.veritone.com:3002/api/v1/rootfolder", "protocol": "https", "host": ["local", "veritone", "com"], "port": "3002", "path": ["api", "v1", "rootfolder"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}