import env from '../../../env';
import supertest from 'supertest';
import createConfig from '../../../config';
import { createExpressApp } from '../..';

jest.mock('@server/src/application/credential/actions/util/aws.ts', () => ({
  generateS3ReadOnlyCredential: jest.fn().mockImplementation(() => ({
    storage: 's3',
    bucket: 'bucket1',
    credential: {
      accessKeyId: 'accessKeyId1',
      secretAccessKey: 'secretAccessKey1',
      sessionToken: 'sessionToken1',
    },
  })),
  generateS3WritableCredential: jest.fn().mockImplementation(() => ({
    storage: 's3',
    bucket: 'bucket2',
    credential: {
      accessKeyId: 'accessKeyId2',
      secretAccessKey: 'secretAccessKey2',
      sessionToken: 'sessionToken2',
    },
  })),
}));

const mockVerifyJWT = jest.fn().mockImplementation((jwtToken: string) => ({
  data: {
    verifyJWT: null,
  },
  errors: ['jwtToken validation failed'],
}));

const mockValidateToken = jest.fn().mockImplementation((token: string) => {
  if (token === 'validToken') {
    return {
      data: {
        validateToken: {
          token: 'validToken',
        },
      },
    };
  } else {
    return {
      data: {
        validateToken: null,
      },
      errors: ['token validation failed'],
    };
  }
});

jest.mock('../../../util/api/graphQL', () =>
  jest.fn().mockImplementation(() => ({
    verifyJWT: mockVerifyJWT,
    validateToken: mockValidateToken,
    setToken: jest.fn(),
  }))
);

jest.mock('@server/src/env', () => ({
  ...jest.requireActual('@server/src/env').default,
  cloud: 'aws',
  cpuClusterId: 'my-cpu-cluster-id',
  gpuClusterId: 'my-gpu-cluster-id',
  nodeEnv: 'test',
  s3: {
    bucket: 'tracker2-dev',
    accessKey: 'replace with accessKey for local dev, no need for ec2',
    secretKey: 'replace with secretKey for local dev, no need for ec2',
    roleArn: 'arn:aws:iam::026972849384:role/VeritoneGLCAssumeRole',
    region: 'us-east-1',
    expireSecs: 3600,
  },
}));

describe('get credential aws', () => {
  test('get aws blob credential successfully', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    const resp = await supertest(expressApp)
      .get('/api/v1/credential')
      .set('Authorization', 'Bear validToken');
    expect(resp.status).toBe(200);
    expect(resp.body.storage).toBe('s3');
    expect(resp.body).toHaveProperty('bucket');
    expect(resp.body).not.toHaveProperty('storageUrl');
    expect(resp.body.credential.sessionToken).toBe('sessionToken1');
  });
});
