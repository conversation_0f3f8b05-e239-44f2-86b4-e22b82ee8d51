import { useEffect, useState } from 'react';
import { TableProps} from '@components/common/Table/Table';
import { Column, NoSearchResults, Table } from '@components/common';
import { GeneratedTimelineWithMatchGroup } from '@shared-types/tracker';
import GeneratedTimelineRow, {
  GeneratedTimelineRowAdditionalProps
} from '@components/rows/GeneratedTimelineRow/GeneratedTimelineRow';
import { ExpiringString, isExpiringString } from '@utility/expiringString';
import { useIntl } from 'react-intl';
import { selectGeneratedTimelineDeletions } from '@store/modules/event/slice';
import {useSelector} from "react-redux";

interface Props {
  onRowClick: (rowData: GeneratedTimelineWithMatchGroup) => void;
  generatedTimelines: {
    results: Array<GeneratedTimelineWithMatchGroup>;
    status: string;
  };
  getGeneratedTimeline: (params: { page: number; limit: number }) => DispatchPromise;
  tableProps?: Partial<TableProps<GeneratedTimelineWithMatchGroup, GeneratedTimelineRowAdditionalProps>>;
}

const TimelineGeneratedVideos = ({
  onRowClick,
  generatedTimelines,
  getGeneratedTimeline,
  tableProps = {}
}: Props) => {
  const intl = useIntl();
  const [deletedTimelineIds, setDeletedTimelineIds] = useState<string[]>([]);

  const deletedGeneratedTimeLines = useSelector(selectGeneratedTimelineDeletions);

  useEffect(() => {
    const deletedTimelineIdsStr = localStorage.getItem('deletedGeneratedTimelineIds');
    if (!deletedTimelineIdsStr) {
      return;
    }
    const deletedTimelineIds = JSON.parse(deletedTimelineIdsStr);
    if (!Array.isArray(deletedTimelineIds)) {
      return;
    }

    const now = new Date().getTime();
    // Filter out expired items
    const validTimelineIdsNotExpired = deletedTimelineIds.filter(
      (item): item is ExpiringString => isExpiringString(item) && now < item.expiry
    );

    // Filter out items that are not in the current generated timeline list
    const generatedTimelineIds = generatedTimelines.results.map((item) => item.id);
    const validTimelineIds = validTimelineIdsNotExpired.filter((item) => generatedTimelineIds.includes(item.value));

    // Update localStorage and state
    localStorage.setItem('deletedGeneratedTimelineIds', JSON.stringify(validTimelineIds));
    setDeletedTimelineIds(
      validTimelineIds.map((item) => item.value)
    );
  }, [generatedTimelines, deletedGeneratedTimeLines]);

  return (
    <Table<GeneratedTimelineWithMatchGroup,
          GeneratedTimelineRowAdditionalProps>
      RowComponent={GeneratedTimelineRow}
      rowData={generatedTimelines.results}
      onRowClick={onRowClick}
      loading={generatedTimelines.status === 'loading'}
      dataQuery={getGeneratedTimeline}
      queryOnMount
      additionalProps={{
        isPendingDeletion: (id: string) => {
          const deletedGeneratedTimeLine = deletedGeneratedTimeLines.find(
              (item) => item.id === id
          );

          return (deletedGeneratedTimeLine &&
              deletedGeneratedTimeLine.status === 'loading' &&
              deletedGeneratedTimeLine.id === id)
          || deletedTimelineIds.includes(id);
        }
      }}
      emptyComponent={
        <div className="event__tab-container-no-results">
          <NoSearchResults
            hasSearchResults={false}
            title={intl.formatMessage({ id: 'noTimelineGeneratedVideosFound', defaultMessage: 'No Timeline Generated Videos Found' })}
            description={intl.formatMessage({ id: 'createFirstTimelineGeneratedVideo', defaultMessage: 'Please open a match group and navigate to the timeline editor to create your first timeline generated video.' })}
          />
        </div>
      }
      {...tableProps}
    >
      <Column
        title={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
        dataKey="name"
        grow={1}
        minWidth={200}
      />
      <Column
        title={intl.formatMessage({ id: 'fileStatus', defaultMessage: 'File Status' })}
        dataKey="status"
        minWidth={100}
        grow={1}
      />
      <Column
        title={intl.formatMessage({ id: 'fileSize',defaultMessage: 'File Size' })}
        dataKey="videoSizeBytes"
        grow={1}
        minWidth={100}
      />
      <Column
        title={intl.formatMessage({ id: 'matchGroup', defaultMessage: 'Match Group' })}
        dataKey="matchGroup"
        grow={1}
        minWidth={100}
      />
      <Column
        title={intl.formatMessage({ id: 'createdDate', defaultMessage: 'Created Date' })}
        dataKey="createdDateTime"
        grow={1}
        minWidth={100}
      />
    </Table>
  );
};

export default TimelineGeneratedVideos;

