import { Context } from '../../../types';
import { Variables } from 'graphql-request';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  GraphQLError,
  ValidationError,
  ActionValidationError,
} from '@common/errors';
import { validateSortParams } from '../../actions/util/validateSortParams';

interface SearchVars extends Variables {
  search: {
    index: string[];
    type: string;
    limit: number;
    offset: number;
    sort?: { field: string; order: string }[];
    query: {
      operator: string;
      conditions: {
        operator: string;
        conditions: { field: string; operator: string; value: string }[];
      }[];
    };
  };
}

const SORT_FIELD = [
  'id',
  'name',
  'createdDateTime',
  'eventStartDate',
  'eventEndDate',
];
const SORTDIRECTION = ['desc', 'asc'];

const searchEvents = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.searchEvents>> | undefined
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { pageSize, currentPage, event, tag, sortBy, sortDirection } =
    req.query;

  const schemaId = cache.get<string>('eventsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }
  const validationError = validateSortParams(
    sortBy,
    sortDirection,
    SORT_FIELD,
    SORTDIRECTION
  );
  if (validationError) {
    throw new ValidationError(validationError.error);
  }

  try {
    const searchVars: SearchVars = {
      search: {
        index: ['mine'],
        type: schemaId,
        limit: Number(pageSize ?? 50),
        offset: (Number(currentPage ?? 1) - 1) * Number(pageSize ?? 50),
        ...(sortBy &&
          sortDirection && {
            sort: [
              { field: sortBy.toString(), order: sortDirection.toString() },
            ],
          }),
        query: {
          operator: 'or',
          conditions: [],
        },
      },
    };

    const eventConditions =
      event && typeof event === 'string'
        ? [
            {
              field: 'name.fulltext',
              operator: 'query_string',
              value: `*${event.trim()}*`,
            },
          ]
        : [];

    const tagConditions =
      tag && typeof tag === 'string'
        ? [
            {
              field: 'tags.fulltext',
              operator: 'query_string',
              value: `*${tag.trim()}*`,
            },
          ]
        : [];

    if (eventConditions.length > 0) {
      searchVars.search.query.conditions.push({
        operator: 'and',
        conditions: eventConditions,
      });
    }
    if (tagConditions.length > 0) {
      searchVars.search.query.conditions.push({
        operator: 'and',
        conditions: tagConditions,
      });
    }

    const { searchMedia } = await callGQL<
      responses.searchMedia<{
        id: string;
        name: string;
        tags: string[];
        createdBy: string;
        createdByName: string;
        description: string;
        eventStartDate: string;
        eventEndDate: string;
        createdDateTime: string;
        modifiedDateTime: string;
      }>,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, searchVars);

    if (searchMedia) {
      const searchEvents = {
        searchResults: searchMedia.jsondata.results,
        pageSize: searchMedia.jsondata.limit,
        currentPage: searchMedia.jsondata.from / searchMedia.jsondata.limit + 1,
        totalCount: Number(searchMedia.jsondata.totalResults),
        totalPages: Math.ceil(
          Number(searchMedia.jsondata.totalResults) / searchMedia.jsondata.limit
        ),
      };
      const new_data = Object.assign({}, data, {
        searchEvents: searchEvents,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e.message);
  }
};

export default searchEvents;
