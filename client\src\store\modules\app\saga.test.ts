import {
  all,
  put,
  select,
  take,
  race,
  call,
  delay,
  fork,
  takeLatest,
} from 'redux-saga/effects';
import { User, modules } from '@veritone/glc-redux';
import * as Sagas from '@store/modules/app/saga';
import {
  watchAppBoot,
  appSagas,
  storeTokenAfterSuccessfulOAuthGrant,
  clearStoredTokenAfterLogout,
  clearStoredTokenAfterLogoutHandle,
  fetchUserWithStoredTokenOrCookie,
  redirectAndAwaitOAuthGrant,
  watchAppBootHandle,
  checkAndCreateRootFolder,
} from './saga';
import { onSetConfig, selectConfig } from '@store/modules/config/slice';
import { appActions, booting, bootFinished } from '@store/modules/app/slice';
import getApiAuthToken from '@utility/getApiAuthToken';

jest.mock('@veritone/glc-redux', () => ({
  modules: {
    user: {
      fetchUser: jest.fn(() => ({})),
      fetchEnabledApps: jest.fn(() => ({ type: 'FETCH_ENABLED_APPS' })),
      FETCH_USER: 'FETCH_USER',
      FETCH_USER_SUCCESS: 'FETCH_USER_SUCCESS',
      FETCH_USER_FAILURE: 'FETCH_USER_FAILURE',
      FETCH_USER_APPLICATIONS: 'FETCH_USER_APPLICATIONS',
      FETCH_USER_APPLICATIONS_SUCCESS: 'FETCH_USER_APPLICATIONS_SUCCESS',
      FETCH_USER_APPLICATIONS_FAILURE: 'FETCH_USER_APPLICATIONS_FAILURE',
      LOGOUT: 'LOGOUT',
      LOGOUT_FAILURE: 'LOGOUT_FAILURE',
      LOGOUT_SUCCESS: 'LOGOUT_SUCCESS',
      selectUser: jest.fn(),
    },
    auth: {
      setOAuthToken: jest.fn((params) => params),
      OAUTH_GRANT_FLOW_SUCCESS: 'OAUTH_GRANT_FLOW_SUCCESS',
    },
    config: {
      getConfig: jest.fn(() => ({})),
    },
  },
  helpers: {
    createReducer: jest.fn(),
  },
}));
const {
  user: {
    fetchUser,
    FETCH_USER,
    FETCH_USER_SUCCESS,
    FETCH_USER_FAILURE,
    LOGOUT_FAILURE,
    LOGOUT_SUCCESS,
    selectUser,
  },
  auth: { setOAuthToken, OAUTH_GRANT_FLOW_SUCCESS },
  config: { getConfig },
} = modules;
const getState = (state: any) => state;

describe('redirectToVeritoneInternalLogin', () => {
  it('should redirect to loginRoute if it is a veritone.com domain', () => {
    const config = {
      loginRoute: 'https://login.veritone.com',
    };

    const url = 'https://app.veritone.com';

    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        href: url,
      },
    });

    const generator = Sagas.redirectToVeritoneInternalLogin();

    expect(generator.next().value).toEqual(select(getConfig));

    generator.next(config as unknown as Window['config']);

    const expectedUrl = `${config.loginRoute}/?redirect=${encodeURIComponent(
      url
    )}%2F`;
    expect(window.location.href).toBe(expectedUrl);
  });

  // TODO - Works locally but fails in github actions
  // it('should not redirect if loginRoute is not a veritone.com domain', () => {
  //     const loginRoute = 'https://login.veritone.com';
  //     const config = {
  //         loginRoute: 'https://login.notveritone.com',
  //     };

  //     const url = 'https://app.veritone.com';

  //     global.window = Object.create(window);
  //     Object.defineProperty(window, 'location', {
  //         value: {
  //             href: url,
  //         },
  //     });

  //     const generator = Sagas.redirectToVeritoneInternalLogin();

  //     expect(generator.next().value).toEqual(select(getConfig));

  //     generator.next(config as unknown as Window['config']);

  //     const expectedUrl = `${loginRoute}/?redirect=${encodeURIComponent(url)}%2F`;
  //     expect(window.location.href).toBe(expectedUrl);
  // });

  // it('should not redirect if config.loginRoute is not defined', () => {
  //     const loginRoute = 'https://login.veritone.com';

  //     const config = {};

  //     const url = 'https://app.veritone.com';

  //     global.window = Object.create(window);
  //     Object.defineProperty(window, 'location', {
  //         value: {
  //             href: url,
  //         },
  //     });

  //     const generator = Sagas.redirectToVeritoneInternalLogin();

  //     expect(generator.next().value).toEqual(select(getConfig));

  //     generator.next(config as unknown as Window['config']);

  //     const expectedUrl = `${loginRoute}/?redirect=https%3A%2F%2Fapp.veritone.com%2F`;
  //     expect(window.location.href).toBe(expectedUrl);
  // });
});

describe('test fetchUserWithStoredTokenOrCookie', () => {
  describe('return payload', () => {
    it('with existingOAuthToken', () => {
      const state = {
        existingOAuthToken: 'existingOAuthToken',
        race: [],
      };
      const generator = fetchUserWithStoredTokenOrCookie();
      expect(generator.next().value).toEqual(
        call([localStorage, 'getItem'], 'OAuthToken')
      );
      expect(generator.next(state.existingOAuthToken).value).toEqual(
        put(setOAuthToken(state.existingOAuthToken))
      );
      // TODO: Remove any when redux-saga types are updated
      expect(generator.next().value).toEqual(put(fetchUser() as any));
      expect(JSON.stringify(generator.next().value)).toEqual(
        JSON.stringify(
          race([
            take(FETCH_USER_SUCCESS),
            take([
              (a: any) => a.type === FETCH_USER && a.error,
              FETCH_USER_FAILURE,
            ]),
          ])
        )
      );
      const finalStep = generator.next([state.race[0]]);
      expect(finalStep.done).toBeTruthy();
      expect(finalStep.value).toBeFalsy();
    });
    it('without existingOAuthToken', () => {
      const state = {
        race: [],
      };
      const generator = fetchUserWithStoredTokenOrCookie();
      expect(generator.next().value).toEqual(
        call([localStorage, 'getItem'], 'OAuthToken')
      );
      expect(generator.next(null).value).toEqual(
        // TODO: Remove any when redux-saga types are updated
        put(fetchUser() as any)
      );
      expect(JSON.stringify(generator.next().value)).toEqual(
        JSON.stringify(
          race([
            take(FETCH_USER_SUCCESS),
            take([
              (a: any) => a.type === FETCH_USER && a.error,
              FETCH_USER_FAILURE,
            ]),
          ])
        )
      );
      const finalStep = generator.next([state.race[0]]);
      expect(finalStep.done).toBeTruthy();
      expect(finalStep.value).toBeFalsy();
    });
  });

  it('return false', () => {
    const state = {
      existingOAuthToken: 'existingOAuthToken',
      race: [],
    };
    const generator = fetchUserWithStoredTokenOrCookie();
    expect(generator.next().value).toEqual(
      call([localStorage, 'getItem'], 'OAuthToken')
    );
    expect(generator.next(state.existingOAuthToken).value).toEqual(
      put(setOAuthToken(state.existingOAuthToken))
    );
    // TODO: Remove any when redux-saga types are updated
    expect(generator.next().value).toEqual(put(fetchUser() as any));
    expect(JSON.stringify(generator.next().value)).toEqual(
      JSON.stringify(
        race([
          take(FETCH_USER_SUCCESS),
          take([
            (a: any) => a.type === FETCH_USER && a.error,
            FETCH_USER_FAILURE,
          ]),
        ])
      )
    );
    const finalStep = generator.next([state.race[0]]);
    expect(finalStep.done).toBeTruthy();
    expect(finalStep.value).toBeFalsy();
  });
});

describe('test clearStoredTokenAfterLogoutHandle', () => {
  it('with has user', () => {
    const state = {
      user: {
        name: 'test',
      },
      config: {
        loginRoute: 'https://www.test.com',
      },
    };
    const generator = clearStoredTokenAfterLogoutHandle();
    expect(generator.next().value).toEqual(select(selectUser));
    expect(generator.next(getState(state).user).value).toEqual(
      call([localStorage, 'removeItem'], 'OAuthToken')
    );
    expect(generator.next().value).toEqual(
      take([LOGOUT_FAILURE, LOGOUT_SUCCESS])
    );
    expect(generator.next().value).toEqual(delay(1000));
    expect(generator.next().value).toEqual(select(selectConfig));

    const assignMock = jest.fn();
    delete (window as any).location;
    (window as any).location = { assign: assignMock };

    expect(generator.next(getState(state).config).done).toBeTruthy();
  });
  it('without user', () => {
    const state = {
      user: {},
    };
    const generator = clearStoredTokenAfterLogoutHandle();
    expect(generator.next().value).toEqual(select(selectUser));
    expect(generator.next(getState(state).user).done).toBeTruthy();
  });
});

describe('test clearStoredTokenAfterLogoutHandle', () => {
  describe('with routeType !== ROUTE_AUTH', () => {
    it('with has routePayload', () => {
      const generator = redirectAndAwaitOAuthGrant();

      expect(generator.next().value).toEqual(put(bootFinished()));
      expect(generator.next().value).toEqual(take(OAUTH_GRANT_FLOW_SUCCESS));
      expect(generator.next().value).toEqual(put(booting()));
      expect(generator.next().done).toBeTruthy();
    });
    it('with empty routePayload', () => {
      const generator = redirectAndAwaitOAuthGrant();

      expect(generator.next().value).toEqual(put(bootFinished()));
      expect(generator.next().value).toEqual(take(OAUTH_GRANT_FLOW_SUCCESS));
      expect(generator.next().value).toEqual(put(booting()));
      expect(generator.next().done).toBeTruthy();
    });
  });
});
describe('getAppStartupDependencies saga', () => {
  it('should dispatch the correct actions', () => {
    const generator = Sagas.getAppStartupDependencies();

    expect(generator.next().value).toEqual(
      all([
        put({ type: 'FETCH_ENABLED_APPS' }),
        // ...other app dependencies
      ])
    );
  });
});
describe('test watchAppBootHandle', () => {
  it('with has user', () => {
    const state = {
      user: {
        name: 'User',
        id: 'tdoId',
        userId: 'userId',
        mfaInfo: {
          phoneNumber: 'phoneNumber',
          email: 'email',
          mfaEnabled: false,
          smsVoiceVerifiedDateTime: 'smsVoiceVerifiedDateTime',
          gaVerifiedDateTime: 'gaVerifiedDateTime',
          defaultOption: 'defaultOption',
          pendingRegistration: 'pendingRegistration',
        },
        firstName: 'firstName',
        lastName: 'lastName',
        permissions: null,
        roles: null,
        roleIds: null,
        organizationId: null,
        organization: null,
        jsondata: null,
        email: null,
        acls: null,
        rootFolder: null,
        passwordUpdatedDateTime: null,
        lastLoginDateTime: null,
        createdDateTime: null,
        modifiedDateTime: null,
        userSettings: null,
        imageUrl: null,
        status: null,
        token: null,
        kvp: null,
      } as User,
      config: {
        opticalTrackingEngineId: 'opticalTrackingEngineId',
      },
      auth: { sessionToken: 'sessionToken1' },
    };
    const generator = watchAppBootHandle();
    expect(generator.next().value).toEqual(select(getConfig));
    jest
      .spyOn(Sagas, 'fetchUserWithStoredTokenOrCookie')
      .mockImplementation(function* () {
        yield 'fetchUserWithStoredTokenOrCookie';
        return state.user;
      });
    expect(generator.next(getState(state).config).value);
    expect(generator.next(getState(state).config).value).toEqual(
      put(onSetConfig(state.config as unknown as Window['config']))
    );
    jest
      .spyOn(Sagas, 'getAppStartupDependencies')
      .mockImplementation(function* () {
        yield 'getAppStartupDependencies';
      });
    expect(generator.next().value);
    expect(generator.next().value).toEqual(select(getApiAuthToken));
    expect(generator.next(getState(state).auth.sessionToken).value).toEqual(
      call(checkAndCreateRootFolder, state.config as any, 'sessionToken1')
    );
    expect(generator.next().value).toEqual(put(bootFinished()));
    expect(generator.next().done).toBeTruthy();
  });

  describe('with has not user', () => {
    it('with useOAuthGrant in config', () => {
      const state = {
        user: undefined as unknown as false, // TODO - Fix this
        config: {
          useOAuthGrant: 'useOAuthGrant',
        },
      };
      const generator = watchAppBootHandle();
      expect(generator.next().value).toEqual(select(getConfig));
      jest
        .spyOn(Sagas, 'fetchUserWithStoredTokenOrCookie')
        .mockImplementation(function* () {
          yield 'fetchUserWithStoredTokenOrCookie';
          return state.user;
        });
      expect(generator.next(getState(state).config).value);
      expect(generator.next().value).toEqual(
        put(onSetConfig(state.config as unknown as Window['config'])) // TODO - Fix this
      );
      jest
        .spyOn(Sagas, 'redirectAndAwaitOAuthGrant')
        .mockImplementation(function* () {
          yield 'redirectAndAwaitOAuthGrant' as any; // TODO - Justify or fix
        });
      expect(generator.next().value);
      expect(generator.next().done).toBeTruthy();
    });
    it('without useOAuthGrant in config', () => {
      const state = {
        user: undefined as unknown as false, // TODO - Fix this
        config: {},
      };
      const generator = watchAppBootHandle();
      expect(generator.next().value).toEqual(select(getConfig));
      jest
        .spyOn(Sagas, 'fetchUserWithStoredTokenOrCookie')
        .mockImplementation(function* () {
          yield 'fetchUserWithStoredTokenOrCookie';
          return state.user;
        });
      expect(generator.next(getState(state).config).value);
      expect(generator.next().value).toEqual(
        put(onSetConfig(state.config as unknown as Window['config']))
      );
      jest
        .spyOn(Sagas, 'redirectToVeritoneInternalLogin')
        .mockImplementation(function* () {
          yield 'redirectToVeritoneInternalLogin' as any; // TODO - fix
        });
      expect(generator.next().value);
      expect(generator.next().done).toBeTruthy();
    });
  });

  describe('watchAppBoot saga', () => {
    it('should take latest appActions.booting action and call watchAppBootHandle', () => {
      const generator = watchAppBoot();

      expect(generator.next().value).toEqual(
        takeLatest(appActions.booting, watchAppBootHandle)
      );

      expect(generator.next().done).toBeTruthy();
    });
  });

  describe('appSagas saga', () => {
    it('should fork the correct sagas', () => {
      const generator = appSagas();

      expect(generator.next().value).toEqual(
        all([
          fork(watchAppBoot),
          fork(storeTokenAfterSuccessfulOAuthGrant),
          fork(clearStoredTokenAfterLogout),
        ])
      );

      expect(generator.next().done).toBeTruthy();
    });
  });

  describe('clearStoredTokenAfterLogout saga', () => {
    it('should take latest LOGOUT action and call clearStoredTokenAfterLogoutHandle', () => {
      const generator = clearStoredTokenAfterLogout();

      // Check that the first yielded value is a takeLatest effect
      expect(generator.next().value).toEqual(
        takeLatest('LOGOUT', clearStoredTokenAfterLogoutHandle)
      );

      // Check that the generator is done
      expect(generator.next().done).toBeTruthy();
    });
  });

  describe('storeTokenAfterSuccessfulOAuthGrant saga', () => {
    it('should take latest OAUTH_GRANT_FLOW_SUCCESS action and call localStorage.setItem', () => {
      const mockAction = setOAuthToken('testToken');
      const generator = storeTokenAfterSuccessfulOAuthGrant();

      // Check that the first yielded value is a takeLatest effect
      const takeLatestEffect: any = generator.next().value;
      expect(takeLatestEffect).toEqual(
        takeLatest('OAUTH_GRANT_FLOW_SUCCESS', expect.any(Function))
      );

      // Get the generator function passed to takeLatest
      const handleActionGenerator = takeLatestEffect.payload.args[1];

      // Check the inner generator function
      const innerGenerator = handleActionGenerator(mockAction);
      expect(innerGenerator.next().value).toEqual(
        call([localStorage, 'setItem'], 'OAuthToken', mockAction.payload)
      );

      // Check that the inner generator is done
      expect(innerGenerator.next().done).toBeTruthy();
    });
  });
});
