import { callGQL } from '../../../util/api/graphQL/callGraphQL';
import { ForbiddenError, UnauthorizedError } from '@common/errors';
import { Context } from '../../types';

interface validateJWTTokenResponse {
  verifyJWT: {
    jwtToken: string;
    payload: {
      contentOrganizationId: string;
    };
  };
}

const validateJWTTokenQuery = (token: string) => `
  mutation verifyJWT{
    verifyJWT(jwtToken: "${token}")
    {
      jwtToken
      payload
    }
  }
`;

const validateJwtToken = async <
  ReqPayload,
  Data extends { authorizedOrgId: string },
>(
  context: Context<ReqPayload, Data>
) => {
  const { req, log, data } = context;

  const bearerHeader = req.get('Authorization');
  if (bearerHeader) {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];
    if (bearerToken) {
      try {
        const query = validateJWTTokenQuery(
          bearerToken.replace(/[Bb]earer /, '')
        );
        const headers = { Authorization: req.headers.authorization };
        const response = await callGQL<
          validateJWTTokenResponse,
          ReqPayload,
          Data
        >(context, headers, query);

        if (response?.verifyJWT?.jwtToken) {
          data.authorizedOrgId =
            response.verifyJWT.payload.contentOrganizationId;
          return context;
        }
      } catch (_err) {
        log.error('API JWT Token Validation failed');
        throw new ForbiddenError();
      }
    } else {
      log.error('API JWT Token Validation failed');
      throw new UnauthorizedError();
    }
  } else {
    log.error('API JWT Token Validation failed');
    throw new UnauthorizedError();
  }
};

export default validateJwtToken;
