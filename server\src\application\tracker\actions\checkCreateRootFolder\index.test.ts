import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import checkCreateRootFolder from './';

let cxt: Context<object, object>;

let existingRootFolers = [] as {
  id: string;
  name: string;
  ownerId: string | null;
}[];
jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => {
      if (_query.includes('createRootFolders')) {
        return Promise.resolve({
          createRootFolders: [
            {
              id: 'createdId789',
              name: 'Root Admin cms Root Folder',
              ownerId: null,
            },
          ],
        });
      }
      return Promise.resolve({ rootFolders: existingRootFolers });
    }
  ),
}));

describe('checkCreateRootFolder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('create root folder when there is no a root folder', async () => {
    existingRootFolers.length = 0;
    const response = await checkCreateRootFolder(cxt);
    const want = {
      rootFolderId: 'createdId789',
      status: 'created',
    };
    expect(callGQL).toHaveBeenCalledTimes(2);
    expect(response?.data.rootFolder).toEqual(want);
  });

  it('return root folder when there is a root folder', async () => {
    existingRootFolers.push({
      id: 'existingId123',
      name: 'Root Admin cms Root Folder',
      ownerId: null,
    });
    const response = await checkCreateRootFolder(cxt);
    const want = {
      rootFolderId: 'existingId123',
      status: 'existing',
    };
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(response?.data.rootFolder).toEqual(want);
  });
});
