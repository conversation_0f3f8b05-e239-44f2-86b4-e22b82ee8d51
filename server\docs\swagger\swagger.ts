import { OpenAPIV3 } from 'openapi-types';
import env from '../../src/env';

const buildUrl = (url: string): OpenAPIV3.ServerObject => ({ url: `${url}` });
const getUrls = () => {
  const urls: OpenAPIV3.ServerObject[] = [];
  if (env?.nodeEnv === 'development') {
    urls.push(buildUrl('https://local.veritone.com:3002'));
  } else if (env?.apiRoot) {
    urls.push(buildUrl(env.apiRoot.replace('api', 'track')));
  } else {
    urls.push(buildUrl('https://track.stage.us-1.veritone.com'));
  }
  return urls;
};

export const swaggerDocument: OpenAPIV3.Document = {
  openapi: '3.0.1',
  info: {
    version: '1.0.0',
    title: 'Track APIs',
    description: 'Providing backend apis to work in Track application.',
  },
  servers: getUrls(),
  security: [
    {
      bearerAuth: [],
    },
  ],
  paths: {
    // check app health
    '/api/health': {
      get: {
        summary: 'Health Check',
        responses: {
          200: { $ref: '#/components/responses/healthCheck' },
        },
      },
    },

    // event apis
    '/api/v1/event': {
      post: {
        summary: 'create Event',
        requestBody: { $ref: '#/components/requestBodies/createEvent' },
        responses: {
          200: { $ref: '#/components/responses/event' },
          400: { $ref: '#/components/responses/badRequest' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/events': {
      get: {
        summary: 'get events',
        parameters: [
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/sortDirection' },
          { $ref: '#/components/parameters/sortBy' },
          { $ref: '#/components/parameters/eventSearch' },
          { $ref: '#/components/parameters/tag' },
        ],
        responses: {
          200: { $ref: '#/components/responses/getEvents' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/events/ids': {
      get: {
        summary: 'get events by ids',
        parameters: [{ $ref: '#/components/parameters/pendingEventIds' }],
        responses: {
          200: { $ref: '#/components/responses/getEvents' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/event/{eventId}': {
      get: {
        summary: 'get Event by id',
        parameters: [{ $ref: '#/components/parameters/eventId' }],
        responses: {
          200: { $ref: '#/components/responses/event' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      patch: {
        summary: 'edit Event',
        parameters: [{ $ref: '#/components/parameters/eventId' }],
        requestBody: { $ref: '#/components/requestBodies/updateEvent' },
        responses: {
          200: { $ref: '#/components/responses/event' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      delete: {
        summary: 'delete Event by id',
        parameters: [{ $ref: '#/components/parameters/eventId' }],
        responses: {
          200: { $ref: '#/components/responses/deleteEvent' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/tags': {
      get: {
        summary: 'get tags',
        responses: {
          200: { $ref: '#/components/responses/tags' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },

    // match-group apis
    '/api/v1/match-groups': {
      post: {
        summary: 'create match-group',
        requestBody: { $ref: '#/components/requestBodies/createMatchGroup' },
        responses: {
          200: { $ref: '#/components/responses/matchGroup' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      get: {
        summary: 'get match-groups',
        parameters: [
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/sortDirection' },
          { $ref: '#/components/parameters/sortType' },
          { $ref: '#/components/parameters/eventIdQuery' },
        ],
        responses: {
          200: { $ref: '#/components/responses/matchGroups' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/match-groups/{matchGroupId}': {
      get: {
        summary: 'get match-groups',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        responses: {
          200: { $ref: '#/components/responses/matchGroups' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      patch: {
        summary: 'create match-group',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        requestBody: {
          $ref: '#/components/requestBodies/updateMatchGroup',
        },
        responses: {
          200: { $ref: '#/components/responses/matchGroup' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/match-groups/{matchGroupId}/generate-timeline': {
      patch: {
        summary: 'generate timeline',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        requestBody: { $ref: '#/components/requestBodies/generateTimeline' },
        responses: {
          200: { $ref: '#/components/responses/matchGroup' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/match-groups/{matchGroupId}/timeline': {
      delete: {
        summary: 'delete timeline',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        requestBody: { $ref: '#/components/requestBodies/deleteTimeline' },
        responses: {
          200: { $ref: '#/components/responses/deleteTimeline' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/match-groups/{matchGroupId}/selected-tracklets': {
      get: {
        summary: 'get selected tracklets',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        responses: {
          200: { $ref: '#/components/responses/selectedTracklets' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      delete: {
        summary: 'delete selected tracklets',
        parameters: [{ $ref: '#/components/parameters/matchGroupId' }],
        requestBody: {
          $ref: '#/components/requestBodies/deleteSelectedTracklets',
        },
        responses: {
          200: { $ref: '#/components/responses/deleteSelectedTracklets' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/match-groups/{matchGroupId}/search/{searchId}': {
      get: {
        summary: 'get match-group search',
        parameters: [
          { $ref: '#/components/parameters/matchGroupId' },
          { $ref: '#/components/parameters/searchId' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/fileIds' },
          { $ref: '#/components/parameters/type' },
        ],
        responses: {
          200: { $ref: '#/components/responses/matchGroup' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      delete: {
        summary: 'delete match-group search',
        parameters: [
          { $ref: '#/components/parameters/matchGroupId' },
          { $ref: '#/components/parameters/searchId' },
        ],
        responses: {
          200: { $ref: '#/components/responses/deleteMatchGroupSearch' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },

    // file apis
    '/api/v1/file': {
      post: {
        summary: 'create file',
        requestBody: { $ref: '#/components/requestBodies/createFile' },
        responses: {
          200: { $ref: '#/components/responses/createFile' },
          400: { $ref: '#/components/responses/badRequest' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/file/{fileId}/ingest': {
      post: {
        summary: 'ingest file',
        parameters: [{ $ref: '#/components/parameters/fileId' }],
        requestBody: { $ref: '#/components/requestBodies/ingestFile' },
        responses: {
          200: { $ref: '#/components/responses/ingestFile' },
          400: { $ref: '#/components/responses/badRequest' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/files': {
      get: {
        summary: 'get file',
        parameters: [
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/sortDirection' },
          { $ref: '#/components/parameters/sortBy' },
          { $ref: '#/components/parameters/eventIdQuery' },
          { $ref: '#/components/parameters/fileSearch' },
        ],
        responses: {
          200: { $ref: '#/components/responses/getFile' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/event/{eventId}/file/{fileId}/results': {
      get: {
        summary: 'get file results',
        parameters: [
          { $ref: '#/components/parameters/eventId' },
          { $ref: '#/components/parameters/fileId' },
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/type' },
        ],
        responses: {
          200: { $ref: '#/components/responses/fileSearchResults' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },

    '/api/v1/event/{eventId}/file/{fileId}/results/intersection': {
      get: {
        summary: 'get file intersection results',
        parameters: [
          { $ref: '#/components/parameters/eventId' },
          { $ref: '#/components/parameters/fileId' },
          { $ref: '#/components/parameters/pageSize' },
          { $ref: '#/components/parameters/currentPage' },
          { $ref: '#/components/parameters/type' },
          { $ref: '#/components/parameters/xMin' },
          { $ref: '#/components/parameters/xMax' },
          { $ref: '#/components/parameters/yMin' },
          { $ref: '#/components/parameters/yMax' },
        ],
        responses: {
          200: { $ref: '#/components/responses/fileSearchIntersectionResults' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/bounding-boxes': {
      get: {
        summary: 'get bounding boxes',
        parameters: [
          { $ref: '#/components/parameters/fileIdQuery' },
          { $ref: '#/components/parameters/trackletId' },
          { $ref: '#/components/parameters/type' },
          { $ref: '#/components/parameters/startTimeMs' },
          { $ref: '#/components/parameters/stopTimeMs' },
        ],
        responses: {
          200: { $ref: '#/components/responses/getBoundingBoxes' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/file/{fileId}': {
      get: {
        summary: 'get file',
        parameters: [{ $ref: '#/components/parameters/fileId' }],
        responses: {
          200: { $ref: '#/components/responses/getFile' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
      delete: {
        summary: 'delete file',
        parameters: [{ $ref: '#/components/parameters/fileId' }],
        responses: {
          200: { $ref: '#/components/responses/deleteFile' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },

    // other apis
    '/api/v1/credential': {
      get: {
        summary: 'get credential',
        responses: {
          200: { $ref: '#/components/responses/getCredential' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/thumbnails': {
      post: {
        summary: 'create thumbnails',
        requestBody: { $ref: '#/components/requestBodies/thumbnails' },
        responses: {
          200: { $ref: '#/components/responses/thumbnails' },
          401: { $ref: '#/components/responses/unauthorized' },
        },
      },
    },
    '/api/v1/rootfolder': {
      post: {
        summary: 'check and create root folder',
        responses: {
          200: { $ref: '#/components/responses/rootfolder' },
          401: { $ref: '#/components/responses/unauthorized' },
          403: { $ref: '#/components/responses/forbidden' },
          500: { $ref: '#/components/responses/internalServerError' },
        },
      },
    },
  },
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    responses: {
      healthCheck: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'I am alive!',
                },
              },
            },
          },
        },
      },
      unauthorized: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                code: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Unauthorized' },
                requestIds: {
                  type: 'array',
                  items: { type: 'string' },
                  example: ['123'],
                },
              },
            },
          },
        },
      },
      forbidden: {
        description: 'Forbidden',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                code: { type: 'number', example: 403 },
                message: { type: 'string', example: 'Forbidden' },
                requestIds: {
                  type: 'array',
                  items: { type: 'string' },
                  example: ['123'],
                },
              },
            },
          },
        },
      },
      badRequest: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                error: {
                  type: 'string',
                  description: 'Error message.',
                },
              },
            },
          },
        },
      },
      internalServerError: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                code: { type: 'number', example: 500 },
                message: { type: 'string', example: 'Internal Server Error' },
                requestIds: {
                  type: 'array',
                  items: { type: 'string' },
                  example: ['123'],
                },
              },
            },
          },
        },
      },
      event: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/event',
            },
          },
        },
      },
      deleteEvent: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'id of the Event.',
                },
                message: {
                  type: 'string',
                  description: 'message.',
                },
                code: {
                  type: 'string',
                  description: 'code number.',
                },
              },
            },
          },
        },
      },
      tags: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    type: 'string',
                  },
                  description: 'all tags.',
                },
              },
            },
          },
        },
      },
      matchGroup: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                matchGroup: {
                  $ref: '#/components/schemas/matchGroup',
                },
              },
            },
          },
        },
      },
      matchGroups: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                eventId: {
                  type: 'string',
                  description: 'id of the Event.',
                },
                results: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/matchGroup',
                  },
                  description: 'all match-groups.',
                },
                currentPage: {
                  type: 'number',
                  description: 'current page.',
                },
                pageSize: {
                  type: 'number',
                  description: 'number of match-groups per page.',
                },
                totalCount: {
                  type: 'number',
                  description: 'total match-groups.',
                },
                totalPages: {
                  type: 'number',
                  description: 'total pages.',
                },
                sortType: {
                  type: 'string',
                  description: 'sort by field.',
                },
                sortDirection: {
                  type: 'string',
                  description: 'sort by direction.',
                },
              },
            },
          },
        },
      },
      deleteTimeline: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                matchGroup: { $ref: '#/components/schemas/matchGroup' },
                generatedTimelineId: { type: 'string' },
                message: { type: 'string' },
              },
            },
          },
        },
      },
      selectedTracklets: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/tracklet',
                  },
                  description: 'all tracklets.',
                },
                matchGroupId: {
                  type: 'string',
                  description: 'id of the match-group.',
                },
                matchGroupName: {
                  type: 'string',
                  description: 'name of the match-group.',
                },
                eventId: {
                  type: 'string',
                  description: 'id of the Event.',
                },
              },
            },
          },
        },
      },
      deleteSelectedTracklets: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
              },
            },
          },
        },
      },
      deleteMatchGroupSearch: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                matchGroupId: { type: 'string' },
                searchId: { type: 'string' },
                message: { type: 'string' },
                code: { type: 'number' },
              },
            },
          },
        },
      },
      fileSearchIntersectionResults: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/tracklet',
                  },
                },
                type: { type: 'string' },
                fileId: { type: 'string' },
                eventId: { type: 'string' },
                currentPage: { type: 'number' },
                pageSize: { type: 'number' },
                totalCount: { type: 'number' },
                totalPages: { type: 'number' },
              },
            },
          },
        },
      },
      fileSearchResults: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/tracklet',
                  },
                },
                type: { type: 'string' },
                fileId: { type: 'string' },
                eventId: { type: 'string' },
                currentPage: { type: 'number' },
                pageSize: { type: 'number' },
                totalCount: { type: 'number' },
                totalPages: { type: 'number' },
              },
            },
          },
        },
      },
      getFile: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                file: { $ref: '#/components/schemas/file' },
              },
            },
          },
        },
      },
      createFile: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                uploadUrl: { type: 'string' },
                fileId: { type: 'string' },
                getUrl: { type: 'string' },
              },
            },
          },
        },
      },
      ingestFile: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
              },
            },
          },
        },
      },
      deleteFile: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                fileId: { type: 'string' },
                code: { type: 'number' },
              },
            },
          },
        },
      },
      getEvents: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/event',
                  },
                  description: 'all events.',
                },
                currentPage: { type: 'number' },
                pageSize: { type: 'number' },
                totalCount: { type: 'number' },
                totalPages: { type: 'number' },
              },
            },
          },
        },
      },
      getBoundingBoxes: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      trackletId: { type: 'string' },
                      startTimeMs: { type: 'number' },
                      stopTimeMs: { type: 'number' },
                      boundingPoly: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            x: { type: 'number' },
                            y: { type: 'number' },
                          },
                        },
                      },
                    },
                  },
                  description: 'all bounding boxes.',
                },
              },
            },
          },
        },
      },
      getCredential: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                storage: { type: 'string' },
                storageUrl: { type: 'string' },
                bucket: { type: 'string' },
                region: { type: 'string' },
                credential: {
                  type: 'object',
                  properties: {
                    accessKeyId: { type: 'string' },
                    secretAccessKey: { type: 'string' },
                    sessionToken: { type: 'string' },
                    sasToken: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
      thumbnails: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                thumbnails: { type: 'object' },
              },
            },
          },
        },
      },
      rootfolder: {
        description: 'success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                rootFolderId: {
                  type: 'string',
                  description: 'id of the root folder.',
                },
                status: {
                  type: 'string',
                  description:
                    'the status of the root folder. it could be "created", "existing", "failed"',
                },
              },
            },
          },
        },
      },
    },
    requestBodies: {
      createEvent: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'name of the event.',
                },
                description: {
                  type: 'string',
                  description: 'description of the event.',
                },
                eventStartDate: {
                  type: 'string',
                  format: 'date-time',
                  description: 'start date the event.',
                },
                eventEndDate: {
                  type: 'string',
                  format: 'date-time',
                  description: 'end date of the event.',
                },
              },
            },
          },
        },
      },
      updateEvent: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'name of the Event.',
                },
                description: {
                  type: 'string',
                  description: 'description of the Event.',
                },
                tags: {
                  type: 'array',
                  items: {
                    type: 'string',
                  },
                  description: 'tags of the Event.',
                },
                eventStartDate: {
                  type: 'string',
                  format: 'date-time',
                  description: 'start date of the Event.',
                },
                eventEndDate: {
                  type: 'string',
                  format: 'date-time',
                  description: 'end date of the Event.',
                },
              },
            },
          },
        },
      },
      createMatchGroup: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'name of the match-group.',
                },
                eventId: {
                  type: 'string',
                  description: 'description of the event.',
                },
              },
              required: ['name', 'eventId'],
            },
          },
        },
      },
      updateMatchGroup: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                searches: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      referenceTrackletId: { type: 'string' },
                      searchName: { type: 'string' },
                      searchTime: { type: 'string', format: 'date-time' },
                    },
                    required: ['id', 'referenceTrackletId', 'searchName'],
                  },
                },
                selectedTracklets: {
                  type: 'array',
                  items: { type: 'string' },
                },
              },
            },
          },
        },
      },
      generateTimeline: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                generatedTimelineName: { type: 'string' },
                timelineProject: {
                  type: 'object',
                  properties: {
                    groups: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          name: { type: 'string' },
                          tracklets: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/tracklet' },
                          },
                        },
                      },
                    },
                  },
                },
              },
              required: ['timelineProject'],
            },
          },
        },
      },
      deleteTimeline: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                generatedTimelineId: { type: 'string' },
              },
              required: ['generatedTimelineId'],
            },
          },
        },
      },
      deleteSelectedTracklets: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                selectedTrackletIds: {
                  type: 'array',
                  items: { type: 'string' },
                },
              },
              required: ['selectedTrackletIds'],
            },
          },
        },
      },
      createFile: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                eventId: { type: 'string' },
                fileName: { type: 'string' },
                fileType: { type: 'string' },
              },
              required: ['eventId', 'fileName', 'fileType'],
            },
          },
        },
      },
      ingestFile: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                fileType: { type: 'string' },
                getUrl: { type: 'string' },
              },
              required: ['fileType', 'getUrl'],
            },
          },
        },
      },
      thumbnails: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                tracklets: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      trackletId: { type: 'string' },
                      orgId: { type: 'number' },
                      fileId: { type: 'number' },
                    },
                    required: ['trackletId', 'orgId', 'fileId'],
                  },
                },
              },
              required: ['tracklets'],
            },
          },
        },
      },
    },
    parameters: {
      // path parameters
      eventId: {
        name: 'eventId',
        in: 'path',
        description: 'eventId',
        required: true,
        schema: {
          type: 'string',
        },
      },
      matchGroupId: {
        name: 'matchGroupId',
        in: 'path',
        description: 'match-group id',
        required: true,
        schema: {
          type: 'string',
        },
      },
      searchId: {
        name: 'searchId',
        in: 'path',
        description: 'search id',
        required: true,
        schema: {
          type: 'string',
        },
      },
      fileId: {
        name: 'fileId',
        in: 'path',
        description: 'file id',
        required: true,
        schema: {
          type: 'string',
        },
      },
      // query parameters
      xMin: {
        name: 'xMin',
        in: 'query',
        description: 'Specifies a minimum x value for a search box.',
        required: true,
        schema: {
          type: 'number',
          default: 0.1,
          minimum: 0,
          maximum: 1,
        },
      },
      xMax: {
        name: 'xMax',
        in: 'query',
        description: 'Specifies a maximum x value for a search box.',
        required: true,
        schema: {
          type: 'number',
          default: 0.2,
          minimum: 0,
          maximum: 1,
        },
      },
      yMin: {
        name: 'yMin',
        in: 'query',
        description: 'Specifies a minimum y value for a search box.',
        required: true,
        schema: {
          type: 'number',
          default: 0.1,
          minimum: 0,
          maximum: 1,
        },
      },
      yMax: {
        name: 'yMax',
        in: 'query',
        description: 'Specifies a maximum y value for a search box.',
        required: true,
        schema: {
          type: 'number',
          default: 0.2,
          minimum: 0,
          maximum: 1,
        },
      },
      eventIdQuery: {
        name: 'eventId',
        in: 'query',
        description: 'eventId',
        required: true,
        schema: {
          type: 'string',
        },
      },
      fileIdQuery: {
        name: 'fileId',
        in: 'query',
        description: 'file id.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      fileIds: {
        name: 'fileIds',
        in: 'query',
        description: 'file ids.',
        required: false,
        schema: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      trackletId: {
        name: 'trackletId',
        in: 'query',
        description: 'Tracklet id.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      currentPage: {
        name: 'currentPage',
        in: 'query',
        description: 'The current page.',
        required: false,
        schema: {
          type: 'integer',
          default: 1,
        },
      },
      pageSize: {
        name: 'pageSize',
        in: 'query',
        description: 'Number of results per page.',
        required: false,
        schema: {
          type: 'integer',
          enum: [10, 50, 100],
          default: 100,
        },
      },
      sortDirection: {
        name: 'sortDirection',
        in: 'query',
        description: 'Specifies the sort order.',
        required: false,
        schema: {
          type: 'string',
          enum: ['asc', 'desc'],
          default: 'asc',
        },
      },
      sortType: {
        name: 'sortType',
        in: 'query',
        description: 'Specifies the field by which to sort.',
        required: false,
        schema: {
          type: 'string',
          enum: ['createdDateTime', 'modifiedDateTime'],
          default: 'createdDateTime',
        },
      },
      type: {
        name: 'type',
        in: 'query',
        description: 'type of tracklet.',
        required: true,
        schema: {
          type: 'string',
          enum: ['person', 'vehicle'],
        },
      },
      sortBy: {
        name: 'sortBy',
        in: 'query',
        description: 'sort by field.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      eventSearch: {
        name: 'event',
        in: 'query',
        description: 'event name.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      fileSearch: {
        name: 'file',
        in: 'query',
        description: 'file name.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      tag: {
        name: 'tag',
        in: 'query',
        description: 'tag name.',
        required: false,
        schema: {
          type: 'string',
        },
      },
      pendingEventIds: {
        name: 'pendingEventIds',
        in: 'query',
        description: 'event ids.',
        required: true,
        schema: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      startTimeMs: {
        name: 'startTimeMs',
        in: 'query',
        description: 'start time in milliseconds.',
        required: false,
        schema: {
          type: 'number',
        },
      },
      stopTimeMs: {
        name: 'stopTimeMs',
        in: 'query',
        description: 'stop time in milliseconds.',
        required: false,
        schema: {
          type: 'number',
        },
      },
    },
    schemas: {
      matchGroup: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'id of the match-group.',
          },
          name: {
            type: 'string',
            description: 'name of the match-group.',
          },
          eventId: {
            type: 'string',
            description: 'id of the Event.',
          },
          searches: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'id of the search.',
                },
                searchName: {
                  type: 'string',
                  description: 'name of the search.',
                },
                referenceTrackletId: {
                  type: 'string',
                  description: 'reference tracklet id.',
                },
                searchTime: {
                  type: 'string',
                  description: 'search time.',
                },
              },
            },
            description: 'all searches.',
          },
          selectedTracklets: {
            type: 'array',
            items: {
              type: 'string',
            },
          },
          modifiedDateTime: {
            type: 'string',
            description: 'modification date.',
          },
          timelineProject: {
            type: 'object',
            properties: {
              groups: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    tracklets: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/tracklet' },
                    },
                  },
                },
              },
              modifiedDateTime: { type: 'string' },
              modifiedUserId: { type: 'string' },
            },
          },
        },
      },
      tracklet: {
        type: 'object',
        properties: {
          orgId: { type: 'string', description: 'organization id.' },
          trackletId: { type: 'string', description: 'tracklet id.' },
          fileId: { type: 'string', description: 'file id.' },
          fileName: { type: 'string', description: 'file name.' },
          startTimeMs: {
            type: 'number',
            description: 'start time in milliseconds.',
          },
          stopTimeMs: {
            type: 'number',
            description: 'stop time in milliseconds.',
          },
          attributes: {
            type: 'object',
            description: 'attributes of the tracklet.',
          },
          thumbnailUrls: {
            type: 'object',
            properties: {
              first: { type: 'string', description: 'thumbnail url.' },
              best: { type: 'string', description: 'thumbnail url.' },
            },
          },
          type: { type: 'string', description: 'type of the tracklet.' },
          confidence: { type: 'number', description: 'confidence.' },
        },
      },
      file: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          fileName: { type: 'string' },
          status: {
            type: 'string',
            enum: ['processed', 'processing', 'error', 'pending', 'unknown'],
          },
          length: { type: 'number' },
          createdByName: { type: 'string' },
          uploadDateTime: { type: 'string', format: 'date-time' },
          location: { type: 'string' },
          fileType: { type: 'string' },
          fileSize: { type: 'number' },
          eventId: { type: 'string' },
          eventName: { type: 'string' },
          thumbnailUrl: { type: 'string' },
          primaryAsset: {
            type: 'object',
            properties: {
              signedUri: { type: 'string' },
            },
          },
          streams: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                uri: { type: 'string' },
                protocol: { type: 'string' },
              },
            },
          },
          frameRate: { type: 'number' },
        },
      },
      event: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'id of the Event.',
          },
          name: {
            type: 'string',
            description: 'name of the Event.',
          },
          tags: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'tags of the Event.',
          },
          createdBy: {
            type: 'string',
            description: 'who created the Event.',
          },
          createdByName: {
            type: 'string',
            description: 'name of user who created the Event.',
          },
          description: {
            type: 'string',
            description: 'description of the Event.',
          },
          eventStartDate: {
            type: 'string',
            description: 'start date of the Event.',
          },
          eventEndDate: {
            type: 'string',
            description: 'end date of the Event.',
          },
          createdDateTime: {
            type: 'string',
            description: 'event creation date.',
          },
          modifiedDateTime: {
            type: 'string',
            description: 'event modification date.',
          },
          matchGroupsCount: {
            type: 'number',
            description: 'total match-groups of the Event.',
          },
          filesCount: {
            type: 'number',
            description: 'total files of the Event.',
          },
        },
      },
    },
  },
};
