import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@emotion/react';
import { store } from '@store/store';
import { initTheme } from '@theme/setTheme';
import { createMaterialTheme } from './theme/material';
import '@theme/color.scss';
import './main.scss';
import AppBar from '@components/common/AppBar';
import AppContainer from '@components/common/AppContainer';
import router from '@components/common/Routes/Routes';
import { RouterProvider } from 'react-router-dom';
import { Snackbar } from '@components/common';

// Load the css theme vars into body element for use in the material theme provider
initTheme();

// Create the material theme
const theme = createMaterialTheme();

import '@theme/material.scss';

// Moved here from configureAppStore to avoid test warnings
// Lost many brain cells on this
store.dispatch({ type: 'app/booting' });

/* eslint-disable @typescript-eslint/no-non-null-assertion */
ReactDOM.createRoot(document.getElementById('root')!).render(
  // <React.StrictMode>
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <AppBar />
        <AppContainer>
          <RouterProvider router={router} />
          <Snackbar className="main__snackbar" timeout={30000} />
        </AppContainer>
      </ThemeProvider>
    </Provider>
  // </React.StrictMode>,
);
