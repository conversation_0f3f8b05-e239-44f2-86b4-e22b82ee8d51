import NodeCache from 'node-cache';
import getMatchGroups from '../getMatchGroups';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '@util/api/graphQL';
import { Context, RequestHeader } from '@application/types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { queries } from '../../graphQL';
import { JobStatus, VideoSpliceDetails } from '../../../../../../types/tracker';

let cxt: Context<object, object>;

const mockStructuredDataObjects = {
  records: [
    {
      id: 'a-matchgroup-id',
      data: {
        id: 'a-matchgroup-id',
        name: 'Test Match Group',
        eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
        selectedTracklets: ['trackletId1', 'trackletId2'],
        generatedTimelines: [
          {
            id: 'timeline1',
            name: 'Timeline Project 1',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user1',
            tdoId: 'tdo1',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
          {
            id: 'timeline2',
            name: 'Timeline Project 2',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user1',
            tdoId: 'tdo2',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
        ],
      },
      createdDateTime: '2024-05-03T21:14:26.769Z',
      modifiedDateTime: '2024-05-03T21:57:48.320Z',
    },
    {
      id: 'b-matchgroup-id',
      data: {
        id: 'b-matchgroup-id',
        name: 'Test Match Group',
        eventId: '3c370c7b-325c-4256-8fff-b34a6ec4fdc1',
        selectedTracklets: ['trackletId1', 'trackletId2'],
        generatedTimelines: [
          {
            id: 'timeline3',
            name: 'Timeline Project 3',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user4',
            tdoId: 'tdo3',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
          {
            id: 'timeline4',
            name: 'Timeline Project 4',
            createdDateTime: '20224-01-01T00:00:00Z',
            createdUserId: 'user4',
            tdoId: 'tdo4',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
          },
        ],
      },
      createdDateTime: '2024-05-03T21:14:26.769Z',
      modifiedDateTime: '2024-05-03T21:57:48.320Z',
    },
  ],
  count: 2,
  offset: 0,
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes(queries.searchStructuredDataObjects({}))) {
        return Promise.resolve({
          structuredDataObjects: mockStructuredDataObjects,
        });
      }
      return Promise.resolve({ structuredDataObjects: { records: [] } });
    }
  ),
}));

jest.mock('@tracker/actions/getSplicingTdoDetails', () => ({
  getSplicingTdoDetails: jest
    .fn()
    .mockImplementation((): { [key: string]: VideoSpliceDetails } => ({
      tdo1: {
        downloadUrl: 'mock-download-url-1',
        status: JobStatus.Complete,
      },
      tdo2: {
        downloadUrl: 'mock-download-url-2',
        status: JobStatus.Complete,
      },
      tdo3: {
        downloadUrl: 'mock-download-url-3',
        status: JobStatus.Complete,
      },
      tdo4: {
        downloadUrl: 'mock-download-url-4',
        status: JobStatus.Complete,
      },
    })),
}));

describe('Get Match Groups', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        query: {
          pageSize: 50,
          currentPage: 1,
          sortDirection: 'asc',
          sortType: 'createdDateTime',
          eventId: 'eventId',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'matchGroupsSchemaId');
  });

  it('Query w/ empty value searches all match groups', async () => {
    cxt.req.query.eventId = '';

    const response = await getMatchGroups(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        schemaId: 'matchGroupsSchemaId',
        limit: 50,
        offset: 0,
        sort: 'asc',
        sortType: 'createdDateTime',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/ no params passed to search all match groups', async () => {
    cxt.req.query = {};

    const response = await getMatchGroups(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        schemaId: 'matchGroupsSchemaId',
        limit: 10000,
        offset: 0,
        sort: 'asc',
        sortType: 'createdDateTime',
      }
    );

    expect(response).not.toBeNull();
  });

  it('Get matchgroups with no params and with generatedTimelines', async () => {
    cxt.req.query = {};

    const response = await getMatchGroups(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        schemaId: 'matchGroupsSchemaId',
        limit: 10000,
        offset: 0,
        sort: 'asc',
        sortType: 'createdDateTime',
      }
    );

    expect(response.data.matchGroupsCount).toBe(2);

    // check results with generatedTimelines and status
    expect(response.data.matchGroups.searchResults).toEqual([
      {
        id: 'a-matchgroup-id',
        name: 'Test Match Group',
        eventId: '3c370c7a-325c-4256-8fff-b34a6ec4fdc1',
        selectedTracklets: ['trackletId1', 'trackletId2'],
        generatedTimelines: [
          {
            id: 'timeline1',
            name: 'Timeline Project 1',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user1',
            tdoId: 'tdo1',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
            downloadUrl: 'mock-download-url-1',
            status: 'complete',
          },
          {
            id: 'timeline2',
            name: 'Timeline Project 2',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user1',
            tdoId: 'tdo2',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
            downloadUrl: 'mock-download-url-2',
            status: 'complete',
          },
        ],
        createdDateTime: '2024-05-03T21:14:26.769Z',
        modifiedDateTime: '2024-05-03T21:57:48.320Z',
      },
      {
        id: 'b-matchgroup-id',
        name: 'Test Match Group',
        eventId: '3c370c7b-325c-4256-8fff-b34a6ec4fdc1',
        selectedTracklets: ['trackletId1', 'trackletId2'],
        generatedTimelines: [
          {
            id: 'timeline3',
            name: 'Timeline Project 3',
            createdDateTime: '2022-01-01T00:00:00Z',
            createdUserId: 'user4',
            tdoId: 'tdo3',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
            downloadUrl: 'mock-download-url-3',
            status: 'complete',
          },
          {
            id: 'timeline4',
            name: 'Timeline Project 4',
            createdDateTime: '20224-01-01T00:00:00Z',
            createdUserId: 'user4',
            tdoId: 'tdo4',
            resolution: '1920x1080',
            outputFormat: 'mp4',
            videoLengthMs: 60000,
            videoSizeBytes: 5000000,
            downloadUrl: 'mock-download-url-4',
            status: 'complete',
          },
        ],
        createdDateTime: '2024-05-03T21:14:26.769Z',
        modifiedDateTime: '2024-05-03T21:57:48.320Z',
      },
    ]);

    expect(response).not.toBeNull();
  });
});
