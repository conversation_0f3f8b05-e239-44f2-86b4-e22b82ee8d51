import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { CreateTimelineJobPayloadResponse } from '../../../../../../types/responses';
import env from '../../../../env';

const createTimelineJob = async <
  ReqPayload,
  Data extends Partial<CreateTimelineJobPayloadResponse> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & responses.createSpliceJob> | undefined
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };
  const { videoSliceEngineId, cpuClusterId } = env;
  const { timelineProject, generatedTimelineName } = data;

  if (!videoSliceEngineId || !timelineProject || !generatedTimelineName) {
    throw new ActionError(
      `Missing required data: ${[!videoSliceEngineId ? 'videoSliceEngineId' : '', !timelineProject ? 'timelineProject' : '', !generatedTimelineName ? 'generatedTimelineName' : ''].filter(Boolean).join(', ')}`
    );
  }

  const clips = timelineProject?.groups?.map((group) => [
    {
      tdoId: group.tracklets[0].fileId,
      startTimeMs: group.tracklets[0].startTimeMs,
      stopTimeMs: group.tracklets[0].stopTimeMs,
    },
  ]);

  try {
    const useCluster = !!cpuClusterId;

    const { createJob } = await callGQL<responses.createJob, ReqPayload, Data>(
      context,
      headers,
      queries.createSpliceJob(useCluster),
      {
        payload: { clips, outputFilename: generatedTimelineName },
        engineId: videoSliceEngineId,
        clusterId: cpuClusterId,
      }
    );

    log.debug(`Splice Job created: ${JSON.stringify(createJob)}`);
    if (createJob) {
      const new_data = Object.assign({}, data, {
        spliceJob: {
          tdoId: createJob.targetId,
          jobId: createJob.id,
          status: createJob.status,
        },
      });
      const new_context = Object.assign({}, context, {
        data: new_data,
      });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createTimelineJob;
