import env, { Env } from '../../../env';
import supertest from 'supertest';
import createConfig from '../../../config';
import { createExpressApp } from '../..';

jest.mock('../../../application/credential/actions/util/azure', () => ({
  generateAzureReadOnlyCredential: jest.fn().mockImplementation(() => ({
    storage: 'blob',
    storageUrl: 'storageUrl',
    credential: { sasToken: 'readonlyCredential' },
  })),
  generateAzureWritableCredential: jest.fn().mockImplementation(() => ({
    storage: 'blob',
    storageUrl: 'storageUrl',
    credential: { sasToken: 'writableCredential' },
  })),
}));

const mockVerifyJWT = jest.fn().mockImplementation((jwtToken: string) => ({
  data: {
    verifyJWT: null,
  },
  errors: ['jwtToken validation failed'],
}));

const mockValidateToken = jest.fn().mockImplementation((token: string) => {
  if (token === 'validToken') {
    return {
      data: {
        validateToken: {
          token: 'validToken',
        },
      },
    };
  } else {
    return {
      data: {
        validateToken: null,
      },
      errors: ['token validation failed'],
    };
  }
});

jest.mock('../../../util/api/graphQL', () =>
  jest.fn().mockImplementation(() => ({
    verifyJWT: mockVerifyJWT,
    validateToken: mockValidateToken,
    setToken: jest.fn(),
  }))
);

jest.mock('@server/src/env', () => ({
  ...jest.requireActual('@server/src/env').default,
  cloud: 'azure',
  cpuClusterId: 'my-cpu-cluster-id',
  gpuClusterId: 'my-gpu-cluster-id',
  nodeEnv: 'test',
}));

describe('get credential azure', () => {
  test('get azure blob credential successfully', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    const resp = await supertest(expressApp)
      .get('/api/v1/credential')
      .set('Authorization', 'Bear validToken');
    expect(resp.status).toBe(200);
    expect(resp.body.storage).toBe('blob');
    expect(resp.body).toHaveProperty('storageUrl');
    expect(resp.body).not.toHaveProperty('bucket');
    expect(resp.body.credential.sasToken).toBe('readonlyCredential');
  });
});
