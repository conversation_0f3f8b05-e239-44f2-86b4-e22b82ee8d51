import { render, fireEvent, screen } from '@testing-library/react';
import { Column, ASCENDING_SORT, DESCENDING_SORT } from './Column';

describe('Column common component', () => {

  it('renders', () => {
    render(<Column index={0} dataKey={''} grow={0} title={''} />);
    expect(screen.getByTestId('column-0')).toBeInTheDocument();
  });

  it('renders a title given a title prop', () => {
    render(<Column title="title" dataKey={''} grow={0} />);
    expect(screen.getByTestId('column-title').innerHTML).toBe('title');
  });

  it('passes through children as title children', () => {
    render(
      <Column dataKey={''} grow={0} title={''}>
        <div data-testid="title-child" />
      </Column>
    );

    expect(screen.getByTestId('title-child')).toBeInTheDocument();
  });

  it('makes the sort arrows visable when the column is sortable', () => {
    render(<Column sortable dataKey={''} grow={0} title={''} />);

    expect(screen.getByTestId('column-sort-container')).toHaveClass('column__sort material-icons sortable');
  });

  it('no sort arrows exist when the column is not sortable', () => {
    render(<Column dataKey={''} grow={0} title={''} />);

    expect(screen.getByTestId('column-sort-container')).toHaveClass('column__sort material-icons');
  });

  it('gives the up arrow "active" class when sortOrder is ASCENDING_SORT and it is sortable', () => {
    render(<Column sortOrder={ASCENDING_SORT} sortable dataKey={''} grow={0} title={''} />);

    expect(screen.getByTestId('column-up-arrow')).toHaveClass('column__sort-asc material-icons active');
  });

  it('gives the down arrow "active" class when sortOrder is DESCENDING_SORT and it is sortable', () => {
    render(<Column sortOrder={DESCENDING_SORT} sortable dataKey={''} grow={0} title={''} />);

    expect(screen.getByTestId('column-down-arrow')).toHaveClass('column__sort-dsc material-icons active');
  });

  it('displays a custom aria-label based on the sort status', () => {
    render(<Column title="Thing" sortOrder={ASCENDING_SORT} sortable index={0} dataKey={''} grow={0} />);

    expect(screen.getByTestId('column-0')).toHaveAttribute('aria-label', 'Thing column, sorted in ascending order');
  });

  it('calls onClick on click', () => {
    const mockFn = jest.fn();

    render(<Column sortable onClick={mockFn} index={0} dataKey={''} grow={0} title={''} />);

    fireEvent.click(screen.getByTestId('column-0'));

    expect(mockFn).toHaveBeenCalled();
  });

  it('calls onClick on key up with enter key', () => {
    const mockFn = jest.fn();

    render(<Column sortable onClick={mockFn} index={0} dataKey={''} grow={0} title={''} />);

    fireEvent.keyUp(screen.getByTestId('column-0'), { keyCode: 13 });

    expect(mockFn).toHaveBeenCalled();
  });

  it('does not call onClick on key up with other keys then enter', () => {
    const mockFn = jest.fn();

    render(<Column sortable onClick={mockFn} index={0} dataKey={''} grow={0} title={''} />);

    fireEvent.keyUp(screen.getByTestId('column-0'), { keyCode: 1 });

    expect(mockFn).not.toHaveBeenCalled();
  });
});
