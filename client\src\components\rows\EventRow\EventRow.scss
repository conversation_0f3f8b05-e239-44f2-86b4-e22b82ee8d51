.event-row {
  display: block;
  cursor: pointer;
  border-bottom: solid 1px var(--divider);

  &:first-child {
    border-top: solid 1px var(--divider);
  }

  &:hover {
    background-color: var(--row-active);
    filter: contrast(1.05);
  }
  &.selected {
    background-color: var(--row-active);
  }

  .event-row__row-details {
    margin-top: -10px;
    margin-bottom: 10px;
    transition: height 300ms;
    overflow: hidden;

    .event-row__row-details-line {
      opacity: 0.5;
      background-color: var(--text-primary);
      width: 3px;
      height: 131px;
      margin-left: 70px;
      margin-right: 27px;
      transition: height 300ms;
      float: left;
    }
  }

  .event-row__row {
    position: relative;
    display: flex;
    padding: 11.5px 0 11.5px 18px;

    &.pending {
      color: var(--disabled);
    }

    .event-row__cell {
      display: flex;
      justify-content: center;
      flex-direction: column;

      .event-row__cell-select {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .event-row__cell {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 23px 16px 23px 0;
        line-height: 24px;
        text-transform: capitalize;

        @include size-2;

        &.pending {
          font-style: italic;
        }
      }
    }

    .event-row__cell-name {
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: left;
      white-space: nowrap;
      padding-right: 5px;
    }
  }
}
