/* eslint-disable @cspell/spellchecker */

export const AttributeValueMap: { [key: string]: string } = {
    // PERSON

    // Accessories
    BagAny: 'Any Bag',
    Handtrunk: 'Briefcase',
    Muffler: 'Scarf',
    Shoulderbag: 'Shoulder Bag',
    // LowerColor
    Trousers: 'Pants',
    // UpperType
    Tshirt: 'T-Shirt',

    // VEHICLE

    // make
    bmw: 'BMW',
    gmc: 'GMC',
    'land-rover': 'Land Rover',

    // model
    'f12-berlinetta': 'F12 Berlinetta',
    'flying-spur': 'Flying Spur',
    'gonza-5': 'Glanza 5',
    'grand-cherokee': 'Grand Cherokee',
    grandcherokee: 'Grand Cherokee',
    ilx: 'ILX',
    'land-cruiser': 'Land Cruiser',
    'lx-470': 'LX 470',
    'lx-570': 'LX 570',
    'lx-570l': 'LX 570L',
    mdx: 'MDX',
    ml: 'ML',
    'model-3': 'Model 3',
    'model-s': 'Model S',
    'model-x': 'Model X',
    'range-rover': 'Range Rover',
    'rangesport': 'Range Sport',
    'rav4': 'RAV4',
    rdx: 'RDX',
    rsx: 'RSX',
    rx: 'RX',
    'rx-350': 'RX 350',
    'santa-fe': 'Santa Fe',
    'santefe': 'Santa Fe',
    sl: 'SL',
    'town-and-country': 'Town and Country',
    'xc90': 'XC90',
    'xdrive': 'xDrive',
    'xj': 'XJ',
    'xl': 'XL',
    'xts': 'XTS',
    // type
    suv: 'SUV',
};

export function getAttributeValue(key: string): string {
  return AttributeValueMap[key] || key;
}
