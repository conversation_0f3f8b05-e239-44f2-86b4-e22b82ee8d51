import { Messages } from '.';
import { LOCALES } from '../locales';

const messagesEN: Messages = {
  actions: 'Actions',
  addTag: 'Add tag...',
  aFile: 'a file',
  all: 'All',
  allEvents: 'All Events',
  anEvent: 'an event',
  attributes: 'Attributes',
  browse: 'Browse',
  cancel: 'Cancel',
  clearSelections: 'Clear Selections',
  clearFilter: 'Clear All',
  complete: 'Complete',
  confirm: 'Confirm',
  confirmMessage: 'Enter {type} name to confirm.',
  confirmDeleteEvent: 'You are about to delete {eventName}. All associated files, match groups, searches, and exports will be removed. Are you sure you want to delete it?',
  confirmDeleteMatchGroup: 'Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.',
  continue: 'Continue',
  create: 'Create',
  createdBy: 'Created by',
  createdDate: 'Created Date',
  createFirstMatchGroup: 'Please open a file and create your first match group with \"Find Matches\".',
  createFirstTimelineGeneratedVideo: 'Please open a match group and navigate to the timeline editor to create your first timeline generated video.',
  date: 'Date',
  delete: 'Delete',
  deleteEvent: 'Delete Event',
  deleteFile: 'Delete File',
  deleteFileMessage: 'You are about to delete {fileName}. Are you sure you want to delete it?',
  deleteMatchGroupSearchMessage: 'You are about to delete {searchName}. Are you sure you want to delete it?',
  deleteSelectedTrackletsMessage: 'You are about to delete {noTracklets} selected detections. Are you sure you want to delete them?',
  deleteSelections: 'Delete Selections',
  deleteVideo: 'Delete Video',
  detections: 'Detections',
  downloadVideo: 'Download Video',
  dragAndDropFiles: 'Drag and drop your files here or browse to them',
  enterDescriptionOrNotes: 'Enter description or notes...',
  error: 'Error',
  events: 'Events',
  eventCreator: 'Event Creator',
  eventName: 'Event Name',
  eventTime: 'Event Time',
  existingMatchGroup: 'Existing Match Group',
  failed: 'Failed',
  fewerMatches: 'Fewer Matches',
  file: 'File',
  files: 'Files',
  fileGPSLocation: 'File GPS Location',
  fileMetadata: 'File Metadata',
  fileName: 'File Name',
  fileSize: 'File Size',
  fileStatus: 'File Status',
  fileType: 'File Type',
  fileUploadedBy: 'File Uploaded by',
  filter: 'Filter',
  findMatches: 'Find Matches',
  items: 'items',
  loading: 'Loading',
  matchGroup: 'Match Group',
  matchGroups: 'Match Groups',
  matchGroupName: 'Match Group Name',
  matchGroupSearchResults: 'Match Group Search Results',
  searchResults: 'Search Results',
  markAsVerified: 'Mark as Verified',
  Mb: 'Mb',
  moreMatches: 'More Matches',
  new: 'New',
  newEvent: 'New Event',
  newEventName: 'New Event Name',
  newMatchGroup: 'New Match Group',
  noEventFound: 'No Event Found',
  noEvents: 'There are no events.',
  noFilesAvailable: 'No Files Available',
  noFilesFound: 'No Files Found',
  noMatchGroupsFound: 'No Match Groups Found',
  noSearches: 'There are no searches',
  none: 'None',
  noTimelineGeneratedVideosFound: 'No Timeline Generated Videos Found',
  noTrackletsDetected: 'No detections Detected',
  of: 'of',
  pending: 'Pending',
  pendingDeletion: 'Pending Deletion',
  people: 'people',
  potentialMatchSearch: 'Potential Match Search',
  potentialMatchSearches: 'Potential Match Searches',
  processing: 'Processing',
  selectTrackletToViewDetails: 'Select a Detection to View Details',
  selectPotentialMatchSearch: 'Select a Potential Match Search',
  processed: 'Processed',
  running: 'Running',
  resultsPerPage: 'Results Per Page',
  retryUpload: 'Failed. Click Upload to retry.',
  save: 'Save',
  search: 'Search',
  searchEvents: 'Search Events',
  searchEventsTagsAndFiles: 'Search Events, Tags, and Files',
  searchAllFile: 'Search All Files in this Event',
  select: 'Select',
  selected: 'Selected',
  selectAFile: 'Select a file',
  selectATrackletToViewDetails: 'Select a Detection to View Details',
  showMatchScore: 'Show Match Score',
  thumbnailScale: 'Thumbnail Scale',
  timelineEditor: 'Timeline Editor',
  timelineGeneratedVideos: 'Timeline Generated Videos',
  timelineVideoCreatedBy: 'Timeline Video Created by',
  timelineVideoInfoLocation: 'Timeline Video Info & Location',
  tracklets: 'Detections',
  typeNewTag: 'Type to add new tag',
  unavailable: 'Unavailable',
  unknown: 'Unknown',
  upload: 'Upload',
  uploaded: 'Uploaded',
  uploading: 'Uploading',
  uploadFile: 'Upload File',
  uploadFirstFile: 'Please upload your first file for {eventName}.',
  uploadDate: 'Upload Date',
  uploadToWhichEvent: 'Which event are you uploading to?',
  videoLength: 'Video Length',
  vehicles: 'Vehicles',
  vehicleAndPersonDetection: 'Vehicle and Person Detection',
  verifiedMatches: 'Verified Matches',
  viewEvent: 'View Event',
  viewFile: 'View File',
  viewMatchGroup: 'View Match Group',
  viewPotentialMatchSearches: 'View Potential Match Searches',
  viewSearch: 'View Search',
  Accessory: 'Accessory',
  Backpack: 'Backpack',
  Footwear: 'Footwear',
  Female: 'Female',
  Male: 'Male',
  Lower: 'Lower',
  Upper: 'Upper',
  Body: 'Body',
  Face: 'Face',
  FootwearColor: 'Footwear Color',
  FootwearType: 'Footwear Type',
  Gender: 'Gender',
  HairColor: 'Hair Color',
  HairType: 'Hair Type',
  LowerColor: 'Lower Color',
  LowerType: 'Lower Type',
  UpperColor: 'Upper Color',
  UpperType: 'Upper Type',
  Color: 'Color',
  Make: 'Make',
  Model: 'Model',
  Type: 'Type',
  UpperSleeve: 'Upper Sleeve',
  Hair: 'Hair',
  Sleeve: 'Sleeve',
  snackSelectMatchGroup: 'Please select a match group or create a new one.',
  snackSelectAttribute: 'Please select an attribute to search.',
  findAttributeMatch: 'Find potential matches against attributes you choose.',
  addResultsToMatchGroup: 'Add results to a match group.',
  addFirstTracklet: 'Add your first detections by selecting them in the Potential Search Results.',
  noSearchResultsFound: 'There are no searches yet for "{matchGroup}". To add a search, open a file, select a Detection, and click Find Matches.',
  attributeSearch: 'Attribute Search',
  viewModifyAttributeSearch: 'View or modify search parameters',
  disableConfidence: "Confidence is disabled",
  noTrackletsFound: "No tracklets found",
  selectMatchGroup: "Select a Match Group",
  searchPeople: "Search People",
  searchVehicles: "Search Vehicles",
  chooseAttributes: "Choose Attributes: {attributeCount} Selected",
  resetSelection: 'Reset Selection',
  removeBoundingBox: 'Remove',
  filterTrackletsByRegion: 'Show detections in selected region',
};

export default {
  [LOCALES.ENGLISH]: messagesEN,
};
