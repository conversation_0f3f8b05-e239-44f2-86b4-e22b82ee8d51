import * as ReqTypes from './requests';
import {
  CheckCreateRootFolderStatus,
  Event,
  File,
  MatchGroup,
  Tracklet,
  TrackletBoundingBox,
} from './tracker';

/* GET */
export interface GetEventResponse {
  event: Event;
}

export interface SearchEventsResponse {
  results: Event[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface GetMatchGroupResponse {
  matchGroup: MatchGroup;
}

export interface GetMatchGroupsResponse {
  eventId: string;
  results: MatchGroup[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  sortType: string;
  sortDirection: string;
}

export interface GetMatchGroupSelectedTrackletsResponse {
  results?: Tracklet[];
  matchGroupId: string;
  matchGroupName: string;
  eventId: string;
}

export interface GetMatchGroupSearchResultsResponse {
  results: Tracklet[];
  type?: 'person' | 'vehicle';
  referenceTrackletId: string;
  searchId: string;
  searchName: string;
  matchGroupId: string;
  matchGroupName: string;
  eventId: string;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface GetSearchResultsResponse {
  results: Tracklet[];
  type: 'person' | 'vehicle';
  referenceTrackletId: string;
  searchId: string;
  searchName: string;
  matchGroupId: string;
  matchGroupName: string;
  eventId: string;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  fileNames: string[];
  allFiles: { tdoId: string; fileName: string }[];
}

export interface GetFileSearchResultsResponse {
  results: Tracklet[];
  type?: string;
  fileId: string;
  eventId: string;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface SearchTrackletsWithIntersectingBoxResponse {
  results: Tracklet[];
  type: string;
  fileId: string;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface GetFileResponse {
  file: File;
}

export interface SearchFilesResponse {
  results: Array<File>;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface GetTagsResponse {
  results: Array<string>;
}

export type GetEventPayloadResponse = ReqTypes.GetEventPayload;

export interface GetCredentialResponse {
  storage: string;
  storageUrl?: string;
  bucket?: string;
  region?: string;
  credential: {
    accessKeyId?: string;
    secretAccessKey?: string;
    sessionToken?: string;
    sasToken?: string;
  };
}

export interface LookupLatestSchemaId {
  registryId: string;
  schemaId: string;
}

/* PATCH */
export interface UpdateEventResponse {
  event: Event;
}

export interface UpdateFileResponse {
  file: File;
}

export interface UpdateEventPayloadResponse
  extends ReqTypes.UpdateEventPayload {
  currentTime: string;
}

export interface UpdateFilePayloadResponse extends ReqTypes.UpdateFilePayload {
  currentTime: string;
}

export interface UpdateMatchGroupResponse {
  matchGroup: MatchGroup;
}

export interface UpdateMatchGroupPayloadResponse
  extends ReqTypes.UpdateMatchGroupPayload {}

export interface UpdateMatchGroupPayloadResponse
  extends ReqTypes.UpdateMatchGroupPayload {
  currentTime: string;
}

export interface CreateTimelineJobPayloadResponse
  extends ReqTypes.GenerateTimeLine {
  currentTime: string;
}

export type SaveGeneratedTimeline = Pick<MatchGroup, 'generatedTimelines'>;

/* POST */
export interface CreateEventResponse {
  event?: Event;
}

export interface UploadFileResponse {
  uploadUrl: string;
  fileId: string;
  getUrl: string;
}

export interface CreateEventPayloadResponse
  extends ReqTypes.CreateEventPayload {
  currentTime: string;
}

export interface UploadFilePayloadResponse extends ReqTypes.UploadFilePayload {
  currentTime: string;
}

export type IngestFilePayloadResponse = ReqTypes.IngestFilePayload;

export interface CreateMatchGroupResponse {
  matchGroup?: MatchGroup;
}

export interface CreateMatchGroupsPayloadResponse
  extends ReqTypes.CreateMatchGroupPayload {}

export interface CheckCreateRootFolderResponse {
  rootFolderId: string;
  status: CheckCreateRootFolderStatus;
}

/* DELETE */
export interface DeleteEventResponse {
  id: string;
  message: string;
  code?: number;
}

export interface DeleteFileResponse {
  message: string;
  fileId: string;
  code?: number;
}

export interface DeleteMatchGroupResponse {
  message: string;
  matchGroupId: string;
  code?: number;
}

export interface IngestFileResponse {
  message: string;
}

export interface DeleteMatchGroupSearchResponse {
  matchGroupId: string;
  searchId: string;
  message: string;
  code?: number;
}

export interface DeleteTrackletsResponse {
  trackletIds: string[];
  message: string;
}

export interface GetBoundingBoxesResponse {
  results: TrackletBoundingBox[];
}

export interface DeleteGeneratedTimelineResponse
  extends ReqTypes.DeleteGeneratedTimelinePayload {
  message: string;
}

export interface GetThumbnailsResponse {
  thumbnails: Record<
    string,
    {
      thumbnailUrls: { best: string };
      expiresDateTime: string;
    }
  >;
}
