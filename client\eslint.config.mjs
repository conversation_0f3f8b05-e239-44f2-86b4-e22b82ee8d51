/* eslint-disable @cspell/spellchecker */
import { defineConfig, globalIgnores } from "eslint/config";
import react from "eslint-plugin-react";
import promise from "eslint-plugin-promise";
import lodash from "eslint-plugin-lodash";
import importPlugin from "eslint-plugin-import";
import reactHooks from "eslint-plugin-react-hooks";
import jestDom from "eslint-plugin-jest-dom";
import testingLibrary from 'eslint-plugin-testing-library';
import globals from "globals";
import tseslint from 'typescript-eslint';
import cspellESLintPluginRecommended from '@cspell/eslint-plugin/recommended';
import formatjs from 'eslint-plugin-formatjs';
import pluginCypress from 'eslint-plugin-cypress/flat';

import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default defineConfig([globalIgnores([
    "**/node_modules",
    "**/config",
    "**/coverage",
    "**/docs",
    "**/scripts",
    "**/build",
    "src/utility/redact-pii",
    "**/*test.js",
    "**/vite.config.ts",
]),
  js.configs.recommended,
  ...tseslint.configs.recommended,
  react.configs.flat.recommended,
  reactHooks.configs['recommended-latest'],
  promise.configs['flat/recommended'],
  jestDom.configs["flat/recommended"],
  testingLibrary.configs['flat/react'],
  importPlugin.flatConfigs.recommended,
  cspellESLintPluginRecommended,
  formatjs.configs.recommended,
  //   eslintPluginPrettierRecommended,
  //   eslintConfigPrettier,
  pluginCypress.configs.recommended,
  {
    extends: compat.extends(
        "plugin:lodash/recommended",
    ),

    plugins: {
        lodash,
    },

    languageOptions: {
        globals: {
            ...globals.browser,
            ...globals.amd,
            ...globals.node,
            ...globals.jest,
            React: "readonly",
            Promise: "readonly",
            WeakMap: "readonly",
            JSX: true,
        },

        parser: tseslint.parser,
    },

    settings: {
        react: {
            version: "detect",
        },
        'import/resolver': {
            typescript: {},
            // node: {
            //   extensions: ['.js', '.jsx', '.ts', '.tsx', '.d.ts'],
            // }
          },
    },

    rules: {
        "promise/no-return-wrap": "warn",
        "promise/param-names": "warn",
        "promise/no-native": "off",
        "promise/no-promise-in-callback": "warn",
        "promise/no-callback-in-promise": "off",
        "lodash/prefer-lodash-method": "off",
        "lodash/import-scope": "off",
        "lodash/path-style": ["warn", "as-needed"],
        "lodash/prefer-noop": "off",
        "no-debugger": "warn",
        "no-var": "warn",

        "no-unused-vars": ["warn", {
            vars: "all",
            args: "none",
        }],

        "block-scoped-var": "warn",
        curly: "warn",
        "no-tabs": "warn",
        "react/jsx-no-bind": "off",

        "react/jsx-handler-names": ["off", {
            eventHandlerPropPrefix: "",
        }],

        "react/jsx-key": "warn",
        "react/jsx-no-comment-textnodes": "warn",

        "react/jsx-pascal-case": ["warn", {
            allowAllCaps: true,
        }],

        "react/no-did-mount-set-state": "warn",
        "react/no-did-update-set-state": "warn",

        "react/self-closing-comp": ["warn", {
            component: true,
            html: true,
        }],

        "react/sort-comp": "warn",
        "react/prefer-es6-class": "warn",
        "react/style-prop-object": "warn",
        "react/no-unescaped-entities": "warn",
        "react/default-props-match-prop-types": "warn",
        "react/forbid-foreign-prop-types": "warn",
        "react/no-access-state-in-setstate": "warn",
        "react/no-children-prop": "warn",
        "react/no-typos": "warn",
        "react/no-this-in-sfc": "warn",
        "react/no-unused-prop-types": "warn",
        "import/no-cycle": "warn",
        "@typescript-eslint/prefer-function-type": ["error"],
        "@typescript-eslint/unified-signatures": "error",
        "@typescript-eslint/consistent-type-definitions": "error",

        "@typescript-eslint/explicit-member-accessibility": ["off", {
            accessibility: "explicit",
        }],

        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-empty-interface": "error",
        "@typescript-eslint/no-inferrable-types": "error",
        "@typescript-eslint/no-misused-new": "error",
        "@typescript-eslint/no-non-null-assertion": "error",
        "@typescript-eslint/type-annotation-spacing": "off",
        semi: "off",
        indent: "off",
        "comma-dangle": "off",
        "react/jsx-curly-newline": "off",
        "react/state-in-constructor": "off",
        "react/static-property-placement": "off",
        "react/jsx-props-no-spreading": "off",
        "import/named": 2,
        "import/namespace": 2,
        "import/default": 2,
        "import/export": 2,
        "import/prefer-default-export": "off",
        "max-len": "off",
        "arrow-parens": "off",
        "padded-blocks": "off",
        "no-undef": "off",
        "react/jsx-filename-extension": "off",
        "react/jsx-indent": "off",
        "no-param-reassign": "off",
        "class-methods-use-this": "off",
        "object-curly-newline": "off",
        "react/no-array-index-key": "off",
        "no-confusing-arrow": "off",
        "no-unsafe-optional-chaining": "off",
        "prefer-promise-reject-errors": "off",
        "function-paren-newline": "off",
        "react/jsx-wrap-multilines": "off",
        "react/jsx-closing-tag-location": "off",
        "react/require-default-props": "off",
        "operator-linebreak": "off",
        "react/forbid-prop-types": "off",
        "implicit-arrow-linebreak": "off",
        "import/no-named-as-default": "off",
        "import/extensions": "off",
        "consistent-return": "off",
        "react/jsx-closing-bracket-location": "off",
        "no-nested-ternary": "off",
        "func-names": "off",
        "no-restricted-syntax": "off",
        camelcase: "off",
        "no-return-assign": "off",
        "no-mixed-operators": "off",
        "react/no-danger": "off",
        "operator-assignment": "off",
        "no-bitwise": "off",
        "jsx-a11y/accessible-emoji": "off",
        "template-curly-spacing": "off",
        "no-restricted-globals": "off",
        "react/jsx-boolean-value": "off",
        "import/no-named-as-default-member": "off",
        "jsx-a11y/img-redundant-alt": "off",

        "no-use-before-define": ["off", {
            variables: false,
        }],

        "react/jsx-no-duplicate-props": ["error", {
            ignoreCase: false,
        }],

        "prefer-rest-params": "off",
        "promise/always-return": "off",
        "promise/no-nesting": "off",
        "require-await": "off",
        "react/function-component-definition": "off",
        "react/jsx-no-useless-fragment": "off",
        "default-param-last": "off",
        eqeqeq: ["error", "always"],
        "arrow-body-style": "error",
        "eol-last": "warn",
        "guard-for-in": "error",
        "no-restricted-modules": ["error", "rxjs/Rx"],
        "no-caller": "error",
        "prefer-const": "error",
        "spaced-comment": "warn",
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",
        "react/jsx-uses-react": "off",
        "react/react-in-jsx-scope": "off",
        "lodash/prefer-invoke-map": "off",
        '@cspell/spellchecker': [
          'error',
          {
            // TODO: Do we want to enable any of these?
            checkIdentifiers: false,
            checkComments: false,
            ignoreImports: true,
          }
        ],

        // TODO: Enable and fix
        'formatjs/no-literal-string-in-jsx': 'off',
        'formatjs/enforce-description': 'off',

        "@typescript-eslint/no-unused-vars": [
          "error",
          {
            "args": "all",
            "argsIgnorePattern": "^_",
            "caughtErrors": "all",
            "caughtErrorsIgnorePattern": "^_",
            "destructuredArrayIgnorePattern": "^_",
            "varsIgnorePattern": "^_",
            "ignoreRestSiblings": true
          }
        ],
        "@typescript-eslint/no-unused-expressions": [
          "error",
          {
            allowShortCircuit: true,
            allowTernary: true,
            // TODO: Does this make sense?
            enforceForJSX: false,
          }
        ],
        // TODO: Enable rules below here and fix issues
        // "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-require-imports": "off",
        "promise/catch-or-return": "off",
        "promise/valid-params": "off",
    },
}, {
    files: ["**/*.ts", "**/*.tsx"],

    rules: {
        "no-unused-vars": "off",
        "import/named": "off",
        "no-useless-escape": "off",
        "no-undef": "off",
        "no-redeclare": "off",
        "react/prop-types": "off",
        "react/no-unused-prop-types": "off",
        "@typescript-eslint/no-redeclare": ["error"],
        "lodash/prefer-lodash-typecheck": "off",
        "lodash/prop-shorthand": "off",
        "testing-library/no-wait-for-multiple-assertions": "off",
    },
}, {
    files: ["./test/**/*.ts", "./test/**/*.js", "**/*.test.tsx", "**/*.test.ts"],

    rules: {
        "no-import-assign": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/no-unused-expressions": "off",
        "@typescript-eslint/no-explicit-any": "off",
    },
}, {
    files: ["./**/*.ts"],
    ignores: ["./**/*.spec.ts", "./**/*.test.ts"],
}, {
    files: ["**/*.test.ts", "**/*.spec.ts", "**/*.test.tsx", "**/*.spec.tsx"],
    rules: {
        '@cspell/spellchecker': 'off',
    },
},
{
    // TODO: Add french dictionary and update this rule
    files: ["**/fr-CA.tsx"],
    rules: {
        '@cspell/spellchecker': 'off',
    },
},
  {
    files: ['cypress/**/*.{js,ts,jsx,tsx}'],
    rules: {
      '@typescript-eslint/no-floating-promises': 'off',
      '@typescript-eslint/no-misused-promises': 'off',
      'cypress/no-assigning-return-values': 'error',
      'cypress/no-unnecessary-waiting': 'error',
      'no-trailing-spaces': 'error',
      indent: ['error', 2],
      'eol-last': 'error',
      'no-multiple-empty-lines': ['error', { max: 1, maxBOF: 0, maxEOF: 0 }],
      'comma-spacing': ['error', { before: false, after: true }],
      'key-spacing': ['error', { beforeColon: false, afterColon: true }],
      'object-curly-spacing': ['error', 'always'],
      'array-bracket-spacing': ['error', 'never'],
      'block-spacing': 'error',
      'space-before-blocks': 'error',
      'space-infix-ops': 'error',
    },
  },

]);
