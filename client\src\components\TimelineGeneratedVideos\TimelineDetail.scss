.timeline__detail {
  font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  padding: 16px 16px 10px;

  .timeline__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .timeline__title {
      font-size: 1em;
      font-weight: 400;
      line-height: 22px;
      text-align: left;
      margin: 0;
    }
    .timeline__status {
      font-weight: 700;
      font-size: 0.8em;
      line-height: 18px;
      padding: 1px 10px;
      border-radius: 2px;
      color: white;
      &.pending {
        background-color: var(--status-pending);
      }
      &.complete {
        background-color: var(--status-complete);
      }
      &.running {
        background-color: var(--status-running);
      }
      &.cancelled {
        background-color: var(--status-cancelled);
      }
      &.queued {
        background-color: var(--status-queued);
      }
      &.failed {
        background-color: var(--status-failed);
      }
    }
  }
  .timeline__date-created {
    font-weight: 400;
    font-size: 0.9em;
    line-height: 16px;
    color: #666;
  }

  .timeline__info {
    line-height: 16px;

    .timeline__created-by {
      font-weight: 600;
      font-size: 0.8em;
      margin-bottom: 16px;
      color: var(--text-primary);
    }

    .timeline__details {
      border: 1px solid #f0faff;
      border-radius: 8px;
      background-color: #f0faff;
      margin-bottom: 15px;
      padding: 16px;

      .timeline__info {
        margin-bottom: 10px;
        font-size: 0.7em;
        font-weight: 600;
        line-height: 16px;
        color: #424242;
      }
      .timeline__meta {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .timeline__file-name {
          font-size: 1em;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .timeline__file-size,
        .timeline__video-length {
          font-size: 0.9em;
          color: #666;
        }
        .timeline__file-size {
          display: flex;
          gap: 5px;
          .file-size-value {
            color: var(--text-primary);
          }
        }

        .timeline__video-status-not-complete {
          opacity: 0;
        }

        .timeline__video-length {
          display: flex;
          gap: 5px;
          .video-length-value {
            color: var(--text-primary);
          }
        }
      }

      .timeline__match-group {
        margin-top: 10px;
        color: #666;
        font-size: 0.9em;
        font-weight: 400;
        line-height: 16px;
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }
  .timeline__detail-divider {
    height: 1px;
    width: 100%;
    background: var(--divider);
  }
  .timeline__actions {
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
    button {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .timeline__detail-disabled-button {
      cursor: default;

      span, img, svg {
        opacity: 0.4;
      }
    }

    span {
      color: var(--text-primary);
      text-transform: none;
      font-family: 'Nunito Sans', 'Roboto', sans-serif;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
    }
  }
}
