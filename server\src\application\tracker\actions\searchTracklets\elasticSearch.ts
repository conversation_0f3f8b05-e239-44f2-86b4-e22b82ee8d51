import { Context, RequestHeader } from '@application/types';
import { FingerprintSearchResult } from '@tracker/graphQL/responses';
import {
  Tracklet,
  TrackletBoundingBox,
  ConvertedAttributes,
  BoundingPoly,
  Point,
} from '../../../../../../types/tracker';
import { queries, responses } from '@tracker/graphQL';
import { getTdos } from '../getTdos';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { isEmpty, isEqual, uniq, uniqWith } from 'lodash';
import env from '../../../../env';
import { buildAttributes } from './buildAttributes';
import NodeCache from 'node-cache';
import { generateThumbnailUrl } from '../getThumbnails/generateThumbnailUrl';
import InternalServerError from '@common/errors/InternalServerError';

const QueryLimit = 2000;
const SeriesLimit = 10000;

interface ByTracklet {
  trackletId: string;
  fileId?: never;
  startTimeMs?: number;
  stopTimeMs?: number;
}

interface ByFile {
  trackletId?: never;
  fileId: string;
  startTimeMs?: number;
  stopTimeMs?: number;
}

interface Condition {
  operator: string;
  field?: string;
  value?: string | number[];
  values?: string[] | number[];
  conditions?: Condition[];
  gt?: string;
  threshold?: number;
  k?: number;
  numCandidates?: number;
  sourceEngineIds?: string[];
}

const isBoundingPoly = (points: Point[]): points is BoundingPoly =>
  points.length === 4;

export async function seriesSearch<ReqPayload, Data>({
  searchParam,
  context,
  headers,
}: {
  searchParam: ByTracklet | ByFile;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const boundingBoxes: TrackletBoundingBox[] = [];
  const searchVars = {
    search: {
      index: ['mine'],
      seriesLimit: SeriesLimit,
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_object',
            field: 'object-recognition.series',
            query: {
              operator: 'and',
              conditions: [],
            },
            innerHits: {
              sort: [
                {
                  'object-recognition.series.start': { order: 'asc' },
                },
              ],
            },
          } as {
            operator: string;
            field: string;
            query?: {
              operator: string;
              conditions: {
                operator: string;
                field: string;
                value?: string;
                gte?: number;
                lte?: number;
              }[];
            };
            value?: string;
          },
        ],
      },
    },
  };

  if (
    searchParam.trackletId &&
    searchVars?.search?.query?.conditions?.[0]?.query
  ) {
    searchVars.search.query.conditions[0].query.conditions.push({
      operator: 'term',
      field: 'referenceId',
      value: searchParam.trackletId,
    });
  }
  if (
    searchParam.startTimeMs &&
    searchVars?.search?.query?.conditions?.[0]?.query
  ) {
    searchVars.search.query.conditions[0].query.conditions.push({
      operator: 'range',
      field: 'start',
      gte: searchParam.startTimeMs,
    });
  }
  if (
    searchParam.stopTimeMs &&
    searchVars?.search?.query?.conditions?.[0]?.query
  ) {
    searchVars.search.query.conditions[0].query.conditions.push({
      operator: 'range',
      field: 'end',
      lte: searchParam.stopTimeMs,
    });
  }
  if (searchParam.fileId) {
    searchVars.search.query.conditions.push({
      operator: 'term',
      field: 'recordingId',
      value: searchParam.fileId,
    });
    // limitation on the search query. Must have a condition in the
    // series. Without this, the search query will not return series data.
    if (
      searchParam.startTimeMs === undefined &&
      searchVars?.search?.query?.conditions?.[0]?.query
    ) {
      searchVars.search.query.conditions[0].query.conditions.push({
        operator: 'range',
        field: 'start',
        gte: 0,
      });
    }
  }
  let hasMore: boolean;
  do {
    hasMore = false;
    let nextStartTimeMs: number | undefined = undefined;
    const curBoundingBoxes: TrackletBoundingBox[] = [];
    const { searchMedia } = await callGQL<
      responses.searchMedia<{
        hits: {
          'object-recognition': {
            series: {
              start: number;
              end: number;
              boundingPoly: BoundingPoly;
              referenceId: string;
            }[];
          };
        }[];
      }>,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, searchVars);

    const results = searchMedia.jsondata?.results ?? [];
    for (const result of results) {
      hasMore = result.hits.length === SeriesLimit;
      for (const hit of result.hits) {
        const seriesData = hit?.['object-recognition']?.series ?? [];
        for (const series of seriesData) {
          const boundingPoly = series.boundingPoly.map((point) => ({
            x: point.x,
            y: point.y,
          }));
          if (isBoundingPoly(boundingPoly)) {
            curBoundingBoxes.push({
              trackletId: series.referenceId,
              boundingPoly,
              startTimeMs: series.start,
              stopTimeMs: series.end,
            });
            nextStartTimeMs = series.start;
          }
        }
      }
    }
    context?.log.info('Got bounding boxes ', curBoundingBoxes.length);
    if (hasMore) {
      if (nextStartTimeMs === undefined) {
        hasMore = false;
        context?.log.error(
          'No start time found when there are more results. This should not happen'
        );
      } else if (
        curBoundingBoxes.length > 0 &&
        curBoundingBoxes[0].startTimeMs ===
          curBoundingBoxes[curBoundingBoxes.length - 1].startTimeMs
      ) {
        nextStartTimeMs = curBoundingBoxes[0].startTimeMs + 1;
        context?.log.error(
          `Start time ${curBoundingBoxes[0].startTimeMs} is same in this query with SeriesLimit ${SeriesLimit}. This should not happen. Next start time is set to ${nextStartTimeMs}`
        );
      }
    }
    for (const curBoundingBox of curBoundingBoxes) {
      boundingBoxes.push(curBoundingBox);
    }
    if (hasMore && searchVars.search.query.conditions[0].query) {
      for (
        let i = 0;
        i < searchVars.search.query.conditions[0].query.conditions.length;
        i++
      ) {
        if (
          searchVars.search.query.conditions[0].query.conditions[i].field ===
          'start'
        ) {
          searchVars.search.query.conditions[0].query.conditions.splice(i, 1);
          break;
        }
      }
      searchVars.search.query.conditions[0].query.conditions.push({
        operator: 'range',
        field: 'start',
        gte: nextStartTimeMs,
      });
    }
  } while (hasMore);
  const dedupedBoundingBoxes = uniqWith(boundingBoxes, isEqual);
  return dedupedBoundingBoxes;
}

export async function searchTrackletById<ReqPayload, Data>({
  trackletId,
  context,
  headers,
}: {
  trackletId: string;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const limit = QueryLimit;
  const offset = 0;
  const searchVars = {
    search: {
      index: ['fingerprint'],
      limit: limit,
      offset: offset,
      select: ['id', 'referenceId', 'label'],
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'term',
            field: 'referenceId',
            value: trackletId,
          },
        ],
      },
    },
  };

  const { searchMedia } = await callGQL<
    responses.searchMedia<{
      id: string;
      label: string;
      referenceId: string;
    }>,
    ReqPayload,
    Data
  >(context, headers, queries.searchMedia, searchVars);

  const results = {
    tdoId: searchMedia.jsondata?.results?.[0].id,
    referenceId: searchMedia.jsondata?.results?.[0]?.referenceId,
    label: searchMedia.jsondata?.results?.[0].label,
  };

  return results;
}

export async function fingerprintSearch<ReqPayload, Data>({
  referenceTrackletIds,
  fileIds,
  type,
  limit,
  offset,
  context,
  headers,
  attributes,
  threshold,
  times,
  trackerEngineId,
}: {
  referenceTrackletIds?: string[];
  fileIds?: string[];
  type?: string[];
  limit: number;
  offset: number;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
  attributes?: ConvertedAttributes;
  threshold?: number;
  times?: boolean;
  trackerEngineId: string;
}) {
  const { log, cache } = context;

  // const { s3 } = env;
  if (isEmpty(type)) {
    type = ['person', 'vehicle'];
  }

  let attributesConditions: Condition[] = [];
  if (attributes) {
    attributesConditions = buildAttributesQuery(attributes);
  }

  // If we have a threshold, we need to get the vector to do an similarity search
  let fingerprintVector: number[] = [];
  if (threshold && referenceTrackletIds && referenceTrackletIds.length > 0) {
    const { searchMedia: searchMediaTracklet } = await callGQL<
      responses.fingerprintSearch,
      ReqPayload,
      Data
    >(context, headers, queries.searchMedia, {
      search: {
        index: ['fingerprint'],
        select: ['fingerprintVector'],
        limit: 1,
        offset: 0,
        query: {
          operator: 'and',
          conditions: [
            {
              operator: 'terms',
              field: 'referenceId',
              values: referenceTrackletIds,
            },
          ],
        },
      },
    });

    // We will search by vector for the similarity search
    referenceTrackletIds = [];

    fingerprintVector =
      searchMediaTracklet.jsondata.results[0].fingerprintVector;

    if (fingerprintVector.length === 0) {
      throw new InternalServerError(
        'No fingerprint vector found for the given detection'
      );
    }
  }

  const conditions: Condition[] = [
    {
      operator: 'term',
      field: 'sourceEngineId',
      value: trackerEngineId,
    },
    ...(type && type.length > 0
      ? [
          {
            operator: 'terms',
            field: 'label',
            values: type,
          },
        ]
      : []),
    ...(referenceTrackletIds && referenceTrackletIds.length > 0
      ? [
          {
            operator: 'terms',
            field: 'referenceId',
            values: referenceTrackletIds,
          },
        ]
      : []),
    ...(fileIds && fileIds.length > 0
      ? [
          {
            operator: 'terms',
            field: 'recordingId',
            values: fileIds,
          },
        ]
      : []),
    ...(threshold && fingerprintVector && fingerprintVector.length > 0
      ? [
          {
            operator: 'fingerprint_approximation',
            field: 'fingerprintVector',
            threshold,
            k: env?.approxParamK ?? 500,
            numCandidates: env?.approxParamNC ?? 1250,
            sourceEngineIds: [trackerEngineId],
            value: fingerprintVector,
          },
        ]
      : []),
    ...attributesConditions,
  ];

  // const aggregateSearchMedias: responses.aggregateFingerprintSearch = {
  //   searchMedia: {
  //     jsondata: {
  //       results: [],
  //       totalResults: 0,
  //       limit: 0,
  //       from: 0,
  //       to: 0,
  //     },
  //   },
  // };
  // let aggregateOffset = 0;
  // const aggregateLimit = 1000;
  // let aggregateCount = 0;

  // do {
  //   const { searchMedia: aggregateSearchMedia } = await callGQL<
  //     responses.aggregateFingerprintSearch,
  //     ReqPayload,
  //     Data
  //   >(context, headers, queries.searchMedia, {
  //     // Todo: Update the ES query when aggregation is implemented on the fingerprint index
  //     search: {
  //       select: ['referenceId', 'id'],
  //       index: ['fingerprint'],
  //       fields: ['recordingId'],
  //       limit: aggregateLimit,
  //       offset: 0,
  //       query: {
  //         operator: 'and',
  //         conditions,
  //       },
  //       sort: [{
  //         field: "referenceId",
  //         order: "asc"
  //       }]
  //     },
  //   });
  //   aggregateOffset += aggregateLimit;
  //   aggregateCount = aggregateSearchMedia.jsondata.results.length;

  //   const previousRefId = aggregateSearchMedia.jsondata.results?.[aggregateSearchMedia.jsondata.results.length - 1]?.referenceId
  //   if (previousRefId) {
  //     const cond = conditions.find(c => c.operator === 'range' && c.field === 'referenceId');

  //     if (cond) {
  //       cond.gt = previousRefId;
  //     } else {
  //       conditions.push({
  //         operator: 'range',
  //         field: 'referenceId',
  //         gt: previousRefId
  //       });
  //     }
  //   }

  //   if (aggregateCount > 0) {
  //     aggregateSearchMedias.searchMedia.jsondata.results.push(
  //       ...aggregateSearchMedia.jsondata.results
  //     );
  //   }
  // } while (aggregateCount === aggregateLimit);

  // const condIndex = conditions.findIndex(c => c.operator === 'range' && c.field === 'referenceId');

  // if (condIndex > -1) {
  //   conditions.splice(condIndex, 1);
  // }

  const aggregateLimit = 10000;

  const { searchMedia: aggregateSearchMedias } = await callGQL<
    responses.aggregateFingerprintSearch,
    ReqPayload,
    Data
  >(context, headers, queries.searchMedia, {
    search: {
      index: ['fingerprint'],
      fields: ['recordingId'],
      aggregate: [
        {
          name: 'recordingId',
          field: 'recordingId',
          operator: 'term',
          limit: aggregateLimit,
        },
      ],
      query: {
        operator: 'and',
        conditions,
      },
    },
  });

  const { searchMedia } = await callGQL<
    responses.fingerprintSearch,
    ReqPayload,
    Data
  >(context, headers, queries.searchMedia, {
    search: {
      index: ['fingerprint'],
      select: [
        'id',
        'score',
        'referenceId',
        'organizationId',
        'assetId',
        'tags',
        'label',
        'sourceEngineId',
        'recordingId',
        'vendor',
      ],
      limit,
      offset,
      query: {
        operator: 'and',
        conditions,
      },
    },
  });

  const searchResults = searchMedia.jsondata.results;

  const uniqTdoIds = uniq(
    aggregateSearchMedias.jsondata.aggregations.recordingId.buckets.map(
      (bucket) => bucket.key
    )
  );

  // get FileName
  const tdoIds = searchResults.map(
    (result: FingerprintSearchResult) => result.id
  );
  let tdos: { [key: string]: { id: string; name: string } } = {};
  if (tdoIds.length > 0) {
    tdos = await getTdos({
      tdoIds,
      context,
      headers,
    });
  }
  const includeThumbnail = context.req.query.thumbnails === 'true';
  const includeTime = !!times;
  const tracklets: Tracklet[] = [];
  const batchQuerySize = 10;
  for (let i = 0; i < searchResults.length; i += batchQuerySize) {
    const batch = searchResults.slice(i, i + batchQuerySize);
    const trackletPromises = batch.map((result) =>
      buildTrackletFromSearchResult({
        searchResult: result,
        tdos,
        log,
        cache,
        includeThumbnail,
        includeTime,
        context,
        headers,
        trackerEngineId,
      })
    );
    const trackletBatch = await Promise.all(trackletPromises);

    tracklets.push(...trackletBatch);
  }

  return {
    tdoIds: uniqTdoIds,
    tracklets,
    totalResults: searchMedia.jsondata.totalResults,
    limit: searchMedia.jsondata.limit,
    from: searchMedia.jsondata.from,
    to: searchMedia.jsondata.to,
  };
}

export async function trackletTimeSearch<ReqPayload, Data>({
  trackletId,
  context,
  headers,
}: {
  trackletId: string;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const querySearchMedia = `
  query searchMedia($startTimeSearch: JSONData!, $endTimeSearch: JSONData!) {
    startTimeSearchMedia:searchMedia(search: $startTimeSearch) {
      jsondata
    }
    endTimeSearchMedia:searchMedia(search: $endTimeSearch) {
      jsondata
    }
  }
`;

  const searchVars = {
    startTimeSearch: {
      index: ['mine'],
      seriesLimit: 1,
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_object',
            field: 'object-recognition.series',
            query: {
              operator: 'and',
              conditions: [
                {
                  operator: 'term',
                  field: 'referenceId',
                  value: trackletId,
                },
              ],
            },
            innerHits: {
              sort: [
                {
                  'object-recognition.series.start': { order: 'asc' },
                },
              ],
            },
          },
        ],
      },
    },
    endTimeSearch: {
      index: ['mine'],
      seriesLimit: 1,
      query: {
        operator: 'and',
        conditions: [
          {
            operator: 'query_object',
            field: 'object-recognition.series',
            query: {
              operator: 'and',
              conditions: [
                {
                  operator: 'term',
                  field: 'referenceId',
                  value: trackletId,
                },
              ],
            },
            innerHits: {
              sort: [
                {
                  'object-recognition.series.end': { order: 'desc' },
                },
              ],
            },
          },
        ],
      },
    },
  };

  const { startTimeSearchMedia, endTimeSearchMedia } = await callGQL<
    {
      startTimeSearchMedia: {
        jsondata: {
          results: {
            hits: {
              'object-recognition': {
                series: {
                  start: number;
                  end: number;
                }[];
              };
            }[];
          }[];
        };
      };
      endTimeSearchMedia: {
        jsondata: {
          results: {
            hits: {
              'object-recognition': {
                series: {
                  start: number;
                  end: number;
                }[];
              };
            }[];
          }[];
        };
      };
    },
    ReqPayload,
    Data
  >(context, headers, querySearchMedia, searchVars);

  let startTime: number | undefined = undefined;
  let endTime: number | undefined = undefined;
  const startTimeResults = startTimeSearchMedia.jsondata?.results ?? [];
  startTimeOuterLoop: for (const result of startTimeResults) {
    for (const hit of result.hits) {
      const seriesData = hit?.['object-recognition']?.series ?? [];
      for (const series of seriesData) {
        startTime = series.start;
        // the query should return only one result with the start time.
        // But, just in case, we break the loop if we find first one.
        if (startTime !== undefined) {
          break startTimeOuterLoop;
        }
      }
    }
  }
  const endTimeResults = endTimeSearchMedia.jsondata?.results ?? [];
  endTimeOuterLoopFor: for (const result of endTimeResults) {
    for (const hit of result.hits) {
      const seriesData = hit?.['object-recognition']?.series ?? [];
      for (const series of seriesData) {
        endTime = series.end;
        // the query should return only one result with the end time.
        // But, just in case, we break the loop if we find first one.
        if (endTime !== undefined) {
          break endTimeOuterLoopFor;
        }
      }
    }
  }
  return { startTime, endTime };
}

export async function buildTrackletFromSearchResult<ReqPayload, Data>({
  searchResult,
  tdos,
  includeThumbnail,
  includeTime,
  cache,
  log,
  context,
  headers,
  trackerEngineId,
}: {
  searchResult: FingerprintSearchResult;
  tdos: { [key: string]: { id: string; name: string } };
  cache?: NodeCache;
  includeThumbnail?: boolean;
  includeTime: boolean;
  log?: Logger;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
  trackerEngineId: string;
}) {
  let startTimeMs;
  let stopTimeMs;
  if (searchResult.vendor?.startTimeMs && searchResult.vendor?.stopTimeMs) {
    startTimeMs = searchResult.vendor?.startTimeMs;
    stopTimeMs = searchResult.vendor?.stopTimeMs;
  } else if (includeTime) {
    const { startTime, endTime } = await trackletTimeSearch({
      trackletId: searchResult.referenceId,
      context,
      headers,
    });
    startTimeMs = startTime;
    stopTimeMs = endTime;
  }
  const { attributes, unknownKey } = buildAttributes(
    searchResult.tags,
    trackerEngineId
  );
  if (unknownKey.length > 0) {
    log?.error(
      `Unknown attributes - tdo:${searchResult.id}, tracklet:${searchResult.referenceId}, tag.key: ${unknownKey.join(', ')}`
    );
  }
  const tdoId = searchResult.id;
  let thumbnailUrlBest = '';
  if (includeThumbnail) {
    const orgId = searchResult.organizationId;
    // The best thumbnail is used,
    thumbnailUrlBest = await generateThumbnailUrl({
      orgId,
      tdoId,
      trackletId: searchResult.referenceId,
      type: 'best',
      cache,
    });
  }

  const tracklet: Tracklet = {
    orgId: searchResult.organizationId,
    trackletId: searchResult.referenceId,
    fileId: searchResult.id,
    fileName: tdos[tdoId]?.name,
    startTimeMs: startTimeMs ?? 0,
    stopTimeMs: stopTimeMs ?? 0,
    attributes,
    thumbnailUrls: { best: thumbnailUrlBest },
    type: searchResult.label,
    confidence: searchResult.score ?? 0,
  };
  return tracklet;
}

function buildAttributesQuery(
  selectedAttributes: ConvertedAttributes
): Condition[] {
  const conditions: Condition[] = [];
  Object.keys(selectedAttributes).forEach((category) => {
    const attributeCondition: Condition = {
      operator: 'and',
      conditions: [
        {
          operator: 'term',
          field: 'tags.key',
          value: category,
        },
        {
          operator: 'terms',
          field: 'tags.value',
          values: selectedAttributes[category],
        },
      ],
    };

    conditions.push(attributeCondition);
  });

  return conditions;
}
