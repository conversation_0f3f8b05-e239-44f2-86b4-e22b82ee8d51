import { select, SelectEffect } from 'redux-saga/effects';
import { combineSlices, configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware, { Middleware } from 'redux-saga';
import { apiMiddleware } from 'redux-api-middleware';
import { createLogger as createLoggerMiddleware } from 'redux-logger';
import HttpClient from './dependencies/httpClient';
import rootSaga from './rootSaga';
import { homeSlice, HomeSliceState } from './modules/home/<USER>';
import { eventSlice, EventSliceState } from './modules/event/slice';
import { fileSlice, FileSliceState } from './modules/file/slice';
import { AppBarState, appSlice } from './modules/app/slice';
import { configSlice } from "./modules/config/slice";
import { locationSlice, LocationState } from './modules/location/slice';
import { uploadSlice, UploadSliceState } from './modules/upload/slice';
import { AuthState, modules, UserState } from '@veritone/glc-redux';
import { searchResultsSlice, SearchResultsSliceState } from './modules/searchResults/slice';
import { matchGroupSlice, matchGroupSliceState } from './modules/matchGroup/slice';
import { timelineEditorSlice, TimelineEditorSliceState } from './modules/timelineEditor/slice';

const { auth, user, config } = modules;
const { namespace: userNamespace, reducer: userReducer } = user;
const { namespace: authNamespace, reducer: authReducer } = auth;

const rootReducer = combineSlices(
  homeSlice,
  appSlice,
  eventSlice,
  fileSlice,
  locationSlice,
  configSlice,
  uploadSlice,
  searchResultsSlice,
  matchGroupSlice,
  timelineEditorSlice,
  {
    [userNamespace]: userReducer,
    [authNamespace]: authReducer,
    [config.namespace]: (state = window.config ?? {}) => state,
  }
);

const shouldHaveDevTools = () => (location.href.includes('stage') || (location.href.includes('dev')) || location.href.includes('local.veritone')) && window.config?.nodeEnv !== 'test';

// TODO: What was the purpose of this?
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const extraArgument = { http: new HttpClient(window.config) };

export const configureAppStore = (preloadedState = {}) => {
  const reduxSagaMonitorOptions = {};
  const sagaMiddleware = createSagaMiddleware(reduxSagaMonitorOptions);
  // const customMiddleware = createMiddleware();
  const middleware: Middleware[] = [apiMiddleware as Middleware, sagaMiddleware];

  if (shouldHaveDevTools()) {
    middleware.push(createLoggerMiddleware({}) as Middleware);
  }

  const store = configureStore({
    reducer: rootReducer,
    middleware: (gDM) => gDM({
      serializableCheck: false,
      thunk: {
        extraArgument: { http: new HttpClient(window.config) }
      }
    }).concat([...middleware]),
    preloadedState,
    devTools: shouldHaveDevTools(),
  });

  sagaMiddleware.run(rootSaga);

  return store;
};

export const store = configureAppStore();

export interface RootState {
  home: HomeSliceState;
  app: AppBarState;
  file: FileSliceState;
  event: EventSliceState;
  location: LocationState;
  upload: UploadSliceState;
  matchGroup: matchGroupSliceState;
  searchResults: SearchResultsSliceState;
  timeline: TimelineEditorSliceState;
  config: Window['config'];
  user: UserState;
  auth: AuthState & { readonly userId: string };
}

export type ExtraArgument = typeof extraArgument;

export type AppStore = typeof store;
export type AppDispatch = AppStore["dispatch"];

export function selectState<T>(selector: (s: RootState) => T): SelectEffect {
  return select(selector);
}

export function selectUserState(s: RootState): UserState {
  return s.user;
}
