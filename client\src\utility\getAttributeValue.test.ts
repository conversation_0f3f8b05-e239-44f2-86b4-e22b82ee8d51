import { getAttributeValue, AttributeValueMap } from './getAttributeValue';

describe('getAttributeValue', () => {
    it('should return the correct value for existing keys', () => {
        ['Muffler', 'Trousers', 'bmw', 'f12-berlinetta'].forEach((key) => {
            expect(getAttributeValue(key)).toBe(AttributeValueMap[key]);
        });
    });

    it('should return the provided key for non-existing keys', () => {
        expect(getAttributeValue('Black')).toBe('Black');
        expect(getAttributeValue('UnknownKey')).toBe('UnknownKey');
    });

    // TODO: What is this testing, the function is only defined to take a string
    // either function type should be updated or this should be removed. most likely the function
    // type definition should be updated
    it('should return the provided key for null or undefined', () => {
        expect(getAttributeValue(null as any)).toBe(null);
        expect(getAttributeValue(undefined as any)).toBe(undefined);
    });
});
