import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  Select,
  SelectChangeEvent,
  Skeleton,
  Slider,
  styled,
  Tooltip,
} from '@mui/material';
import {
  DetectedAttributes,
  Pagination,
  Tracklet as TrackletComp,
  TrackletLoading
} from '@components/common';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import {
  getBoundingBoxes,
  getEvent,
  getFile,
  getMatchGroup,
  getSearchResultsById,
  selectAllTracklets,
  selectBoundingBoxes,
  selectFileFilter,
  selectMatchGroup,
  selectSearchResults,
  selectSelectedTracklet,
  selectSelectedTrackletFile,
  setSearchResultsPagination,
  setSelectedTracklet,
  unselectAllTracklets,
  selectConfidenceThreshold,
  setConfidenceThreshold,
  getThumbnails,
  selectThumbnails,
  setAttributes,
} from '@store/modules/searchResults/slice';
import { useNavigate } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { useAppDispatch } from '@store/hooks';
import { useSelector } from 'react-redux';
import { range, uniq, throttle } from 'lodash';
import { Tracklet } from '@shared-types/tracker';
import TrackletSelectionHeader from '@components/TrackletSelectionHeader/TrackletSelectionHeader';
import FileMetadata from '@components/common/FileMetadata/FileMetadata';
import { MediaPlayer } from '@veritone/glc-react';
import { PlayerReference } from 'video-react';
import { frameAlignStartTimeMs, frameAlignStopTimeMs } from '@utility/frames';
import ThumbnailScaler from '@components/common/ThumbnailScaler/ThumbnailScaler';
import { I18nTranslate } from '@i18n';
import ls from 'localstorage-slim';
import './SearchResults.scss';
import SearchAllFilesDialog from '@components/SearchAllFilesDialog/SearchAllFilesDialog';
import cn from 'classnames';

interface Props {
  eventId?: string;
  matchGroupId?: string;
  searchId?: string;
}

const PLAYER_LOADED = 1;

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&::before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  flexDirection: 'row',
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  paddingTop: 0,
  paddingLeft: theme.spacing(2),
  paddingBottom: theme.spacing(2),
}));

/* eslint-disable react-hooks/exhaustive-deps */

const SearchResults = (props: Props) => {
  const { eventId, matchGroupId, searchId } = props;
  const [expanded, setExpanded] = useState<
    '' | 'attributes' | 'file-meta' | 'ai-engines'
  >('');
  const [thumbnailScale, setThumbnailScale] = useState(Number(localStorage.getItem('thumbnailScale') ?? 100));
  const [selectViewingTracklet, setSelectViewingTracklet] = useState<Tracklet | undefined>(undefined);
  const [showConfidenceScore, setShowConfidenceScore] = useState(Boolean(ls.get('showConfidenceScore') ?? false));
  const [isPlaying, setIsPlaying] = useState(false);
  const [openAttributeDialog, setOpenAttributeDialog] = useState(false);
  const [movieDoneLoading, setMovieDoneLoading] = useState(false);
  const [mediaContainerHeight, setMediaContainerHeight] = useState(390);

  const playerRef = useRef<PlayerReference | null>(null);

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const searchResults = useSelector(selectSearchResults);
  const selectedTracket = useSelector(selectSelectedTracklet);
  const selectedTrackletFile = useSelector(selectSelectedTrackletFile);
  const matchGroup = useSelector(selectMatchGroup);
  const filesFilter = useSelector(selectFileFilter);
  const confidenceThreshold = useSelector(selectConfidenceThreshold);
  const boundingBoxes = useSelector(selectBoundingBoxes);
  const thumbnails = useSelector(selectThumbnails);

  const selectedMatchGroup = matchGroup.data?.searches?.find((s) => s.id === searchId);
  const referenceId = selectedMatchGroup?.referenceTrackletId;
  const trackletsInMatchGroup = matchGroup.data?.selectedTracklets;

  const fileIds = filesFilter?.fileIds;
  const searchResultsLoading = searchResults.apiStatus === 'loading';
  const currentFile = selectedTrackletFile.file;
  const fileLoading = selectedTrackletFile.apiStatus === 'loading';
  const noFile = !currentFile && !fileLoading;
  const hasFile = currentFile && !fileLoading;

  const streams = currentFile?.streams;
  const thumbnailAssets = currentFile?.thumbnailAssets;
  const frameRate = currentFile?.frameRate ?? 30;
  const isFileAndBoundingBoxesLoaded = selectedTrackletFile?.selectedTracklet?.trackletId === boundingBoxes[0]?.trackletId;

  const isAttributeSearch = !referenceId && matchGroup.data && !searchResultsLoading;
  const selectedSearchAttributes = selectedMatchGroup?.attributes;

  const { pageSize, currentPage } = searchResults;

  // Autoplay logic
  const autoplayStatusRef = useRef<'' | 'Seeking' | 'Seeked' | 'Playing' | 'Done'>('');
  const autoplayStopRef = useRef(0);
  const seekToAfterFileSet = useRef({ trackletId: '', seekTime: 0 });
  const previousPlayRef = useRef<'' | 'Play' | 'Pause'>('');
  //  Make an educated guess at the player's initial height based on the current
  //  window's width, and the player's height for a 16:9 video.
  const playerHeightRef = useRef<number | undefined>(window.innerWidth / 4.4);
  const mediaContainerRef = useRef<HTMLDivElement | null>(null);
  const skeletonContainerRef = useRef<HTMLDivElement | null>(null);

  const setDefaultAutoplay = () => {
    autoplayStatusRef.current = '';
    autoplayStopRef.current = 0;
  };

  useEffect(() => {
    const missingTracklets = searchResults.results?.reduce<
      Array<{ trackletId: string; orgId: string; fileId: string }>
    >((arr, { trackletId, orgId, fileId }) => {
      const thumbnailInCache = thumbnails && trackletId in thumbnails;
      const isExpired = thumbnailInCache && new Date(thumbnails[trackletId].expiresDateTime) <= new Date();

      if (!thumbnailInCache || isExpired) {
        arr.push({ trackletId, orgId, fileId });
      }
      return arr;
    }, []);

    if (missingTracklets?.length) {
      dispatch(getThumbnails(missingTracklets));
    }
  }, [searchResults]);

  useEffect(() => {
    if (isFileAndBoundingBoxesLoaded) {
      playerRef.current?.subscribeToStateChange((state) => {
        setIsPlaying(!state.paused);

        // 1.) Track when seeking
        if (autoplayStatusRef.current === 'Seeking' && !state.seeking) {
          autoplayStatusRef.current = 'Seeked';
        }
        // 2.) Track when seeked
        else if (autoplayStatusRef.current === 'Seeked' && !state.paused) {
          autoplayStatusRef.current = 'Playing';
          playerRef.current?.play();
          if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        } else if (autoplayStatusRef.current === 'Seeked' && state.paused) {
          if (previousPlayRef.current === 'Pause' || previousPlayRef.current === '') {
            playerRef.current?.pause();
          } else if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        }
        // 3.) Track while playing
        else if (autoplayStatusRef.current === 'Playing' && !state.paused) {
          previousPlayRef.current = 'Play';
          const alignedStopTimeMs = frameAlignStopTimeMs(autoplayStopRef.current, frameRate);
          const frameAlignCurrentTimeMs = frameAlignStartTimeMs(state.currentTime * 1000, frameRate);
          if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
            autoplayStatusRef.current = 'Done';
            playerRef.current?.pause();
            previousPlayRef.current = 'Pause';
          }
        } else if (autoplayStatusRef.current === 'Playing' && state.paused) {
          previousPlayRef.current = 'Pause';
          playerRef.current?.pause();
        }
        // 4.) Track when done playing
        else if (autoplayStatusRef.current === 'Done') {
          setDefaultAutoplay();
        }

        if (state.readyState === PLAYER_LOADED) {
          setMovieDoneLoading(true);
          //  Get the height of the player to be used for the skeleton when loading, and for the
          //  player's container when it is not loading
          const mediaPlayerEl = mediaContainerRef.current?.getElementsByClassName('media-player')[0];
          if (mediaPlayerEl) {
            playerHeightRef.current = mediaPlayerEl.getBoundingClientRect().height;
          }
        }
      });
    }
  }, [isFileAndBoundingBoxesLoaded, frameRate]);

  useEffect(() => {
    if (!eventId) {
      return navigate(`/`, { replace: true });
    }

    if (!matchGroupId) {
      return navigate(`/event/${eventId}`, { replace: true });
    }
    dispatch(getEvent({ eventId }));
    dispatch(getMatchGroup({ matchGroupId }));
    getSearchResultsByIdRequest();
  }, [eventId, matchGroupId, searchId]);

  useEffect(() => {
    if (
      matchGroup.data && searchId &&
      !selectedMatchGroup
    ) {
      navigate(`/event/${eventId}`);
    }
  }, [matchGroup]);

  useEffect(() => {
    if (selectedTrackletFile?.selectedTracklet && boundingBoxes.length && isFileAndBoundingBoxesLoaded) {

      let frameIndexId: number | undefined = undefined;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const autoplayTrackletStartToStop = (stopTimeMs: number, player: PlayerReference) => {
        const alignedStopTimeMs = frameAlignStopTimeMs(stopTimeMs, frameRate);

        const currentTime = player.getState().player?.currentTime;
        const frameAlignCurrentTimeMs = frameAlignStartTimeMs(currentTime * 1000, frameRate);
        if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
          playerRef.current?.pause();
          if (frameIndexId !== undefined) {
            cancelAnimationFrame(frameIndexId);
            frameIndexId = undefined;
          }
        } else {
          frameIndexId = requestAnimationFrame(() => autoplayTrackletStartToStop(alignedStopTimeMs, player));
        }
      };

      if (selectedTrackletFile?.selectedTracklet && boundingBoxes.length) {
        if (seekToAfterFileSet.current.trackletId === selectedTrackletFile?.selectedTracklet.trackletId) {
          // Bypass normal autoplay mode if a timestamp was clicked on a tracklet
          const seekTime = Math.ceil((seekToAfterFileSet.current.seekTime / 1000) * frameRate) / frameRate;
          playerRef.current?.seek(seekTime);
          seekToAfterFileSet.current.trackletId = '';
        } else {
          // Normal autoplay mode (stop after over)
          autoplayStopRef.current = selectedTrackletFile?.selectedTracklet.stopTimeMs;
          autoplayStatusRef.current = 'Seeking';
          const seekTime = Math.ceil((selectedTrackletFile?.selectedTracklet.startTimeMs / 1000) * frameRate) / frameRate;
          playerRef.current?.seek(seekTime);
        }
      }
    }
  }, [boundingBoxes, selectedTrackletFile, isFileAndBoundingBoxesLoaded]);

  useEffect(() => {
    if (!fileLoading && selectViewingTracklet) {
      setTimeout(() => setExpanded('attributes'), 400);  // expand the details section
      if (mediaContainerRef.current?.style) {
        mediaContainerRef.current.style.height = `${playerHeightRef.current}px`;
      }
    } else {
      setExpanded('');  //  collapse the details section
      setMovieDoneLoading(false);
      if (skeletonContainerRef.current?.style) {
        skeletonContainerRef.current.style.height = `${playerHeightRef.current}px`;
      }
    }
  }, [fileLoading, selectViewingTracklet]);

  useEffect(() => {
    //  Listen for resizing events and adjust the variable (playerSkeletonHeightReef) that keeps track of
    //  the height of the player, so we know how high to make the skeleton and mediaPlayerBoundBox that
    //  backs the media player.
    const handleResize = () => {
      const mediaPlayerEl = mediaContainerRef.current?.getElementsByClassName('media-player')[0];
        if (mediaPlayerEl) {
          playerHeightRef.current = mediaPlayerEl.getBoundingClientRect().height;
          setMediaContainerHeight(playerHeightRef.current);
        }
    };

    const handleResizeThrottled = throttle(handleResize, 200,
      {
        leading: false,
        trailing: true
      });

    window.addEventListener('resize', handleResizeThrottled);
    return () => {
      window.removeEventListener('resize', handleResizeThrottled);
    };

  }, []);

  useEffect(() => {
    if (selectViewingTracklet) {
      dispatch(getFile({ tracklet: selectViewingTracklet }));
      dispatch(getBoundingBoxes({ trackletId: selectViewingTracklet.trackletId }));
    }
  }, [selectViewingTracklet, dispatch]);

  const getFileAndSeekTo = (tracklet: Tracklet, time: number) => {
    if (tracklet.trackletId === selectedTrackletFile?.selectedTracklet?.trackletId) {
      const seekTime = Math.ceil((time / 1000) * frameRate) / frameRate;
      playerRef.current?.seek(seekTime);
    }
    setSelectViewingTracklet(tracklet);
    seekToAfterFileSet.current = { trackletId: tracklet.trackletId, seekTime: time };
  };

  const handleDetailAccordionChange =
    (panel: 'attributes' | 'file-meta' | 'ai-engines') =>
      (_event: React.SyntheticEvent, newExpanded: boolean) => {
        setExpanded(newExpanded ? panel : '');
      };

  const handleTrackletClick = (tracklet: Tracklet, event?: React.MouseEvent) => {
    dispatch(setSelectedTracklet({ tracklet, shiftKey: event?.shiftKey }));
    setDefaultAutoplay();
  };

  const offsetPage = (pageOffset: number) => () => {
    const { currentPage, pageSize } = searchResults;
    dispatch(
      setSearchResultsPagination({
        pageSize,
        currentPage: currentPage + pageOffset,
      })
    );
    getSearchResultsByIdRequest(currentPage + pageOffset);
  };


  const changePage = (page: number) => {
    const { pageSize } = searchResults;
    dispatch(
      setSearchResultsPagination({
        pageSize,
        currentPage: page,
      })
    );
    getSearchResultsByIdRequest(page);
  };


  const setPageSize = () => (pageSize: number) => {
    dispatch(setSearchResultsPagination({ pageSize, currentPage: 1 }));
    getSearchResultsByIdRequest(1, pageSize);
  };

  const handleMatchSearchChange = (event: SelectChangeEvent<string>) => {
    navigate(
      `/event/${eventId}/match-group/${matchGroupId}/potential-match-search/${event.target.value}`
    );
    dispatch(setAttributes({ searchId: event.target.value }));
  };

  const handleShowConfidenceScoreChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowConfidenceScore(event.target.checked);
    ls.set('showConfidenceScore', event.target.checked);
  };

  const getSearchResultsByIdRequest = (page?: number, limit?: number, newFileIds?: string[]) => {
    if (searchId) {
      dispatch(
        getSearchResultsById({
          matchGroupId,
          searchId,
          fileIds: uniq(newFileIds ? newFileIds : fileIds),
          page: page ? page : currentPage,
          limit: limit ?? pageSize
        })
      );
    }
  };

  const getUserSelectedTrackletFile = (tracklet: Tracklet) => {
    setSelectViewingTracklet(tracklet);
  };

  const handleSelectAllTracklets = () => {
    dispatch(selectAllTracklets({ tracklets: searchResults?.results ?? [] }));
  };

  const handleUnselectAllTracklets = () => {
    dispatch(unselectAllTracklets());
  };

  const confidenceContent = (
    <div className="search-results__header-container-footer-confidence">
      <span className={cn('search-results__header-container-footer-confidence-text', { disabled: !referenceId })}>{I18nTranslate.TranslateMessage('fewerMatches')}</span>
      <Slider
        className="search-results__header-container-footer-confidence-slider"
        data-testid="search-results-confidence-slider"
        value={1 - confidenceThreshold}
        min={0}
        max={0.94}
        step={0.02}
        onChange={(_, newValue) =>
          dispatch(setConfidenceThreshold(1 - (Array.isArray(newValue) ? newValue[0] : newValue)))
        }
        onChangeCommitted={() => getSearchResultsByIdRequest()}
        disabled={referenceId ? false : true}
      />
      <span className={cn('search-results__header-container-footer-confidence-text', { disabled: !referenceId })}>{I18nTranslate.TranslateMessage('moreMatches')}</span>
      <FormGroup className="search-results__header-container-footer-confidence-checkbox">
        <FormControlLabel
          control={
            <Checkbox
              sx={{ width: 20, height: 20, padding: 0 }}
              checked={referenceId ? showConfidenceScore : false}
              onChange={handleShowConfidenceScoreChange}
              data-testid="search-results-confidence-checkbox"
            />
          }
          disabled={referenceId ? false : true}
          label={<span className="search-results__header-container-footer-confidence-checkbox-label">{I18nTranslate.TranslateMessage('showMatchScore')}</span>}
        />
      </FormGroup>
    </div>
  );

  return (
    <div className="search-results" data-testid="search-results">
      <div className="search-results__main-content">
        <div className="search-results__video_and_attributes">
          <div className="search-results__potential-match-search">
            <div className="search-results__potential-match-search-label">
              {I18nTranslate.TranslateMessage('selectPotentialMatchSearch')}
            </div>
            <FormControl fullWidth>
              <Select
                labelId="potential-match-search-drop-label"
                value={
                  !matchGroup.data?.searches ||
                    matchGroup.data?.searches?.length === 0
                    ? ''
                    : searchId ?? ''
                }
                onChange={handleMatchSearchChange}
                data-testid="potential-match-search-drop"
              >
                {matchGroup.data?.searches?.map((s) => (
                  <MenuItem
                    data-testid={`potential-match-search-drop-${s.id}`}
                    key={`MatchGroupSearchDrop-${s.id}`}
                    value={s.id}
                  >
                    {s.searchName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          {isAttributeSearch && (
            <div className="search-results__view-modify-attribute-button-container">
              <Button
                color="primary"
                size="small"
                variant="text"
                className="search-results__view-modify-attribute-button"
                data-testid="search-results__view-modify-attribute-button"
                onClick={() => setOpenAttributeDialog(true)}
              >
                {I18nTranslate.TranslateMessage('viewModifyAttributeSearch')}
              </Button>
            </div>
          )}
          {isAttributeSearch && (
            <SearchAllFilesDialog
              open={openAttributeDialog}
              onClose={() => setOpenAttributeDialog(false)}
              selectedSearchAttributes={selectedSearchAttributes}
              updateSearchResults={getSearchResultsByIdRequest}
            />
          )}
          {!noFile &&
            <div className="search-results__video">
              {!fileLoading && streams &&
                <div ref={mediaContainerRef} className={cn({'search-results__media-container': !fileLoading},
                    {'search-results__media-container-showing': movieDoneLoading})} style={{height: `${mediaContainerHeight}px`}}>
                    <MediaPlayer
                        ref={playerRef}
                        uri=""
                        streams={streams}
                        frameRate={frameRate}
                        boundingBoxes={boundingBoxes}
                        maxHeight={500}
                        thumbnailAssets={thumbnailAssets}
                    />
                </div>
              }
              {fileLoading &&
                  <Skeleton
                      ref={skeletonContainerRef}
                      id={'playerSkeletonId'}
                      className="search-results__tracklet-detail-loading"
                      variant="rectangular"
                      height={400}
                  />
              }
            </div >
          }
          <div className="search-results__tracklet-detail">
            {noFile && (
              <div className="search-results__tracklet-detail-no-file">
                {I18nTranslate.TranslateMessage('selectTrackletToViewDetails')}.
              </div>
            )}
            {fileLoading ? (
              <>
                <Skeleton
                  className="search-results__tracklet-detail-loading"
                  variant="rectangular"
                  height={48}
                />
                <Skeleton
                  className="search-results__tracklet-detail-loading search-results__accordion-ai-engines"
                  variant="rectangular"
                  height={48}
                />
                <Skeleton
                  className="search-results__tracklet-detail-loading"
                  variant="rectangular"
                  height={48}
                />
              </>
            ) : (
              <>
                {hasFile && (
                  <>
                    <Accordion
                      expanded={expanded === 'attributes'}
                      onChange={handleDetailAccordionChange('attributes')}
                    >
                      <AccordionSummary>Detected Attributes</AccordionSummary>
                      <AccordionDetails>
                        <Box
                          className="detected-attributes__box"
                          display="flex"
                          gap={1}
                          flexWrap="wrap"
                          maxWidth={500}
                        >
                          <DetectedAttributes
                            attributes={
                              selectedTrackletFile.selectedTracklet?.attributes
                            }
                          />
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                    <Accordion
                      className="search-results__accordion-ai-engines"
                      expanded={expanded === 'ai-engines'}
                      onChange={handleDetailAccordionChange('ai-engines')}
                    >
                      <AccordionSummary>AI Engines</AccordionSummary>
                      <AccordionDetails>
                        <Box
                          className="detected-attributes__box"
                          display="flex"
                          gap={1}
                          flexWrap="wrap"
                          maxWidth={500}
                        >
                          Vehicle and Person Detection
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                    <Accordion
                      expanded={expanded === 'file-meta'}
                      onChange={handleDetailAccordionChange('file-meta')}
                    >
                      <AccordionSummary>File Metadata</AccordionSummary>
                      <AccordionDetails>
                        <FileMetadata {...currentFile} />
                      </AccordionDetails>
                    </Accordion>
                  </>
                )}
              </>
            )}
          </div>
        </div >
        <div className="search-results__detail">
          <div className="search-results__header-container">
            <TrackletSelectionHeader
              userSelectedTracklets={selectedTracket}
              tracklets={searchResults?.results ?? []}
              selectAllTracklets={handleSelectAllTracklets}
              unselectAllTracklets={handleUnselectAllTracklets}
            />
            {referenceId ? confidenceContent : <Tooltip title={I18nTranslate.TranslateMessage('disableConfidence')}>{confidenceContent}</Tooltip>}
          </div>
          <div
            className="search-results__tabbed-detections-main-container"
            data-testid="tabbed-detections"
          >
            <div className="search-results__tabbed-detections-tab-panels main__scrollbar">
              <div
                className="main__tracklet_thumbnails-container"
                data-testid="person-matched-detection-tab"
              >
                <div className="main__tracklet_thumbnails-tracklets">
                  {!searchResultsLoading &&
                    searchResults.results?.map((tracklet, index) => {
                      const selected = selectViewingTracklet?.trackletId === tracklet.trackletId;
                      const checked = selectedTracket?.find(
                        (selected) =>
                          selected.trackletId === tracklet.trackletId
                      ) !== undefined;
                      const thumbnailUrl = thumbnails?.[tracklet.trackletId]?.thumbnailUrls?.best;
                      const thumbnailIsExpired = new Date(thumbnails?.[tracklet.trackletId]?.expiresDateTime) <= new Date();
                      const confidenceScore = Math.round((tracklet.confidence ?? 0) * 100);

                      return thumbnailUrl && !thumbnailIsExpired ? (
                        <TrackletComp
                          checked={checked}
                          confidenceScore={confidenceScore}
                          showConfidenceScore={referenceId ? showConfidenceScore : false}
                          selected={selected}
                          playing={isPlaying}
                          showCheck={true}
                          isInMatchGroup={trackletsInMatchGroup?.includes(tracklet.trackletId)}
                          isReference={referenceId === tracklet.trackletId}
                          onTrackletStartTimeClick={(time) => getFileAndSeekTo(tracklet, time)}
                          onTrackletStopTimeClick={(time) => getFileAndSeekTo(tracklet, time)}
                          handleTrackletClick={getUserSelectedTrackletFile}
                          handleTrackletCheck={handleTrackletClick}
                          thumbnailUrl={thumbnailUrl}
                          thumbnailScale={thumbnailScale}
                          tracklet={tracklet}
                          index={index}
                        />
                      ) : (
                        <TrackletLoading
                          thumbnailScale={thumbnailScale}
                          index={index} />
                      );
                    })
                  }
                  {searchResultsLoading &&
                    range(100).map((i) => (
                      <Skeleton
                        className="main__tracklet_thumbnails-tracklet skeleton"
                        key={`matched-detection-tab-tracklet-${i}`}
                        variant="rectangular"
                        style={{
                          width: `${113 * thumbnailScale / 100}px`,
                          height: `${113 * thumbnailScale / 100}px`,
                        }}
                      />
                    ))}
                  {!searchId && (
                    <div className="search-results__no-search-found" data-testid="search-results__no-search-found">
                      {I18nTranslate.TranslateMessage('noSearchResultsFound', { matchGroup: matchGroup.data?.name })}
                    </div>
                  )}
                  {searchResults.results?.length === 0 && (
                    <div className="search-results__no-search-results-found" data-testid="search-results__no-search-results-found">
                      {I18nTranslate.TranslateMessage('noTrackletsFound')}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <Divider />
            <div className="search-results__tabbed-detections-footer">
              <Pagination
                prevPage={offsetPage(-1)}
                nextPage={offsetPage(1)}
                setPageSize={setPageSize()}
                totalCount={searchResults.totalCount}
                currentPage={searchResults.currentPage}
                totalPages={searchResults.totalPages}
                pageSize={searchResults.pageSize}
                loading={searchResults.apiStatus === 'loading'}
                setPage={changePage}
              />
              <ThumbnailScaler
                scale={thumbnailScale}
                setScale={setThumbnailScale}
                loading={searchResults.apiStatus === 'loading'}
              />
            </div>
          </div>
        </div>
      </div>
    </div >
  );
};

export default SearchResults;
