import { takeEvery, type ForkEffect, put, select } from 'redux-saga/effects';
import { deleteEvent, selectEventDeletion } from './slice';

import { getEvents, selectEvents } from '../home/<USER>';

export function* watchDeleteEvent() {
  const { currentPage, pageSize } = yield select(selectEvents);
  const { error } = yield select(selectEventDeletion);
  if (!error) {
    yield put(getEvents({ currentPage, pageSize }));
  }
}

export function* watchEventSagas(): Generator<ForkEffect, void> {
  yield takeEvery(deleteEvent.fulfilled, watchDeleteEvent);
}

const eventSagas = watchEventSagas;

export default eventSagas;
