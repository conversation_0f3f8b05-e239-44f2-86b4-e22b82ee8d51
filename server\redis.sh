#!/bin/bash

# Check if tracker-redis container already exists
if docker ps -a --filter "name=tracker-redis" --format '{{.Status}}' | grep -q "Up"; then
    echo "tracker-redis container is already running."
elif docker ps -a --filter "name=tracker-redis" --format '{{.Status}}' | grep -q "Exited"; then
    # Start the tracker-redis container
    echo "Starting tracker-redis container..."
    docker start tracker-redis
else
    # Create tracker-redis container
    echo "Creating tracker-redis container..."
    docker run -p 6379:6379 -it -d --name tracker-redis redis:5.0.14-buster
fi

