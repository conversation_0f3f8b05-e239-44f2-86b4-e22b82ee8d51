import type { PayloadAction } from "@reduxjs/toolkit";
import { createAppSlice } from "../../createAppSlice";

const initialState: any = {};

export const configSlice = createAppSlice({
  name: "config",
  initialState,
  reducers: create => ({
    onSetConfig: create.reducer(
      (state, action: PayloadAction<Window['config']>) => state = action.payload,
    ),
  }),
  selectors: {
    selectConfig: config => config,
  },
});

export const { onSetConfig } = configSlice.actions;

export const { selectConfig } = configSlice.selectors;

export const { actions: configActions, reducer: configReducer } = configSlice;
