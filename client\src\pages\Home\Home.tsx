import {
  SyntheticEvent,
  useState,
  MouseEvent,
  useMemo,
  useEffect,
} from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import cn from 'classnames';
import {
  Ta<PERSON>,
  Tab,
  Button,
  TextField,
  Chip,
  Tooltip,
  Skeleton,
} from '@mui/material';
import {
  Table,
  Column,
  Breadcrumbs,
  NoSearchResults,
  SearchAndUpload,
  createSnackNotification,
  AlertLevel,
} from '@components/common';
import { EventRow, FileRow } from '@components/index';
import { useAppDispatch } from '@store/hooks';
import {
  selectEvents,
  setEventPagination,
  selectFiles,
  setFilesPagination,
  selectEventSelected,
  setSort,
  getEventById as getEventByIdHome,
  updateEventSelected,
  selectFileSelected,
  updateFileSelected,
  updateFileById,
  updateEventById,
} from '@store/modules/home/<USER>';
import {
  PendingUploadFile,
  selectEvents as selectSearchEvents,
  selectFiles as selectSearchFiles,
} from '@store/modules/upload/slice';
import './Home.scss';
import { FilesState, EventsState } from '@store/modules/home/<USER>';
import { clone, isString } from 'lodash';
import {
  deleteEvent,
  selectEventDeletion,
} from '@store/modules/event/slice';
import { deleteFile, selectFileDeletion } from '@store/modules/file/slice';
import ConfirmDialog from '@components/common/ConfirmDialog';
import { Event, File } from '@shared-types/tracker';
import { bytesToMb, toLocalTime } from '@utility/convert';
import { ExpiringString, isExpiringString } from '@utility/expiringString';
import { PendingUploadFileRow } from '@components/rows/PendingUploadFileRow/PendingUploadFileRow';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import PendingCreateEventRow from '@components/rows/PendingCreateEventRow/PendingCreateEventRow';
import { getLocalStorage, removeLocalStorage } from '@utility/localStorage';

const Home = () => {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const { search } = useLocation();
  const navigate = useNavigate();
  const [tab, setTab] = useState('events');
  const [hasSearchResults] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletedFileIds, setDeletedFileIds] = useState<string[]>([]);
  const [deletedEventIds, setDeletedEventIds] = useState<string[]>([]);

  const events = useSelector(selectEvents);
  const searchEventResults = useSelector(selectSearchEvents);
  const files = useSelector(selectFiles);
  const searchFileResults = useSelector(selectSearchFiles);
  const eventDeletion = useSelector(selectEventDeletion);
  const fileDeletion = useSelector(selectFileDeletion);
  const eventSelected = useSelector(selectEventSelected);
  const fileSelected = useSelector(selectFileSelected);
  const [editableFile, setEditableFile] = useState<File | undefined>();
  const [editableEvent, setEditableEvent] = useState<Event | undefined>();

  const tabMapping = (
    isSearch: boolean
  ): Record<string, EventsState | FilesState> => ({
    events: isSearch ? searchEventResults : events,
    files: isSearch ? searchFileResults : files,
  });

  const searchParams = useMemo(
    () => Object.fromEntries(new URLSearchParams(search)),
    [search]
  );
  const { terms: searchTerm, activeTab, page, limit } = searchParams;
  const isSearch = isString(searchTerm) && searchTerm !== '';
  const [pendingUploadFiles, setPendingUploadFiles] = useState<
    PendingUploadFile[]
  >([]);
  const [pendingCreateEvents, setPendingCreateEvents] = useState<Event[]>([]);

  const eventStatus = isSearch ? searchEventResults.status : events.status;

  useEffect(() => {
    const tabs = ['events', 'files'];
    if (activeTab && tabs.includes(activeTab)) {
      setTab(activeTab);
    } else {
      navigate(
        '/?' +
        new URLSearchParams({ ...searchParams, activeTab: tab }).toString()
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  useEffect(() => {
    // Get pending create events from localStorage
    const pendingCreateEvents = getLocalStorage('pendingCreateEvents');
    if (pendingCreateEvents) {
      const { value, expiry }: { value: Event[]; expiry: number } =
        pendingCreateEvents;
      if (expiry > new Date().getTime()) {
        setPendingCreateEvents(value);
      } else {
        removeLocalStorage('pendingCreateEvents');
      }
    }
  }, [events, searchEventResults]);

  useEffect(() => {
    const deletedFileIdsStr = localStorage.getItem('deletedFileIds');
    if (!deletedFileIdsStr) {
      return;
    }
    try {
      const parsedDeletedFileIds = JSON.parse(deletedFileIdsStr);
      const deletedFileIds = Array.isArray(parsedDeletedFileIds)
        ? parsedDeletedFileIds
        : [];

      const now = new Date().getTime();

      // Filter out expired items
      const validFileIds = deletedFileIds.filter(
        (item): item is ExpiringString =>
          isExpiringString(item) && now < item.expiry
      );

      // Update localStorage and state
      localStorage.setItem('deletedFileIds', JSON.stringify(validFileIds));
      setDeletedFileIds(validFileIds.map((item) => item.value));
    } catch (e) {
      console.error(e);
    }
  }, [files, searchFileResults]);

  useEffect(() => {
    const deletedEventIdsStr = localStorage.getItem('deletedEventIds');
    if (!deletedEventIdsStr) {
      return;
    }
    try {
      const parsedDeletedEventIds = JSON.parse(deletedEventIdsStr);
      const deletedEventIds = Array.isArray(parsedDeletedEventIds)
        ? parsedDeletedEventIds
        : [];

      const now = new Date().getTime();

      // Filter out expired items
      const validEventIds = deletedEventIds.filter(
        (item): item is ExpiringString =>
          isExpiringString(item) && now < item.expiry
      );

      // Update localStorage and state
      localStorage.setItem('deletedEventIds', JSON.stringify(validEventIds));
      setDeletedEventIds(validEventIds.map((item) => item.value));
    } catch (e) {
      console.error(e);
    }
  }, [events, searchEventResults]);

  const getPendingUploadFilesCount = () =>
    (isSearch ? searchFileResults.status : files.status) !== 'loading'
      ? pendingUploadFiles.length
      : 0;

  // Only needed until fix is implemented on VE-5434
  useEffect(() => {
    const handlePendingUploadFiles = () => {
      const pendingUploadFilesStr = localStorage.getItem('pendingUploadFiles');
      if (!pendingUploadFilesStr) {
        return;
      }
      try {
        const parsedPendingUploadFiles = JSON.parse(pendingUploadFilesStr);
        const pendingUploadFiles: PendingUploadFile[] = Array.isArray(
          parsedPendingUploadFiles
        )
          ? parsedPendingUploadFiles
          : [];

        const now = new Date().getTime();

        const results = isSearch ? searchFileResults.results : files.results;

        // Filter out files that are found in the current files list
        const updatedPendingUploadFiles: PendingUploadFile[] =
          pendingUploadFiles.filter(
            (file) =>
              !results.some((f) => f.id === file.id) &&
              new Date(file.expirationDate).getTime() > now
          );

        // Update localStorage and state
        localStorage.setItem(
          'pendingUploadFiles',
          JSON.stringify(updatedPendingUploadFiles)
        );
        setPendingUploadFiles(updatedPendingUploadFiles);
      } catch (e) {
        console.error(e);
      }
    };

    handlePendingUploadFiles();

    window.addEventListener('pendingUploadFiles', handlePendingUploadFiles);

    return () => {
      window.removeEventListener(
        'pendingUploadFiles',
        handlePendingUploadFiles
      );
    };
  }, [files, isSearch, searchFileResults]);

  const handleEventNameSaveClick = () => {
    if (!editableEvent) {
      return;
    }
    let isNameChanged = false;
    const eventData = isSearch ? searchEventResults.results : events.results;
    for (const event of eventData) {
      if (event.id === editableEvent.id) {
        if (event.name !== editableEvent.name) {
          isNameChanged = true;
          break;
        }
      }
    }
    if (!isNameChanged) {
      return;
    }

    const updatedSelectedEvent = clone(editableEvent);
    updatedSelectedEvent.name = editableEvent.name.trim();

    dispatch(
      updateEventById({
        eventId: updatedSelectedEvent.id,
        name: updatedSelectedEvent.name,
      })
    );
  };

  const handleFileNameSave = () => {
    if (!editableFile) {
      return;
    }

    const fileData = isSearch ? searchFileResults.results : files.results;
    const isNameChanged = fileData.some(file => file.id === editableFile.id && file.fileName !== editableFile.fileName);
    if (!isNameChanged) {
      return;
    }

    dispatch(
      updateFileById({
        fileId: editableFile.id,
        fileName: editableFile.fileName.trim()
      })
    );
  };

  const handleEventRowClick = (rowData: Event) => {
    dispatch(getEventByIdHome({ eventId: rowData.id }));
    setEditableEvent(undefined);
  };
  const handleEventRowDoubleClick = (rowData: Event) => {
    navigate(`/event/${rowData.id}`);
  };
  const handleFileRowClick = (rowData: File) => {
    dispatch(updateFileSelected(rowData));
  };
  const handleFileRowDoubleClick = (rowData: File) => {
    if (rowData.status === 'processed') {
      navigate(`/event/${rowData.eventId}/file/${rowData.id}`);
    } else {
      const notificationText =
        rowData.status === 'processing' || rowData.status === 'pending'
          ? 'File has not finished processing'
          : 'File failed to process';
      createSnackNotification(
        rowData.status === 'processing' || rowData.status === 'pending'
          ? AlertLevel.Warning
          : AlertLevel.Error,
        rowData.status === 'processing' || rowData.status === 'pending'
          ? 'Warning'
          : 'Error',
        notificationText
      );
    }
  };

  const resetSelected = () => {
    dispatch(updateEventSelected(undefined));
    dispatch(updateFileSelected(undefined));
  };

  const isSelected = () =>
    eventSelected.status === 'loading' || eventSelected.event || fileSelected.file;

  const handleTabChange = (_: SyntheticEvent, newTab: string) => {
    const sameTab = tab === newTab;
    const currentPage = sameTab
      ? page
      : tabMapping(isSearch)[newTab]?.currentPage?.toString() ?? '1';
    const currentLimit = sameTab
      ? limit
      : tabMapping(isSearch)[newTab]?.pageSize?.toString() ?? '50';

    navigate(
      '/?' +
      new URLSearchParams({
        ...searchParams,
        activeTab: newTab,
        page: currentPage,
        limit: currentLimit,
      }).toString()
    );

    setTab(newTab);
    resetSelected();
    dispatch(setSort(undefined));
  };

  const setSortSearchParameters = ({
    column: sort,
    direction: dir,
  }: {
    column?: string;
    direction: string;
  }) => {
    dispatch(setSort(sort ? { sort, dir } : undefined));
  };

  return (
    <div className="home" data-testid="home">
      <div className="home__main-content">
        <div className="home__tables">
          <Breadcrumbs />
          {isSearch && (
            <div className="home__search-heading">
              <div className="home__search-heading-title">
                {I18nTranslate.TranslateMessage('searchResults')}
              </div>
              {hasSearchResults && (
                <div className="home__search-heading-items-returned">
                  {`${isSearch
                    ? searchFileResults.totalCount +
                    searchEventResults.totalCount
                    : files.totalCount + events.totalCount
                    } ${intl.formatMessage({ id: 'items', defaultMessage: 'items' })}`}
                </div>
              )}
            </div>
          )}
          {hasSearchResults && (
            <>
              <Tabs value={tab} onChange={handleTabChange}>
                <Tab
                  value="events"
                  label={intl.formatMessage({ id: 'events', defaultMessage: 'Events' })}
                  data-testid="home-events-tab"
                  icon={
                    <Chip
                      label={`${isSearch
                        ? searchEventResults.totalCount
                        : events.totalCount
                        }`}
                      size="small"
                    />
                  }
                  iconPosition="start"
                />
                <Tab
                  value="files"
                  label={intl.formatMessage({ id: 'files', defaultMessage: 'Files' })}
                  data-testid="home-files-tab"
                  icon={
                    <Chip
                      label={`${(isSearch
                        ? searchFileResults.totalCount
                        : files.totalCount) + getPendingUploadFilesCount()
                        }`}
                      size="small"
                    />
                  }
                  iconPosition="start"
                />
              </Tabs>
              <hr />
              <div className="home__tab-container">
                {tab === 'events' && (
                  <div>
                    {eventStatus !== 'loading' &&
                      pendingCreateEvents.length > 0 && (
                        <>
                          <Table<Event>
                            RowComponent={PendingCreateEventRow}
                            className="home__create-pending-events"
                            rowData={pendingCreateEvents}
                            disablePagination
                          >
                            <Column
                              title={intl.formatMessage({ id: 'eventName', defaultMessage: 'New Event'})}
                              dataKey="name"
                              grow={1}
                            />
                            <Column
                              title={intl.formatMessage({ id: 'eventTime', defaultMessage:'Event Time'})}
                              dataKey="eventStartDate"
                              grow={1}
                            />
                          </Table>
                        </>
                      )}
                    <Table<
                      Event,
                      {
                        isPendingDeletion: (id: string) => boolean;
                      }
                    >
                      className={isSearch ? 'table__search-result' : ''}
                      RowComponent={EventRow}
                      onRowClick={handleEventRowClick}
                      onDoubleRowClick={handleEventRowDoubleClick}
                      additionalProps={{
                        isPendingDeletion: (id: string) =>
                          (eventDeletion.status === 'loading' &&
                            id === eventDeletion.id) ||
                          deletedEventIds.includes(id),
                      }}
                      rowData={
                        isSearch ? searchEventResults.results : events.results
                      }
                      onDataQuery={(params) =>
                        isSearch
                          ? navigate(
                            '/?' +
                            new URLSearchParams({
                              ...searchParams,
                              activeTab: 'events',
                              page: params.page.toString(),
                              limit: params.limit.toString(),
                            }).toString()
                          )
                          : navigate(
                            '/?' +
                            new URLSearchParams({
                              activeTab: 'events',
                              page: params.page.toString(),
                              limit: params.limit.toString(),
                            }).toString()
                          )
                      }
                      loading={eventStatus === 'loading'}
                      pagination={isSearch ? searchEventResults : events}
                      setPagination={setEventPagination}
                      setSort={setSortSearchParameters}
                      emptyMessage={intl.formatMessage({ id: 'noEvents', defaultMessage:'There are no events.'})}
                      selectedId={eventSelected.event?.id}
                    >
                      <Column
                        title={intl.formatMessage({ id: 'eventName', defaultMessage: 'Event Name'})}
                        dataKey="name"
                        grow={1}
                        sortable
                      />
                      <Column
                        title={intl.formatMessage({ id: 'eventTime', defaultMessage:'Event Time'})}
                        dataKey="eventStartDate"
                        grow={1}
                        sortable
                      />
                    </Table>
                  </div>
                )}
                {tab === 'files' && (
                  <div>
                    {getPendingUploadFilesCount() > 0 && (
                      <>
                        <Table<PendingUploadFile>
                          RowComponent={PendingUploadFileRow}
                          className="home__upload-pending-files main__scrollbar"
                          rowData={pendingUploadFiles}
                          disablePagination
                        >
                          <Column
                            title={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
                            dataKey="fileName"
                            grow={1}
                          />
                          <Column
                            title={intl.formatMessage({ id: 'fileStatus', defaultMessage: 'File Status' })}
                            dataKey="status"
                            grow={1}
                          />
                          <Column
                            title={intl.formatMessage({ id: 'uploadDate', defaultMessage: 'Upload Date' })}
                            dataKey="uploadDate"
                            grow={1}
                          />
                        </Table>
                      </>
                    )}
                    <Table<
                      File,
                      {
                        isPendingDeletion: (id: string) => boolean;
                      }
                    >
                      className={
                        isSearch
                          ? getPendingUploadFilesCount() > 0
                            ? 'table__search-result-with-pending-files'
                            : 'table__search-result'
                          : getPendingUploadFilesCount() > 0
                            ? 'table__with-pending-files'
                            : ''
                      }
                      RowComponent={FileRow}
                      rowData={
                        isSearch ? searchFileResults.results : files.results
                      }
                      onRowClick={handleFileRowClick}
                      onDoubleRowClick={handleFileRowDoubleClick}
                      additionalProps={{
                        isPendingDeletion: (id: string) =>
                          (fileDeletion.status === 'loading' &&
                            id === fileDeletion.id) ||
                          deletedFileIds.includes(id),
                      }}
                      onDataQuery={(params) =>
                        isSearch
                          ? navigate(
                            '/?' +
                            new URLSearchParams({
                              ...searchParams,
                              activeTab: 'files',
                              page: params.page.toString(),
                              limit: params.limit.toString(),
                            }).toString()
                          )
                          : navigate(
                            '/?' +
                            new URLSearchParams({
                              activeTab: 'files',
                              page: params.page.toString(),
                              limit: params.limit.toString(),
                            }).toString()
                          )
                      }
                      setSort={setSortSearchParameters}
                      loading={
                        (isSearch ? searchFileResults.status : files.status) ===
                        'loading'
                      }
                      pagination={isSearch ? searchFileResults : files}
                      setPagination={setFilesPagination}
                      emptyMessage={
                        pendingUploadFiles.length === 0
                          ? 'There are no files.'
                          : undefined
                      }
                      selectedId={fileSelected.file?.id}
                    >
                      <Column
                        title={intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}
                        dataKey="fileName"
                        grow={1}
                        sortable
                      />
                      <Column
                        title={intl.formatMessage({ id: 'fileStatus', defaultMessage: 'File Status' })}
                        dataKey="status"
                        grow={1}
                      />
                      <Column
                        title={intl.formatMessage({ id: 'uploadDate', defaultMessage: 'Upload Date' })}
                        dataKey="uploadDate"
                        grow={1}
                        sortable
                      />
                    </Table>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <div className="home__detail">
          <SearchAndUpload />
          {hasSearchResults && (
            <div className="home__detail-container">
              {isSelected() ? (
                <>
                  {(eventSelected.status === 'loading' ||
                    eventSelected.event) && (
                      <>
                        {editableEvent ? (
                          <div className='home__detail-name-textfield-wrapper'>
                            <TextField
                              hiddenLabel
                              defaultValue={
                                eventSelected.event?.name ? undefined : 'Normal'
                              }
                              variant="filled"
                              size="small"
                              autoFocus
                              value={editableEvent.name}
                              onChange={(e) => {
                                setEditableEvent({
                                  ...editableEvent,
                                  name: e.target.value,
                                });
                              }}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleEventNameSaveClick();
                                  setEditableEvent(undefined);
                                }
                              }}
                              slotProps={{
                                input: {
                                  disableUnderline: true,
                                },
                                htmlInput: {
                                  className: 'home__detail-name-textfield',
                                  'data-testid': 'home-detail-event-name-textfield',
                                }
                              }}
                              className="home__detail-name-textfield"
                            />
                            <div
                              className="material-icons"
                              onClick={() => {
                                setEditableEvent(undefined);
                              }}
                            >
                              cancel
                            </div>
                            <div
                              className="material-icons"
                              onClick={() => {
                                handleEventNameSaveClick();
                                setEditableEvent(undefined);
                              }}
                            >
                              save
                            </div>
                          </div>
                        ) : eventSelected.status === 'loading' ? (
                          <Skeleton
                            className="home__detail-name-container skeleton"
                            variant="rectangular"
                            width={100}
                          />
                        ) : (
                          <div
                            className={`home__detail-name-container ${editableEvent ? 'home__detail-hidden' : ''
                              }`}
                          >
                            <Tooltip title={eventSelected.event?.name}>
                              <div
                                className="home__detail-name"
                                data-testid="home-detail-name"
                                onClick={(e: MouseEvent<HTMLDivElement>) => {
                                  if (e.detail === 2) {
                                    setEditableEvent(eventSelected.event);
                                  }
                                }}
                              >
                                {eventSelected.event?.name}
                              </div>
                            </Tooltip>
                            <div
                              className="material-icons"
                              data-testid="home-detail-event-name-edit"
                              onClick={() => setEditableEvent(eventSelected.event)}
                            >
                              edit
                            </div>
                          </div>
                        )}
                        {eventSelected.status === 'loading' ? (
                          <Skeleton
                            className="home__detail-date skeleton"
                            variant="rectangular"
                            width={150}
                          />
                        ) : (
                          <div
                            className="home__detail-date"
                            data-testid="home-detail-created-date"
                          >
                            {`${intl.formatMessage({ id: 'date', defaultMessage: 'Date' })}: `}
                            {I18nTranslate.TranslateDate(eventSelected.event?.eventStartDate)}
                          </div>
                        )}
                        {eventSelected.status === 'loading' ? (
                          <Skeleton
                            className="home__detail-creator skeleton"
                            variant="rectangular"
                            width={150}
                          />
                        ) : (
                          <div
                            className="home__detail-creator"
                            data-testid="home-detail-creator-name"
                          >
                            {`${intl.formatMessage({ id: 'eventCreator', defaultMessage: 'Event Creator' })}: ${eventSelected.event?.createdByName ?? 'Officer Name'
                              }`}
                          </div>
                        )}

                        <div className="home__detail-files-match-group-container">
                          <div className="home__detail-details">
                            {eventSelected.status === 'loading' ? (
                              <Skeleton
                                className="home__detail-files skeleton"
                                variant="rectangular"
                                width={150}
                              />
                            ) : (
                              <div className="home__detail-files">
                                <div className="material-icons">folder</div>
                                <div
                                  className="home__detail-files-title"
                                  data-testid="home-detail-file-count"
                                >
                                  {`${eventSelected.event?.filesCount ?? 0} ${(eventSelected.event?.filesCount ?? 0) > 1
                                    ? intl.formatMessage({ id: 'files', defaultMessage: 'Files' })
                                    : intl.formatMessage({ id: 'file', defaultMessage: 'File' })
                                    }`}
                                </div>
                              </div>
                            )}
                            {eventSelected.status === 'loading' ? (
                              <Skeleton
                                className="home__detail-match-group skeleton"
                                variant="rectangular"
                                width={150}
                              />
                            ) : (
                              <div className="home__detail-match-group">
                                <img src="/match-group.svg" alt="match group" />
                                <div
                                  className="home__detail-files-title"
                                  data-testid="home-detail-matchgroup-count"
                                >
                                  {`${eventSelected.event?.matchGroupsCount ?? 0
                                    } ${(eventSelected.event?.matchGroupsCount ?? 0) >
                                      1
                                      ? intl.formatMessage({ id: 'matchGroups', defaultMessage: 'Match Groups' })
                                      : intl.formatMessage({ id: 'matchGroup', defaultMessage: 'Match Group' })
                                    }`}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="home__detail-actions">
                          {
                            eventSelected.status === 'loading' ? (
                              <>
                                {
                                  new Array<number>(1, 2).map(value => (
                                    <Skeleton
                                      key={value}
                                      variant="rectangular"
                                      width={100}
                                      height={25}
                                    />
                                  ))
                                }
                              </>
                            ) : (
                              <>
                                <Button
                                  data-testid="home-view-event-button"
                                  onClick={() => {
                                    navigate(`/event/${eventSelected.event?.id}`);
                                  }}
                                  onAuxClick={() =>
                                    window.open(
                                      `/event/${eventSelected.event?.id}`,
                                      '_blank'
                                    )
                                  }
                                >
                                  <img src="/check-circle.svg" alt="check" />
                                  <span>
                                    {I18nTranslate.TranslateMessage('viewEvent')}
                                  </span>
                                </Button>
                                <Button
                                  data-testid="home-delete-event-button"
                                  onClick={() => setDeleteDialogOpen(true)}
                                >
                                  <img src="/x-circle.svg" alt="check" />
                                  <span>
                                    {I18nTranslate.TranslateMessage('deleteEvent')}
                                  </span>
                                </Button>
                              </>
                            )
                          }
                          {eventSelected.event && (
                            <ConfirmDialog
                              open={deleteDialogOpen}
                              title={intl.formatMessage({ id: 'deleteEvent', defaultMessage: 'Delete Event' })}
                              content={
                                <>
                                  {I18nTranslate.TranslateMessageWithBoldValue(
                                    'confirmDeleteEvent',
                                    {
                                      eventName: eventSelected.event.name,
                                    }
                                  )}
                                </>
                              }
                              confirmText="Delete"
                              cancelText="Cancel"
                              onConfirm={() => {
                                eventSelected.event &&
                                  dispatch(
                                    deleteEvent(eventSelected.event.id.toString())
                                  );
                                dispatch(updateEventSelected(undefined));
                                setDeleteDialogOpen(false);
                              }}
                              onClose={() => setDeleteDialogOpen(false)}
                              reEnterName
                              name={eventSelected.event.name}
                              type="event"
                            />
                          )}
                        </div>
                      </>
                    )}
                  {fileSelected.file && (
                    <>
                      {editableFile && (
                        <div className="home__detail-name-container">
                          <TextField
                            hiddenLabel
                            defaultValue="Normal"
                            variant="filled"
                            size="small"
                            value={editableFile.fileName}
                            onChange={(e) => {
                              setEditableFile({
                                ...editableFile,
                                fileName: e.target.value,
                              });
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleFileNameSave();
                                setEditableFile(undefined);
                              }
                            }}
                            slotProps={{
                              input: {
                                disableUnderline: true,
                              },
                              htmlInput: {
                                className: 'home__detail-name-textfield',
                                'data-testid': 'home-detail-file-name-textfield',
                              }
                            }}
                          />
                          <div
                            className="material-icons"
                            onClick={() => {
                              setEditableFile(undefined);
                            }}
                          >
                            cancel
                          </div>
                          <div
                            className="material-icons"
                            onClick={() => {
                              handleFileNameSave();
                              setEditableFile(undefined);
                            }}
                          >
                            save
                          </div>
                        </div>
                      )}
                      {!editableFile && (
                        <div className="home__detail-name-container">
                          {fileSelected.status !== 'loading' ? (
                            <>
                              <Tooltip title={fileSelected.file.fileName}>
                                <div
                                  className="home__detail-name"
                                  data-testid="home-detail-name"
                                  onClick={(e: MouseEvent<HTMLDivElement>) => {
                                    if (e.detail === 2) {
                                      setEditableFile(fileSelected.file);
                                    }
                                  }}
                                >
                                  {fileSelected.file.fileName}
                                </div>
                              </Tooltip>
                              <div
                                className="material-icons"
                                data-testid="home-detail-file-name-edit"
                                onClick={() => {
                                  setEditableFile(fileSelected.file);
                                }}
                              >
                                edit
                              </div>
                            </>
                          ) : (
                            <Skeleton
                              className="home__detail-name-container skeleton"
                              variant="rectangular"
                              width={100}
                            />
                          )}
                        </div>
                      )}
                      <div
                        className="home__detail-date"
                        data-testid="home-detail-date"
                      >
                        {/* Upload date of files is defined even if their status is unknown or an error */}
                        {`${intl.formatMessage({
                          id: 'uploaded',
                          defaultMessage: 'Uploaded',
                        })}:`} {I18nTranslate.TranslateDate(toLocalTime(fileSelected.file?.uploadDate))}
                      </div>
                      <div
                        className="home__detail-creator"
                        data-testid="home-detail-creator"
                      >
                        {`${intl.formatMessage({ id: 'fileUploadedBy', defaultMessage: 'File Uploaded by' })} ${fileSelected.file.createdByName ?? 'Officer Name'
                          }`}
                      </div>
                      {fileSelected.file.status === 'processed' &&
                        <div className="home__detail-files-match-group-container">
                          <div className="home__detail-details file">
                            <div className="home__detail-file-heading" />
                            <div className="home__detail-file-info">
                              <div className="home__detail-file-size-container">
                                <div
                                  className="home__detail-file-size"
                                  data-testid="home-detail-file-size"
                                >
                                  {`${intl.formatMessage({ id: 'fileSize', defaultMessage: 'File Size' })}: `}
                                  <span>
                                    {`${bytesToMb(
                                      fileSelected.file?.fileSize
                                    )} ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`}
                                  </span>
                                </div>
                                <div className="home__detail-file-more-info">
                                  <div className="material-symbols-outlined">
                                    info
                                  </div>
                                  <div className="home__detail-file-more-info-text">
                                    {' '}
                                    <i>More Info</i>
                                  </div>
                                </div>
                              </div>
                              <div
                                className="home__detail-file"
                                data-testid="home-detail-file"
                              >
                                {/* <img src="/badge.svg" alt="badge" /> */}
                                {`${intl.formatMessage({
                                  id: 'fileGPSLocation',
                                  defaultMessage: 'File GPS Location'
                                })}: `}
                                <div className="home__detail-files-title">
                                  {fileSelected.file.location !== 'unavailable'
                                    ? fileSelected.file.location
                                    : intl.formatMessage({ id: 'unavailable', defaultMessage: 'Unavailable' })}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      }
                      <div className="home__detail-actions">
                        <Button
                          data-testid="home-view-file-button"
                          className={cn('home__detail-button', {
                            'home__detail-disabled-button':
                              fileSelected.file.status !== 'processed',
                          })}
                          onClick={() => {
                            if (
                              fileSelected.file &&
                              fileSelected.file.status &&
                              fileSelected.file.status !== 'processed'
                            ) {
                              const notificationText =
                                fileSelected.file.status === 'processing' ||
                                  fileSelected.file.status === 'pending'
                                  ? 'File has not finished processing'
                                  : 'File failed to process';
                              createSnackNotification(
                                fileSelected.file.status === 'processing' ||
                                  fileSelected.file.status === 'pending'
                                  ? AlertLevel.Warning
                                  : AlertLevel.Error,
                                fileSelected.file.status === 'processing' ||
                                  fileSelected.file.status === 'pending'
                                  ? 'Warning'
                                  : 'Error',
                                notificationText
                              );
                              return;
                            }
                            if (fileSelected.file) {
                              navigate(
                                `/event/${fileSelected.file.eventId}/file/${fileSelected.file.id}`
                              );
                            }
                          }}
                          onAuxClick={() => {
                            if (
                              fileSelected.file &&
                              fileSelected.file.status &&
                              fileSelected.file.status === 'processed'
                            ) {
                              window.open(
                                `/event/${fileSelected.file.eventId}/file/${fileSelected.file.id}`,
                                '_blank'
                              );
                            }
                          }}
                        >
                          <img src="/check-circle.svg" alt="check" />
                          <span>
                            {I18nTranslate.TranslateMessage('viewFile')}
                          </span>
                        </Button>
                        <Button
                          data-testid="home-delete-file-button"
                          onClick={() => setDeleteDialogOpen(true)}
                        >
                          <img src="/x-circle.svg" alt="check" />
                          <span>
                            {I18nTranslate.TranslateMessage('deleteFile')}
                          </span>
                        </Button>
                        {fileSelected.file && (
                          <ConfirmDialog
                            open={deleteDialogOpen}
                            title={intl.formatMessage({ id: 'deleteFile', defaultMessage: 'Delete File' })}
                            content={I18nTranslate.TranslateMessageWithBoldValue(
                              'deleteFileMessage',
                              { fileName: fileSelected.file.fileName }
                            )}
                            confirmText={intl.formatMessage({ id: 'delete', defaultMessage: 'Delete' })}
                            cancelText={intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
                            onConfirm={() => {
                              if (fileSelected.file) {
                                dispatch(deleteFile(fileSelected.file.id.toString()));
                              }
                              dispatch(updateFileSelected(undefined));
                              setDeleteDialogOpen(false);
                            }}
                            onClose={() => setDeleteDialogOpen(false)}
                          />
                        )}
                      </div>
                    </>
                  )}
                </>
              ) : (
                <div
                  className="home__detail-container-empty"
                  data-testid="home-container-empty"
                >
                  {`${intl.formatMessage({ id: 'select', defaultMessage: 'Select' })} ${tab === 'events'
                    ? intl.formatMessage({ id: 'anEvent', defaultMessage: 'an event' })
                    : intl.formatMessage({ id: 'aFile', defaultMessage: 'a file' })
                    }.`}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <NoSearchResults hasSearchResults={hasSearchResults} />
    </div>
  );
};

export default Home;
