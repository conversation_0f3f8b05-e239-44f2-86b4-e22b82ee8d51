import { Context } from '@application/types';
import {
  ActionError,
  NotFoundError,
  ActionValidationError,
} from '@common/errors';
import { queries, responses } from '@tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  MatchGroup,
  VideoSpliceDetails,
} from '../../../../../../types/tracker';
import { getSplicingTdoDetails } from '../getSplicingTdoDetails';

const getMatchGroup = async <ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data & Partial<responses.getMatchGroup>>> => {
  const { cache, data, req, log: _log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { matchGroupId } = req.params;
  if (!matchGroupId) {
    throw new ActionError('No matchGroupId provided');
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  const { structuredDataObject } = await callGQL<
    responses.searchStructuredDataObject<MatchGroup>,
    ReqPayload,
    Data
  >(context, headers, queries.getMatchGroup, {
    id: matchGroupId,
    schemaId: schemaId,
  });

  const tdoIds =
    structuredDataObject?.data?.generatedTimelines
      ?.map((generatedTimeline) => generatedTimeline.tdoId)
      ?.filter((tdoId) => tdoId !== undefined) ?? [];

  let videoSpliceTdoDetails: {
    [key: string]: VideoSpliceDetails;
  } = {};

  if (structuredDataObject?.data && tdoIds.length > 0) {
    videoSpliceTdoDetails = await getSplicingTdoDetails({
      tdoIds,
      context,
      headers,
    });
  }

  structuredDataObject?.data?.generatedTimelines?.forEach(
    (generatedTimeline, index) => {
      if (generatedTimeline.tdoId) {
        const videoSpliceDetailsByTdoId =
          videoSpliceTdoDetails[generatedTimeline.tdoId];
        if (
          videoSpliceDetailsByTdoId &&
          structuredDataObject?.data?.generatedTimelines?.[index]
        ) {
          structuredDataObject.data.generatedTimelines[index] = {
            ...generatedTimeline,
            ...videoSpliceDetailsByTdoId,
          };
        }
      }
    }
  );

  if (!structuredDataObject) {
    throw new NotFoundError('No match group found or provided');
  }

  const new_data = Object.assign({}, data, {
    matchGroup: structuredDataObject?.data,
    matchGroupId: structuredDataObject?.id,
  });

  const new_context = Object.assign({}, context, { data: new_data });
  return new_context;
};

export default getMatchGroup;
