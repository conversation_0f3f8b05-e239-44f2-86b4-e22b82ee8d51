import { Attributes, AttributesJson } from '@shared-types/tracker';

export const convertJsonAttributes = (jsonAttributes: AttributesJson) => {
  const attributes: Attributes = {};
  Object.values(jsonAttributes).forEach(({ key, label, value }) => {
    if (!attributes[key]) {
      attributes[key] = [{ label, value, key }];
    } else {
      attributes[key].push({ label, value, key });
    }
  });
  return attributes;
};
