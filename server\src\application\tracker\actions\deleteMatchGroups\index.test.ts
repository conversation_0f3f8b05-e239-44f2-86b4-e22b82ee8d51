import NodeCache from 'node-cache';
import { responses } from '../../graphQL';
import { Variables } from 'graphql-request';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import deleteMatchGroups from '../deleteMatchGroups';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, responses.getMatchGroups>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve()
  ),
}));

describe('Delete Match Groups', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        matchGroups: {
          pagination: {
            pageSize: 50,
            currentPage: 1,
            totalPages: 10,
            totalCount: 500,
          },
          sort: {
            field: 'field',
            direction: 'direction',
          },
          eventId: 'eventId',
          searchResults: [
            {
              id: 'id',
              name: 'name',
              eventId: 'eventId',
              searches: [],
              selectedTracklets: [],
              modifiedDateTime: 'modifiedDateTime',
              timelineProject: {},
              generatedTimelines: [],
            },
          ],
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'schemaId');
  });

  it('Successfully deletes a match group', async () => {
    const response = await deleteMatchGroups(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { schemaId: 'schemaId', sdoId: 'id' }
    );
    expect(response).not.toBeNull();
  });

  it('Does not call if there are no files', async () => {
    cxt.data.matchGroups.searchResults = [];
    await deleteMatchGroups(cxt);
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if matchGroups is undefined', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.matchGroups = undefined;
    await expect(async () => await deleteMatchGroups(cxt)).rejects.toThrow(
      'No matchGroups provided'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if schemaId is undefined', async () => {
    cxt.cache.del('matchGroupsSchemaId');
    await expect(async () => await deleteMatchGroups(cxt)).rejects.toThrow(
      'schemaId not found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
