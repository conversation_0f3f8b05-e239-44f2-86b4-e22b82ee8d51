import { getUsers } from '.';
describe('getUsers', () => {
  // This is a driver programe to help troubleshooting getUsers with real data.
  // It is disabled on purpose. To enable it, remove the 'x' before 'it'.
  // Provide userIds and bearer token for troubleshooting. (the gql url is the apiRoot in apiConfig.json)
  xit('driver programe to test getUsers', async () => {
    const userIds = ['9aa0aa08-0950-4e49-b471-fdeceed12f89'];
    const context = { log: console } as any;
    const headers = {
      authorization: 'bearer a988d92a-c326-4ee3-b4a5-59151bb75dae',
    };
    const response = await getUsers({ userIds, context, headers });
    expect(response).not.toBeUndefined();
  }, 10000);
});
