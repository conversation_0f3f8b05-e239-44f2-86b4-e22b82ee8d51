import NodeCache from 'node-cache';
import deleteFiles from '../deleteFiles';
import { responses } from '../../graphQL';
import { Variables } from 'graphql-request';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { has } from 'lodash';

let cxt: Context<object, responses.searchFiles>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (
        query.includes('searchMedia') &&
        variables &&
        !has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              aggregations: {
                recordingId: {
                  doc_count_error_upper_bound: 0,
                  sum_other_doc_count: 0,
                  buckets: [],
                },
              },
            },
          },
        });
      }

      return Promise.resolve({
        createdStructuredData: { id: 'id' },
        structuredDataObjects: {
          records: [],
        },
      });
    }
  ),
}));

describe('Delete Files', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        searchFiles: {
          searchResults: [
            {
              id: 'id',
              fileName: 'fileName',
              status: 'processing',
              length: 0,
              createdByName: 'createdBy',
              uploadDate: 'uploadDate',
              location: 'location',
              fileType: 'fileType',
              fileSize: 0,
              eventId: 'eventId',
              eventName: 'eventName',
              thumbnailUrl: 'thumbnailUrl',
              primaryAsset: {
                signedUri: 'signedUri',
              },
              streams: [
                {
                  uri: 'uri',
                  protocol: 'protocol',
                },
              ],
              frameRate: 24,
            },
          ],
          pageSize: 50,
          currentPage: 1,
          totalCount: 500,
          totalPages: 10,
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('matchGroupsSchemaId', 'schemaId');
  });

  it('Successfully deletes a file', async () => {
    const response = await deleteFiles(cxt);
    expect(callGQL).toHaveBeenCalledTimes(4);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['fingerprint'],
          limit: 100,
          offset: 0,
          query: {
            conditions: [
              {
                field: 'sourceEngineId',
                operator: 'term',
                value: 'd77d6133-a801-472c-bc7e-48ddafec8590',
              },
              {
                field: 'label',
                operator: 'terms',
                values: ['person', 'vehicle'],
              },
              {
                field: 'recordingId',
                operator: 'terms',
                values: ['id'],
              },
            ],
            operator: 'and',
          },
          select: [
            'id',
            'score',
            'referenceId',
            'organizationId',
            'assetId',
            'tags',
            'label',
            'sourceEngineId',
            'recordingId',
            'vendor',
          ],
        },
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        limit: 10000,
        offset: 0,
        schemaId: 'schemaId',
        sort: 'asc',
        sortType: 'createdDateTime',
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      { tdoId: 'id' }
    );
    expect(response).not.toBeNull();
  });

  it('Does not call if there are no files', async () => {
    cxt.data.searchFiles.searchResults = [];
    await deleteFiles(cxt);
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws error if searchFiles is undefined', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.searchFiles = undefined;
    await expect(async () => await deleteFiles(cxt)).rejects.toThrow(
      'No files found'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
