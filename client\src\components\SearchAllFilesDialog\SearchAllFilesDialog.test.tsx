import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import SearchAllFilesDialog from './SearchAllFilesDialog';
import configureMockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { I18nProvider, LOCALES } from '@i18n';
import { Attributes } from '@shared-types/tracker';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

const mockStore = configureMockStore();

const store = mockStore({
  event: {
    files: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    matchGroups: {
      eventId: '',
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
      sortType: '',
      sortDirection: '',
    },
    event: {
      id: '',
      name: '',
      tags: [],
      createdBy: '',
      createdByName: '',
      description: '',
      eventStartDate: '',
      eventEndDate: '',
      createdDateTime: '',
      modifiedDateTime: '',
      status: 'idle',
      matchGroupsCount: 0,
      filesCount: 0,
    },
    eventDeletion: {
      id: '',
      status: 'idle',
      message: '',
    },
    matchGroupSearchDeletion: {
      status: 'idle',
      message: '',
      matchGroupId: '',
      searchId: '',
    },
    tags: {
      results: [],
      status: 'idle',
      error: '',
    },
    generatedTimelines: {
      results: [],
      status: 'idle',
      error: '',
    },
    deleteGeneratedTimeline: {
      status: 'idle',
      error: '',
    },
    attributes: {
      person: {},
      vehicle: {},
    },
    createMatchGroup: {
      status: 'idle',
      error: '',
      id: '',
    },
    updateMatchGroup: {
      status: 'idle',
      error: '',
    },
  },
  searchResults: {
    searchResults: {
      results: [],
      referenceTrackletId: '',
      searchId: '',
      searchName: '',
      matchGroupId: '',
      matchGroupName: '',
      eventId: '',
      type: 'person',
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      totalPages: 0,
      apiStatus: 'idle',
      error: '',
      fileNames: [],
      allFiles: [],
    },
    matchGroup: {
      data: undefined,
      apiStatus: 'idle',
      error: '',
    },
    matchGroups: {
      eventId: '',
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
      sortType: '',
      sortDirection: '',
    },
    createMatchGroup: {
      status: 'idle',
      error: '',
      id: '',
    },
    updateMatchGroup: {
      status: 'idle',
      error: '',
    },
    event: {
      data: undefined,
      apiStatus: 'idle',
      error: '',
    },
    fileFilter: {
      fileNames: [],
      selectedFileNames: [],
      displayString: '',
      fileIds: [],
      apiStatus: 'idle',
    },
    selectedTracklet: undefined,
    selectedTrackletFile: {
      file: undefined,
      apiStatus: 'idle',
      error: '',
    },
    confidenceThreshold: 0.8,
    boundingBoxes: {
      data: [],
      apiStatus: 'idle',
      error: '',
    },
    thumbnailUrls: {},
    attributes: {
      person: {},
      vehicle: {},
    },
  },
});

const mockSelectedSearchAttributes: Attributes = {
  person: [
    {
      key: 'Accessory',
      label: 'AccessoryBackpack',
      value: 'Backpack',
    },
    {
      key: 'Accessory',
      label: 'AccessoryBagAny',
      value: 'BagAny',
    },
    {
      key: 'Accessory',
      label: 'AccessoryGlasses',
      value: 'Glasses',
    },
    {
      key: 'FootwearType',
      label: 'FootwearTypeBoots',
      value: 'Boots',
    },
    {
      key: 'LowerColor',
      label: 'LowerColorBlack',
      value: 'Black',
    },
  ],
};

const mockUpdateSearchResults = jest.fn();

describe('SearchAllFilesDialog', () => {
  const mockOnClose = jest.fn();
  test('render SearchAllFilesDialog open with components', () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAllFilesDialog open={true} onClose={mockOnClose} />
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('search-all-files-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__title')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-attribute-type')).toBeInTheDocument();

    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toHaveTextContent('Choose Attributes: 0 Selected');

    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-clear-all')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-clear-all')).toBeDisabled();

    expect(screen.queryAllByTestId('search-all-files-dialog__content-attribute-selection-column')).toHaveLength(3);

    expect(screen.getByTestId('search-all-files-dialog__content-match-group-select')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-match-group-new-match-add')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-match-group-existing-group-text')).toBeInTheDocument();

    expect(screen.getByTestId('search-all-files-dialog__actions-cancel')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__actions-search')).toBeInTheDocument();
  });

  test('render SearchAllFilesDialog closed', () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAllFilesDialog open={false} onClose={mockOnClose} />
        </I18nProvider>
      </Provider>
    );
    expect(screen.queryByTestId('search-all-files-dialog')).not.toBeInTheDocument();
  });

  test('render SearchAllFilesDialog open with create new Match Group input enabled', async () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <SearchAllFilesDialog open={true} onClose={mockOnClose} />
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('search-all-files-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-match-group-select')).toBeInTheDocument();

    const newMatchGroupButton = screen.getByTestId('search-all-files-dialog__content-match-group-new-match-add');
    expect(newMatchGroupButton).toBeInTheDocument();

    fireEvent.click(newMatchGroupButton);

    await waitFor(() => {
      expect(screen.getByTestId('search-all-files-dialog__content-match-group-new-match-input')).toBeInTheDocument();
      expect(screen.getByTestId('search-all-files-dialog__content-match-group-new-match-confirm')).toBeInTheDocument();
      expect(screen.getByTestId('search-all-files-dialog__content-match-group-new-match-cancel')).toBeInTheDocument();
    });
  });

  test('render SearchAllFilesDialog with saved selected searched attributes', async () => {
    render(
      <Provider store={store}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <MemoryRouter initialEntries={['/search/123']}>
            <Routes>
              <Route
                path="/search/:searchId"
                element={<SearchAllFilesDialog open={true} onClose={mockOnClose} selectedSearchAttributes={mockSelectedSearchAttributes} updateSearchResults={mockUpdateSearchResults} />}
              />
            </Routes>
          </MemoryRouter>
        </I18nProvider>
      </Provider>
    );

    expect(screen.getByTestId('search-all-files-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toBeInTheDocument();
    expect(screen.getByTestId('search-all-files-dialog__content-attribute-selected-count')).toHaveTextContent('Choose Attributes: 5 Selected');
  });
});
