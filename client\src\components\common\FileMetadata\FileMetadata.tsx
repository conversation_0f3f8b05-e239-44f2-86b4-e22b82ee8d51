import { Box, List, ListItem, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { bytesToMb, toLocalTime, millisToTimeFormatted } from '@utility/convert';
import './FileMetadata.scss';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';

function FileMetadata({
  fileName,
  uploadDate,
  location,
  fileType,
  fileSize,
  length,
}: Props) {
  const intl = useIntl();
  return (
    <Box className="file-metadata" sx={{ flexGrow: 1, maxWidth: 500 }}>
      <Grid size={{ xs: 12, md: 12 }}>
        <List>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'fileName', defaultMessage: 'File Name' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {fileName}
            </Typography>
          </ListItem>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'uploadDate', defaultMessage: 'Upload Date' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {I18nTranslate.TranslateDate(toLocalTime(uploadDate ?? ''))}
            </Typography>
          </ListItem>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'fileGPSLocation', defaultMessage: 'File GPS Location' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {location}
            </Typography>
          </ListItem>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'fileType', defaultMessage: 'File Type' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {fileType}
            </Typography>
          </ListItem>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'fileSize', defaultMessage: 'File Size' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {`${bytesToMb(fileSize ?? 0)} ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`}
            </Typography>
          </ListItem>
          <ListItem>
            <Typography component="div">
              {`${intl.formatMessage({ id: 'videoLength', defaultMessage: 'Video Length' })}: `}
            </Typography>
            <Typography component="div" sx={{ ml: 1 }}>
              {millisToTimeFormatted((length ?? 0) * 1000 )}
            </Typography>
          </ListItem>
        </List>
      </Grid>
    </Box>
  );
}

interface Props {
  fileName?: string;
  uploadDate?: string;
  location?: string;
  fileType?: string;
  fileSize?: number;
  length?: number;
}

export default FileMetadata;
