import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';
import * as ResTypes from '../../../../../../types/responses';
import { v4 as uuidv4 } from 'uuid';

const createMatchGroupStructuredData = async <
  ReqPayload,
  Data extends Partial<
    ResTypes.CreateMatchGroupsPayloadResponse & responses.getMe
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          ResTypes.CreateMatchGroupsPayloadResponse &
            responses.getMe &
            responses.structuredMatchGroupData
        >
    >
  | undefined
> => {
  const { cache, data, req, log, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };

  const { name, eventId, userId, timelineProject, generatedTimelines } = data;
  if (!name || !eventId || !userId) {
    throw new ActionError(
      `Missing required data: ${[!name ? 'name' : '', !eventId ? 'eventId' : '', !userId ? 'userId' : ''].filter(Boolean).join(', ')}`
    );
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  const matchGroupId = uuidv4();

  try {
    const { createStructuredData } = await callGQL<
      responses.structuredMatchGroupData,
      ReqPayload,
      Data
    >(context, headers, queries.createStructuredData, {
      id: matchGroupId,
      schemaId,
      data: {
        id: matchGroupId,
        name,
        eventId,
        searches: [],
        selectedTracklets: [],
        ...(timelineProject && { timelineProject }),
        ...(generatedTimelines && { generatedTimelines }),
      },
    });
    if (redisWrapper && data.userOrganizationId) {
      await redisWrapper.event.del(eventId, data.userOrganizationId);
    }
    if (createStructuredData) {
      const new_data = Object.assign({}, data, { createStructuredData });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createMatchGroupStructuredData;
