import {
  File,
  MatchGroup,
  Event,
  Tracklet,
  TrackletBoundingBox,
} from '@shared-types/tracker';
import {
  GetSearchResultsResponse,
  GetMatchGroupsResponse,
} from '@shared-types/responses';
import { ApiStatus } from '@store/types';

export interface TrackletsState extends Tracklet {
  status: ApiStatus;
  error?: string;
}

export interface FileDeletionState {
  status: ApiStatus;
  message: string;
  error?: string;
}

export interface FileState {
  file?: File;
  selectedTracklet?: Tracklet;
  apiStatus: ApiStatus;
  error?: string;
}

export interface SearchResultsState extends GetSearchResultsResponse {
  apiStatus: ApiStatus;
  error?: string;
}
export interface MatchGroupState {
  data?: MatchGroup;
  apiStatus: ApiStatus;
  error?: string;
}

export interface MatchGroupsState extends GetMatchGroupsResponse {
  status: ApiStatus;
  error?: string;
}

export interface CreateMatchGroup {
  status: ApiStatus;
  error?: string;
  id: string;
}

export interface UpdateMatchGroup {
  status: ApiStatus;
  error?: string;
}

export interface EventState {
  data?: Event;
  apiStatus: ApiStatus;
  error?: string;
}

export interface FileFilterState {
  fileNames: string[];
  selectedFileNames: string[];
  displayString: string;
  fileIds: string[];
  apiStatus: ApiStatus;
}

export interface BoundingBoxesState {
  data: TrackletBoundingBox[];
  apiStatus: ApiStatus;
  error?: string;
}
