import {
  ContainerSASPermissions,
  StorageSharedKeyCredential,
  generateBlobSASQueryParameters,
} from '@azure/storage-blob';
import env from '../../../../env';
import NodeCache from 'node-cache';
import ActionValidationError from '@common/errors/ActionValidationError';

const EXPIRE_IN_SECS = 3600 * 6;
const containerUrl = `https://${env.blob?.account}.blob.${env.blob?.endpointSuffix}/${env.blob?.container}`;

const CACHE_SEC = 3600;
const blobWritableCredentialCacheKey = 'blobWritableCredentialCacheKey';
const blobReadOnlyCredentialCacheKey = 'blobReadOnlyCredentialCacheKey';

export function generateAzureWritableCredential(cache?: NodeCache) {
  const blob = env.blob;
  if (!blob) {
    throw new ActionValidationError('Blob config not found');
  }
  const key =
    blob.key === 'ENV_AZURE_BLOB_ACCOUNT_KEY'
      ? process.env.ENV_AZURE_BLOB_ACCOUNT_KEY?.replace(/(\r\n|\n|\r)/gm, '')
      : blob.key;
  if (!key) {
    throw new ActionValidationError(
      'Missing blob.key. Please set env variables ENV_AZURE_BLOB_ACCOUNT_KEY for local development.'
    );
  }
  let sasToken: string | undefined = cache?.get(blobWritableCredentialCacheKey);
  // Writable SAS token will be passed to engine. The expiration is configured
  // in datacenter config.
  if (!sasToken) {
    sasToken = generateBlobSAS({
      account: blob.account,
      key,
      containerName: blob.container,
      expireSecs: blob.expireSecs,
      writable: true,
    });
    cache?.set(blobWritableCredentialCacheKey, sasToken, CACHE_SEC);
  }
  return {
    storage: 'blob',
    storageUrl: containerUrl,
    credential: { sasToken },
  };
}

export function generateAzureReadOnlyCredential(cache?: NodeCache) {
  const blob = env.blob;
  if (!blob) {
    throw new ActionValidationError('Blob config not found');
  }
  const key =
    blob.key === 'ENV_AZURE_BLOB_ACCOUNT_KEY'
      ? process.env.ENV_AZURE_BLOB_ACCOUNT_KEY?.replace(/(\r\n|\n|\r)/gm, '')
      : blob.key;
  if (!key) {
    throw new ActionValidationError(
      'Missing blob.key. Please set env variables ENV_AZURE_BLOB_ACCOUNT_KEY for local development.'
    );
  }

  let sasToken: string | undefined = cache?.get(blobReadOnlyCredentialCacheKey);
  // Readonly SAS token is used for signed url for frontend, so it needs to set to
  // a value greater that expiration of signed url.
  // No value is passed, so the default value EXPIRE_IN_SECS + CACHE_SEC is used.
  if (!sasToken) {
    sasToken = generateBlobSAS({
      account: blob.account,
      key,
      containerName: blob.container,
    });
    cache?.set(blobReadOnlyCredentialCacheKey, sasToken, CACHE_SEC);
  }
  return {
    storage: 'blob',
    storageUrl: containerUrl,
    credential: { sasToken },
  };
}

export function generateBlobSAS({
  account,
  key,
  containerName,
  expireSecs = EXPIRE_IN_SECS + CACHE_SEC, // default expire time is url expire time + cache time. No MAX value because it is generated by Account key, but it could be set by the SAS expiration policy
  writable = false,
}: {
  account: string;
  key: string;
  containerName: string;
  expireSecs?: number;
  writable?: boolean;
}) {
  const credentials = new StorageSharedKeyCredential(account, key);
  const permissions = new ContainerSASPermissions();
  permissions.read = true;
  if (writable) {
    permissions.write = true;
    permissions.create = true;
    permissions.delete = true;
    permissions.list = true;
  }
  const expiryTime = new Date(Date.now() + expireSecs * 1000);
  const sasToken = generateBlobSASQueryParameters(
    {
      containerName,
      expiresOn: expiryTime,
      permissions,
    },
    credentials
  ).toString();
  return sasToken;
}
