# Search

### Create Schema

```js
mutation createDataRegistry {
  createDataRegistry(
    input: {
    id: "885835e5-33ce-4eb4-94df-d43f31618de0"
    name: "track-search"
    description: "support searching for tracklets"
    source: "field deprecated"
  }
) {
    id
  }
}
```

```js
mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
    dataRegistryId: "885835e5-33ce-4eb4-94df-d43f31618de0"
    majorVersion: 1
    schema: {
      type: "object"
      title: "track-search"
      required: [
       "tdoId",
       "label",
       "tags"
      ]
      properties: {
        tags: {
            type: "array",
            items: {
                type: "string"
            }
        },
        label: {
            type: "string"
        },
        tdoId: {
            type: "string"
        }
      }
      description: "support tracklet search"
    }
  }
) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

```js
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```
