import { buildReadOnlyUrl } from '@application/credential/actions/util/buildUrl';
import NodeCache from 'node-cache';

export async function generateThumbnailUrl({
  orgId,
  tdoId,
  trackletId,
  type,
  cache,
}: {
  orgId: string;
  tdoId: string;
  trackletId: string;
  type: 'first' | 'best';
  cache?: NodeCache;
}) {
  const thumbnailPath = `thumbnail/${orgId}/${tdoId}/${trackletId}_${type}.jpg`;
  const thumbnailUrl = await buildReadOnlyUrl(thumbnailPath, cache);
  return thumbnailUrl;
}
