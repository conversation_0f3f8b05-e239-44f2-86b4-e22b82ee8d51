// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
      "mkdirp",
      "tsmerge",
      "githubusercontent",
      "streetsidesoftware",
      "vsmarketplacebadge",
      "visualstudio",
      "powerbi",
      "pangea",
      "badeball",

      "aiware",
      "veritone",

      "tdoid",
      "tdos",
      "sdos",
      "udrs",
      "jsondata",
      "tracklet",
      "tracklets",
      "matchgroup",
      "clearall",
      "rootfolder",
      "clickoff",
      "videocam",
      "setstate",
      "multilines",
      "appbar",

      "tshirt", // TODO: look at whether possible to update this
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [
  ]
}