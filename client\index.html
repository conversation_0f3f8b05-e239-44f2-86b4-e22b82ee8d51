<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Veritone Track</title>
  <script nonce="NGINX_CSP_NONCE">
    if (!location.href.includes('local.veritone.com')) {
      window['config'] = {}
      window['build'] = 'GIT_COMMIT'
      window['version'] = 'VERSION'
    }
    window.config = window.aiwareConfig || window.config;
  </script>
  <script type="module">
    const aiwareJSPath = window.config?.aiwareJSPath;
    const aiwareJSVersion = window.config?.aiwareJSVersion;
    document
      .getElementsByTagName('head')[0]
      .insertAdjacentHTML(
        'afterbegin',
        `<link rel="stylesheet" type="text/css" href="${aiwareJSPath + aiwareJSVersion}/index.esm.css">`
      );
    const aiwareJSScript = document.createElement('script');
    aiwareJSScript.setAttribute(
      'src',
      `${aiwareJSPath + aiwareJSVersion}/index.esm.js`
    );
    aiwareJSScript.setAttribute('type', 'module');
    aiwareJSScript.setAttribute('nonce', 'NGINX_CSP_NONCE');
    document.head.appendChild(aiwareJSScript);

  </script>
</head>

<body class="dark">
  <div id="root"> </div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
