import fs from 'fs';
import path from 'path';
import { Attributes, AttributesJson } from '../../../../../../types/tracker';
import { get } from 'lodash';

let cachedAttributesJson: {
  person: Attributes;
  vehicle: Attributes;
} | null = null;

const convertJsonAttributes = (jsonAttributes: AttributesJson) => {
  const attributes: Attributes = {};
  Object.values(jsonAttributes).forEach(({ key, label, value }) => {
    if (!attributes[key]) {
      attributes[key] = [{ label, value, key }];
    } else {
      attributes[key].push({ label, value, key });
    }
  });
  return attributes;
};

function loadJsonFile(trackerEngineId: string) {
  try {
    const configPath = path.join(
      __dirname,
      '../../../../../../assets/attributes.json'
    );
    const configFile = fs.readFileSync(configPath, 'utf8');
    const attributesJson = JSON.parse(configFile);
    let personJsonAttributes = attributesJson.attr_v2;
    let vehicleJsonAttributes = attributesJson.attr_vehicle_v17_1_0;

    const personAttributeKey = `${trackerEngineId}_attr`;
    const vehicleAttributeKey = `${trackerEngineId}_attr_vehicle`;

    if (personAttributeKey in attributesJson) {
      personJsonAttributes = get(attributesJson, personAttributeKey);
    }
    if (vehicleAttributeKey in attributesJson) {
      vehicleJsonAttributes = get(attributesJson, vehicleAttributeKey);
    }

    cachedAttributesJson = {
      person: convertJsonAttributes(personJsonAttributes),
      vehicle: convertJsonAttributes(vehicleJsonAttributes),
    };
    return cachedAttributesJson;
  } catch (error) {
    console.error(`Failed to read or parse the configuration file: ${error}`);
  }
}

function getJsonConfig(trackerEngineId: string) {
  if (!cachedAttributesJson) {
    return loadJsonFile(trackerEngineId);
  }
  return cachedAttributesJson;
}

export function buildAttributes(
  tags: { key: string; value?: string }[],
  trackerEngineId: string
) {
  getJsonConfig(trackerEngineId);

  const cachedAttributesCombined = {
    ...cachedAttributesJson?.person,
    ...cachedAttributesJson?.vehicle,
  };

  const attributes: Attributes = {};
  const unknownKey: string[] = [];
  for (const tag of tags ?? []) {
    const lowKey = tag.key;
    const value = tag.value ?? '';
    if (cachedAttributesCombined && cachedAttributesCombined[lowKey]) {
      if (!attributes[lowKey]) {
        attributes[lowKey] = [];
      }
      attributes[lowKey].push({
        label: `${lowKey}${value}`,
        key: lowKey,
        value: value,
      });
    } else {
      unknownKey.push(lowKey);
    }
  }
  return { attributes, unknownKey };
}
