import { Button } from '@mui/material';
import Checkbox from '@mui/material/Checkbox';
import Popover from '@mui/material/Popover';
import './ExportDialog.scss';

interface ExportDialogProps {
  matchGroupName: string;
  anchorEl: HTMLElement | null;
  onClose: () => void;
}


function ExportDialog({ matchGroupName, anchorEl, onClose }: ExportDialogProps) {
  const open = Boolean(anchorEl);
  const id = open ? 'export-popover' : undefined;

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      sx={{ marginTop: '10px' }}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <div className="export-dialog">
        <h2 className="export-dialog__heading">Export</h2>
        <p className="export-dialog__description">If you are satisfied with your searches you can export them to illuminate or compile them into a video file. Are you ready to export?</p>
        <div className="export-dialog__checkbox">
          <div className="export-dialog__checkbox-item">
            <Checkbox sx={{ padding: 0 }} />
            <span className="export-dialog__checkbox-label">{`Export [${matchGroupName}] to Illuminate`}</span>
          </div>
          <div className="export-dialog__checkbox-item">
            <Checkbox sx={{ padding: 0 }} />
            <span className="export-dialog__checkbox-label">Export Selections to Illuminate</span>
          </div>
        </div>
        <div className="export-dialog__buttons">
          <Button className="export-dialog__button export-dialog__button--cancel" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" className="export-dialog__button export-dialog__button--export">
            Export
          </Button>
        </div>
      </div>
    </Popover>
  );
}

export default ExportDialog;
