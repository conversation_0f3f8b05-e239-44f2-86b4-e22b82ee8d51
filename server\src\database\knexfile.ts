// export default {
//   client: 'mssql',
//   connection: {
//     user: process.env.MSSQL_USER,
//     password: process.env.MSSQL_PASSWORD,
//     server: process.env.MSSQL_HOST,
//     port: process.env.MSSQL_PORT,
//     database: process.env.MSSQL_DATABASE,
//     connectionTimeout: 30000,
//     pool: {
//       min: 2,
//       max: 11,
//     },
//     options: {
//       encrypt: true,
//     },
//   },
//   migrations: {
//     directory: __dirname + '/migrations',
//     extension: 'ts',
//     loadExtensions: ['.js', '.ts'],
//   },
//   useNullAsDefault: true,
// };
