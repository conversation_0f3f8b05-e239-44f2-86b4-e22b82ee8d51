import { Context } from '../../../types';
import { responses } from '@tracker/graphQL';
import { ActionError } from '@common/errors';

const deleteGeneratedTimeline = async <
  ReqPayload,
  Data extends Partial<
    responses.getMatchGroup & { generatedTimelineId: string }
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data } = context;
  const { generatedTimelineId, matchGroup } = data;
  if (!generatedTimelineId) {
    throw new ActionError('No generatedTimelineId provided');
  }
  if (!matchGroup) {
    throw new ActionError('No matchGroup found');
  }

  const generatedTimelines = matchGroup.generatedTimelines?.filter(
    ({ id }) => id !== generatedTimelineId
  );

  const new_data = Object.assign({}, data, {
    matchGroup: { ...matchGroup, generatedTimelines },
  });
  const new_context = Object.assign({}, context, { data: new_data });
  return new_context;
};
export default deleteGeneratedTimeline;
