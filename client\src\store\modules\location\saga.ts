import { all, fork, select, take, takeLatest } from "typed-redux-saga";
import { modules } from "@veritone/glc-redux";
import { onRouteChanged, selectHistory, selectCurrentLocation } from "./slice";
import { bootDidFinish, bootFinished } from "../app/slice";
import { isEqual } from "lodash";
// import { routes as routesMap } from "@components/common/Routes/Routes";

const {
  user: { userIsAuthenticated },
} = modules;

// let currentRouteTask: Task;

export function* watchRouteSagasHandle() {
    const hasBooted = yield* select(bootDidFinish);
    if (!hasBooted) {
        yield* take(bootFinished);
    }
    const userIsAuthed = yield* select(userIsAuthenticated);
    if (!userIsAuthed) {
        return;
    }

    const currentLocation = yield* select(selectCurrentLocation);
    const history = yield* select(selectHistory);

    if (
        currentLocation.pathname === history?.pathname &&
        isEqual(currentLocation.payload, history?.payload)
    ) {
        return;
    }

    // if (currentRouteTask) {
    //     yield* cancel(currentRouteTask);
    // }

    // const routeSaga = find(routesMap, {path: currentLocation.pathname});
    // if (routeSaga?.saga) {
    //     currentRouteTask = yield* fork(routeSaga.saga);
    // }
}

export function* watchRoutesSagas() {
  yield* takeLatest(onRouteChanged, watchRouteSagasHandle);
}

export default function* routes() {
  yield* all([fork(watchRoutesSagas)]);
}
