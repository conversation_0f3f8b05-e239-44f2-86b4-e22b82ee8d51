import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            contentTemplates: [{ id: 'id', sdo: { data: {} } }],
          },
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [
                {
                  id: 'id',
                  name: 'name',
                  tags: ['tag1', 'tag2'],
                  createdBy: 'createdBy',
                  createdByName: 'createdByName',
                  description: 'description',
                  eventStartDate: 'eventStartDate',
                  eventEndDate: 'eventEndDate',
                  createdDateTime: 'createdDateTime',
                  modifiedDateTime: 'modifiedDateTime',
                },
                {
                  id: 'id2',
                  name: 'name2',
                  tags: ['tag3', 'tag4'],
                  createdBy: 'createdBy2',
                  createdByName: 'createdByName',
                  description: 'description2',
                  eventStartDate: 'eventStartDate2',
                  eventEndDate: 'eventEndDate2',
                  createdDateTime: 'createdDateTime2',
                  modifiedDateTime: 'modifiedDateTime2',
                },
                {
                  id: 'id3',
                  name: 'name3',
                  tags: ['tag3', 'tag5'],
                  createdBy: 'createdBy3',
                  createdByName: 'createdByName',
                  description: 'description3',
                  eventStartDate: 'eventStartDate3',
                  eventEndDate: 'eventEndDate3',
                  createdDateTime: 'createdDateTime3',
                  modifiedDateTime: 'modifiedDateTime3',
                },
              ],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
    }
  ),
}));

describe('get tags', () => {
  it('get tags', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/tags')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.searchMedia,
      {
        search: {
          index: ['mine'],
          limit: 1000,
          offset: 0,
          query: {
            operator: 'or',
            conditions: [],
          },
          type: 'schemaId',
        },
      }
    );
  });
});
