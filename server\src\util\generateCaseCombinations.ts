const generateCaseCombinations = (str: string): string[][] =>
  str
    .trim()
    .split(' ')
    .map((word) => {
      const combinations: string[] = [];
      const seen = new Set();

      // generate all combinations for the word
      for (let i = 0; i < Math.pow(2, word.length); i++) {
        let combination = '';

        for (let j = 0; j < word.length; j++) {
          // for each character, uppercase or lowercase based on the bit at the corresponding position in i
          combination +=
            (i & Math.pow(2, j)) === 0
              ? word[j].toLowerCase()
              : word[j].toUpperCase();
        }

        // if new, add to the combinations array and the seen set
        if (!seen.has(combination)) {
          combinations.push(combination);
          seen.add(combination);
        }
      }

      return combinations;
    });

export default generateCaseCombinations;
