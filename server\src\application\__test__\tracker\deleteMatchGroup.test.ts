import NodeCache from 'node-cache';
import Redis from 'ioredis';
import supertest from 'supertest';
import { Variables } from 'graphql-request';
import createExpressApp from '@application/express';
import { Context, RequestHeader } from '@application/types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import createTrackerApp from '@tracker/index';
import RedisWrapper from '../../../redisWrapper';
import createConfig from '../../../config';
import env from '../../../env';
import { queries, responses } from '../../tracker/graphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getMe)) {
        return Promise.resolve({
          me: {
            id: 'mock-userId',
            email: 'mock-userEmail',
            organizationId: 'mock-userOrganizationId',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes(queries.getMatchGroup)) {
        return Promise.resolve({
          structuredDataObject: {
            id: 'a-matchgroup-id',
            data: {
              id: 'a-matchgroup-id',
              name: 'Test Match Group',
              eventId: 'an-event-id',
            },
          },
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
    }
  ),
}));

jest.mock('../../tracker/actions/getMatchGroups', () =>
  jest.fn(async (context: Context<object, responses.getMatchGroups>) => ({
    ...context,
    data: {
      ...context.data,
      matchGroups: {
        pagination: {
          pageSize: 50,
          currentPage: 1,
          totalPages: 0,
          totalCount: 0,
        },
        sort: {
          field: 'field',
          direction: 'direction',
        },
        eventId: 'eventId',
        searchResults: [],
      },
    },
  }))
);

jest.mock('../../tracker/actions/getJsonData', () => ({
  fetchJsonData: jest.fn(),
}));

describe('delete matchgroup', () => {
  it('delete matchgroup successfully', async () => {
    const config = await createConfig({ env });
    const redisWrapper = new RedisWrapper(new Redis(), config.log);

    redisWrapper.event = {
      ...redisWrapper.event,
      set: jest.fn(),
      get: jest.fn().mockResolvedValue({
        id: 'an-event-id',
        name: 'EventName',
      }),
    };

    const trackerApp = createTrackerApp({
      log: config.log,
      cache: new NodeCache(),
      redisWrapper: redisWrapper,
    });
    const expressApp = await createExpressApp({
      config: { ...config, trackerApp },
    });

    const resp = await supertest(expressApp)
      .delete('/api/v1/match-groups/a-matchgroup-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteStructuredData,
      {
        sdoId: 'a-matchgroup-id',
        schemaId: 'schemaId',
      }
    );

    expect(redisWrapper.event.get).toHaveBeenCalledWith(
      'an-event-id',
      'mock-userOrganizationId'
    );

    expect(redisWrapper.event.set).toHaveBeenCalledWith(
      'an-event-id',
      'mock-userOrganizationId',
      expect.anything()
    );
  });

  it('cannot delete matchgroup w/o matchgroupId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/match-groups')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
