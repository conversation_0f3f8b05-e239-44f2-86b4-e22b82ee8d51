import { responses } from '@tracker/graphQL';
import { Event } from '../../../../../../types/tracker';
import { Context } from '../../../types';

const updateRedisEvent = async <
  ReqPayload,
  Data extends responses.getMe & { eventId: string } & {
    redisEvent: Partial<Event>;
  },
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, redisWrapper } = context;
  if (redisWrapper && data.userOrganizationId) {
    const redisEvent = await redisWrapper.event.get(
      data.eventId,
      String(data.userOrganizationId)
    );
    if (redisEvent) {
      const newEvent: Event = {
        ...redisEvent,
        ...data.redisEvent,
      };
      await redisWrapper.event.set(
        data.eventId,
        String(data.userOrganizationId),
        newEvent
      );
    }
  }

  return context;
};

export default updateRedisEvent;
