import { trackletTimeSearch } from './elasticSearch';
describe('trackletTimeSearch', () => {
  // This is a driver programe to help troubleshooting trackletTimeSearch with real data.
  // It is disabled on purpose. To enable it, remove the 'x' before 'it'.
  // Provide trackletId and bearer token for troubleshooting. (the gql url is the apiRoot in apiConfig.json)
  xit('driver programe to test trackletTimeSearch', async () => {
    const trackletId = '36a0af0f-8e18-438c-ae40-28976a9968ae';
    const context = { log: console } as any;
    const headers = {
      authorization: 'bearer 0171865b-f368-42bb-b41f-abcfc19aa2cd',
    };
    const response = await trackletTimeSearch({ trackletId, context, headers });
    expect(response.startTime).not.toBeUndefined();
    expect(response.endTime).not.toBeUndefined();
  }, 10000);
});
