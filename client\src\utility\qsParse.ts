import qs from "qs";

// TODO: This cast to T is not safe -- can we really guarantee that the parsed query string will always be of type T?
// I've swapped from T extending unknown to T extends qs.ParsedQs to make it more clear that T is a type of parsed query string.
// but this is only a slight improvement
export const qsParse = <T extends qs.ParsedQs>(search: string) => qs.parse(search, { ignoreQueryPrefix: true }) as T;
