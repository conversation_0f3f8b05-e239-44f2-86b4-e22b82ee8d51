# Event

### Create Schema

```js
mutation createDataRegistry {
  createDataRegistry(
    input: {
    id: "ab2a98f0-dc3d-486d-8d9a-c2147aeb43bc"
    name: "track-event"
    description: "Track Event"
    source: "field deprecated"
  }
) {
    id
  }
}
```

```js
mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
    dataRegistryId: "ab2a98f0-dc3d-486d-8d9a-c2147aeb43bc"
    majorVersion: 1
    schema: {
      type: "object"
      title: "track-event"
      description: "Track: Event"
      required: [
        "id"
        "name"
        "description"
        "createdBy"
        "createdByName"
        "eventStartDate"
        "eventEndDate"
        "tags"
        "createdDateTime"
        "modifiedDateTime"
      ]
      properties: {
        id: { type: "string" }
        name: { type: "string" }
        description: { type: "string" }
        createdBy: { type: "string" }
        createdByName: {type: "string" }
        eventStartDate: { type: "string" }
        eventEndDate: { type: "string" }
        tags: { type: "array", items: { type: "string" } }
        createdDateTime: { type: "string" }
        modifiedDateTime: { type: "string" }
      }
    }
  }
) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

```js
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```
