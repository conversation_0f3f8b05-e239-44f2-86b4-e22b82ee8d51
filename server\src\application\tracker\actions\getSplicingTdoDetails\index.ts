import { Context, RequestHeader } from '@application/types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { ActionError, GraphQLError } from '@common/errors';
import { responses } from '../../graphQL';
import {
  JobStatus,
  TemporalData,
  VideoSpliceDetails,
} from '../../../../../../types/tracker';
const batchQuerySize = 10;

export async function getSplicingTdoDetails<ReqPayload, Data>({
  tdoIds,
  context,
  headers,
}: {
  tdoIds: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  if (tdoIds.length === 0) {
    throw new ActionError('No tdoIds provided');
  }

  try {
    const validData: TemporalData[] = [];
    for (let i = 0; i < tdoIds.length; i += batchQuerySize) {
      const batch = tdoIds.slice(i, i + batchQuerySize);
      const updateQuery: Array<string> = [];
      batch.forEach((tdoId, index) => {
        updateQuery.push(
          `temporalDataObject_${tdoId}_${index}:temporalDataObject(id: "${tdoId}") {
            id
            name
            jobs {
              records {
                status
              }
            }
            assets(assetType: "spliced-media") {
              count
              records {
                signedUri
                assetSize
                details
              }
            }
          }`
        );
      });

      const query = `query { ${updateQuery.join('\n')} }`;

      // The forth parameter makes this request error tolerant - if a TDO does not exist, it will NOT throw, it will return the rest from the batch
      const temporalDataObject = await callGQL<
        responses.getGeneratedTimelineDetails,
        ReqPayload,
        Data
      >(context, headers, query, {}, true);
      const result: TemporalData[] = Object.values(temporalDataObject);
      validData.push(...result.filter((res) => res?.id));
    }
    const tdos: {
      [key: string]: VideoSpliceDetails;
    } = {};

    for (const data of validData) {
      if (!tdos[data.id]) {
        // mock for testing
        // if (
        //   data.assets.records.length > 0 &&
        //   data?.jobs?.records?.[0]?.status === JobStatus.Complete
        // ) {
        //   data.assets.records[0].details = {
        //     width: 480,
        //     height: 270,
        //     duration: 14.901888,
        //     fileType: 'video/mp4',
        //     videoFrameRate: 30,
        //     audioSampleRate: 44100,
        //     audioNumChannels: 2,
        //   };
        // }

        tdos[data.id] = {
          status: data?.jobs?.records?.[0]?.status,
          ...(data?.jobs?.records?.[0]?.status === JobStatus.Complete && {
            downloadUrl: data?.assets?.records?.[0]?.signedUri,
            videoSizeBytes: data?.assets?.records?.[0]?.assetSize ?? 0,
            outputFormat: data?.assets?.records?.[0]?.details?.fileType ?? '',
            // Note: videoLengthMs is in milliseconds and duration in seconds
            videoLengthMs:
              (data?.assets?.records?.[0]?.details?.duration ?? 0) * 1000,
            resolution:
              data?.assets?.records?.[0]?.details?.width &&
              data?.assets?.records?.[0]?.details?.height
                ? `${data?.assets?.records?.[0]?.details?.width}x${data?.assets?.records?.[0]?.details?.height}`
                : '',
          }),
        };
      }
    }
    return tdos;
  } catch (e) {
    console.error(e);
    throw new GraphQLError(e);
  }
}
