import React from 'react';
import { render, waitFor, screen, fireEvent } from '@testing-library/react';
import { Table, Column } from '@components/common';
import { I18nProvider, LOCALES } from '@i18n';

describe('Table common component', () => {
  it('renders', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 0,
            totalPages: 0,
            currentPage: 0,
            pageSize: 10
          }}
          rowData={[]}
        />
      </I18nProvider>
    );

    expect(screen.getByTestId('table')).toBeInTheDocument();
  });

  it('renders 3 columns in the table', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table pagination={{
          totalCount: 3,
          totalPages: 1,
          currentPage: 1,
          pageSize: 10
        }}>
          <Column dataKey={''} grow={0} title={''} />
          <Column dataKey={''} grow={0} title={''} />
          <Column dataKey={''} grow={0} title={''} />
        </Table>
      </I18nProvider>
    );

    expect(screen.getByTestId('column-0')).toBeInTheDocument();
    expect(screen.getByTestId('column-1')).toBeInTheDocument();
    expect(screen.getByTestId('column-2')).toBeInTheDocument();
  });

  it('does not render a bad column type', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table pagination={{
          totalCount: 3,
          totalPages: 1,
          currentPage: 1,
          pageSize: 10
        }} >
          <Table />
        </Table>
      </I18nProvider>
    );

    expect(screen.queryByTestId('column-0')).not.toBeInTheDocument();
  });

  it('renders 3 rows when three data points exist with a supplied RowComponent', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 0 }, { id: 1, a: 1 }, { id: 2, a: 2 }]}
          RowComponent={() => <div data-testid="table-custom-row" />}
        />
      </I18nProvider>
    );

    expect(screen.queryAllByTestId('table-custom-row')).toHaveLength(3);
  });

  it('renders 3 rows when three data points exist in text rows', () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 0 }, { id: 1, a: 1 }, { id: 2, a: 2 }]}
         />
      </I18nProvider>
    );

    expect(screen.queryAllByTestId('table-text-row')).toHaveLength(3);
  });

  it('sorts by ascending when a column is clicked for the first time', async () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 1, b: 'b' }, { id: 1, a: 0, b: 'c' }, { id: 2, a: 2, b: 'a' }]}
          RowComponent={
            ({ rowData }) => <div data-testid="table-custom-row">
              {`${rowData.a}-${rowData.b}`}
            </div>
          }
        >
          <Column dataKey="a" sortable grow={0} title={''} />
          <Column dataKey="b" sortable grow={0} title={''} />
        </Table >
      </I18nProvider>
    );

    // Rows are ordered in ascending order after being clicked the first time
    fireEvent.click(screen.getByTestId('column-0'));

    await waitFor(() => {
      const rows = screen.queryAllByTestId('table-custom-row');

      ['1-b', '0-c', '2-a'].forEach((text, idx) =>
        expect(rows[idx]).toHaveTextContent(new RegExp(`^${text}$`))
      );
    });
  });

  it('sorts by descending when a column is clicked for the second time', async () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 1, b: 'b' }, { id: 1, a: 0, b: 'c' }, { id: 2, a: 2, b: 'a' }]}
          RowComponent={
            ({ rowData }) => <div data-testid="table-custom-row">
              {`${rowData.a}-${rowData.b}`}
            </div>
          }
        >
          <Column dataKey="a" sortable grow={0} title={''} />
          <Column dataKey="b" sortable grow={0} title={''} />
        </Table>
      </I18nProvider>
     );


    // Rows are ordered in ascending order after being clicked the first time
    fireEvent.click(screen.getByTestId('column-0'));

    // Rows are ordered in descending order after being click the second time
    fireEvent.click(screen.getByTestId('column-0'));

    await waitFor(() => {
      const rows = screen.queryAllByTestId('table-custom-row');
      ['1-b', '0-c', '2-a'].forEach((text, idx) =>
        expect(rows[idx]).toHaveTextContent(new RegExp(`^${text}$`))
      );
    });
  });

  it('returns to original sort order when a column is clicked for the third time', async () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 1, b: 'b' }, { id: 1, a: 0, b: 'c' }, { id: 2, a: 2, b: 'a' }]}
          RowComponent={
            ({ rowData }) => <div data-testid="table-custom-row">
              {`${rowData.a}-${rowData.b}`}
            </div>
          }
        >
          <Column dataKey="a" sortable grow={0} title={''} />
          <Column dataKey="b" sortable grow={0} title={''} />
        </Table>
      </I18nProvider>
    );

    // Rows are ordered in ascending order after being clicked the first time
    fireEvent.click(screen.getByTestId('column-0'));

    // Rows are ordered in descending order after being click the second time
    fireEvent.click(screen.getByTestId('column-0'));

    // Rows return to original order after being clicked three times
    fireEvent.click(screen.getByTestId('column-0'));

    await waitFor(() => {
      const rows = screen.queryAllByTestId('table-custom-row');

      ['1-b', '0-c', '2-a'].forEach((text, idx) =>
        expect(rows[idx]).toHaveTextContent(new RegExp(`^${text}$`))
      );
    });
  });

  it('sorts by ascending when a column is clicked for the first time, different column and sort algorithim', async () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Table
          pagination={{
            totalCount: 3,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10
          }}
          rowData={[{ id: 0, a: 1, b: 'b' }, { id: 1, a: 2, b: 'a' }, { id: 2, a: 0, b: 'c' }]}
          RowComponent={
            ({ rowData }) => <div data-testid="table-custom-row">
              {`${rowData.a}-${rowData.b}`}
            </div>
          }
        >
          <Column dataKey="a" sortable grow={0} title={''} />
          <Column dataKey="b" sortable grow={0} title={''} />
        </Table >
      </I18nProvider>
    );

    // Rows are ordered in ascending order after being clicked the first time
    fireEvent.click(screen.getByTestId('column-1'));

    await waitFor(() => {
      const rows = screen.queryAllByTestId('table-custom-row');

      ['1-b', '2-a', '0-c'].forEach((text, idx) =>
        expect(rows[idx]).toHaveTextContent(new RegExp(`^${text}$`))
      );
    });
  });
});
