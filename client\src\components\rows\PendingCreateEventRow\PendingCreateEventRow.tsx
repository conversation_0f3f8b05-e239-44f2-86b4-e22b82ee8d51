import { AlertLevel, createSnackNotification } from '@components/common';
import './PendingCreateEventRow.scss';
import { RowProps } from '@components/common/Table/Table';
import { Event } from '@shared-types/tracker';
import { toLocalTime } from '@utility/convert';
import { I18nTranslate } from '@i18n';

export function PendingCreateEventRow({
  colData,
  rowData,
}: RowProps<Event>) {
  const { id } = rowData;

  const renderCell = ({ dataKey }: { dataKey: string }) => {
    switch (dataKey) {
      case 'name':
        return (
          <div
            role="cell"
            className="pending-event-row__cell-name"
            data-testid="pending-event-row-name"
          >
            {rowData[dataKey]}
          </div>
        );
      case 'eventStartDate':
        return (
          <div role="cell" className="pending-event-row__cell-name">
            {I18nTranslate.TranslateDate(toLocalTime(rowData[dataKey]))}
          </div>
        );
    }
  };

  const eventRowOnClick = () => {
    createSnackNotification(
      AlertLevel.Warning,
      'Warning',
      'Event has not yet processed.'
    );
  };

  const eventRowOnDoubleClick = () => {
    createSnackNotification(
      AlertLevel.Warning,
      'Warning',
      'Event has not yet processed.'
    );
  };

  return (
    <div
      role="row"
      className="pending-event-row"
    >
      <div
        className="pending-event-row__row"
        data-testid={`event-row-${id}`}
        onClick={eventRowOnClick}
        onDoubleClick={eventRowOnDoubleClick}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="pending-event-row__cell"
            data-testid={`event-row-cell-${id}-${index}`}
            key={`EventRowCell-${id}-${index}-${dataKey}`}
            style={{
              flexGrow: grow,
              width,
              minWidth: width ?? minWidth,
            }}
          >
            {renderCell({ dataKey })}
          </div>
        ))}
      </div>
    </div>
  );
}

export default PendingCreateEventRow;
