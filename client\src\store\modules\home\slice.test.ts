import {
  SearchEventsResponse,
  SearchFilesResponse,
} from '@shared-types/responses';
import {
  setEventPagination,
  getEvents,
  getFiles,
  setFilesPagination,
  selectEvents,
  selectFiles,
} from './slice';
import { configureAppStore } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import axios from 'axios';

jest.mock('@utility/getApiAuthToken', () => jest.fn());
jest.mock('axios');
(getApiAuthToken as jest.Mock).mockReturnValue('test-token');

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetEventsResponse: SearchEventsResponse = {
  results: [
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'New event name',
      description: 'New description',
      createdDateTime: '2024-04-11T21:44:35.441Z',
      modifiedDateTime: '2024-04-11T21:44:52.430Z',
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 10,
      filesCount: 10,
    },
    {
      eventEndDate: '2024-04-10T17:02:10Z',
      createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
      createdByName: 'Test User',
      name: 'Event Test 1',
      description: 'New description',
      createdDateTime: '2024-04-11T00:10:13.876Z',
      modifiedDateTime: '2024-04-11T00:10:24.681Z',
      id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventStartDate: '2024-04-10T17:02:10Z',
      tags: ['Tag 1', 'Tag 2'],
      matchGroupsCount: 0,
      filesCount: 0,
    },
  ],
  currentPage: 1,
  pageSize: 10000,
  totalCount: 2,
  totalPages: 1,
};

const mockGetFilesResponse: SearchFilesResponse = {
  results: [
    {
      id: 'b17597d1-56a3-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_1.mp4',
      status: 'processed',
      length: 256,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 128,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: 'abcdefg-56a3-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_2.mp4',
      status: 'processing',
      length: 512,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 1024,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
    {
      id: 'abcdefg-1234-4c2e-a7c5-6c094e5fa25b',
      createdByName: 'Test User',
      fileName: 'test_file_3.mp4',
      status: 'error',
      length: 128,
      uploadDate: '2024-04-10T17:02:10Z',
      location: 'GPS',
      fileType: 'video',
      fileSize: 512,
      eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
      eventName: 'Test Event',
      thumbnailUrl: 'thumbnailUrl',
      primaryAsset: {
        signedUri: 'signedUri',
      },
      streams: [
        {
          uri: 'uri',
          protocol: 'protocol',
        },
      ],
      frameRate: 25,
    },
  ],
  currentPage: 1,
  pageSize: 100,
  totalCount: 3,
  totalPages: 1,
};

const initialStateForMock = {
  home: {
    events: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    files: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    pendingEvents: [],
  },
};

describe('home slice', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should set pagination for events', () => {
    // pagination object
    const pagination = { currentPage: 4, pageSize: 50 };

    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // dispatch the set event pagination action
    mockedStore.dispatch(setEventPagination(pagination));

    // expect pagination to be in the state
    expect(mockedStore.getState().home.events.currentPage).toEqual(
      pagination.currentPage
    );
    expect(mockedStore.getState().home.events.pageSize).toEqual(
      pagination.pageSize
    );
  });

  it('should get events', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.resolve({ data: mockGetEventsResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(getEvents({ currentPage: 1, pageSize: 10000 }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectEvents(newState)).toEqual({
      ...mockGetEventsResponse,
      status: 'idle',
    });
  });

  it('should throw error while getting events', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/events') && method === 'get') {
        return Promise.reject({ message: 'error' });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(getEvents({ currentPage: 1, pageSize: 10000 }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'failure' status
    expect(selectEvents(newState)).toEqual({
      ...selectEvents(newState),
      status: 'failure',
    });
  });

  it('should set pagination for files', () => {
    // pagination object
    const pagination = { currentPage: 4, pageSize: 50 };

    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // dispatch the set event pagination action
    mockedStore.dispatch(setFilesPagination(pagination));

    // expect pagination to be in the state
    expect(mockedStore.getState().home.files.currentPage).toEqual(
      pagination.currentPage
    );
    expect(mockedStore.getState().home.files.pageSize).toEqual(
      pagination.pageSize
    );
  });

  it('should get files and use sortBy createdDate in desc order by default', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/files') && url?.includes('sortBy=createdTime&sortDirection=desc') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(getFiles({ currentPage: 1, pageSize: 10000 }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse,
      status: 'idle',
    });
  });

  it('should get files', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(getFiles({ currentPage: 1, pageSize: 10000 }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse,
      status: 'idle',
    });
  });

  it('should throw error while getting files', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/files') && method === 'get') {
        return Promise.reject({ message: 'error' });
      }
    });

    // dispatch events action
    await mockedStore.dispatch(getFiles({ currentPage: 1, pageSize: 10000 }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'failure' status
    expect(selectFiles(newState)).toEqual({
      ...selectFiles(newState),
      status: 'failure',
    });
  });
});
