import { render, screen, fireEvent } from '@testing-library/react';
import NotFound from '@pages/NotFound/NotFound';
import { BrowserRouter, useNavigate } from 'react-router-dom';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
}));

describe('NotFound', () => {
    it('renders NotFound component', () => {
        const { container } = render(
            <BrowserRouter>
                <NotFound />
            </BrowserRouter>
          );
        expect(container).toMatchSnapshot();
        expect(screen.getByTestId('NotFound-header')).toBeInTheDocument();
        expect(screen.getByTestId('NotFound-text')).toBeInTheDocument();
    });

    it('navigates to home when button is clicked', () => {
        const navigate = jest.fn();
        (useNavigate as jest.Mock).mockReturnValue(navigate);
        render(<NotFound />);
        const button = screen.getByTestId('NotFound-button');
        fireEvent.click(button);
        expect(navigate).toHaveBeenCalledWith('/');
    });
});
