import { act, render, screen, waitFor } from '@testing-library/react';
import TimelineGenerateVideoDialog from './TimelineGenerateVideoDialog';
import { JobStatus } from '@shared-types/tracker';
import { I18nProvider, LOCALES } from '@i18n';

describe('TimelineVideoDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  const mockOnClose = jest.fn();
  const mockOnSaveAndGenerateTimeline = jest.fn();
  const mockMatchGroup = {
    id: '0677087b-831b-407d-bce0-f143978898e9',
    name: 'GreenShirtWoman',
    eventId: '2fc73b9e-f5f8-4dd4-8483-1c4971b4041e',
    searches: [
      {
        id: 'd7510dd8-f57b-4bd0-9ee0-b00a4c81fa0e',
        searchName: 'Potential Match Search 0',
        searchTime: '2024-08-09T15:12:13.815Z',
        referenceTrackletId: '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
      },
    ],
    createdDateTime: '2024-08-09T15:09:32.951Z',
    timelineProject: {
      groups: [
        {
          id: '9df3917e-230c-4668-a291-e0aad6751032',
          name: 'Default Group',
          tracklets: [
            {
              orgId: '1',
              type: 'person',
              fileId: '**********',
              fileName: 'Walking Tour 1.mp4',
              attributes: {
                body: [{ key: '1', label: 'Body', value: 'Average' }],
                face: [{ key: '2', label: 'Face', value: 'Left' }],
                hair: [
                  { key: '3', label: 'Hair Color', value: 'colorBlack' },
                  { key: '4', label: 'Hair Type', value: 'typeLong' },
                ],
                lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                upper: [
                  { key: '6', label: 'Upper Color', value: 'colorGreen' },
                  { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                ],
                gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                accessories: [
                  { key: '9', label: 'Accessories', value: 'BagAny' },
                ],
              },
              confidence: 2.000019,
              stopTimeMs: 3385666,
              trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
              startTimeMs: 3384233,
              thumbnailUrls: {
                best: '',
              },
            },
          ],
        },
      ],
    },
    modifiedDateTime: '2024-08-19T17:40:16.782Z',
    selectedTracklets: [
      '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
      '6b123adc-1fe7-4113-b2b2-8cac16962e08',
      '836f05f4-22dd-477c-9afa-85ebc4658dea',
    ],
    generatedTimelines: [
      {
        id: '3240004533',
        name: 'GreenShirtWoman-timeline-1.mp4',
        tdoId: '3240004533',
        status: JobStatus.Complete,
        timeline: {
          groups: [
            {
              id: 'd13ae78d-990e-4a63-9189-d8a30ce01d4c',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '**********',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 1385300,
                  trackletId: '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
                  startTimeMs: 1384100,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
            {
              id: '3896f6fa-adc2-43eb-99a6-9cd095e94447',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '**********',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 3385666,
                  trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
                  startTimeMs: 3384233,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
          ],
        },
        resolution: '1280x720',
        downloadUrl: '',
        outputFormat: 'video/mp4',
        createdUserId: '5a638922-bc0a-46ca-b43d-b12363888221',
        videoLengthMs: 2681.641,
        videoSizeBytes: 1232530,
        createdDateTime: '2024-08-19T15:52:13.021Z',
      },
      {
        id: '3240004534',
        name: 'GreenShirtWoman-timelines-2.mp4',
        tdoId: '3240004534',
        status: JobStatus.Complete,
        timeline: {
          groups: [
            {
              id: 'f608f483-f061-48fb-8f92-c3c17ed65a56',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '**********',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 3385666,
                  trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
                  startTimeMs: 3384233,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
          ],
        },
        resolution: '1280x720',
        downloadUrl: '',
        outputFormat: 'video/mp4',
        createdUserId: 'userId',
        videoLengthMs: 1433.3329999999999,
        videoSizeBytes: 675705,
        createdDateTime: '2024-08-19T15:53:01.189Z',
      },
    ],
  };

  test('renders TimelineVideoDialog component', async () => {
    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <TimelineGenerateVideoDialog
          open
          onClose={mockOnClose}
          onSaveAndGenerateTimeline={mockOnSaveAndGenerateTimeline}
          matchGroup={mockMatchGroup}
        />
      </I18nProvider>
    );

    // Renders the component
    expect(
      screen.getByTestId('timeline-generate-video-dialog')
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'You can optionally name your file below. This timeline generated video will be saved to'
      )
    ).toBeInTheDocument();

    // Renders the input component with generated timeline name
    const inputElement = screen.getByLabelText('File Name');
    expect(inputElement).toHaveValue('GreenShirtWoman-timeline-3.mp4');

    expect(
      screen.getByTestId('timeline-generate-video-dialog-cancel-action')
    ).toBeInTheDocument();
    await act(() => {
      screen
        .getByTestId('timeline-generate-video-dialog-cancel-action')
        .click();
    });

    // onClose function should be called
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });
});
