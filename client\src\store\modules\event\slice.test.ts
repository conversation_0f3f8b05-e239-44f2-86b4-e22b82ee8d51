import { DeleteEventResponse, GetEventResponse } from '@shared-types/responses';
import {
  getEventById,
  selectEvent,
  deleteEvent,
  setFilesPagination,
  setMatchGroupsPagination,
  updateEventById,
  getFiles,
  selectFiles,
  EventSliceState,
} from './slice';
import { configureAppStore } from '@store/store';
import getApiAuthToken from '@utility/getApiAuthToken';
import axios from 'axios';

jest.mock('@utility/getApiAuthToken', () => jest.fn());
jest.mock('axios');
(getApiAuthToken as jest.Mock).mockReturnValue('test-token');

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetEventByIdResponse: GetEventResponse = {
  event: {
    eventEndDate: "2024-04-10T17:02:10Z",
    createdBy: "c38b16a7-f623-4dd4-9847-0f135bef9dc5",
    createdByName: "Test User",
    name: "Event Test 1",
    description: "New description",
    createdDateTime: "2024-04-11T00:10:13.876Z",
    modifiedDateTime: "2024-04-11T00:10:24.681Z",
    id: "947db3be-91ec-4e4b-a00f-6ad2ae06e25d",
    eventStartDate: "2024-04-10T17:02:10Z",
    tags: [
      "Tag 1",
      "Tag 2"
    ],
    matchGroupsCount: 10,
    filesCount: 10
  }
};

const mockGetFilesResponse = {
  files: {
    results: [
      {
        id: 'fileId1',
        fileName: 'myFile1',
        status: 'processed',
        length: 100,
        uploadDate: '2024-04-11T00:10:13.876Z',
        location: 'location1',
        fileType: 'type1',
        fileSize: 512,
        eventId: 'eventId1',
        eventName: 'eventName1',
      },
      {
        id: 'fileId2',
        fileName: 'myFile2',
        status: 'processed',
        length: 200,
        uploadDate: '2024-04-11T00:10:13.876Z',
        location: 'location2',
        fileType: 'type2',
        fileSize: 1024,
        eventId: 'eventId2',
        eventName: 'eventName2',
      },
    ],
    currentPage: 1,
    pageSize: 10,
    totalCount: 2,
    totalPages: 1,
    status: 'idle',
    error: '',
  },
};

const mockDeleteEventResponse: DeleteEventResponse = {
  id: 'fileId1',
  message: 'Event deleted',
};

const initialStateForMock: { event: EventSliceState } = {
  event: {
    files: {
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
    },
    fileSelected: {
      file: undefined,
      status: 'idle',
      error: '',
    },
    matchGroups: {
      eventId: '',
      results: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      status: 'idle',
      error: '',
      sortType: '',
      sortDirection: '',
    },
    event: {
      id: '',
      name: '',
      tags: [],
      createdBy: '',
      createdByName: '',
      description: '',
      eventStartDate: '',
      eventEndDate: '',
      createdDateTime: '',
      modifiedDateTime: '',
      status: 'idle',
      matchGroupsCount: 0,
      filesCount: 0,
    },
    eventDeletion: {
      id: '',
      status: 'idle',
      message: '',
    },
    matchGroupSearchDeletion: {
      status: 'idle',
      message: '',
      matchGroupId: '',
      searchId: '',
    },
    tags: {
      results: [],
      status: 'idle',
      error: '',
    },
    generatedTimelines: {
      results: [],
      status: 'idle',
      error: '',
    },
    generatedTimelineDeletions: [],
    attributes: {
      person: {},
      vehicle: {},
    },
    createMatchGroup: {
      status: 'idle',
      error: '',
      id: '',
    },
    updateMatchGroup: {
      status: 'idle',
      error: '',
    },
  }
};

describe('eventSlice', () => {

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should set pagination for files', () => {
    const mockedStore = configureAppStore(initialStateForMock);

    // pagination obj
    const pagination = {
      currentPage: 5,
      pageSize: 50,
    };

    // dispatch the set files pagination action
    mockedStore.dispatch(setFilesPagination(pagination));

    // get the updated state
    const newState = mockedStore.getState();

    // expect the files state to be updated with the pagination object
    expect(newState.event.files).toEqual({
      ...initialStateForMock.event.files,
      ...pagination,
    });

  });

  test('should handle getFiles and use sortBy createdDate in desc order by default', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      // Should include createdTime and desc in the url
      if (url?.includes('/files') && url?.includes('sortBy=createdTime&sortDirection=desc') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse.files });
      }
    });

    // dispatch getFiles action
    await mockedStore.dispatch(getFiles({ page: 1, limit: 100 }));

    // get the updated state
    const newState = mockedStore.getState();

    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse.files,
      status: 'idle',
    });
  });

  test('should handle getFiles with sorting params', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/files') && method === 'get') {
        return Promise.resolve({ data: mockGetFilesResponse.files });
      }
    });

    // dispatch getFiles action
    await mockedStore.dispatch(getFiles({ page: 1, limit: 100, sort: 'fileName', dir: 'asc' }));

    // get the updated state
    const newState = mockedStore.getState();

    expect(selectFiles(newState)).toEqual({
      ...mockGetFilesResponse.files,
      status: 'idle',
    });
  });

  test('should update event description', async () => {
    const mockedStore = configureAppStore(initialStateForMock);
    const description = 'New description for event';
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'patch') { 
        return Promise.resolve({ data: { event: { ...mockGetEventByIdResponse.event, description } } });
      }
    });

    await mockedStore.dispatch(updateEventById({eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d', description}));
    const newState = mockedStore.getState();
    expect(newState.event.event.description).toEqual(description);
  });

  test('should attempt to update event with an error', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'patch') {
        return Promise.reject({ message: 'Unable to update event' });
      }
    });

    const description = 'New description';
    await mockedStore.dispatch(updateEventById({eventId: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d', description}));
    const newState = mockedStore.getState();
    expect(newState.event.event.status).toEqual('failure');
  });

  test('should set pagination for matchGroups', () => {
    const mockedStore = configureAppStore(initialStateForMock);

    // pagination obj
    const pagination = {
      currentPage: 8,
      pageSize: 100,
    };

    // dispatch the set files pagination action
    mockedStore.dispatch(setMatchGroupsPagination(pagination));

    // get the updated state
    const newState = mockedStore.getState();

    // expect the files state to be updated with the pagination object
    expect(newState.event.matchGroups).toEqual({
      ...initialStateForMock.event.matchGroups,
      ...pagination,
    });

  });

  test('should get event by id', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'get') {
        return Promise.resolve({ data: mockGetEventByIdResponse });
      }
    });

    // dispatch eventById action
    await mockedStore.dispatch(getEventById({ eventId: mockGetEventByIdResponse.event.id }));

    // get the updated state
    const newState = mockedStore.getState();

    // state should equal the mocked response including 'idle' status
    expect(selectEvent(newState)).toEqual({
      ...mockGetEventByIdResponse.event,
      status: 'idle',
    });
  });

  test('should reject getting an event by id with an error', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    // mock the axios request to reject with an error
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'get') {
        return Promise.reject({
          response: {
            data: {
              message: "The event does not exist in current organization or belongs to another organization",
            }
          }
        });
      }
    });

    // dispatch eventById action
    await mockedStore.dispatch(getEventById({ eventId: mockGetEventByIdResponse.event.id }));

    // get the updated state
    const newState = mockedStore.getState();

    // should have a failure status
    expect(newState.event.event.status).toEqual('failure');
  });

  test('should delete an event', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'delete') {
        return Promise.resolve({ data: mockDeleteEventResponse });
      }
    });

    await mockedStore.dispatch(deleteEvent('1'));

    const newState = mockedStore.getState();

    expect(newState.event.eventDeletion).toEqual({
      id: '',
      error: '',
      message: '',
      status: 'idle',
    });
  });

  test('reject deleting an event and return an error', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'delete') {
        return Promise.reject({ message: 'error' });
      }
    });

    await mockedStore.dispatch(deleteEvent('1'));

    const newState = mockedStore.getState();

    expect(newState.event.eventDeletion).toEqual({
      ...newState.event.eventDeletion,
      status: 'failure',
    });
  });

  test('handle a 500 error while deleting an event', async () => {
    const mockedStore = configureAppStore(initialStateForMock);

    // mock the axios request to return a 500 error
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (url?.includes('/event') && method === 'delete') {
        return Promise.resolve({ data: { ...mockDeleteEventResponse, code: 500, } });
      }
    });

    // dispatch the delete event action
    await mockedStore.dispatch(deleteEvent('1'));

    // get the updated state
    const newState = mockedStore.getState();

    // expect the event deletion state to have a failure status and error message
    expect(newState.event.eventDeletion).toEqual({
      id: '',
      message: '',
      status: 'failure',
      error: 'Delete Event Failed',
    });
  });
});
