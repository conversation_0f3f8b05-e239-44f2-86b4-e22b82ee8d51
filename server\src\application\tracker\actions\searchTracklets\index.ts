import { Context, RequestHeader } from '@application/types';
import { ActionError, GraphQLError, NotFoundError } from '@common/errors';
import {
  ConvertedAttributes,
  File,
  MatchGroup,
  Search,
} from '../../../../../../types/tracker';
import { fingerprintSearch } from './elasticSearch';
import { responses } from '@tracker/graphQL';
import { getTdoByFolderAll } from './getTdoByFolder';
import { every, isArray, isString, uniq } from 'lodash';
import { getFileNameByTdos } from './getFileNameByTdos';
import { convertAttributes } from './convertAttributes';

const validDetectionType = ['person', 'vehicle'];
const searchTracklets = async <
  ReqPayload,
  Data extends Partial<
    | ((responses.searchMatchTracklets & { matchGroup?: MatchGroup }) &
        Partial<responses.getEvent>)
    | ((responses.searchFileTracklets & { file?: File }) &
        Partial<responses.getEvent>)
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    | (Data & responses.searchMatchTracklets)
    | (Data & responses.searchFileTracklets)
    | (Data & Partial<responses.getEvent>)
  >
> => {
  const { data, req, log } = context;
  // Commenting as headers variable is not used
  // const headers = { Authorization: req.headers.authorization };
  const {
    pageSize,
    currentPage,
    type,
    fileIds,
    eventId,
    fileId,
    attributes,
    threshold,
  } = req.query;

  let detectionType: string[] = validDetectionType;
  if (type && typeof type === 'string') {
    const lowerType = type.toLowerCase();
    if (validDetectionType.includes(lowerType)) {
      detectionType = [lowerType];
    } else {
      log.error(`Invalid detection type ${type}, default to ${detectionType}`);
    }
  }
  // Pull the engine id stored in the event, otherwise use the default.
  // Do not ever change the default engine GUID. This will break old events that do not store the engine id.
  const trackerEngineId =
    data?.event?.trackerEngineId ?? 'd77d6133-a801-472c-bc7e-48ddafec8590';

  const pgSize = Number(pageSize ?? 100);
  const currentPg = Number(currentPage ?? 1);
  const limit = pgSize;
  const offset = (currentPg - 1) * pgSize;

  if ('matchGroup' in data && data.matchGroup) {
    return await searchTrackletsByMatchGroup({
      matchGroup: data.matchGroup,
      fileIds: validateFileIds(fileIds) ? fileIds : undefined,
      detectionType,
      type: isString(type) ? type : undefined,
      currentPage: currentPg,
      pgSize,
      limit,
      offset,
      context,
      threshold: isString(threshold) ? parseFloat(threshold) : undefined,
      attributes: attributes as ConvertedAttributes | undefined,
      trackerEngineId,
    });
  } else if ('file' in data && data.file) {
    return await searchTrackletsByFile({
      file: data.file,
      fileId: isString(fileId) ? fileId : undefined,
      eventId: isString(eventId) ? eventId : undefined,
      detectionType,
      type: isString(type) ? type : undefined,
      currentPage: currentPg,
      pgSize,
      limit,
      offset,
      context,
      attributes: attributes as ConvertedAttributes | undefined,
      trackerEngineId,
    });
  } else {
    throw new ActionError('No match group or file provided');
  }
};

async function searchTrackletsByMatchGroup<ReqPayload, Data>({
  matchGroup,
  detectionType,
  fileIds,
  type,
  limit,
  offset,
  context,
  attributes,
  threshold,
  trackerEngineId,
}: {
  matchGroup: MatchGroup;
  detectionType: string[];
  fileIds?: string | string[];
  type?: string;
  currentPage: number;
  pgSize: number;
  limit: number;
  offset: number;
  context: Context<ReqPayload, Data>;
  attributes?: ConvertedAttributes;
  threshold?: number;
  trackerEngineId: string;
}) {
  const { req, log } = context;
  const headers = { Authorization: req.headers.authorization };
  let tdoIds: string[] = [];
  let allTdos: { tdoId: string; fileName: string }[] = [];

  if (fileIds && typeof fileIds === 'string') {
    tdoIds = [fileIds];
  } else if (fileIds && Array.isArray(fileIds)) {
    tdoIds = [...fileIds];
  } else {
    const folderTdos = await getTdoByFolderAll({
      folderId: matchGroup.eventId,
      context,
      headers,
    });
    tdoIds = folderTdos.folder.childTDOs.records.map((tdo) => tdo.id);
    allTdos = folderTdos.folder.childTDOs.records.map((tdo) => ({
      tdoId: tdo.id,
      fileName: tdo.name,
    }));
  }

  const { searchId } = req.params;
  const times = req.query.times === 'true';

  if (!searchId) {
    throw new ActionError('No searchId provided');
  }

  const search = matchGroup.searches?.find((s: Search) => s.id === searchId);
  const searchAttributes = search?.attributes;

  if (!search) {
    throw new NotFoundError('Search does not exist in match group searches');
  }

  try {
    const searchResults =
      tdoIds.length > 0
        ? await fingerprintTrackletSearch({
            fileIds: tdoIds,
            type: detectionType,
            referenceTrackletId: search.referenceTrackletId,
            limit,
            offset,
            context,
            headers,
            attributes: search.referenceTrackletId
              ? attributes
              : convertAttributes(searchAttributes),
            threshold,
            times,
            trackerEngineId,
          })
        : {
            tdoIds: [],
            tracklets: [],
            totalCount: 0,
            from: 0,
            to: 0,
            limit: 100,
          };

    const uniqFileNames = await getFileNameByTdos({
      tdoIds: searchResults.tdoIds,
      context,
      headers,
    });

    const matchGroupSearch = {
      results: searchResults.tracklets,
      type,
      referenceTrackletId: search.referenceTrackletId,
      searchId: search.id,
      searchName: search.searchName,
      matchGroupId: matchGroup.id,
      matchGroupName: matchGroup.name,
      eventId: matchGroup.eventId,
      currentPage: Math.floor(searchResults.from / searchResults.limit) + 1,
      pageSize: searchResults.limit,
      totalCount: searchResults.totalCount,
      totalPages: Math.ceil(searchResults.totalCount / searchResults.limit),
      tdoIds: searchResults.tdoIds,
      fileNames: uniq(uniqFileNames?.map((tdo) => tdo.name)),
      allTdos: uniq(allTdos),
    };
    const new_data = { matchGroupSearch, matchGroup };
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e.message);
    throw new GraphQLError(e.message);
  }
}

async function searchTrackletsByFile<ReqPayload, Data>({
  file,
  fileId,
  eventId,
  detectionType,
  type,
  currentPage,
  pgSize,
  limit,
  offset,
  context,
  attributes,
  trackerEngineId,
}: {
  file: File;
  fileId?: string;
  eventId?: string;
  detectionType: string[];
  type?: string;
  currentPage: number;
  pgSize: number;
  limit: number;
  offset: number;
  context: Context<ReqPayload, Data>;
  attributes?: ConvertedAttributes;
  trackerEngineId: string;
}) {
  const { req, log, data } = context;
  const headers = { Authorization: req.headers.authorization };
  const times = req.query.times === 'true';

  let tdoIds: string[] = [];
  if (fileId || file.id) {
    tdoIds = [fileId ?? file.id];
  }

  try {
    const searchResults = await fingerprintTrackletSearch({
      fileIds: tdoIds,
      type: detectionType,
      limit,
      offset,
      context,
      headers,
      attributes,
      times,
      trackerEngineId,
    });
    const totalPages = Math.ceil(searchResults.totalCount / limit);
    const fileSearch = {
      results: searchResults.tracklets,
      type,
      eventId: eventId,
      currentPage,
      pageSize: pgSize,
      totalCount: searchResults.totalCount,
      totalPages,
    };
    const new_data = Object.assign({}, data, { fileSearch, file });
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e.message);
    throw new GraphQLError(e.message);
  }
}

async function fingerprintTrackletSearch<ReqPayload, Data>({
  referenceTrackletId,
  fileIds,
  type,
  limit,
  offset,
  context,
  headers,
  attributes,
  threshold,
  times,
  trackerEngineId,
}: {
  referenceTrackletId?: string;
  fileIds?: string[];
  type: string[];
  limit: number;
  offset: number;
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
  attributes?: ConvertedAttributes;
  threshold?: number;
  times?: boolean;
  trackerEngineId: string;
}) {
  const {
    tracklets,
    from,
    to,
    limit: limitReturned,
    totalResults,
    tdoIds,
  } = await fingerprintSearch({
    referenceTrackletIds: referenceTrackletId
      ? [referenceTrackletId]
      : undefined,
    fileIds,
    type,
    limit,
    offset,
    context,
    headers,
    attributes,
    threshold,
    times,
    trackerEngineId,
  });

  const results = {
    tdoIds,
    tracklets,
    totalCount: totalResults,
    from,
    to,
    limit: limitReturned,
  };

  return results;
}

function validateFileIds(fileIds: unknown): fileIds is string[] | string {
  return (isArray(fileIds) && every(fileIds, isString)) || isString(fileIds);
}

export default searchTracklets;
