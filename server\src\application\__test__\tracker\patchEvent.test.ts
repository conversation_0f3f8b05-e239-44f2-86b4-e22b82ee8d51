import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.useFakeTimers().setSystemTime(new Date('2024-04-01T00:00:00.000Z'));
jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.lookupLatestSchemaId)) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (
        query.includes(
          queries.searchStructuredDataObjects({ eventId: 'an-event-id' })
        )
      ) {
        return Promise.resolve({
          structuredDataObjects: {
            records: [],
            count: 0,
            limit: 10,
            offset: 0,
            orderBy: [],
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: 0,
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'an-event-id',
            contentTemplates: [
              {
                id: 'id',
                sdo: {
                  id: 'sdoId',
                  data: {
                    id: 'eventId',
                    name: 'oldName',
                    tags: ['oldTag'],
                    createdBy: 'oldCreatedBy',
                    createdByName: 'oldCreatedByName',
                    description: 'oldDescription',
                    eventStartDate: 'oldEventStartDate',
                    eventEndDate: 'oldEventEndDate',
                  },
                },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (query.includes(queries.createStructuredData)) {
        return Promise.resolve({
          createStructuredData: {},
        });
      }
    }
  ),
}));

describe('patch event', () => {
  it('patch event', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .patch('/api/v1/event/an-event-id')
      .set('Authorization', 'Bearer validToken')
      .send({ name: 'newName' })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.updateFolder,
      {
        folderId: 'an-event-id',
        name: 'newName',
      }
    );

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'sdoId',
        schemaId: 'schemaId',
        data: {
          id: 'eventId',
          name: 'newName',
          tags: ['oldTag'],
          createdBy: 'oldCreatedBy',
          createdByName: 'oldCreatedByName',
          description: 'oldDescription',
          eventStartDate: 'oldEventStartDate',
          eventEndDate: 'oldEventEndDate',
          modifiedDateTime: '2024-04-01T00:00:00.000Z',
        },
      }
    );
  });

  it('patch event with empty description', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .patch('/api/v1/event/an-event-id')
      .set('Authorization', 'Bearer validToken')
      .send({ name: 'newName', description: '' })
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.updateFolder,
      {
        folderId: 'an-event-id',
        name: 'newName',
      }
    );

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.createStructuredData,
      {
        id: 'sdoId',
        schemaId: 'schemaId',
        data: {
          id: 'eventId',
          name: 'newName',
          tags: ['oldTag'],
          createdBy: 'oldCreatedBy',
          createdByName: 'oldCreatedByName',
          description: '',
          eventStartDate: 'oldEventStartDate',
          eventEndDate: 'oldEventEndDate',
          modifiedDateTime: '2024-04-01T00:00:00.000Z',
        },
      }
    );
  });

  it('cannot patch event w/o eventId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .patch('/api/v1/event')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
