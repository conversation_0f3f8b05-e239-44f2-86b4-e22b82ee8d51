import { render, screen } from '@testing-library/react';
import Pagination from './Pagination';
import { I18nProvider, LOCALES } from '@i18n';

describe('Pagination', () => {
  it('renders without crashing', () => {
    const props = {
      totalCount: 100,
      currentPageIndex: 1,
      pageSize: 10,
      prevPage: jest.fn(),
      nextPage: jest.fn(),
      setPageSize: jest.fn(),
      currentPage: 1,
      totalPages: 10,
      loading: false,
    };

    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <Pagination {...props} />
      </I18nProvider>
    );
    expect(screen.getByTestId('pagination')).toBeInTheDocument();
  });
});
