import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import mkcert from 'vite-plugin-mkcert';
import eslint from 'vite-plugin-eslint';
import csp from 'vite-plugin-csp';
import stylelint from 'vite-plugin-stylelint';
import svgr from 'vite-plugin-svgr';
import gci from 'git-commit-info';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import { createRequire } from 'module';
import safeConfigKeys from './configWhitelist.json';

const require = createRequire(import.meta.url);
const processEnv = process.env.ENVIRONMENT || 'local';
const deployedConfigPath = `./config-${processEnv}.json`;
const localConfigPath = './config.json';
const isDeployed = processEnv && fs.existsSync(deployedConfigPath);
const appConfig = require(isDeployed ? deployedConfigPath : localConfigPath);
const safeConfig = _.pick(appConfig, safeConfigKeys);

console.log(`Node version ${process.versions.node}`);

export default defineConfig({
  assetsInclude: ['**/*.css'],
  resolve: {
    alias: {
      // fs: require.resolve('rollup-plugin-node-builtins'),
      // acorn: require.resolve('rollup-plugin-node-builtins'),
      '@common-modules': path.resolve(__dirname, './src/common-modules'),
      '@components': path.resolve(__dirname, './src/components'),
      '@helpers': path.resolve(__dirname, './src/helpers'),
      "@i18n": path.resolve(__dirname, './src/i18n'),
      '@store': path.resolve(__dirname, './src/store'),
      '@theme': path.resolve(__dirname, './src/theme'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@utility': path.resolve(__dirname, './src/utility'),
      '@shared-types': path.resolve(__dirname, '../types/*'),
      '@shared-assets': path.resolve(__dirname, '../assets'),
    },
  },
  plugins: [
    react(),
    eslint(),
    stylelint(),
    mkcert({ hosts: ['local.veritone.com'] }),
    svgr(),
    csp({
      policy: {
        'base-uri': ["self"],
        'object-src': ["none"],
        'script-src': ["self", "unsafe-eval", "get.aiware.com", "veritone.my.site.com", "cdn.jsdelivr.net", "cdnjs.cloudflare.com", "nonce-NGINX_CSP_NONCE", "*.AIWARE_DOMAIN_TO_REPLACE"],
        'style-src': ["self", "fonts.googleapis.com", "unsafe-inline", "cdn.jsdelivr.net", "static.veritone.com", "veritone.my.site.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        'font-src': ["self", "data:", "fonts.googleapis.com", "cdn.jsdelivr.net", "fonts.gstatic.com", "static.veritone.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        'frame-ancestors': ["self", "support.veritone.com", "*.AIWARE_DOMAIN_TO_REPLACE"],
        'frame-src': ["self", "*.AIWARE_DOMAIN_TO_REPLACE"],
        'worker-src': ["self", "blob:", "*.AIWARE_DOMAIN_TO_REPLACE"],
      },
      hashingMethod: 'sha256',
      hashEnabled: {
        'script-src': false,
        'style-src': false,
        'script-src-attr': false,
        'style-src-attr': false,
      },
      nonceEnabled: {
        'script-src': false,
        'style-src': false,
      },
    }),
    viteStaticCopy({
      targets: [
        {
          src: '../assets/fonts/*',
          dest: 'fonts'
        }
      ]
    })
  ],
  server: {
    host: 'local.veritone.com',
    port: 3004,
  },
  define: {
    buildDetails: {
      hash: gci().hash,
      date: gci().date,
      message: gci().message,
    },
    config: JSON.stringify(safeConfig)
  },
  html: {
    cspNonce: 'NGINX_CSP_NONCE'
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "./src/theme/global.scss";`,
      },
    },
  },
});
