# Match Group

### Create Schema

```js
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "793f0f3a-ed2e-45b4-a8c8-c455e6dc18a9"
    name: "track-match-group"
    description: "Track Match Group v2"
    source: ""
  }) {
    id
  }
}
```

```js

mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
      dataRegistryId: "793f0f3a-ed2e-45b4-a8c8-c455e6dc18a9"
      majorVersion: 2
      schema: {
        type: "object"
        title: "track-match-group"
        description: "Track: Match Group"
        required: ["id", "name", "eventId"]
        properties: {
          id: { type: "string" }
          name: { type: "string" }
          eventId: { type: "string" }
          searches: {
            type: "array"
            items: {
              type: "object"
              properties: {
                id: { type: "string" }
                referenceTrackletId: { type: "string" }
                searchName: { type: "string" }
                attributes: {
                  type: "object"
                  properties: {
                    person: {
                      type: "array"
                      items: {
                        type: "object"
                        properties: {
                          label: { type: "string" }
                          value: { type: "string" }
                          key: { type: "string" }
                        }
                      }
                    }
                    vehicle: {
                      type: "array"
                      items: {
                        type: "object"
                        properties: {
                          label: { type: "string" }
                          value: { type: "string" }
                          key: { type: "string" }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          selectedTracklets: {
            type: "array"
            items: { type: "string" }
            timelineProject: {
              types: "object"
              properties: {
                modifiedDateTime: { type: "string" }
                modifiedBy: { type: "string" }
                groups: {
                  type: "array"
                  items: {
                    type: "object"
                    required: ["id", "name", "tracklets"]
                    properties: {
                      id: { type: "string" }
                      name: { type: "string" }
                      tracklets: {
                        type: "array"
                        items: {
                          type: "object"
                          required: [
                            "trackletId"
                            "fileId"
                            "startTimeMs"
                            "type"
                            "stopTimeMs"
                          ]
                          properties: {
                            trackletId: { type: "string" }
                            fileId: { type: "string" }
                            fileName: { type: "string" }
                            type: { type: "string" }
                            startTimeMs: { type: "integer" }
                            stopTimeMs: { type: "integer" }
                            attributes: {
                              type: "object"
                              properties: {
                                accessories: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                body: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                face: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                footwear: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                gender: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                hair: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                upper: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                                lower: {
                                  type: "array"
                                  items: { type: "string" }
                                }
                              }
                            }
                            thumbnailUrls: {
                              type: "object"
                              required: ["best", "first"]
                              properties: {
                                first: { type: "string" }
                                best: { type: "string" }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            generatedTimelines: {
              type: "array"
              items: {
                type: "object"
                properties: {
                  id: { type: "string" }
                  name: { type: "string" }
                  createdDateTime: { type: "string" }
                  createdUserId: { type: "string" }
                  tdoId: { type: "string" }
                  resolution: { type: "string" }
                  outputFormat: { type: "string" }
                  videoLengthMs: { type: "number" }
                  videoSizeBytes: { type: "integer" }
                  timeline: {
                    types: "object"
                    properties: {
                      modifiedDateTime: { type: "string" }
                      modifiedBy: { type: "string" }
                      groups: {
                        type: "array"
                        items: {
                          type: "object"
                          required: ["id", "name", "tracklets"]
                          properties: {
                            id: { type: "string" }
                            name: { type: "string" }
                            tracklets: {
                              type: "array"
                              items: {
                                type: "object"
                                required: [
                                  "trackletId"
                                  "fileId"
                                  "startTimeMs"
                                  "type"
                                  "stopTimeMs"
                                ]
                                properties: {
                                  trackletId: { type: "string" }
                                  fileId: { type: "string" }
                                  fileName: { type: "string" }
                                  type: { type: "string" }
                                  startTimeMs: { type: "integer" }
                                  stopTimeMs: { type: "integer" }
                                  attributes: {
                                    type: "object"
                                    properties: {
                                      accessories: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      body: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      face: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      footwear: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      gender: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      hair: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      upper: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                      lower: {
                                        type: "array"
                                        items: { type: "string" }
                                      }
                                    }
                                  }
                                  thumbnailUrls: {
                                    type: "object"
                                    required: ["best", "first"]
                                    properties: {
                                      first: { type: "string" }
                                      best: { type: "string" }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}


```

```js
mutation publishSchemaDraft {
  updateSchemaState(input: {
    id: "6dbe6efc-9156-499c-8ef7-b383a657a9d9"
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}

```
