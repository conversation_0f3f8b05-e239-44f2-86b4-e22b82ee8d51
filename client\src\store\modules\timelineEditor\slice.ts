import { v4 as uuidV4 } from 'uuid';
import { createAppSlice } from '../../createAppSlice';
import { TimelineProject, Tracklet } from '@shared-types/tracker';
import { PayloadAction } from '@reduxjs/toolkit';
import { BoundingBoxesState, FileState, UpdateMatchGroup } from './types';
import { RootState } from '@store/store';
import HttpClient from '@store/dependencies/httpClient';
import { AlertLevel, createSnackNotification } from '@components/common';
import getApiAuthToken from '@utility/getApiAuthToken';
import {
  GetBoundingBoxesResponse,
  GetFileResponse,
  GetThumbnailsResponse,
  UpdateMatchGroupResponse,
} from '@shared-types/responses';
import qs from 'qs';
import ls from 'localstorage-slim';

export interface TimelineEditorSliceState {
  timeline: Tracklet[];
  userSelectedTracklet?: Tracklet;
  userSelectedTrackletFile: FileState;
  updateMatchGroup: UpdateMatchGroup;
  isSaved: boolean;
  boundingBoxes: BoundingBoxesState;
  thumbnailUrls: Record<
    string,
    {
      thumbnailUrls: {
        best: string;
      };
      expiresDateTime: string;
    }
  >;
}

const initialState: TimelineEditorSliceState = {
  timeline: [],
  userSelectedTracklet: undefined,
  updateMatchGroup: {
    matchGroup: undefined,
    status: 'idle',
    error: '',
  },
  userSelectedTrackletFile: {
    file: undefined,
    apiStatus: 'idle',
    error: '',
  },
  isSaved: false,
  boundingBoxes: {
    data: [],
    apiStatus: 'idle',
    error: '',
  },
  thumbnailUrls: ls.get('thumbnailUrls') || {},
};

export const timelineEditorSlice = createAppSlice({
  name: 'timeline',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>(); // TODO: Fix getState type
    return {
      getBoundingBoxes: createHttpThunk(
        async (
          {
            fileId,
            trackletId,
            startTimeMs,
            stopTimeMs,
            type,
          }: {
            fileId?: string;
            trackletId?: string;
            startTimeMs?: number;
            stopTimeMs?: number;
            type?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetBoundingBoxesResponse>(signal)(
            `bounding-boxes${qs.stringify(
              { fileId, trackletId, startTimeMs, stopTimeMs, type },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix the getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.boundingBoxes.apiStatus = 'loading';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = '';
          },
          fulfilled: (state, action) => {
            state.boundingBoxes.apiStatus = 'idle';
            state.boundingBoxes.data = action.payload.results;
            state.boundingBoxes.error = '';
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Bounding Boxes failed',
              action.error.message
            );
            state.boundingBoxes.apiStatus = 'failure';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = action.error.message;
          },
        }
      ),
      setUserSelectedTracklet: create.reducer(
        (state, action: PayloadAction<{ tracklet?: Tracklet }>) => {
          state.userSelectedTracklet = action.payload?.tracklet;

          // const found = state.userSelectedTracklet?.find(
          //   (item) => item.trackletId === action.payload.tracklet?.trackletId
          // );
          // if (found) {
          //   state.userSelectedTracklet = state.userSelectedTracklet?.filter(
          //     (item) => item.trackletId !== action.payload.tracklet?.trackletId
          //   );
          // } else {
          //   state.userSelectedTracklet = (
          //     state.userSelectedTracklet ?? []
          //   ).concat(action.payload.tracklet);
          // }
          // if (state.userSelectedTracklet?.length === 0) {
          //   state.userSelectedTrackletFile.file = undefined;
          // }
        }
      ),
      setTimeline: create.reducer(
        (state, action: PayloadAction<{ timeline: Tracklet[] }>) => {
          state.timeline = action.payload.timeline.map((t) => ({
            ...t,
            thumbnailUrls:
              state.thumbnailUrls?.[t.trackletId]?.thumbnailUrls ??
              t.thumbnailUrls,
          }));
          state.isSaved = false;
        }
      ),
      saveTimeline: createHttpThunk(
        async (
          {
            matchGroupId,
            noMessage: _noMessage,
          }: {
            matchGroupId?: string;
            noMessage?: boolean;
          },
          thunkAPI
        ) => {
          if (!matchGroupId) {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              `There was an error saving the timeline`
            );
          }

          const {
            getState,
            signal,
            dispatch: _dispatch,
            extra: { http },
          } = thunkAPI;

          const {
            timeline: {
              updateMatchGroup: { matchGroup: _matchGroup },
              timeline,
            },
          } = getState() as RootState;

          const timelineProject: TimelineProject = {};

          timelineProject.groups = timeline.map((tracklet) => ({
            id: uuidV4(),
            name: 'Default Group',
            tracklets: [
              {
                ...tracklet,
                startTimeMs: tracklet.startTimeMs ?? 0,
                stopTimeMs: tracklet.stopTimeMs ?? 0,
              },
            ],
          }));

          const response = await http.patch<UpdateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            { timelineProject },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = {
              status: 'idle',
              matchGroup: action.payload.matchGroup,
            };
            state.isSaved = false;

            if (!action.meta.arg.noMessage) {
              createSnackNotification(
                AlertLevel.Success,
                'Timeline Saved',
                `Timeline ${action.payload.matchGroup?.name} was saved successfully`
              );
            }
          },
          rejected: (state, action) => {
            state.isSaved = false;
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      saveAndGenerateTimeline: createHttpThunk(
        async (
          {
            matchGroupId,
            noMessage: _noMessage,
            generatedTimelineName,
          }: {
            matchGroupId?: string;
            noMessage?: boolean;
            generatedTimelineName?: string;
          },
          thunkAPI
        ) => {
          if (!matchGroupId) {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              `There was an error saving the timeline`
            );
          }

          const {
            getState,
            signal,
            dispatch: _dispatch,
            extra: { http },
          } = thunkAPI;
          const {
            timeline: {
              updateMatchGroup: { matchGroup: _matchGroup },
              timeline,
            },
          } = getState() as RootState;

          const timelineProject: TimelineProject = {};

          timelineProject.groups = timeline.map((tracklet) => ({
            id: uuidV4(),
            name: 'Default Group',
            tracklets: [tracklet],
          }));

          const response = await http.patch<UpdateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}/generate-timeline`,
            { timelineProject, generatedTimelineName },
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = {
              status: 'idle',
              matchGroup: action.payload.matchGroup,
            };
            state.isSaved = false;

            if (!action.meta.arg.noMessage) {
              createSnackNotification(
                AlertLevel.Success,
                'Generating Timeline video..',
                `Video will be available shortly`
              );
            }
          },
          rejected: (state, action) => {
            state.isSaved = false;
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      getFile: createHttpThunk(
        async ({ tracklet }: { tracklet: Tracklet }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetFileResponse>(signal)(
            `/file/${tracklet.fileId}`,
            // TODO: Fix the getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.userSelectedTrackletFile.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.userSelectedTrackletFile = {
              file: action.payload.file,
              selectedTracklet: action.meta.arg.tracklet,
              apiStatus: 'idle',
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            state.userSelectedTrackletFile.apiStatus = 'failure';
          },
        }
      ),
      getUpdateMatchGroup: createHttpThunk(
        async ({ matchGroupId }: { matchGroupId: string }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<UpdateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = {
              status: 'idle',
              matchGroup: action.payload.matchGroup,
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      clearUserSelectedTrackletsAndFile: create.reducer((state) => {
        state.userSelectedTracklet = undefined;
        state.userSelectedTrackletFile = {
          file: undefined,
          apiStatus: 'idle',
          error: '',
        };
      }),
      getThumbnails: createHttpThunk(
        async (
          tracklets: Array<{
            trackletId: string;
            orgId: string;
            fileId: string;
          }>,
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<GetThumbnailsResponse>(signal)(
            '/thumbnails',
            { tracklets },
            {
              // TODO: Fix the getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          fulfilled: (state, action) => {
            Object.assign(state.thumbnailUrls, action.payload.thumbnails);
            state.thumbnailUrls = Object.entries(state.thumbnailUrls).reduce(
              (acc, [key, value]) =>
                new Date(value.expiresDateTime) <= new Date()
                  ? acc
                  : { ...acc, [key]: value },
              {}
            );
            ls.set('thumbnailUrls', state.thumbnailUrls);

            state.timeline = state.timeline.map((t) => ({
              ...t,
              thumbnailUrls:
                state.thumbnailUrls?.[t.trackletId]?.thumbnailUrls ??
                t.thumbnailUrls,
            }));
          },
          rejected: (_state, _action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Get Thumbnails Failed'
            );
          },
        }
      ),
      setThumbnails: create.reducer(
        (
          state,
          action: PayloadAction<
            Record<
              string,
              {
                thumbnailUrls: {
                  best: string;
                };
                expiresDateTime: string;
              }
            >
          >
        ) => {
          state.thumbnailUrls = action.payload;
          ls.set('thumbnailUrls', state.thumbnailUrls);
        }
      ),
    };
  },
  selectors: {
    selectBoundingBoxes: (state) => state.boundingBoxes.data,
    selectUserSelectedTrackletFile: (state) => state.userSelectedTrackletFile,
    selectTimeline: (state) => state.timeline,
    selectUserSelectedTracklet: (state) => state.userSelectedTracklet,
    selectUpdateMatchGroup: (state) => state.updateMatchGroup,
    selectThumbnails: (state) => state.thumbnailUrls,
  },
});

export const {
  getBoundingBoxes,
  setUserSelectedTracklet,
  getFile,
  setTimeline,
  saveTimeline,
  saveAndGenerateTimeline,
  getUpdateMatchGroup,
  clearUserSelectedTrackletsAndFile,
  getThumbnails,
  setThumbnails,
} = timelineEditorSlice.actions;

export const {
  selectBoundingBoxes,
  selectTimeline,
  selectUserSelectedTrackletFile,
  selectUserSelectedTracklet,
  selectUpdateMatchGroup,
  selectThumbnails,
} = timelineEditorSlice.selectors;
