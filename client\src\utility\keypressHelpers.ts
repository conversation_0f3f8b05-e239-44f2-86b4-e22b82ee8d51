import { KeyboardEvent } from 'react';

export const onEnter = <T = Element>(func?: React.KeyboardEventHandler<T>) => (e: KeyboardEvent<T>) => {
  e.stopPropagation();

  if (e.key === 'Enter' || e.keyCode === 13) {
    func?.(e);
  }
};

export const onSpace = <T = Element>(func?: React.KeyboardEventHandler<T>) => (e: KeyboardEvent<T>) => {
  e.stopPropagation();

  if (e.keyCode === 32) {
    func?.(e);
  }
};

export const onBackspace = <T = Element>(func?: React.KeyboardEventHandler<T>) => (e: KeyboardEvent<T>) => {
  e.stopPropagation();

  if (e.keyCode === 8) {
    func?.(e);
  }
};

export const onEsc = <T = Element>(func?: React.KeyboardEventHandler<T>) => (e: KeyboardEvent<T>) => {
  e.stopPropagation();

  if (e.keyCode === 27) {
    func?.(e);
  }
};

export const onTab = <T = Element>(func?: React.KeyboardEventHandler<T>) => (e: KeyboardEvent<T>) => {
  e.stopPropagation();

  if (e.keyCode === 9) {
    func?.(e);
  }
};

export default {
  onEnter,
  onSpace,
  onBackspace,
  onEsc,
  onTab
};
