import { render, screen } from '@testing-library/react';
import AppBar from '@components/common/AppBar';
import React from 'react';

jest.mock('@utility/hmrUpdate', () => ({
    isHmrReload: false,
}));

describe('AppBar', () => {
    it('renders AppBar component', () => {
        const mockWidget = jest.fn();
        const mockUnmount = jest.fn();
        const mockInit = jest.fn();
        const mockOn = jest.fn();
        const customWindow = {
            aiware: {
                mountWidget: mockWidget.mockImplementation((_config, callback) => callback()),
                unmountWidget: mockUnmount,
                init: mockInit.mockImplementation((_config, callback) => callback()),
                on: mockOn.mockImplementation((_event, callback) => callback()),
                store: {
                  getState: jest.fn(),
                },
            },
            config: {
                apiRoot: '',
                graphQLEndpoint: '',
                veritoneAppId: '',
                nodeEnv:'',
            },
            isAiwareInitialized: false,
        };

        window.aiware = customWindow.aiware;
        window.config = customWindow.config;

        const { asFragment } = render(<AppBar />);
        expect(screen.getByTestId('aiWareAppBar')).toBeInTheDocument();
        expect(asFragment()).toMatchSnapshot();
    });

    it('renders AppBar with config undefined', () => {
        const mockWidget = jest.fn();
        const mockUnmount = jest.fn();
        const mockInit = jest.fn();
        const mockOn = jest.fn();
        const customWindow = {
            aiware: {
                mountWidget: mockWidget.mockImplementation((_config, callback) => callback()),
                unmountWidget: mockUnmount,
                init: mockInit.mockImplementation((_config, callback) => callback()),
                on: mockOn.mockImplementation((_event, callback) => callback()),
            },
            config: {
                apiRoot: '',
                graphQLEndpoint: '',
                veritoneAppId: '',
                nodeEnv: '',
            },
            isAiwareInitialized: undefined,
        };

        delete window.isAiwareInitialized;
        window.config = customWindow.config;

        const { asFragment } = render(<AppBar />);
        expect(screen.getByTestId('aiWareAppBar')).toBeInTheDocument();
        expect(asFragment()).toMatchSnapshot();
    });

    it('aiware.init callback is called when aiware is loaded', () => {
        const mockMountWidget = jest.fn().mockReturnValue('widgetId');
        const mockUnmount = jest.fn();
        const mockInit = jest.fn();
        const mockOn = jest.fn();
        const mockOpenSalesforceChatWindow = jest.fn();

        const customWindow = {
            aiware: {
                mountWidget: mockMountWidget,
                unmountWidget: mockUnmount,
                init: mockInit.mockImplementation((_config, callback) => callback()),
                on: mockOn.mockImplementation((_event, callback) => callback()),
                store: {
                    getState: jest.fn(),
                },
            },
            config: {
                apiRoot: '',
                graphQLEndpoint: '',
                veritoneAppId: '',
                nodeEnv: '',
            },
            isAiwareInitialized: false,
        };

        window.aiware = customWindow.aiware;
        window.openSalesforceChatWindow = mockOpenSalesforceChatWindow;
        window.config = customWindow.config;

        const { asFragment } = render(<AppBar />);
        expect(screen.getByTestId('aiWareAppBar')).toBeInTheDocument();
        expect(asFragment()).toMatchSnapshot();
    });

    it('should call mountWidget and assign widgetIdRef.current', () => {
        const mockInit = jest.fn();
        const mockOn = jest.fn();
        const mockUnmount = jest.fn();
        const mockMountWidget = jest.fn().mockReturnValue('widgetId');
        const mockWidgetIdRef = { current: 'testId' };

        jest.spyOn(React, 'useRef').mockReturnValue(mockWidgetIdRef);

        const customWindow = {
            aiware: {
                init: mockInit.mockImplementation((_config, callback) => callback()),
                on: mockOn.mockImplementation((_config, callback) => callback()),
                mountWidget: mockMountWidget,
                unmountWidget: mockUnmount,
                store:  {
                    getState: jest.fn(),
                },
            },
        };

        window.aiware = customWindow.aiware;
        const { unmount } = render(<AppBar />);

        expect(mockMountWidget).toHaveBeenCalled();
        expect(mockWidgetIdRef.current).toBe('widgetId');

        unmount();
    });
});
