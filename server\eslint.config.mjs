import { defineConfig, globalIgnores } from 'eslint/config';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import jestDom from 'eslint-plugin-jest-dom';
import lodash from 'eslint-plugin-lodash';
import importPlugin from 'eslint-plugin-import';
import eslintConfigPrettier from 'eslint-config-prettier';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';

import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  globalIgnores(['**/node_modules', '**/dist', '**/tmp']),
  js.configs.recommended,
  ...tseslint.configs.recommended,
  importPlugin.flatConfigs.recommended,
  jestDom.configs['flat/recommended'],
  eslintConfigPrettier,
  eslintPluginPrettierRecommended,
  {
    extends: compat.extends('plugin:lodash/recommended'),

    plugins: {
      lodash,
    },

    languageOptions: {
      globals: {
        ...globals.amd,
        ...globals.node,
        ...globals.jest,
        React: 'readonly',
        Promise: 'readonly',
        WeakMap: 'readonly',
        JSX: true,
      },

      parser: tseslint.parser,
      ecmaVersion: 5,
      sourceType: 'commonjs',

      parserOptions: {
        ecmaFeatures: {
          legacyDecorators: true,
        },
      },
    },

    settings: {
      'import/resolver': {
        typescript: {},
        node: {
          extensions: ['.js', '.ts'],
        },
      },
    },

    rules: {
      eqeqeq: ['error', 'always'],
      'arrow-body-style': 'error',
      'eol-last': 'warn',
      'guard-for-in': 'error',
      'no-restricted-modules': ['error', 'rxjs/Rx'],
      'no-caller': 'error',
      'no-redeclare': 'off',
      'spaced-comment': 'warn',
      '@typescript-eslint/prefer-function-type': ['error'],
      '@typescript-eslint/unified-signatures': 'error',
      '@typescript-eslint/consistent-type-definitions': 'error',

      '@typescript-eslint/explicit-member-accessibility': [
        'off',
        {
          accessibility: 'explicit',
        },
      ],

      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-empty-interface': 'error',
      '@typescript-eslint/no-inferrable-types': 'error',
      '@typescript-eslint/no-misused-new': 'error',
      '@typescript-eslint/type-annotation-spacing': 'off',
      'lodash/prefer-invoke-map': 'off',
      'lodash/prefer-lodash-method': 'off',
      'lodash/import-scope': 'off',
      'lodash/prefer-noop': 'off',
      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
        },
      ],
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'],

    rules: {
      'no-unused-vars': 'off',
      'import/named': 'off',
      'no-useless-escape': 'off',
      'no-undef': 'off',
      'react/prop-types': 'off',
      'react/no-unused-prop-types': 'off',
      'lodash/prefer-lodash-typecheck': 'off',
      'lodash/prop-shorthand': 'off',
    },
  },
  {
    files: [
      './test/**/*.ts',
      './test/**/*.js',
      '**/*.test.ts',
      '**/*.test.js',
      '**/__test__/*',
    ],

    rules: {
      'no-import-assign': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'prefer-const': 'off',
      'lodash/prefer-constant': 'off',
    },
  },
]);
