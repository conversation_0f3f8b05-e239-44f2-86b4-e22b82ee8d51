import { render, fireEvent, screen } from '@testing-library/react';
import Tracklet from './Tracklet';

test('handleTrackletClick is not triggered when checkbox is clicked', () => {
  const handleTrackletCheck = jest.fn();
  const handleTrackletClick = jest.fn();
  const tracklet = {
    id: 1,
    orgId: '1',
    trackletId: '1',
    fileId: '1',
    fileName: 'test-file',
    duration: 100,
    startTime: 0,
    endTime: 100,
    thumbnailUrl: 'test-url',
    startTimeMs: 0,
    stopTimeMs: 100,
    attributes: {},
    thumbnailUrls: { best: 'test-url' },
    type: 'test-type',
  };

  render(
    <Tracklet
      tracklet={tracklet}
      index={0}
      thumbnailUrl="test-url"
      thumbnailScale={100}
      showCheck={true}
      checked={false}
      selected={false}
      playing={false}
      handleTrackletClick={handleTrackletClick}
      handleTrackletCheck={handleTrackletCheck}
      onDrag={() => {}}
    />
  );

  const checkbox = screen.getByTestId('Tracklet-0-checkbox');
  fireEvent.click(checkbox);

  expect(handleTrackletCheck).toHaveBeenCalledWith(tracklet, expect.any(Object));
  expect(handleTrackletClick).not.toHaveBeenCalled();
});
