import { Context, RequestHeader } from '../../../types';
import { GraphQLError, ValidationError } from '@common/errors';
import { TrackletBoundingBox } from '../../../../../../types/tracker';
import {
  searchTrackletById,
  seriesSearch,
} from '../searchTracklets/elasticSearch';
import { isString } from 'lodash';

const validDetectionType = ['person', 'vehicle'];

const getBoundingBoxes = async <
  ReqPayload,
  Data extends Partial<{ boundingBoxes: TrackletBoundingBox[] }> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<{ boundingBoxes: TrackletBoundingBox[] }>>
> => {
  const { data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const fileId = req.query.fileId;

  let detectionType: string[] = validDetectionType;
  if (req.query.type && typeof req.query.type === 'string') {
    const lowerType = req.query.type.toLowerCase();
    if (validDetectionType.includes(lowerType)) {
      detectionType = [lowerType];
    } else {
      log.error(
        `Invalid detection type ${req.query.type}, default to ${detectionType}`
      );
    }
  }

  const trackletId = req.query.trackletId;
  const startTimeMs = req.query.startTimeMs
    ? Number(req.query.startTimeMs)
    : undefined;
  const stopTimeMs = req.query.stopTimeMs
    ? Number(req.query.stopTimeMs)
    : undefined;
  let boundingBoxes: TrackletBoundingBox[];

  // Either trackletId or fileId is required
  if (!(trackletId || fileId)) {
    throw new ValidationError('File or trackletId are required');
  }

  try {
    if (trackletId && isString(trackletId)) {
      boundingBoxes = await getBoundingBoxesbyTrackletId({
        trackletId,
        startTimeMs,
        stopTimeMs,
        type: detectionType,
        context,
        headers,
      });
    } else if (fileId && isString(fileId)) {
      boundingBoxes = await getBoundingBoxesByFileId({
        fileId: fileId,
        startTimeMs,
        stopTimeMs,
        type: detectionType,
        context,
        headers,
      });
    } else {
      throw new ValidationError('Invalid file or trackletId');
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }

  const newData = Object.assign({}, data, {
    boundingBoxes,
  });
  return { ...context, data: newData };
};

async function getBoundingBoxesbyTrackletId<ReqPayload, Data>({
  trackletId,
  startTimeMs,
  stopTimeMs,
  type,
  context,
  headers,
}: {
  trackletId: string;
  startTimeMs?: number;
  stopTimeMs?: number;
  type: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const tracklet = await searchTrackletById({ trackletId, context, headers });
  if (!type.includes(tracklet.label)) {
    return [];
  }
  const boundingBoxes = await seriesSearch({
    searchParam: { trackletId, startTimeMs, stopTimeMs },
    context,
    headers,
  });
  return boundingBoxes;
}

async function getBoundingBoxesByFileId<ReqPayload, Data>({
  fileId,
  startTimeMs,
  stopTimeMs,
  type: _type,
  context,
  headers,
}: {
  fileId: string;
  startTimeMs?: number;
  stopTimeMs?: number;
  type: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  // TODO: type is not used here, Need wait for platform to fix issue VE-4084
  // the workaround in getBoundingBoxesbyTrackletId can not be used here because
  // there are so many tracklets in one file, and it will be extremely inefficident
  // to check each tracklet.
  const boundingBoxes = await seriesSearch({
    searchParam: { fileId, startTimeMs, stopTimeMs },
    context,
    headers,
  });
  // <<<<<<< HEAD
  // =======
  //   const trackletMap: { [key: string]: trackerEngineResultObject } = {};
  //   for (const rs of engineResultsArr) {
  //     for (const object of rs.jsondata.object) {
  //       trackletMap[object.referenceId] = object;
  //     }
  //   }
  //   const boundingBoxes: TrackletBoundingBox[] = [];
  //   for (const rs of engineResultsArr) {
  //     for (const series of rs.jsondata.series) {
  //       if (
  //         !isEmpty(type) &&
  //         !type.includes(trackletMap[series.object.referenceId].label)
  //       ) {
  //         continue;
  //       }
  //       const boundingPoly = series.object.boundingPoly.map((point) => ({
  //         x: point.x,
  //         y: point.y,
  //       }));
  //       boundingBoxes.push({
  //         trackletId: series.object.referenceId,
  //         boundingPoly,
  //         startTimeMs: series.startTimeMs,
  //         stopTimeMs: series.stopTimeMs,
  //       });
  //     }
  //   }
  // >>>>>>> 8aff4fffc4ab5747822a6cbd359bb853b0b84a56
  return boundingBoxes;
}

export default getBoundingBoxes;
