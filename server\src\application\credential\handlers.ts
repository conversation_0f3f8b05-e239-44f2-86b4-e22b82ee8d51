import A from './actions';
import E from '@common/errors';
import { NextFunction, Response } from 'express';
import { RequestWithMeta } from '../types';
import GQLApi from '../../util/api/graphQL';
import { getCredentialType } from './actions/generateCredential';
import * as ResTypes from '../../../../types/responses';
import NodeCache from 'node-cache';

const createHandlers = ({
  log,
  gql,
  cache,
}: {
  log: Logger;
  gql: GQLApi;
  cache: NodeCache;
}) => ({
  get: {
    credential: async (
      req: RequestWithMeta<object>,
      res: Response,
      next: NextFunction
    ) => {
      const cxt = {
        req,
        res,
        log,
        data: {},
        gql,
        cache,
      };
      await A.generateCredential(cxt)
        .then(
          A.send<ResTypes.GetCredentialResponse, getCredentialType>((cxt) => ({
            storage: cxt.data.credentialResult.storage,
            storageUrl: cxt.data.credentialResult.storageUrl,
            bucket: cxt.data.credentialResult.bucket,
            region: cxt.data.credentialResult.region,
            credential: cxt.data.credentialResult.credential,
          }))
        )
        .catch((e) => {
          switch (e.constructor) {
            case E.ValidationError:
              return A.sendError(cxt)(400)(e);
            case E.ForbiddenError:
              return A.sendError(cxt)(403)(e);
            case E.UnauthorizedError:
              return A.sendError(cxt)(401)(e);
            case E.ActionError:
            default:
              return A.sendError(cxt)(500)(e);
          }
        })
        .finally(next);
    },
  },
});

export default createHandlers;
