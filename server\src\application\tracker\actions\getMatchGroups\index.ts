import { Context } from '@application/types';
import { GraphQLError, ActionValidationError } from '@common/errors';
import { queries, responses } from '@tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  MatchGroup,
  VideoSpliceDetails,
} from '../../../../../../types/tracker';
import { getSplicingTdoDetails } from '../getSplicingTdoDetails';

const getMatchGroups = async <
  ReqPayload,
  Data extends Partial<
    responses.getMatchGroups & { eventId?: string }
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<
    ReqPayload,
    Data & responses.getMatchGroups & { matchGroupsCount: number }
  >
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const { pageSize, currentPage, sortDirection, sortType } = req.query;

  const eventId = req.query?.eventId ?? req.params?.eventId ?? data?.eventId;

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    const { structuredDataObjects } = await callGQL<
      responses.searchStructuredDataObjects<MatchGroup>,
      ReqPayload,
      Data
    >(
      context,
      headers,
      queries.searchStructuredDataObjects(
        eventId && typeof eventId === 'string' ? { eventId } : {}
      ),
      {
        schemaId: schemaId,
        limit: Number(pageSize ?? 10000),
        offset: (Number(currentPage ?? 1) - 1) * Number(pageSize ?? 10000),
        sort:
          sortDirection &&
          typeof sortDirection === 'string' &&
          ['asc', 'desc'].includes(sortDirection)
            ? sortDirection
            : 'asc',
        sortType:
          sortType &&
          typeof sortType === 'string' &&
          ['createdDateTime', 'modifiedDateTime'].includes(sortType)
            ? sortType
            : 'createdDateTime',
      }
    );

    const tdoIds: string[] = [];
    for (const record of structuredDataObjects.records) {
      for (const generatedTimeline of record.data.generatedTimelines ?? []) {
        if (generatedTimeline.tdoId) {
          tdoIds.push(generatedTimeline.tdoId);
        }
      }
    }

    let videoSpliceTdoDetails: {
      [key: string]: VideoSpliceDetails;
    } = {};

    if (tdoIds.length > 0) {
      videoSpliceTdoDetails = await getSplicingTdoDetails({
        tdoIds,
        context,
        headers,
      });
    }

    structuredDataObjects.records.forEach((record, i) => {
      record.data.generatedTimelines?.forEach((generatedTimeline, j) => {
        if (
          generatedTimeline.tdoId &&
          structuredDataObjects?.records?.[i]?.data?.generatedTimelines?.[j]
        ) {
          const videoSpliceDetailsByTdoId =
            videoSpliceTdoDetails[generatedTimeline.tdoId];

          let newGeneratedTimeline = {
            ...generatedTimeline,
          };
          if (videoSpliceDetailsByTdoId) {
            newGeneratedTimeline = {
              ...generatedTimeline,
              ...videoSpliceDetailsByTdoId,
            };
          }
          structuredDataObjects.records[i].data.generatedTimelines[j] =
            newGeneratedTimeline;
        }
      });
    });

    const new_data = Object.assign({}, data, {
      matchGroups: {
        pagination: {
          pageSize: structuredDataObjects.limit,
          currentPage:
            structuredDataObjects.offset / structuredDataObjects.count + 1,
          totalPages: Math.ceil(
            structuredDataObjects.count / structuredDataObjects.limit
          ),
          totalCount: structuredDataObjects.count,
        },
        sort: structuredDataObjects.orderBy?.[0],
        eventId,
        searchResults: structuredDataObjects.records.map((record) => ({
          ...record.data,
          createdDateTime: record.createdDateTime,
          modifiedDateTime: record.modifiedDateTime,
        })),
      },
      matchGroupsCount: structuredDataObjects.count,
    });
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default getMatchGroups;
