import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';

let existingRootFolers = [] as {
  id: string;
  name: string;
  ownerId: string | null;
}[];

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes('createRootFolders')) {
        return Promise.resolve({
          createRootFolders: [
            {
              id: 'createdId789',
              name: 'Root Admin cms Root Folder',
              ownerId: null,
            },
          ],
        });
      }
      return Promise.resolve({
        rootFolders: existingRootFolers,
      });
    }
  ),
}));

describe('checkCreateRootFolder', () => {
  it('create root folder when no root folder available', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    existingRootFolers.length = 0;
    const resp = await supertest(expressApp)
      .post('/api/v1/rootfolder')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    const want = {
      rootFolderId: 'createdId789',
      status: 'created',
    };
    expect(resp.body).toEqual(want);
  });

  it('return existing root folder when there is a root folder available', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });
    existingRootFolers.push({
      id: 'existingId123',
      name: 'Root Admin cms Root Folder',
      ownerId: null,
    });
    const resp = await supertest(expressApp)
      .post('/api/v1/rootfolder')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    const want = {
      rootFolderId: 'existingId123',
      status: 'existing',
    };
    expect(resp.body).toEqual(want);
  });
});
