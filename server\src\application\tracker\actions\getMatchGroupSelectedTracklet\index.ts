import { Context, RequestHeader } from '@application/types';
import { GraphQLError, NotFoundError } from '@common/errors';
import { MatchGroup } from '../../../../../../types/tracker';
import { responses } from '@tracker/graphQL';
import { fingerprintSearch } from '../searchTracklets/elasticSearch';
import { isEmpty } from 'lodash';

const getMatchGroupSelectedTracklets = async <
  ReqPayload,
  Data extends Partial<
    responses.getMatchGroupSelectedTracklets & {
      matchGroup?: MatchGroup;
    } & responses.getEvent
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  Context<ReqPayload, Data & Partial<responses.getMatchGroupSelectedTracklets>>
> => {
  const { data, req, log } = context;

  // Pull the engine id stored in the event, otherwise use the default.
  // Do not ever change the default engine GUID. This will break old events that do not store the engine id.
  const trackerEngineId =
    data?.event?.trackerEngineId ?? 'd77d6133-a801-472c-bc7e-48ddafec8590';

  const headers = { Authorization: req.headers.authorization };
  const { matchGroup } = data;
  if (!matchGroup) {
    throw new NotFoundError('No match group provided');
  }

  const selectedTrackletIds = matchGroup.selectedTracklets;
  try {
    const searchResults = await searchTracklets({
      referenceTrackletIds: selectedTrackletIds,
      context,
      headers,
      trackerEngineId,
    });

    const selectedTracklets = searchResults
      ? {
          results: searchResults.tracklets,
          matchGroupId: matchGroup.id,
          matchGroupName: matchGroup.name,
          eventId: matchGroup.eventId,
        }
      : {
          matchGroupId: matchGroup.id,
          matchGroupName: matchGroup.name,
          eventId: matchGroup.eventId,
        };
    const new_data = { selectedTracklets, matchGroup };
    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e.message);
    throw new GraphQLError(e.message);
  }
};

async function searchTracklets<ReqPayload, Data>({
  referenceTrackletIds,
  context,
  headers,
  trackerEngineId,
}: {
  referenceTrackletIds?: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
  trackerEngineId: string;
}) {
  if (!referenceTrackletIds || isEmpty(referenceTrackletIds)) {
    return null;
  }

  const searchResults = await fingerprintSearch({
    referenceTrackletIds,
    limit: referenceTrackletIds.length,
    offset: 0,
    context,
    headers,
    type: ['person', 'vehicle'],
    times: context.req.query.times === 'true',
    trackerEngineId,
  });

  const results = {
    tracklets: searchResults.tracklets,
    totalCount: searchResults.totalResults,
    totalPages: Math.ceil(searchResults.totalResults / searchResults.limit),
    currentPage: Math.floor(searchResults.from / searchResults.limit) + 1,
    pageSize: searchResults.limit,
  };
  return results;
}

export default getMatchGroupSelectedTracklets;
