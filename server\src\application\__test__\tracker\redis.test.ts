import createRedisCache from '@tracker/redis';

type CloudType = 'azure' | 'aws';

const env = {
  apiRoot: '',
  credentialApi: '',
  veritoneAppId: '',
  graphQLEndpoint: '',
  nodeEnv: '',
  port: 3002,
  serviceName: '',
  startApi: '',
  useRedis: true,
  cloud: 'aws' as const,
  blob: {
    endpointSuffix: '',
    account: '',
    key: '',
    container: '',
    expireSecs: 0,
  },
  s3: {
    bucket: '',
    accessKey: '',
    secretKey: '',
    roleArn: '',
    region: '',
    expireSecs: 0,
  },
  glcIngestorEngineId: '',
  cpuClusterId: 'my-cpu-cluster-id',
  gpuClusterId: 'my-gpu-cluster-id',
  trackerEngineId: 'test-f6634718-c2b7-40f5-9c2c-a606420104ac',
  validWritableCredentialEngineIds: [
    'test-f6634718-c2b7-40f5-9c2c-a606420104ac',
    'test-d77d6133-a801-472c-bc7e-48ddafec8590',
  ],
  outputWriterEngineId: '',
  videoSliceEngineId: '',
  registryIds: {
    eventsRegistryId: '',
    matchGroupsRegistryId: '',
  },
  redis: {
    host: '',
    port: 0,
  },
};

describe('Redis', () => {
  it('should get and set value', async () => {
    const redisClient = await createRedisCache({
      env,
      log: { info: () => {}, error: () => {}, debug: () => {} },
    });
    if (!redisClient) {
      throw new Error('redisClient not created');
    }

    await redisClient.set('key', 'value');
    const value = await redisClient.get('key');
    expect(value).toBe('value');
  });
});
