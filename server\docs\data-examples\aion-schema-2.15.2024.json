{"type": "object", "properties": {"object": {"type": "array", "items": {"type": "object", "properties": {"fingerprintVector": {"type": "array", "items": {"type": "number"}}, "label": {"type": "string"}, "referenceId": {"type": "string", "format": "utc-millisec"}, "startTimeMs": {"type": "integer"}, "stopTimeMs": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}}}}, "type": {"type": "string"}, "vendor": {"type": "object", "properties": {"label": {"type": "string"}, "thumbnail": {"type": "object", "properties": {"boundingPoly": {"type": "array", "items": {"type": "object", "properties": {"x": {"type": "number"}, "y": {"type": "number"}}}}, "startTimeMs": {"type": "integer"}, "stopTimeMs": {"type": "integer"}}}, "uri": {"type": "string"}}}}}}, "series": {"type": "array", "items": {"type": "object", "properties": {"object": {"type": "array", "items": {"type": "object", "properties": {"boundingPoly": {"type": "array", "items": {"type": "object", "properties": {"x": {"type": "number"}, "y": {"type": "number"}}}}, "referenceId": {"type": "string", "format": "utc-millisec"}}}}, "startTimeMs": {"type": "integer"}, "stopTimeMs": {"type": "integer"}}}}}}