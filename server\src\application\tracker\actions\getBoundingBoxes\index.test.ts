import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import { Context } from '../../../types';
import { createRequest, createResponse } from 'node-mocks-http';
import {
  seriesSearch,
  searchTrackletById,
} from '../searchTracklets/elasticSearch';
import getBoundingBoxes from '.';
import { ValidationError } from '../../../common/errors';

let cxt: Context<object, object>;
jest.mock('../searchTracklets/elasticSearch', () => ({
  searchTrackletById: jest.fn().mockImplementation((x) =>
    Promise.resolve({
      label: 'person',
    })
  ),
  seriesSearch: jest.fn(),
}));

describe('getBoundingBoxes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {},
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        query: {},
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('get boundingbox with file', async () => {
    cxt.req.query.fileId = 'tdo123';

    const response = await getBoundingBoxes(cxt);
    expect(seriesSearch).toHaveBeenCalledTimes(1);
    expect(seriesSearch).toHaveBeenCalledWith({
      searchParam: {
        fileId: 'tdo123',
        startTimeMs: undefined,
        stopTimeMs: undefined,
      },
      context: expect.anything(),
      headers: expect.anything(),
    });
    expect(response).not.toBeNull();
  });

  it('get boundingbox with file, startTimeMs stopTimeMs,type ', async () => {
    cxt.req.query.fileId = 'tdo123';
    cxt.req.query.type = 'person';
    cxt.req.query.startTimeMs = '1000';
    cxt.req.query.stopTimeMs = '2000';

    const response = await getBoundingBoxes(cxt);
    expect(seriesSearch).toHaveBeenCalledTimes(1);
    expect(seriesSearch).toHaveBeenCalledWith({
      searchParam: {
        fileId: 'tdo123',
        startTimeMs: 1000,
        stopTimeMs: 2000,
      },
      context: expect.anything(),
      headers: expect.anything(),
    });
  });

  it('get boundingbox with trackletId', async () => {
    cxt.req.query.trackletId = 'trackletId123';

    const response = await getBoundingBoxes(cxt);
    expect(seriesSearch).toHaveBeenCalledTimes(1);
    expect(seriesSearch).toHaveBeenCalledWith({
      searchParam: { trackletId: 'trackletId123' },
      context: expect.anything(),
      headers: expect.anything(),
    });
    expect(response).not.toBeNull();
  });

  it('throw ValidationError when no file or trackletId provided', async () => {
    async function getSelectedTrackletsNoFileTrackletId() {
      await getBoundingBoxes(cxt);
    }
    expect(getSelectedTrackletsNoFileTrackletId).rejects.toThrow(
      ValidationError
    );
    expect(seriesSearch).toHaveBeenCalledTimes(0);
  });
});
