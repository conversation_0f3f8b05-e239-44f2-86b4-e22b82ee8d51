import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { GraphQLError } from '@common/errors';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import createRootFolder from '../createRootFolder';
import { CheckCreateRootFolderStatus } from '../../../../../../types/tracker';

async function checkCreateRootFolder<ReqPayload, Data extends object = object>(
  context: Context<ReqPayload, Data>
) {
  const { data, log, req } = context;
  const headers = { Authorization: req.headers.authorization };

  try {
    const { rootFolders = [] } = await callGQL<
      responses.getRootFolder,
      ReqPayload,
      Data
    >(context, headers, queries.getRootFolder, {});

    let rootFolderId;
    let status: CheckCreateRootFolderStatus = 'existing';
    const nullOwnerIdRootFolder = rootFolders.find(
      (folder) => folder.ownerId === null
    );
    if (nullOwnerIdRootFolder) {
      rootFolderId = nullOwnerIdRootFolder.id;
      status = 'existing';
    } else {
      const newRootFolder = await createRootFolder(context);
      rootFolderId = newRootFolder.id;
      status = 'created';
    }

    const new_data = Object.assign({}, data, {
      rootFolder: {
        rootFolderId,
        status,
      },
    });

    const new_context = Object.assign({}, context, { data: new_data });
    return new_context;
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
}

export default checkCreateRootFolder;
