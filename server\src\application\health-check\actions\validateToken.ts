import { callGQL } from '../../../util/api/graphQL/callGraphQL';
import { ForbiddenError, UnauthorizedError } from '@common/errors';
import { Context } from '../../types';

interface ValidateTokenResponse {
  validateToken: {
    token: string;
  };
}

const validateTokenQuery = (token: string) => `
  mutation validateToken {
    validateToken(token:"${token}") {
      token
    }
  }
`;

const validateToken = async <ReqPayload, Data>(
  context: Context<ReqPayload, Data>
) => {
  const { req, log } = context;

  const bearerHeader = req.get('Authorization');
  if (bearerHeader) {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];
    if (bearerToken) {
      try {
        const query = validateTokenQuery(bearerToken);
        const headers = { Authorization: req.headers.authorization };
        const response = await callGQL<ValidateTokenResponse, ReqPayload, Data>(
          context,
          headers,
          query
        );
        if (response?.validateToken?.token) {
          return context;
        }
      } catch (_err) {
        log.error('API Token Validation failed');
        throw new ForbiddenError();
      }
    } else {
      log.error('API Token Validation failed');
      throw new UnauthorizedError();
    }
  } else {
    log.error('API Token Validation failed');
    throw new UnauthorizedError();
  }
};

export default validateToken;
