import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import createMatchGroupStructuredData from '../createMatchGroupStructuredData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { omit } from 'lodash';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'matchGroupId'),
}));

let cxt: Context<object, object>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createStructuredData: {} })
  ),
}));

const mockTimelineProject = {
  groups: [
    {
      id: 'group1',
      name: 'Group 1',
      tracklets: [
        {
          trackletId: 'tracklet1',
          tdoId: 'tdo1',
          startTimeMs: 1000,
          stopTimeMs: 2000,
        },
        {
          trackletId: 'tracklet2',
          tdoId: 'tdo2',
          startTimeMs: 3000,
          stopTimeMs: 4000,
        },
      ],
    },
    {
      id: 'group2',
      name: 'Group 2',
      tracklets: [
        {
          trackletId: 'tracklet3',
          tdoId: 'tdo3',
          startTimeMs: 5000,
          stopTimeMs: 6000,
        },
      ],
    },
  ],
  modifiedDateTime: '2024-05-03T21:57:48.320Z',
  modifiedUserId: 'ece0ab78-89bb-43a5-bb2e-05f712415766',
};
const mockData: object = {
  id: 'matchGroupId',
  name: 'name',
  eventId: 'eventId',
  searches: [],
  selectedTracklets: [],
  userId: 'userId',
  timelineProject: mockTimelineProject,
  generatedTimelines: {
    id: 'timeline1',
    timeline: mockTimelineProject,
    name: 'Timeline Project 1',
    createdDateTime: '2022-01-01T00:00:00Z',
    createdUserId: 'user1',
    tdoId: 'tdo1',
    resolution: '1920x1080',
    outputFormat: 'mp4',
    videoLengthMs: 60000,
    videoSizeBytes: 5000000,
  },
  userEmail: 'mock-userEmail',
  userOrganizationId: 'mock-userOrganizationId',
};

describe('Create Match Group Structured Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    jest.spyOn(clientRedis, 'del').mockResolvedValue(1);
    cxt = {
      data: {
        name: '',
        eventId: '',
        searches: [],
        userId: '',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
    cxt.cache.set('matchGroupsSchemaId', 'matchGroupsSchemaId');
  });

  it('Successfully creates structured data', async () => {
    cxt.data = mockData;

    const response = await createMatchGroupStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: omit(mockData, 'userId', 'userEmail', 'userOrganizationId'),
      }
    );
    expect(clientRedis.del).toHaveBeenCalledWith(
      'event-mock-userOrganizationId-eventId'
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is missing required data', async () => {
    cxt.data = {};

    expect(
      async () => await createMatchGroupStructuredData(cxt)
    ).rejects.toThrow('Missing required data');
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is missing required data', async () => {
    const mockData = {
      id: 'matchGroupId',
      name: 'name',
      eventId: 'eventId',
      searches: [
        {
          id: 'searchId',
          searchName: 'searchName',
          referenceTrackletId: 'referenceTrackletId',
        },
      ],
      selectedTracklets: ['selectedTracklets'],
      userId: 'userId',
    };
    cxt.data = mockData;
    cxt.cache.del('matchGroupsSchemaId');
    expect(
      async () => await createMatchGroupStructuredData(cxt)
    ).rejects.toThrow('schemaId not found');
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if data is missing', async () => {
    const mockData = {
      id: 'matchGroupId',
      name: 'name',
      eventId: 'eventId',
      searches: [
        {
          id: 'searchId',
          searchName: 'searchName',
          referenceTrackletId: 'referenceTrackletId',
        },
      ],
      selectedTracklets: ['selectedTracklets'],
      userId: 'userId',
    };

    // Test each missing item
    ['name', 'eventId', 'userId'].forEach((missingItem) => {
      const newMockData = omit(mockData, missingItem);
      cxt.data = newMockData;
      expect(
        async () => await createMatchGroupStructuredData(cxt)
      ).rejects.toThrow('Missing required data');
      expect(callGQL).not.toHaveBeenCalled();
    });
  });
});
