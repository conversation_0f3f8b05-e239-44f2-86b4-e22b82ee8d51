import NodeCache from 'node-cache';
import createFolder from '../createFolder';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import * as ReqTypes from '../../../../../../types/requests';

let cxt: Context<
  object,
  responses.getMe & responses.getParentFolderId & ReqTypes.CreateEventPayload
>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createFolder: {} })
  ),
}));

describe('Create Folder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        name: 'name',
        description: 'description',
        userId: 'userId',
        firstName: 'firstName',
        lastName: 'lastName',
        userEmail: 'userEmail',
        userOrganizationId: 'userOrganizationId',
        eventStartDate: 'eventStartDate',
        eventEndDate: 'eventEndDate',
        parentFolderId: '36b57a3d-3b44-4328-adb0-49d78cd0b8db',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Successfully create a folder', async () => {
    const response = await createFolder(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        name: 'name',
        description: 'description',
        userId: 'userId',
        parentFolderId: '36b57a3d-3b44-4328-adb0-49d78cd0b8db',
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no name', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.name = undefined;

    expect(async () => await createFolder(cxt)).rejects.toThrowError(
      'Missing required data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is no userId', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.userId = undefined;

    expect(async () => await createFolder(cxt)).rejects.toThrowError(
      'Missing required data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is no parentFolderId', async () => {
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.parentFolderId = undefined;

    expect(async () => await createFolder(cxt)).rejects.toThrowError(
      'Missing required data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
