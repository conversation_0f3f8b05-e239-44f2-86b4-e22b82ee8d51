import { Context, RequestHeader } from '@application/types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { GraphQLError } from '@common/errors';
import { responses } from '../../graphQL';
import { isEmpty } from 'lodash';
import { User } from '../../../../../../types/tracker';
const batchQuerySize = 10;

export async function getUsers<ReqPayload, Data>({
  userIds,
  context,
  headers,
}: {
  userIds: string[];
  context: Context<ReqPayload, Data>;
  headers: RequestHeader;
}) {
  const users: User[] = [];
  if (userIds.length === 0) {
    return users;
  }

  for (let i = 0; i < userIds.length; i += batchQuerySize) {
    const batch = userIds.slice(i, i + batchQuerySize);
    const updateQuery: Array<string> = [];
    batch.forEach((userId, index) => {
      updateQuery.push(
        `user_${index}:user(id: "${userId}") {
            id
            firstName
            lastName
          }`
      );
    });

    const query = `query { ${updateQuery.join('\n')} }`;

    const response = await callGQL<responses.getUsers, ReqPayload, Data>(
      context,
      headers,
      query,
      {},
      true
    );
    if (!isEmpty(response.errors)) {
      context.log?.error('failed to get user ', userIds);
      throw new GraphQLError(response.errors[0]);
    }
    // TODO: Need for cast here seems to mean the type is wrong - Fix
    const result = Object.values(response) as User[];
    users.push(...result.filter((res) => res?.id));
  }
  return users;
}
