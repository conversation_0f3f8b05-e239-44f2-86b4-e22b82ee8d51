import env from '../../../env';
import { GraphQLClient, request, Variables } from 'graphql-request';
import { Context, RequestHeader } from '../../../application/types';
import { v4 as uuidv4 } from 'uuid';
import { GraphQLError } from '@common/errors';

export async function callGQL<T, U, V>(
  context: Context<U, V>,
  headers: RequestHeader,
  query: string,
  variables?: Variables,
  allErrorPolicy?: boolean
): Promise<T> {
  const { log } = context;
  const url = `${env.apiRoot}/${env.graphQLEndpoint}`;

  if (!context?.requestIds) {
    context.requestIds = [];
  }
  const rid = uuidv4();

  try {
    if (allErrorPolicy) {
      const noneErrorPolicyClient = new GraphQLClient(url, {
        errorPolicy: 'all',
      });
      context.requestIds.push(rid);

      const res = await noneErrorPolicyClient.rawRequest<T>(
        query,
        variables,
        headers
      );
      return res.data;
    } else {
      const res = await request<T>({
        url: url,
        document: query,
        variables: variables,
        requestHeaders: {
          ...headers,
          'veritone-request-id': rid,
        },
      });

      context.requestIds.push(rid);
      return res;
    }
  } catch (err) {
    log.error(err);
    throw new GraphQLError(err);
  }
}

export async function fetchGraphQLApi<T>({
  endpoint,
  query,
  variables,
  token,
  veritoneAppId,
}: {
  endpoint: string;
  query: string;
  variables?: Record<string, unknown>;
  token?: string | null;
  veritoneAppId: string;
}): Promise<{ data?: T; errors?: unknown[] }> {
  const headers: HeadersInit = {
    Authorization: token ? `bearer ${token}` : '',
    'Content-Type': 'application/json',
    'x-veritone-application': veritoneAppId,
  };

  const result = await fetch(endpoint, {
    method: 'post',
    body: JSON.stringify({
      query,
      variables,
    }),
    headers,
  }).then((r) => r.json());
  return result;
}
