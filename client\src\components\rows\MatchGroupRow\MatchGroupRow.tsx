import cn from 'classnames';
import './MatchGroupRow.scss';
import {
  AlertLevel,
  Column,
  createSnackNotification,
} from '@components/common';
import { Accordion, AccordionSummary, AccordionDetails, Button, IconButton, Menu, MenuItem, styled, Divider } from '@mui/material';
import PotentialMatchSearchRow, {
  PotentialMatchSearchRowType,
} from '../PotentialMatchSearchRow/PotentialMatchSearchRow';
import Table, { RowProps } from '@components/common/Table/Table';
import { useNavigate, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { MatchGroup, Search } from '@shared-types/tracker';
import { selectMatchGroupSearchDeleted } from '@store/modules/event/slice';
import { useEffect, useState } from 'react';
import { isExpiringString } from '@utility/expiringString';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import EditNoteIcon from '@mui/icons-material/EditNote';
import ConfirmDialog from './MatchGroupDialog/MatchGroupDialog';
import { useAppDispatch } from '@store/hooks';
import { deleteMatchGroup, updateMatchGroup } from '@store/modules/matchGroup/slice';
import { DeletedMatchGroupLocalStorage } from '@utility/localStorage';
import ls from 'localstorage-slim';
import { getEventById, selectEventSelected } from '@store/modules/home/<USER>';

const MenuItemCustom = styled(MenuItem)({
  display: 'flex',
  alignItems: 'center',
  gap: 15
});

const MatchGroupAccordion = styled(Accordion)(() => ({
  "&.Mui-disabled": {
    backgroundColor: 'inherit',
    opacity: 1,

    "&: hover": {
      pointer: 'default'
    }
  },
}));

export function MatchGroupRow({
  colData,
  rowData,
  rowIndex,
  onRowClick,
  rowIndex: matchGroupRowIndex,
}: RowProps<MatchGroup>) {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const { id: matchGroupId, name, searches } = rowData;
  const navigate = useNavigate();
  const { eventId } = useParams();
  const matchGroupSearchDeletion = useSelector(selectMatchGroupSearchDeleted);
  const [filteredSearches, setFilteredSearches] = useState<
    Search[] | undefined
  >(searches);
  const eventSelected = useSelector(selectEventSelected);
  const [menuEl, setMenuEl] = useState<null | HTMLElement>(null);
  const open = Boolean(menuEl);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [openConfirmRename, setOpenConfirmRename] = useState<boolean>(false);

  const handleOpenMenu = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    setMenuEl(e.currentTarget);
  };
  const handleCloseMenu = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    setMenuEl(null);
  };

  const handleClickRename = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    handleCloseMenu(e);
    setOpenConfirmRename(true);
  };

  const handleClickDelete = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    handleCloseMenu(e);
    setOpenConfirmDelete(true);
  };

  const handleConfirmRename = (newName?: string) => {
    if (newName && matchGroupId) {
      dispatch(updateMatchGroup({matchGroupName: newName, matchGroupId, rename: true}));
    }
  };

  const handleConfirmDelete = () => {
    if (matchGroupId) {
      dispatch(deleteMatchGroup(matchGroupId)).then(() => {
        if (eventSelected.event) {
          dispatch(getEventById({ eventId: eventSelected.event.id }));
        }
      });
    }
  };

  const isPending = () : boolean => {
    const deletedMatchGroups: DeletedMatchGroupLocalStorage[] = ls.get('deletedMatchGroups') || [];
    const isDeleted = Array.isArray(deletedMatchGroups) && deletedMatchGroups.some(matchGroup => matchGroup.value.matchGroupId === matchGroupId);
    return isDeleted;
  };

  useEffect(() => {
    const deletedSearchIdsStr = localStorage.getItem('deletedSearchIds');
    if (!deletedSearchIdsStr) {
      return;
    }
    const parsedDeletedSearchIds = JSON.parse(deletedSearchIdsStr);
    const deletedSearchIdRecords = Array.isArray(parsedDeletedSearchIds)
      ? parsedDeletedSearchIds.filter(isExpiringString)
      : [];
    const now = new Date().getTime();

    // Filter out expired items and items that don't exist
    const validSearchIds = deletedSearchIdRecords.filter(
      (item) => now < item.expiry
    );

    // Update localStorage and state
    localStorage.setItem('deletedSearchIds', JSON.stringify(validSearchIds));

    const deletedSearchIds = validSearchIds.map(
      (item: { value: string; expiry: number }) => item.value
    );

    setFilteredSearches(
      searches?.filter((search) => !deletedSearchIds.includes(search.id))
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchGroupSearchDeletion]);

  return (
    <>
      <div role="row" className={cn('match-group-row')}>
        <div
          className={cn('match-group-row__row')}
          onClick={() => onRowClick?.(rowData)}
        >
          <MatchGroupAccordion
            key={`MatchGroupRow-${rowIndex}`}
            data-testid="event-match-groups"
            disabled={isPending()}
          >
            <AccordionSummary
              expandIcon={<div className="material-icons">expand_more</div>}
              aria-controls="match-group-row-content"
              id="match-group-row-header"
              data-testid={`match-group-row-${rowIndex}-${rowData.eventId}`}
            >
              {colData?.map(({ grow, dataKey, width, minWidth }, index) => {
                if (dataKey === 'name') {
                  return (
                    <div
                      key={`MatchGroupRowCellName-${index}`}
                      className="match-group-row__name"
                      style={{ flexGrow: grow, maxWidth: width, minWidth }}
                    >
                      {name}
                    </div>
                  );
                }
                if (dataKey === 'numSearches') {
                  return (
                    <div key={`MatchGroupRowCellSearches-${index}`} className="match-group-row__searches" style={{ flexGrow: grow, maxWidth: width, minWidth }}>
                      <span>{filteredSearches?.length}</span>

                      <div className='match-group-row__searches-action'>
                        <Button
                          variant="outlined"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/event/${eventId}/match-group/${matchGroupId}`);
                          }}
                          onAuxClick={() => window.open(`/event/${eventId}/match-group/${matchGroupId}`, '_blank')}
                        >
                          {I18nTranslate.TranslateMessage('viewMatchGroup')}
                        </Button>
                        <IconButton size='small' onClick={handleOpenMenu} data-testid={`match-group-row-menu-icon-${rowIndex}`}>
                          <MoreHorizIcon fontSize='small'/>
                        </IconButton>
                        <Menu
                          anchorEl={menuEl}
                          open={open}
                          onClose={handleCloseMenu}
                          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                          slotProps={{
                            paper: {
                              sx: { width: 170 }
                            }
                          }}
                        >
                          <MenuItemCustom
                            onClick={handleClickRename}
                            data-testid={`match-group-row-menu-rename-${rowIndex}`}
                          >
                            <EditNoteIcon  />
                            <span>Rename</span>
                          </MenuItemCustom>
                          <Divider />
                          <MenuItemCustom
                            onClick={handleClickDelete}
                            data-testid={`match-group-row-menu-delete-${rowIndex}`}
                          >
                            <div className="material-symbols-outlined">delete</div>
                            <span>Delete</span>
                          </MenuItemCustom>
                        </Menu>
                      </div>
                    </div>
                  );
                }
              })}
            </AccordionSummary>
            <AccordionDetails>
              <Table<PotentialMatchSearchRowType>
                RowComponent={PotentialMatchSearchRow}
                rowData={filteredSearches?.map((search) => ({
                  ...search,
                  matchGroupRowIndex,
                  matchGroupId,
                  actions: ''
                }))}
                emptyMessage={`${intl.formatMessage({ id: 'noSearches', defaultMessage: 'There are no searches' })}.`}
                onRowClick={({ id: searchId }) => {
                  if (!matchGroupId) {
                    createSnackNotification(
                      AlertLevel.Error,
                      'Error',
                      'No Match Group ID'
                    );
                    return;
                  }
                  if (!searchId) {
                    createSnackNotification(AlertLevel.Error, 'No Search ID');
                    return;
                  }
                  navigate(`/event/${eventId}/match-group/${matchGroupId}/potential-match-search/${searchId}`);
                }}
                onAuxClick={({ id: searchId }) => {
                  window.open(`/event/${eventId}/match-group/${matchGroupId}/potential-match-search/${searchId}`, '_blank');
                }}
                disablePagination
              >
                <Column
                  title={intl.formatMessage({ id: 'search',defaultMessage: 'Search' })}
                  dataKey="searchName"
                  grow={1}
                />
                <Column
                  title={intl.formatMessage({ id: 'actions',defaultMessage: 'Actions' })}
                  dataKey="actions"
                  grow={1}
                />
              </Table>
            </AccordionDetails>
          </MatchGroupAccordion>
        </div>
      </div>
      <ConfirmDialog
        open={openConfirmRename}
        title="Rename Match Group"
        confirmText="Save"
        onConfirm={handleConfirmRename}
        onClose={() => setOpenConfirmRename(false)}
        value={name ?? ''}
      />
      <ConfirmDialog
        open={openConfirmDelete}
        title="Delete Match Group"
        content={intl.formatMessage({ id: 'confirmDeleteMatchGroup', defaultMessage: 'Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.' })}
        confirmText="Yes, Delete"
        onConfirm={handleConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        isDelete
      />
    </>
  );
}

export default MatchGroupRow;
