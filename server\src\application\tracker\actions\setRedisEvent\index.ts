import { responses } from '@tracker/graphQL';

import { Context } from '../../../types';

const setRedisEvent = async <
  ReqPayload,
  Data extends responses.getMe &
    responses.getEvent &
    responses.getMatchGroups &
    responses.searchFiles,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, req, redisWrapper } = context;
  if (redisWrapper && data.userOrganizationId) {
    await redisWrapper.event.set(
      data.event?.id ?? req.params.eventId,
      String(data.userOrganizationId),
      {
        ...data.event,
        matchGroupsCount: data.matchGroups.pagination.totalCount,
        filesCount: data.searchFiles.totalCount,
      }
    );
  }

  return context;
};

export default setRedisEvent;
