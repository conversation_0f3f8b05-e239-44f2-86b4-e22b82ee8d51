import { Context } from '../../../types';
import { queries, responses } from '@tracker/graphQL';
import deleteTemporalData from '../deleteTemporalData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';

const deleteMatchGroup = async <
  ReqPayload,
  Data extends Partial<responses.getMatchGroup> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { cache, data, log, req } = context;
  const headers = { Authorization: req.headers.authorization };

  const { matchGroupId, matchGroup } = data;
  if (!matchGroupId || !matchGroup) {
    throw new ActionError('No matchGroup provided');
  }

  const schemaId = cache.get('matchGroupsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    const { id, generatedTimelines } = matchGroup;
    if (generatedTimelines !== undefined) {
      for (const timeline of generatedTimelines) {
        await deleteTemporalData({
          ...context,
          data: { fileId: timeline.id },
        });
      }
    }

    await callGQL<responses.deleteStructuredData, ReqPayload, Data>(
      context,
      headers,
      queries.deleteStructuredData,
      {
        sdoId: id,
        schemaId,
      }
    );
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }

  return context;
};
export default deleteMatchGroup;
