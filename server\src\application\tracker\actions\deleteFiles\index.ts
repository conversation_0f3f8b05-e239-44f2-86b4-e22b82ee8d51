import { Context } from '../../../types';
import { responses } from '@tracker/graphQL';
import { ActionError } from '@common/errors';
import searchTracklets from '../searchTracklets';
import deleteTracklets from '../deleteTracklets';
import deleteTemporalData from '../deleteTemporalData';

const deleteFiles = async <
  ReqPayload,
  Data extends Partial<responses.searchFiles> = object,
>(
  context: Context<ReqPayload, Data>
): Promise<Context<ReqPayload, Data>> => {
  const { data, log } = context;

  const { searchFiles } = data;
  if (!searchFiles) {
    throw new ActionError('No files found');
  }

  for (const file of searchFiles.searchResults) {
    const new_cxt = await searchTracklets({ ...context, data: { file } });
    if (
      'fileSearch' in new_cxt.data &&
      new_cxt.data.fileSearch &&
      new_cxt.data.file
    ) {
      await deleteTracklets({
        ...new_cxt,
        // TODO: Understand why this is necessary
        data: new_cxt.data,
      });
    }
    try {
      await deleteTemporalData({ ...context, data: { fileId: file.id } });
    } catch (e) {
      log.error(e);
    }
  }

  return context;
};
export default deleteFiles;
