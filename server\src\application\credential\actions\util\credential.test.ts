import { consoleLogger } from '../../../../logger';
import { getCredential } from './credential';
import {
  generateAzureReadOnlyCredential,
  generateAzureWritableCredential,
} from './azure';
import apiConfig from '../../../../../apiConfig.json';
import { UnauthorizedError } from '../../../common/errors';
import GQLApi from '../../../../util/api/graphQL';
import createGQLApiWithoutToken from '../../../../gql';
import {
  generateS3ReadOnlyCredential,
  generateS3WritableCredential,
} from './aws';

jest.mock('./azure');
jest.mock('./aws');
const mockVerifyJWT = jest.fn().mockImplementation((jwtToken: string) => {
  if (jwtToken === 'jwtToken') {
    return {
      data: {
        verifyJWT: {
          jwtToken: 'jwtToken',
          payload: {
            contentOrganizationId: 1,
            engineId: apiConfig.trackerEngineId,
          },
        },
      },
    };
  } else {
    return {
      data: {
        verifyJWT: null,
      },
      errors: ['jwtToken validation failed'],
    };
  }
});
const mockValidateSessionToken = jest
  .fn()
  .mockImplementation((token: string) => {
    if (token === 'sessionToken') {
      return {
        data: {
          validateToken: {
            token: 'sessionToken',
          },
        },
      };
    } else {
      return {
        data: {
          validateToken: null,
        },
        errors: ['token validation failed'],
      };
    }
  });
jest.mock('../../../../util/api/graphQL', () =>
  jest.fn().mockImplementation(() => ({
    verifyJWT: mockVerifyJWT,
    validateToken: mockValidateSessionToken,
  }))
);

describe('getCredential', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('create azure blob writable credential for tracker engine', async () => {
    const token = 'jwtToken';
    const logger = consoleLogger;
    const mockGql = new GQLApi('endpoint', token, 'veritoneAppId');
    const got = await getCredential({
      gql: mockGql,
      token,
      log: logger,
      cloud: 'azure',
    });
    expect(mockVerifyJWT).toHaveBeenCalledTimes(1);
    expect(mockValidateSessionToken).toHaveBeenCalledTimes(0);
    expect(generateAzureWritableCredential).toHaveBeenCalledTimes(1);
    expect(generateAzureReadOnlyCredential).toHaveBeenCalledTimes(0);
    expect(generateS3WritableCredential).toHaveBeenCalledTimes(0);
    expect(generateS3ReadOnlyCredential).toHaveBeenCalledTimes(0);
    expect(got).not.toBeNull();
  });

  it('create azure blob readonly credential for client providing valid token', async () => {
    const token = 'sessionToken';
    const logger = consoleLogger;
    const mockGql = new GQLApi('endpoint', token, 'veritoneAppId');
    const got = await getCredential({
      gql: mockGql,
      token,
      log: logger,
      cloud: 'azure',
    });
    expect(mockVerifyJWT).toHaveBeenCalledTimes(1);
    expect(mockValidateSessionToken).toHaveBeenCalledTimes(1);
    expect(generateAzureWritableCredential).toHaveBeenCalledTimes(0);
    expect(generateAzureReadOnlyCredential).toHaveBeenCalledTimes(1);
    expect(got).not.toBeNull();
  });

  it('create aws s3 writable credential for tracker engine', async () => {
    const token = 'jwtToken';
    const logger = consoleLogger;
    const mockGql = new GQLApi('endpoint', token, 'veritoneAppId');
    const got = await getCredential({
      gql: mockGql,
      token,
      log: logger,
      cloud: 'aws',
    });
    expect(mockVerifyJWT).toHaveBeenCalledTimes(1);
    expect(mockValidateSessionToken).toHaveBeenCalledTimes(0);
    expect(generateS3WritableCredential).toHaveBeenCalledTimes(1);
    expect(generateS3ReadOnlyCredential).toHaveBeenCalledTimes(0);
    expect(generateAzureWritableCredential).toHaveBeenCalledTimes(0);
    expect(generateAzureReadOnlyCredential).toHaveBeenCalledTimes(0);
    expect(got).not.toBeNull();
  });

  it('create aws s3 readonly credential for tracker engine', async () => {
    const token = 'sessionToken';
    const logger = consoleLogger;
    const mockGql = new GQLApi('endpoint', token, 'veritoneAppId');
    const got = await getCredential({
      gql: mockGql,
      token,
      log: logger,
      cloud: 'aws',
    });
    expect(mockVerifyJWT).toHaveBeenCalledTimes(1);
    expect(mockValidateSessionToken).toHaveBeenCalledTimes(1);
    expect(generateS3WritableCredential).toHaveBeenCalledTimes(0);
    expect(generateS3ReadOnlyCredential).toHaveBeenCalledTimes(1);
    expect(generateAzureWritableCredential).toHaveBeenCalledTimes(0);
    expect(generateAzureReadOnlyCredential).toHaveBeenCalledTimes(0);
    expect(got).not.toBeNull();
  });

  it('throw error for invalid token', async () => {
    const token = 'invalidToken';
    const logger = consoleLogger;
    const mockGql = new GQLApi('endpoint', token, 'veritoneAppId');
    async function getCredentialInvalideToken() {
      await getCredential({
        gql: mockGql,
        token,
        log: logger,
        cloud: 'azure',
      });
    }
    expect(getCredentialInvalideToken).rejects.toThrow(UnauthorizedError);
    expect(mockVerifyJWT).toHaveBeenCalledTimes(1);
    expect(mockValidateSessionToken).toHaveBeenCalledTimes(0);
    expect(generateAzureWritableCredential).toHaveBeenCalledTimes(0);
    expect(generateAzureReadOnlyCredential).toHaveBeenCalledTimes(0);
  });

  xit('getCredential', async () => {
    const token = '1234-ABCD';
    const logger = consoleLogger;
    const gql = createGQLApiWithoutToken();
    gql.setToken(token);
    const got = await getCredential({
      gql,
      token,
      log: logger,
      cloud: 'azure',
    });
    const url = `${got.storageUrl}/thumbnail/chachapoyas4.jpeg?${got.credential}`;
    console.log('url====>', url);
    expect(got).not.toBeNull();
  });
});
