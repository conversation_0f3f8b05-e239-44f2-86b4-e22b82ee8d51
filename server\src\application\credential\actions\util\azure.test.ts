import { generateBlobSAS } from './azure';

describe('generateBlobSAS', () => {
  // it is a driver program to troubleshoot blob api call
  // it is not a unit test and disable for this purpose
  // to troubleshoot blob api, enable this test (remove x) and provide account key
  it('generateBlobSAS', () => {
    const account = 'vtstorcoredev';
    const key =
      'BvvBaAr06T9KASQGYaWHi0Q0PUS2+AABUZuwKknBqaxLkX2SAkQd9GFyt0cYYeAjdfNAerFQ//pf+AStZLgV/Q==';
    const containerName = 'tracker2';
    const got = generateBlobSAS({
      account,
      key,
      containerName,
      writable: true,
    });
    const url = `https://vtstorcoredev.blob.core.usgovcloudapi.net/${containerName}/1/**********/reference-tracketlet-id-mock-1_best.jpeg?${got}`;
    console.log('====>', url);
    expect(got).not.toBeNull();
  });
});
