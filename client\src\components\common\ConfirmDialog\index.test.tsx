import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import ConfirmDialog from './index';
import { I18nProvider, LOCALES } from '@i18n';

test('renders dialog and responds to user actions', () => {
  const handleClose = jest.fn();
  const handleConfirm = jest.fn();

  render(
    <I18nProvider locale={LOCALES.ENGLISH}>
      <ConfirmDialog
        open={true}
        title="Test Dialog"
        content="Test Content"
        confirmText="Confirm"
        cancelText="Cancel"
        onConfirm={handleConfirm}
        onClose={handleClose}
      />
    </I18nProvider>
  );

  // Check that the dialog is rendered with the correct content
  expect(screen.getByTestId('confirm-dialog-title')).toHaveTextContent('Test Dialog');
  expect(screen.getByTestId('confirm-dialog-description')).toHaveTextContent('Test Content');

  // Simulate user clicking the confirm button
  fireEvent.click(screen.getByText('Confirm'));
  expect(handleConfirm).toHaveBeenCalled();

  // Simulate user clicking the cancel button
  fireEvent.click(screen.getByText('Cancel'));
  expect(handleClose).toHaveBeenCalled();
});

test('renders input to enter event name and disables delete button on delete modal', () => {
  const handleClose = jest.fn();
  const handleConfirm = jest.fn();
  const eventName = "event name";
  const failEventName = "fail event name";

  render(
    <I18nProvider locale={LOCALES.ENGLISH}>
      <ConfirmDialog
        open={true}
        title="Test Dialog"
        content="Test Content"
        name={eventName}
        confirmText="Confirm"
        cancelText="Cancel"
        onConfirm={handleConfirm}
        onClose={handleClose}
        reEnterName
        type="file"
      />
    </I18nProvider>
  );

  const input = screen.getByRole('textbox');

  // Check that the dialog is rendered with the correct content
  expect(screen.getByTestId('confirm-dialog-title')).toHaveTextContent('Test Dialog');
  expect(screen.getByTestId('confirm-dialog-description')).toHaveTextContent('Test Content');
  expect(screen.getByTestId('confirm-dialog-delete-input')).toBeInTheDocument();
  expect(screen.getByText('Confirm')).toBeDisabled();

  // Simulate user entering the incorrect event name
  fireEvent.change(input, { target: { value: failEventName } });
  expect(screen.getByText('Confirm')).toBeDisabled();

  // Simulate user entering the correct event name
  fireEvent.change(input, { target: { value: eventName } });
  expect(screen.getByText('Confirm')).toBeEnabled();

  // Simulate user clicking the confirm button
  fireEvent.click(screen.getByText('Confirm'));
  expect(handleConfirm).toHaveBeenCalled();

  // Simulate user clicking the cancel button
  fireEvent.click(screen.getByText('Cancel'));
  expect(handleClose).toHaveBeenCalled();
});

test('renders input to edit name and confirm with new name', () => {
  const handleClose = jest.fn();
  const handleConfirm = jest.fn();
  const searchName = 'search name';
  const newSearchName = 'new search name';

  render(
    <I18nProvider locale={LOCALES.ENGLISH}>
      <ConfirmDialog
        open={true}
        title="Rename Dialog"
        name={searchName}
        confirmText="Save"
        cancelText="Cancel"
        onConfirm={handleConfirm}
        onClose={handleClose}
        editName
      />
    </I18nProvider>
  );

  const input = screen.getByRole('textbox');

  // Check that the dialog is rendered with the correct content
  expect(screen.getByTestId('confirm-dialog-title')).toHaveTextContent('Rename Dialog');
  expect(screen.getByTestId('confirm-dialog-description')).toHaveTextContent('');
  expect(screen.getByTestId('confirm-dialog-edit-input')).toBeInTheDocument();
  expect(screen.getByText('Save')).toBeEnabled();

  // Simulate user entering a blank name
  fireEvent.change(input, { target: { value: '' } });
  expect(screen.getByText('Save')).toBeDisabled();

  // Simulate user entering a new name
  fireEvent.change(input, { target: { value: newSearchName } });
  expect(screen.getByText('Save')).toBeEnabled();

  // Simulate user clicking the save button
  fireEvent.click(screen.getByText('Save'));
  expect(handleConfirm).toHaveBeenCalledWith(newSearchName);

  // Simulate user clicking the cancel button
  fireEvent.click(screen.getByText('Cancel'));
  expect(handleClose).toHaveBeenCalled();
});
