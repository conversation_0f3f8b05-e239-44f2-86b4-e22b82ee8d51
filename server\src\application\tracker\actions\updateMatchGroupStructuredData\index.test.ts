import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import updateMatchGroupStructuredData from '../updateMatchGroupStructuredData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';
import * as ResTypes from '../../../../../../types/responses';

const createCxt = () => {
  const cxt: Context<
    object,
    responses.getMatchGroup & ResTypes.UpdateMatchGroupPayloadResponse
  > = {
    data: {
      matchGroup: {
        id: '',
        eventId: '',
        name: '',
        searches: [],
        modifiedDateTime: '',
      },
      matchGroupId: '',
      name: '',
      currentTime: 'modifiedDateTime',
    },
    req: createRequest({
      headers: {
        authorization: 'Bearer validToken',
      },
    }),
    log: consoleLogger(),
    res: createResponse(),
    cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
    queries: {},
    gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
  };

  cxt.cache.set('matchGroupsSchemaId', 'matchGroupsSchemaId');

  return cxt;
};

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createStructuredData: {} })
  ),
}));

describe('Update Match Group Structured Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Updates name', async () => {
    const cxt = createCxt();
    // existing
    cxt.data.matchGroup = {
      id: 'matchGroupId',
      eventId: 'eventId',
      name: 'name',
      searches: [],
      modifiedDateTime: 'modifiedDateTime',
    };

    // Update
    cxt.data.name = 'newName';

    const response = await updateMatchGroupStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: {
          ...cxt.data.matchGroup,
          name: 'newName',
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Updates searches from empty array', async () => {
    const cxt = createCxt();

    // existing
    cxt.data.matchGroup = {
      id: 'matchGroupId',
      eventId: 'eventId',
      name: 'name',
      searches: [],
      selectedTracklets: ['1111'],
      modifiedDateTime: 'modifiedDateTime',
    };

    // Update
    cxt.data.searches = [
      {
        id: 'searchId',
        searchName: 'searchName',
        referenceTrackletId: 'referenceTrackletId',
      },
    ];

    const response = await updateMatchGroupStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: {
          ...cxt.data.matchGroup,
          searches: cxt.data.searches,
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Updates searches with existing data', async () => {
    const cxt = createCxt();

    // existing
    cxt.data.matchGroup = {
      id: 'matchGroupId',
      eventId: 'eventId',
      name: 'name',
      searches: [
        {
          id: 'searchId',
          searchName: 'searchName',
          referenceTrackletId: 'referenceTrackletId',
        },
      ],
      selectedTracklets: ['selectedTracklets'],
      modifiedDateTime: 'modifiedDateTime',
    };

    // Update
    cxt.data.searches = [
      {
        id: 'updated searchId',
        searchName: 'updated searchName',
        referenceTrackletId: 'updated referenceTrackletId',
      },
    ];

    const response = await updateMatchGroupStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: {
          ...cxt.data.matchGroup,
          searches: cxt.data.searches,
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Updates searches with additional selectedTracklets', async () => {
    const cxt = createCxt();

    // existing
    cxt.data.matchGroup = {
      id: 'matchGroupId',
      eventId: 'eventId',
      name: 'name',
      searches: [
        {
          id: 'searchId',
          searchName: 'searchName',
          referenceTrackletId: 'referenceTrackletId',
        },
      ],
      selectedTracklets: ['selectedTracklet-1'],
      modifiedDateTime: 'modifiedDateTime',
    };

    const updatedTracklets = ['selectedTracklet-1', 'selectedTracklet-2'];

    // Update
    cxt.data.selectedTracklets = updatedTracklets;

    const response = await updateMatchGroupStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'matchGroupId',
        schemaId: 'matchGroupsSchemaId',
        data: {
          ...cxt.data.matchGroup,
          selectedTracklets: updatedTracklets,
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is missing required data', async () => {
    const cxt = createCxt();
    // @ts-expect-error TODO: Does this make sense to test with types?
    cxt.data.matchGroup = undefined;

    expect(
      async () => await updateMatchGroupStructuredData(cxt)
    ).rejects.toThrow('Missing match group data');
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if schemaId is missing', async () => {
    const cxt = createCxt();

    cxt.cache.del('matchGroupsSchemaId');
    expect(
      async () => await updateMatchGroupStructuredData(cxt)
    ).rejects.toThrow('schemaId not found');
    expect(callGQL).not.toHaveBeenCalled();
  });
});
