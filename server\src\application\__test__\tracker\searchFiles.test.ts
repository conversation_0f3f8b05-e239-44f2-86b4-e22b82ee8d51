import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            treeObjectId: 'treeObjectId',
          },
        });
      }
      if (query.includes(queries.searchMedia)) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
              totalResults: {
                value: 0,
                relation: 'eq',
              },
              limit: 10,
              from: 0,
              to: 10,
              timestamp: 1,
            },
          },
        });
      }
    }
  ),
}));

describe('get files', () => {
  it('get files w/o query', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/files?pageSize=50&currentPage=1')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.searchMedia,
      {
        search: {
          index: ['mine'],
          type: 'file',
          select: ['veritone-file'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
            ],
          },
        },
      }
    );
  });

  it('get files w/ query', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get(
        '/api/v1/files?pageSize=50&currentPage=1&eventId=folderId&sortBy=recordingId&sortDirection=desc'
      )
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.searchMedia,
      {
        search: {
          index: ['mine'],
          type: 'file',
          limit: 50,
          offset: 0,
          select: ['veritone-file'],
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
              {
                field: 'parentTreeObjectIds',
                operator: 'terms',
                values: ['treeObjectId'],
              },
            ],
          },
          sort: [
            {
              field: 'recordingId',
              order: 'desc',
            },
          ],
        },
      }
    );
  });
});
