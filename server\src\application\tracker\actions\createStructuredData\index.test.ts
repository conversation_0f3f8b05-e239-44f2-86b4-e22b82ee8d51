import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import createStructuredData from '../createStructuredData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';

let cxt: Context<object, object>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({ createStructuredData: {} })
  ),
}));

describe('Create Structured Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        name: 'name',
        description: 'description',
        userId: 'userId',
        firstName: 'firstName',
        lastName: 'lastName',
        eventStartDate: 'eventStartDate',
        eventEndDate: 'eventEndDate',
        createFolder: { id: 'folderId' },
        currentTime: 'currentTime',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
    cxt.cache.set('eventsSchemaId', 'eventsSchemaId');
  });

  it('Successfully creates structured data', async () => {
    const response = await createStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: '',
        schemaId: 'eventsSchemaId',
        data: {
          id: 'folderId',
          name: 'name',
          description: 'description',
          createdBy: 'userId',
          createdByName: 'firstName lastName',
          eventStartDate: 'eventStartDate',
          eventEndDate: 'eventEndDate',
          tags: [],
          createdDateTime: 'currentTime',
          modifiedDateTime: 'currentTime',
          // trackerEngineId: 'f6634718-c2b7-40f5-9c2c-a606420104ac',
          trackerEngineId: expect.stringMatching(
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/
          ),
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is missing required data', async () => {
    cxt.data = {};

    expect(async () => await createStructuredData(cxt)).rejects.toThrowError(
      'Missing required data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
