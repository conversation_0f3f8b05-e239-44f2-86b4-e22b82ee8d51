import { locationSlice, LocationState } from '@store/modules/location/slice';
import { configureAppStore } from '@store/store';
import { omit } from 'lodash';

const initialStateForMock = {
  location: {
    pathname: "",
    search: "",
    hash: "",
    key: "",
    history: undefined,
    payload: undefined,
  }
};

const mockedLocation = {
  location: {
    pathname: "/test",
    search: "?query=test",
    hash: "#hash",
    key: "key",
    history: {
      hash: "#mockHash",
      key: "mockKey",
      pathname: "/mockPathname",
      search: "?mockSearch",
      payload: { mockData: "mockValue" },
    },
    payload: { data: "test" },
  }
};

describe('locationSlice', () => {
  it('should handle onRouteChanged', () => {

    // create a store with the initial state
    const mockedStore = configureAppStore(initialStateForMock);

    // payload for onRouteChanged
    const payload: LocationState = mockedLocation.location;

    // dispatch the onRouteChanged action
    mockedStore.dispatch(locationSlice.actions.onRouteChanged(payload));

    // expect location to be in the state
    const { location } = mockedStore.getState();
    expect(location).toEqual(payload);
  });
});

describe('locationSlice selectors', () => {

  // create a store with the initial state
  const state: { location: LocationState } = {
    location: mockedLocation.location,
  };

  it('selectLocation returns the location', () => {
    expect(locationSlice.selectors.selectLocation(state)).toEqual(state.location);
  });

  it('selectCurrentLocation returns the current location without history', () => {
    // omit history from the location object
    const { ...location } = mockedLocation.location;
    const locationWithoutHistory = omit(location, 'history');

    // expect the location without history to be returned
    expect(locationSlice.selectors.selectCurrentLocation(state)).toEqual(locationWithoutHistory);
  });

  it('selectPathname returns the pathname', () => {
    expect(locationSlice.selectors.selectPathname(state)).toEqual(mockedLocation.location.pathname);
  });

  it('selectPayload returns the payload', () => {
    expect(locationSlice.selectors.selectPayload(state)).toEqual(mockedLocation.location.payload);
  });

  it('selectHistory returns the history', () => {
    expect(locationSlice.selectors.selectHistory(state)).toEqual(mockedLocation.location.history);
  });
});
