import {
  act,
  render,
  screen,
  waitFor,
  fireEvent,
  within,
} from '@testing-library/react';
import MatchGroup from './MatchGroup';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureAppStore } from '@store/store';
import axios from 'axios';
import {
  GetFileResponse,
  GetMatchGroupResponse,
  GetMatchGroupSelectedTrackletsResponse,
  UpdateMatchGroupResponse,
  GetMatchGroupSearchResultsResponse,
  GetMatchGroupsResponse,
  CreateMatchGroupResponse,
} from '@shared-types/responses';
import { ThemeProvider } from '@emotion/react';
import { createMaterialTheme } from '@theme/material';
import { JobStatus, Search } from '@shared-types/tracker';
import {
  getAttributeCount,
  TrackletAttributes,
} from '../../../test/testConstants';
import { getAttributeValue } from '@utility/getAttributeValue';
import { I18nProvider, LOCALES } from '@i18n';
import { last } from 'lodash';
import {
  AlertLevel,
  createSnackNotification,
} from '@components/common/Snackbar/Snackbar';
import { setThumbnails } from '@store/modules/searchResults/slice';
import { setThumbnails as setThumbnailsTimeline } from '@store/modules/timelineEditor/slice';
import { setThumbnails as setThumbnailsMatchGroup } from '@store/modules/matchGroup/slice';

jest.mock('axios');

jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  Tabs: (props: {
    'data-testid': string;
    value: string;
    onChange: (_: void, value: string) => void;
  }) => (
    <div data-testid={props?.['data-testid']}>
      {['search', 'detail', 'timeline'].map((tab) => (
        <button
          key={tab}
          data-testid={`match-group-tab-${tab}`}
          onClick={() => props.onChange(undefined, tab)}
        >
          {tab}
        </button>
      ))}
    </div>
  ),
}));

jest.mock('@components/common/Snackbar/Snackbar', () => ({
  ...jest.requireActual('@components/common/Snackbar/Snackbar'),
  createSnackNotification: jest.fn(),
}));

const eventId = '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5';
const matchGroupId = 'a85f9b41-f550-4754-8871-518266dbae49';
const groupId = 'fed5a705-98c8-44ef-8097-2067f552657f';

jest.mock('uuid', () => ({
  v4: jest.fn(() => groupId),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  // useLocation: jest.fn(() => ({ pathname: 'event/1b9c7f1a-f759-4065-94c3-4dd48868fb06/match-group/44573a2a-fc44-45bb-ba03-4d6f25762f1e/potential-match-search/930aa3b3-5150-4d56-90aa-6d15d3a82761', search: '' })),
  useParams: () => ({
    eventId,
    matchGroupId,
    searchId: '16b5f647-7358-4812-87f4-93e0468f19a6',
  }),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockNavigate = jest.fn();

const mockGetMatchGroupSelectedTrackletsResponse: GetMatchGroupSelectedTrackletsResponse =
  {
    results: [
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'file-1.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
      {
        orgId: '1',
        trackletId: '22',
        fileId: '2',
        fileName: 'file-2.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.8,
      },
      {
        orgId: '1',
        trackletId: '33',
        fileId: '3',
        fileName: 'fileName 3',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.6,
      },
    ],
    matchGroupId,
    matchGroupName: 'John Doe',
    eventId,
  };

const mockGetMatchGroupSelectedTrackletsResponseWithDuplicates: GetMatchGroupSelectedTrackletsResponse =
  {
    results: [
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'fileName 1',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
      {
        orgId: '1',
        trackletId: '22',
        fileId: '2',
        fileName: 'fileName 2',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.8,
      },
      {
        orgId: '1',
        trackletId: '33',
        fileId: '3',
        fileName: 'fileName 3',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.6,
      },
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'fileName 1',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
    ],
    matchGroupId,
    matchGroupName: 'John Doe',
    eventId,
  };

const mockGetMatchGroupsResponse: GetMatchGroupResponse = {
  matchGroup: {
    id: matchGroupId,
    name: 'test match group',
    eventId: eventId,
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Potential Match Search 1',
        referenceTrackletId: 'reference-tracketlet-id-mock-1',
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Potential Match Search 2',
        referenceTrackletId: 'reference-tracketlet-id-mock-2',
      },
    ],
    modifiedDateTime: '2024-05-16T22:50:21.111Z',
    selectedTracklets: ['11', '22', '33'],
  },
};

const mockPatchGetMatchGroupsResponse: CreateMatchGroupResponse = {
  matchGroup: {
    eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
    id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
    modifiedDateTime: '2024-08-20T22:50:21.111Z',
    name: 'test match group',
    searches: [
      {
        id: '16b5f647-7358-4812-87f4-93e0468f19a6',
        searchName: 'Potential Match Search 1',
        referenceTrackletId: 'reference-tracketlet-id-mock-1',
      },
      {
        id: '12345678-5150-4d56-90aa-6d15d3a82761',
        searchName: 'Potential Match Search 2',
        referenceTrackletId: 'reference-tracketlet-id-mock-2',
      },
      {
        referenceTrackletId: '11',
        id: 'f2cf67a9-36d9-4122-bc17-d48c5c12799b',
        searchName: 'Potential Match Search 3',
      },
    ],
    selectedTracklets: [],
  },
};
const mockGetFileResponse: GetFileResponse = {
  file: {
    id: '1',
    createdByName: 'Test User',
    fileName: 'file-1.mp4',
    status: 'processed',
    length: 1000,
    uploadDate: '2021-01-01T00:00:00.000Z',
    location: '',
    fileType: 'mp4',
    fileSize: 2048,
    eventId: 'eventId',
    eventName: 'eventName',
    thumbnailUrl: '',
    primaryAsset: {
      signedUri: 'signedUri',
    },
    streams: [
      {
        uri: 'uri',
        protocol: 'protocol',
      },
    ],
    frameRate: 25,
  },
};

const mockGetFileResponse2: GetFileResponse = {
  file: {
    id: '2',
    createdByName: 'Test User',
    fileName: 'file-2.mp4',
    status: 'processed',
    length: 1000,
    uploadDate: '2021-01-02T00:00:00.000Z',
    location: '',
    fileType: 'mp4',
    fileSize: 2048,
    eventId: 'eventId',
    eventName: 'eventName',
    thumbnailUrl: '',
    primaryAsset: {
      signedUri: 'signedUri',
    },
    streams: [
      {
        uri: 'uri',
        protocol: 'protocol',
      },
    ],
    frameRate: 30,
  },
};

const mockSaveTimeLineResponse: UpdateMatchGroupResponse = {
  matchGroup: {
    id: '0677087b-831b-407d-bce0-f143978898e9',
    name: 'GreenShirtWoman',
    eventId: '2fc73b9e-f5f8-4dd4-8483-1c4971b4041e',
    searches: [
      {
        id: 'd7510dd8-f57b-4bd0-9ee0-b00a4c81fa0e',
        searchName: 'Potential Match Search 0',
        searchTime: '2024-08-09T15:12:13.815Z',
        referenceTrackletId: '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
      },
    ],
    createdDateTime: '2024-08-09T15:09:32.951Z',
    timelineProject: {
      groups: [
        {
          id: 'da6d1d5a-dc1e-480b-b28c-2b029e908d6e',
          name: 'Default Group',
          tracklets: [
            {
              orgId: '1',
              type: 'person',
              fileId: '3220025384',
              fileName: 'Walking Tour 1.mp4',
              attributes: {
                body: [{ key: '1', label: 'Body', value: 'Average' }],
                face: [{ key: '2', label: 'Face', value: 'Left' }],
                hair: [
                  { key: '3', label: 'Hair Color', value: 'colorBlack' },
                  { key: '4', label: 'Hair Type', value: 'typeLong' },
                ],
                lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                upper: [
                  { key: '6', label: 'Upper Color', value: 'colorGreen' },
                  { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                ],
                gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                accessories: [
                  { key: '9', label: 'Accessories', value: 'BagAny' },
                ],
              },
              confidence: 2.000019,
              stopTimeMs: 3385666,
              trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
              startTimeMs: 3384233,
              thumbnailUrls: {
                best: '',
              },
            },
          ],
        },
      ],
    },
    modifiedDateTime: '2024-08-19T16:51:33.332Z',
    selectedTracklets: [
      '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
      '6b123adc-1fe7-4113-b2b2-8cac16962e08',
      '836f05f4-22dd-477c-9afa-85ebc4658dea',
    ],
    generatedTimelines: [
      {
        id: '3240004533',
        name: 'GreenShirtWoman-timeline-1.mp4',
        tdoId: '3240004533',
        status: 'complete' as JobStatus,
        timeline: {
          groups: [
            {
              id: 'd13ae78d-990e-4a63-9189-d8a30ce01d4c',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '3220025384',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 1385300,
                  trackletId: '2b9dd13e-05ce-4af1-a2a4-366f7b97d840',
                  startTimeMs: 1384100,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
            {
              id: '3896f6fa-adc2-43eb-99a6-9cd095e94447',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '3220025384',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 3385666,
                  trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
                  startTimeMs: 3384233,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
          ],
        },
        resolution: '1280x720',
        downloadUrl: '',
        outputFormat: 'video/mp4',
        createdUserId: '5a638922-bc0a-46ca-b43d-b12363888221',
        videoLengthMs: 2681.641,
        videoSizeBytes: 1232530,
        createdDateTime: '2024-08-19T15:52:13.021Z',
      },
      {
        id: '3240004534',
        name: 'GreenShirtWoman-timelines-2.mp4',
        tdoId: '3240004534',
        status: 'complete' as JobStatus,
        timeline: {
          groups: [
            {
              id: 'f608f483-f061-48fb-8f92-c3c17ed65a56',
              name: 'Default Group',
              tracklets: [
                {
                  orgId: '1',
                  type: 'person',
                  fileId: '3220025384',
                  fileName: 'Walking Tour 1.mp4',
                  attributes: {
                    body: [{ key: '1', label: 'Body', value: 'Average' }],
                    face: [{ key: '2', label: 'Face', value: 'Left' }],
                    hair: [
                      { key: '3', label: 'Hair Color', value: 'colorBlack' },
                      { key: '4', label: 'Hair Type', value: 'typeLong' },
                    ],
                    lower: [{ key: '5', label: 'Lower', value: 'eTrousers' }],
                    upper: [
                      { key: '6', label: 'Upper Color', value: 'colorGreen' },
                      { key: '7', label: 'Sleeve Type', value: 'sleeveLong' },
                    ],
                    gender: [{ key: '8', label: 'Gender', value: 'Female' }],
                    accessories: [
                      { key: '9', label: 'Accessories', value: 'BagAny' },
                    ],
                  },
                  confidence: 2.000019,
                  stopTimeMs: 3385666,
                  trackletId: '6b123adc-1fe7-4113-b2b2-8cac16962e08',
                  startTimeMs: 3384233,
                  thumbnailUrls: {
                    best: '',
                  },
                },
              ],
            },
          ],
        },
        resolution: '1280x720',
        downloadUrl: '',
        outputFormat: 'video/mp4',
        createdUserId: '5a638922-bc0a-46ca-b43d-b12363888221',
        videoLengthMs: 1433.3329999999999,
        videoSizeBytes: 675705,
        createdDateTime: '2024-08-19T15:53:01.189Z',
      },
    ],
  },
};
const mockGetMatchGroupSearchResultsResponse: GetMatchGroupSearchResultsResponse =
  {
    results: [
      {
        orgId: '1',
        trackletId: '11',
        fileId: '1',
        fileName: 'file-1.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.9,
      },
      {
        orgId: '1',
        trackletId: '22',
        fileId: '2',
        fileName: 'file-2.mp4',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.8,
      },
      {
        orgId: '1',
        trackletId: '33',
        fileId: '3',
        fileName: 'fileName 3',
        startTimeMs: 10000,
        stopTimeMs: 20000,
        type: 'person',
        attributes: TrackletAttributes,
        thumbnailUrls: {
          best: '/tracklet-example-best.png',
        },
        confidence: 0.6,
      },
    ],
    type: 'person',
    referenceTrackletId: '99999',
    searchId: '16b5f647-7358-4812-87f4-93e0468f19a6',
    searchName: 'Potential Search 1',
    matchGroupId: 'a85f9b41-f550-4754-8871-518266dbae49',
    matchGroupName: 'John Doe',
    eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
    currentPage: 1,
    pageSize: 50,
    totalCount: 100,
    totalPages: 2,
  };

const mockGetMatchGroupsResponseWithEventId: GetMatchGroupsResponse = {
  eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
  results: [
    {
      id: '44573a2a-fc44-45bb-ba03-4d6f25762f1e',
      name: 'test match group',
      eventId: '83b9fc86-df85-46b6-99fe-f4d04ef2bdd5',
      searches: [
        {
          id: '16b5f647-7358-4812-87f4-93e0468f19a6',
          searchName: 'Potential Match Search 1',
          referenceTrackletId: 'reference-tracketlet-id-mock-1',
        },
        {
          id: '12345678-5150-4d56-90aa-6d15d3a82761',
          searchName: 'Potential Match Search 2',
          referenceTrackletId: 'reference-tracketlet-id-mock-2',
        },
      ],
      modifiedDateTime: '2024-05-16T22:50:21.111Z',
      selectedTracklets: [],
    },
  ],
  currentPage: 1,
  pageSize: 50,
  totalCount: 100,
  totalPages: 2,
  sortType: 'name',
  sortDirection: 'asc',
};

async function selectCheckboxById(dataTestId: string) {
  await waitFor(() => {
    expect(screen.getByTestId(dataTestId)).toBeInTheDocument();
  });

  const checkbox = screen.getByTestId(dataTestId);
  await act(() => {
    checkbox.click();
  });
}

describe('Match Group page', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  function verifyAttributeKeysAndValues() {
    Object.keys(TrackletAttributes).map((attribute, _i) => {
      const attributeKeyCount = screen.getByTestId(
        `detected-attributes__chips-${attribute}`
      ).childNodes.length;
      expect(
        TrackletAttributes[attribute as keyof typeof TrackletAttributes].length
      ).toEqual(attributeKeyCount);
      TrackletAttributes?.[attribute as keyof typeof TrackletAttributes]?.map(
        (attributeRecord, _j) => {
          const chipValue = getAttributeValue(attributeRecord.value);
          expect(
            screen.getByTestId(
              `detected-attributes__chip-label-${attribute}-${attributeRecord.value}`
            )
          ).toHaveTextContent(chipValue);
        }
      );
    });
  }

  test('Match Group page loads tracklets', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsMatchGroup({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });

    // Filter
    expect(screen.getByTestId('match-group-filter-select')).toBeInTheDocument();
  });

  test('SearchResult render best thumbnail', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );
    expect(screen.getByTestId('match-group')).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
        expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
    expect(screen.getByTestId('Tracklet-0-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-1-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
    expect(screen.getByTestId('Tracklet-2-img')).toHaveAttribute(
      'src',
      '/tracklet-example-best.png'
    );
  });

  test('Match Group page shows no tracklets detected result', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: { ...mockGetMatchGroupSelectedTrackletsResponse, results: [] },
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });
    // Show tracklets not found msg
    await waitFor(() => {
      expect(
        screen.getByTestId('match-group-no-tracklets-found')
      ).toBeInTheDocument();
    });

    // Filter
    const fileSelect = screen.getByTestId('match-group-filter-select');
    expect(fileSelect).toBeInTheDocument();
    expect(fileSelect).toHaveClass('Mui-disabled');
  });

  test('Match Group page shows skeleton but does not show no tracklets detected msg', async () => {
    jest.useFakeTimers();

    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // Mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return new Promise((resolve) => setTimeout(resolve, 3000)); // 3 seconds delay
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Fast-forward until all timers have been executed
    await act(async () => {
      jest.advanceTimersByTime(3000); // Fast-forward 3 seconds
    });

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    // Show tracklets not found msg
    await waitFor(() => {
      expect(
        screen.getByTestId('matched-detection-tab-tracklet-skeleton-0')
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId('match-group-no-tracklets-found')
      ).not.toBeInTheDocument();
    });

    jest.useRealTimers();
  });

  test('Selecting a tracklet shows attributes', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsMatchGroup({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // Click a tracklet
    await act(() => {
      screen.getByTestId('Tracklet-0').click();
    });

    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );
    });

    // Attributes are displaying
    verifyAttributeKeysAndValues();
  });

  test('Selecting tracklets then clearing the selections', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsMatchGroup({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    await waitFor(async () => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // select all tracklets
    if (mockGetMatchGroupSelectedTrackletsResponse.results) {
      for (const [
        index,
        _result,
      ] of mockGetMatchGroupSelectedTrackletsResponse.results.entries()) {
        await waitFor(() => {
          expect(
            screen.getByTestId(`Tracklet-${index}-checkbox`)
          ).toBeInTheDocument();
        });

        // Click a tracklet
        const checkbox = screen.getByTestId(`Tracklet-${index}-checkbox`);
        await act(async () => {
          checkbox.click();
        });
      }
    }

    await act(async () => {
      screen.getByTestId('Tracklet-0').click();
    });

    // Wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );
    });

    // Wait for all tracklets to be selected
    await waitFor(() => {
      expect(
        screen.queryByTestId(
          'tracklet-selection-header-selected-tracklets-label'
        )
      ).toHaveTextContent(
        `${
          (mockGetMatchGroupSelectedTrackletsResponse?.results ?? []).length
        } selected`
      );
    });

    // Attributes are displaying
    verifyAttributeKeysAndValues();

    // Clear all selections
    await act(async () => {
      screen.getByTestId('match-group-clear-selections').click();
    });

    // Wait for all tracklets to be unselected
    await waitFor(() => {
      expect(
        screen.queryByTestId(
          'tracklet-selection-header-selected-tracklets-label'
        )
      ).not.toBeVisible();
    });

    // Deselect the file and expect no attributes
    // Wait for all attributes to be removed
    // await waitFor(() => {
    //   expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(0);
    // });
  });

  test('Selecting tracklets then deleting the selections', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsMatchGroup({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/match-groups') && method === 'delete') {
        return Promise.resolve({
          data: {
            message: 'Success',
            matchSelectedTracklets:
              mockGetMatchGroupSelectedTrackletsResponse?.results?.slice(1),
          },
        });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });
    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );
    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();

    // expect(screen.getByTestId('match-group-tabs')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    // select all tracklets
    if (mockGetMatchGroupSelectedTrackletsResponse.results) {
      for (const [
        index,
        _result,
      ] of mockGetMatchGroupSelectedTrackletsResponse.results.entries()) {
        await waitFor(() => {
          expect(
            screen.getByTestId(`Tracklet-${index}-checkbox`)
          ).toBeInTheDocument();
        });

        // Click a tracklet
        const checkbox = screen.getByTestId(`Tracklet-${index}-checkbox`);
        await act(async () => {
          checkbox.click();
        });
      }
    }

    // Should be 0, no file was selected
    // await waitFor(() => {
    //   expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(0);
    // });

    await act(async () => {
      screen.getByTestId('Tracklet-0').click();
    });

    // Wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );
    });

    // Wait for all tracklets to be selected
    await waitFor(() => {
      expect(
        screen.queryByTestId(
          'tracklet-selection-header-selected-tracklets-label'
        )
      ).toHaveTextContent(
        `${
          (mockGetMatchGroupSelectedTrackletsResponse?.results ?? []).length
        } selected`
      );
    });

    // Select Delete all selections
    await act(async () => {
      screen.getByTestId('match-group-delete-selections').click();
    });

    // Wait for confirm dialog to appear
    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument();
    });

    // Cancel selected
    fireEvent.click(screen.getByTestId('confirm-dialog-cancel-action'));

    // Attributes should still be displaying
    verifyAttributeKeysAndValues();

    // Delete all selections
    await act(async () => {
      screen.getByTestId('match-group-delete-selections').click();
    });

    // Wait for confirm dialog to appear
    await waitFor(() => {
      expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument();
    });

    // Delete selected
    fireEvent.click(screen.getByTestId('confirm-dialog-confirm-action'));

    // Wait for all tracklets to be unselected
    await waitFor(() => {
      expect(
        screen.queryByTestId(
          'tracklet-selection-header-selected-tracklets-label'
        )
      ).not.toBeVisible();
    });

    // Deselect the file and expect no attributes
    // Wait for all attributes to be removed
    // await waitFor(() => {
    //   expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(0);
    // });
  });

  test('Selecting tracklets and loads the video srcs', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsMatchGroup({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file/1') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
      if (url?.includes('/file/2') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse2 });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    expect(screen.getByTestId('match-group')).toBeInTheDocument();

    act(() => {
      screen.getByTestId('match-group-tab-detail').click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
    });

    // click first tracklet
    await act(() => {
      screen.getByTestId('Tracklet-0').click();
    });

    // wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );

      // check if selected tracklet's video src is loaded
      expect(
        mockedStore.getState().matchGroup.userSelectedTrackletFile.file.fileName
      ).toEqual(
        mockedStore.getState().matchGroup.matchSelectedTracklets.results[0]
          .fileName
      );
      expect(
        mockedStore.getState().matchGroup.userSelectedTrackletFile.file.fileName
      ).toEqual('file-1.mp4');
    });

    // click second tracklet
    await act(() => {
      screen.getByTestId('Tracklet-1').click();
    });

    // wait for all attributes to display
    await waitFor(() => {
      expect(screen.queryAllByTestId('detected-attributes__chip')).toHaveLength(
        getAttributeCount(TrackletAttributes)
      );

      // check if selected tracklet's video src is loaded
      expect(
        mockedStore.getState().matchGroup.userSelectedTrackletFile.file.fileName
      ).toEqual(
        mockedStore.getState().matchGroup.matchSelectedTracklets.results[1]
          .fileName
      );
      expect(
        mockedStore.getState().matchGroup.userSelectedTrackletFile.file.fileName
      ).toEqual('file-2.mp4');
    });
  });

  test('save button will save the timeline', async () => {
    const mockedStore = configureAppStore();

    // mock axios request
    mockedAxios.request.mockImplementation(async (params) => {
      const { url, method } = params;
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();

    // Click a potential match search
    act(() => {
      screen.getByTestId('match-group-tab-timeline').click();
    });

    // Timeline renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Click the save button
    await act(() => {
      screen.getByTestId('match-group-save-timeline-button').click();
    });

    // Api called with timeline update
    await waitFor(() => {
      expect(mockedAxios.request).toHaveBeenCalledWith({
        url: 'http://localhost/api/v1/match-groups/a85f9b41-f550-4754-8871-518266dbae49',
        headers: {
          Authorization: 'Bearer null',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        data: {
          timelineProject: {
            groups: [],
          },
        },
        maxRedirects: 0,
        withCredentials: false,
        method: 'patch',
        signal: expect.any(AbortSignal),
      });
    });
  });

  test('generate button will save the timeline', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async (params) => {
      const { url, method } = params;
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/match-groups') && method === 'patch') {
        return Promise.resolve({ data: mockSaveTimeLineResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();

    // Click a potential match search
    act(() => {
      screen.getByTestId('match-group-tab-timeline').click();
    });

    // Timeline renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });

    // Save Timeline
    await act(() => {
      screen.getByTestId('match-group-save-timeline-button').click();
    });

    // Click the save button
    await act(() => {
      screen.getByTestId('match-group-save-generate-timeline-button').click();
    });

    // Render the save timeline dialog
    await waitFor(() => {
      expect(
        screen.getByTestId('timeline-generate-video-dialog')
      ).toBeInTheDocument();
    });

    await act(() => {
      screen
        .getByTestId('timeline-generate-video-dialog-confirm-action')
        .click();
    });

    await waitFor(() => {
      expect(
        screen.queryByTestId('timeline-generate-video-dialog')
      ).not.toBeInTheDocument();
    });

    // Api called with timeline update
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];

      expect(lastCall[0]).toEqual({
        url: 'http://localhost/api/v1/match-groups/a85f9b41-f550-4754-8871-518266dbae49/generate-timeline',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          Authorization: 'Bearer null',
        },
        data: {
          timelineProject: { groups: [] },
          generatedTimelineName: 'GreenShirtWoman-timeline-3.mp4',
        },
        maxRedirects: 0,
        withCredentials: false,
        method: 'patch',
        signal: expect.any(AbortSignal),
      });
    });
  });

  test('renders file dropdown component with uniq filename menuItem and checkbox', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsTimeline({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponseWithDuplicates,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-timeline').click();
    });

    // Person tracklets load
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });

    // Filter
    expect(
      await screen.findByTestId('match-group-filter-select')
    ).toBeInTheDocument();

    // Click on the select dropdown
    const selectElement = within(
      screen.getByTestId('match-group-filter-select')
    ).getByRole('combobox');
    fireEvent.mouseDown(selectElement);

    // Check if the options and checkboxes are rendered
    await waitFor(() => {
      const option1 = screen.getByTestId('match-group-filter-select-item-0');
      const option2 = screen.getByTestId('match-group-filter-select-item-1');
      const option3 = screen.getByTestId('match-group-filter-select-item-2');
      const checkbox1 = screen.getByTestId(
        'match-group-filter-select-item-checkbox-0'
      );
      const checkbox2 = screen.getByTestId(
        'match-group-filter-select-item-checkbox-1'
      );
      const checkbox3 = screen.getByTestId(
        'match-group-filter-select-item-checkbox-2'
      );
      expect(option1).toBeInTheDocument();
      expect(option1).toHaveTextContent('fileName 1');
      expect(checkbox1).toBeInTheDocument();
      expect(option2).toBeInTheDocument();
      expect(option2).toHaveTextContent('fileName 2');
      expect(checkbox2).toBeInTheDocument();
      expect(option3).toBeInTheDocument();
      expect(option3).toHaveTextContent('fileName 3');
      expect(checkbox3).toBeInTheDocument();
      expect(
        screen.queryByTestId('match-group-filter-select-item-3')
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId('match-group-filter-select-item-checkbox-3')
      ).not.toBeInTheDocument();
    });
  });

  test('filter tracklet by file name', async () => {
    const mockedStore = configureAppStore();

    mockedStore.dispatch(
      setThumbnailsTimeline({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-timeline').click();
    });

    // Before filter, this page should contain 3 tracklets
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });

    // Filter
    const selectElement = within(
      screen.getByTestId('match-group-filter-select')
    ).getByRole('combobox');
    fireEvent.mouseDown(selectElement);
    await selectCheckboxById('match-group-filter-select-item-checkbox-0');

    // After filter, this page should contain 1 tracklet
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.queryByTestId('Tracklet-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('Tracklet-2')).not.toBeInTheDocument();
    });

    const clearFileFilterButton = screen.queryByTestId(
      'match-group-clear-file-filter-button'
    );
    expect(clearFileFilterButton).toBeInTheDocument();
    act(() => {
      clearFileFilterButton?.click();
    });

    // After clear filter, this page should contain 3 tracklets
    await waitFor(() => {
      expect(screen.getByTestId('Tracklet-0')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-1')).toBeInTheDocument();
      expect(screen.getByTestId('Tracklet-2')).toBeInTheDocument();
    });
  });

  test('Thumbnails scale', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnailsTimeline({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );

    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/selected-tracklets') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSelectedTrackletsResponseWithDuplicates,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
      if (url?.includes('/file') && method === 'get') {
        return Promise.resolve({ data: mockGetFileResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <I18nProvider locale={LOCALES.ENGLISH}>
          <Router>
            <MatchGroup />
          </Router>
        </I18nProvider>
      </Provider>
    );

    // Page renders
    await waitFor(() => {
      expect(screen.getByTestId('match-group')).toBeInTheDocument();
    });
    act(() => {
      screen.getByTestId('match-group-tab-timeline').click();
    });

    // timeline-editor renders
    await waitFor(() => {
      expect(screen.getByTestId('timeline-editor')).toBeInTheDocument();
    });
    // expect(mockNavigate).toHaveBeenCalledWith(`/event/${eventId}/match-group/${matchGroupId}`);

    const tracklet = await screen.findByTestId('Tracklet-0');
    expect(tracklet).toBeInTheDocument();
    expect(tracklet).toHaveStyle({ width: '113px' });

    const thumbnailScaler = screen.getByTestId('thumbnail-scaler');
    const thumbnailScalerButton = screen.getByTestId('thumbnail-scaler-button');
    expect(thumbnailScaler).toBeInTheDocument();
    expect(thumbnailScalerButton).toBeInTheDocument();

    // Show slider
    fireEvent.click(thumbnailScalerButton);
    const thumbnailScalerSlider = screen.getByTestId('thumbnail-scaler-slider');
    expect(thumbnailScalerSlider).toBeInTheDocument();

    // Increment scale to 200%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: 1 });
    expect(tracklet).toHaveStyle({ width: '226px' });

    // Decrement scale to 10%
    fireEvent.mouseDown(thumbnailScalerSlider, { clientX: -1 });
    expect(tracklet).toHaveStyle({ width: '67.8px' });
  });

  test('Selecting tracklets for Add to button', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('match-group')).toBeInTheDocument();
    act(() => {
      screen.getByTestId('match-group-tab-search').click();
    });
    // Add to button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeDisabled();
    });

    // Tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');
    await selectCheckboxById('Tracklet-1-checkbox');

    // Add to button is not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeEnabled();
    });

    // Click Add to button
    await act(() => {
      screen.getByTestId('file-and-filter-matches__add-to-button').click();
    });

    // Check store for selected tracklets
    await waitFor(() => {
      expect(
        mockedStore.getState().searchResults.selectedTracklet
      ).toHaveLength(2);
    });
  });

  test('Selecting tracklets for Find Matches Popover and closing the popover', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/match-groups') &&
        url?.includes('?eventId') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockGetMatchGroupsResponseWithEventId });
      }
    });
    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });

    // Tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');

    // Find Matches button is not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeEnabled();
    });

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Click close button
    await act(() => {
      screen.getByTestId('find-matches-popover__close').click();
    });

    // Find Matches Popover closes
    await waitFor(() => {
      expect(
        screen.queryByTestId('find-matches-popover')
      ).not.toBeInTheDocument();
    });
  });

  test('Selecting tracklet for Find Matches Popover and increment potential match search name', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/match-groups') &&
        url?.includes('?eventId') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockGetMatchGroupsResponseWithEventId });
      }
      if (url?.includes('/match-groups') && method === 'patch') {
        return Promise.resolve({ data: mockPatchGetMatchGroupsResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();
    // Add to button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });

    // Tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');

    // Find Matches button is not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeEnabled();
    });

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });
    const selectElement = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Check potential match search name
    await waitFor(() => {
      expect(
        last(
          mockedStore.getState().searchResults.matchGroup.data
            .searches as Search[]
        )?.searchName
      ).toEqual('Potential Match Search 2');
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    // Check update matchGroup action
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      expect(lastCall[0].data).toEqual({
        searches: [
          {
            id: '16b5f647-7358-4812-87f4-93e0468f19a6',
            referenceTrackletId: 'reference-tracketlet-id-mock-1',
            searchName: 'Potential Match Search 1',
          },
          {
            id: '12345678-5150-4d56-90aa-6d15d3a82761',
            referenceTrackletId: 'reference-tracketlet-id-mock-2',
            searchName: 'Potential Match Search 2',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '11',
            searchName: 'Potential Match Search 3',
            searchTime: expect.any(String),
          },
        ],
      });
    });

    // Check potential match search name
    await waitFor(() => {
      expect(
        last(
          mockedStore.getState().searchResults.matchGroup.data
            .searches as Search[]
        )?.searchName
      ).toEqual('Potential Match Search 3');
    });

    // Uncheck tracklet and check new tracklet
    await selectCheckboxById('Tracklet-0-checkbox');
    await selectCheckboxById('Tracklet-1-checkbox');

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });
    const selectElement1 = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement1);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    // Check update matchGroup action
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      expect(lastCall[0].data).toEqual({
        searches: [
          {
            id: '16b5f647-7358-4812-87f4-93e0468f19a6',
            referenceTrackletId: 'reference-tracketlet-id-mock-1',
            searchName: 'Potential Match Search 1',
          },
          {
            id: '12345678-5150-4d56-90aa-6d15d3a82761',
            referenceTrackletId: 'reference-tracketlet-id-mock-2',
            searchName: 'Potential Match Search 2',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '11',
            searchName: 'Potential Match Search 3',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '22',
            searchName: 'Potential Match Search 4',
            searchTime: expect.any(String),
          },
        ],
      });
    });
  });

  test('Selecting tracklet for Find Matches Popover and increment potential match search name', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/match-groups') &&
        url?.includes('?eventId') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockGetMatchGroupsResponseWithEventId });
      }
      if (url?.includes('/match-groups') && method === 'patch') {
        return Promise.resolve({ data: mockPatchGetMatchGroupsResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Add to button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });

    // Tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');

    // Find Matches button is not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeEnabled();
    });

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });
    const selectElement = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Check potential match search name
    await waitFor(() => {
      expect(
        last(
          mockedStore.getState().searchResults.matchGroup.data
            .searches as Search[]
        )?.searchName
      ).toEqual('Potential Match Search 2');
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    // Check update matchGroup action
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      expect(lastCall[0].data).toEqual({
        searches: [
          {
            id: '16b5f647-7358-4812-87f4-93e0468f19a6',
            referenceTrackletId: 'reference-tracketlet-id-mock-1',
            searchName: 'Potential Match Search 1',
          },
          {
            id: '12345678-5150-4d56-90aa-6d15d3a82761',
            referenceTrackletId: 'reference-tracketlet-id-mock-2',
            searchName: 'Potential Match Search 2',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '11',
            searchName: 'Potential Match Search 3',
            searchTime: expect.any(String),
          },
        ],
      });
    });

    // Check potential match search name
    await waitFor(() => {
      expect(
        last(
          mockedStore.getState().searchResults.matchGroup.data
            .searches as Search[]
        )?.searchName
      ).toEqual('Potential Match Search 3');
    });

    // Uncheck tracklet and check new tracklet
    await selectCheckboxById('Tracklet-0-checkbox');
    await selectCheckboxById('Tracklet-1-checkbox');

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });
    const selectElement1 = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement1);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    // Check update matchGroup action
    await waitFor(() => {
      const lastCall =
        mockedAxios.request.mock.calls[
          mockedAxios.request.mock.calls.length - 1
        ];
      expect(lastCall[0].data).toEqual({
        searches: [
          {
            id: '16b5f647-7358-4812-87f4-93e0468f19a6',
            referenceTrackletId: 'reference-tracketlet-id-mock-1',
            searchName: 'Potential Match Search 1',
          },
          {
            id: '12345678-5150-4d56-90aa-6d15d3a82761',
            referenceTrackletId: 'reference-tracketlet-id-mock-2',
            searchName: 'Potential Match Search 2',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '11',
            searchName: 'Potential Match Search 3',
          },
          {
            id: expect.any(String),
            referenceTrackletId: '22',
            searchName: 'Potential Match Search 4',
            searchTime: expect.any(String),
          },
        ],
      });
    });
  });
  test('Selecting tracklets for Find Matches and Add to buttons', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Add to button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeInTheDocument();
    });

    // Find Matches button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeInTheDocument();
    });

    // Add to and Find Matches buttons are disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeDisabled();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });

    // tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');

    // Add to and Find Matches buttons are not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeEnabled();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeEnabled();
    });

    await selectCheckboxById('Tracklet-1-checkbox');

    // Add to button is not disabled and Find Matches button is disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__add-to-button')
      ).toBeEnabled();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });
  });

  test('Selecting tracklets for Find Matches Popover and throwing error for duplicate referenceId', async () => {
    const mockedStore = configureAppStore();
    mockedStore.dispatch(
      setThumbnails({
        '11': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '22': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
        '33': {
          expiresDateTime: new Date(Date.now() + 100000).toISOString(),
          thumbnailUrls: {
            best: '/tracklet-example-best.png',
          },
        },
      })
    );
    // mock axios request
    mockedAxios.request.mockImplementation(async ({ url, method }) => {
      if (
        url?.includes('/match-groups') &&
        url?.includes('/search') &&
        method === 'get'
      ) {
        return Promise.resolve({
          data: mockGetMatchGroupSearchResultsResponse,
        });
      }
      if (
        url?.includes('/match-groups') &&
        url?.includes('?eventId') &&
        method === 'get'
      ) {
        return Promise.resolve({ data: mockGetMatchGroupsResponseWithEventId });
      }
      if (url?.includes('/match-groups') && method === 'patch') {
        return Promise.resolve({ data: mockPatchGetMatchGroupsResponse });
      }
      if (url?.includes('/match-groups') && method === 'get') {
        return Promise.resolve({ data: mockGetMatchGroupsResponse });
      }
    });

    render(
      <Provider store={mockedStore}>
        <ThemeProvider theme={createMaterialTheme()}>
          <I18nProvider locale={LOCALES.ENGLISH}>
            <Router>
              <MatchGroup />
            </Router>
          </I18nProvider>
        </ThemeProvider>
      </Provider>
    );

    // Page renders
    expect(screen.getByTestId('search-results')).toBeInTheDocument();

    // Add to button load
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeInTheDocument();
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeDisabled();
    });

    // Tracklets load
    await selectCheckboxById('Tracklet-0-checkbox');

    // Find Matches button is not disabled
    await waitFor(() => {
      expect(
        screen.getByTestId('file-and-filter-matches__detail-upload-button')
      ).toBeEnabled();
    });

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });
    const selectElement1 = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement1);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    // Click Find Matches button
    await act(() => {
      screen
        .getByTestId('file-and-filter-matches__detail-upload-button')
        .click();
    });

    // Find Matches Popover loads
    await waitFor(() => {
      expect(screen.getByTestId('find-matches-popover')).toBeInTheDocument();
    });

    // Open the select dropdown
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select')
      ).toBeInTheDocument();
    });

    const selectElement2 = screen.getByRole('combobox');
    fireEvent.mouseDown(selectElement2);

    // Select match group
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover-select-match-0')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover-select-match-0').click();
    });

    // Click Continue button
    await waitFor(() => {
      expect(
        screen.getByTestId('find-matches-popover__confirm')
      ).toBeInTheDocument();
      screen.getByTestId('find-matches-popover__confirm').click();
    });

    await waitFor(() => {
      expect(createSnackNotification).toHaveBeenCalledWith(
        AlertLevel.Error,
        'This tracklet already has a search',
        'See Potential Match Search 3'
      );
    });
  });
});
