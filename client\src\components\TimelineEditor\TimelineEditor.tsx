import cn from 'classnames';
import { Button, Skeleton, Slider } from '@mui/material';
import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch } from '@store/hooks';
import { useSelector } from 'react-redux';
import _, { clone, range, throttle } from 'lodash';
import { Duration } from 'luxon';
import { File, Tracklet } from '@shared-types/tracker';
import {
  getBoundingBoxes,
  getFile,
  saveTimeline,
  selectBoundingBoxes,
  selectTimeline,
  selectUserSelectedTrackletFile,
  selectUserSelectedTracklet,
  setTimeline,
  setUserSelectedTracklet,
  getThumbnails,
  selectThumbnails,
} from '@store/modules/timelineEditor/slice';
import { setTimelineProject } from '@store/modules/matchGroup/slice';
import {
  MatchGroupState,
  SelectedTrackletsResults,
} from '@store/modules/matchGroup/types';
import { MediaPlayer } from '@veritone/glc-react';
import { PlayerReference } from 'video-react';
import { Tracklet as TrackletComp, TrackletLoading } from '@components/common';
import ThumbnailScaler from '@components/common/ThumbnailScaler/ThumbnailScaler';
import { frameAlignStartTimeMs, frameAlignStopTimeMs } from '@utility/frames';
import { selectUserState } from '@store/store';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import './TimelineEditor.scss';
import { millisToTimeFormatted } from '@utility/convert';

/* eslint-disable react-hooks/exhaustive-deps */

const DEFAULT_SECONDS_IN_VIEW = 60;

interface Props {
  matchSelectedTracklets: SelectedTrackletsResults;
  matchGroup: MatchGroupState;
}

const TimelineEditor = (props: Props) => {
  const intl = useIntl();
  const { matchSelectedTracklets, matchGroup } = props;

  const dispatch = useAppDispatch();

  const timeline = useSelector(selectTimeline);

  const timelineScrubberContainerRef = useRef<HTMLDivElement>(null);
  const clipsContainerRef = useRef<HTMLDivElement>(null);
  const timelineClipRefs = useRef<HTMLDivElement[]>([]);
  const playerRef = useRef<PlayerReference | null>(null);
  const seekToAfterDragRef = useRef<number | undefined>();
  const timelineRef = useRef<Tracklet[] | undefined>();
  const selectedTimelineTrackletRef = useRef<Tracklet | undefined>();
  const playingTrackletRef = useRef<Tracklet>();
  const scaleFactorRef = useRef<number>();
  const currentFileRef = useRef<File>();
  const autoPlayNextRef = useRef<boolean>(false);
  const dragImages = useRef<HTMLImageElement[]>([]);

  const [thumbnailScale, setThumbnailScale] = useState(
    Number(localStorage.getItem('thumbnailScale') ?? 100)
  );
  const userSelectedTracklet = useSelector(selectUserSelectedTracklet);
  const userSelectedTrackletFile = useSelector(selectUserSelectedTrackletFile);
  const boundingBoxes = useSelector(selectBoundingBoxes);
  const thumbnails = useSelector(selectThumbnails);
  const userState = useSelector(selectUserState);
  const orgId = userState?.user?.organization?.organizationId;
  const [widthOfTimelinePx, setWidthOfTimelinePx] = useState(2500);
  const [secondsInView, setSecondsInView] = useState(DEFAULT_SECONDS_IN_VIEW);
  const [selectedTimelineTracklet, setSelectedTimelineTracklet] = useState<
    Tracklet | undefined
  >();
  const [scaleFactor, setScaleFactor] = useState(1);
  const [timeOffset, setTimeOffset] = useState(0);
  const [timeOffsetDelta, setTimeOffsetDelta] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [timelineDragStart, setTimelineDragStart] = useState(0);
  const [currentTimeDragStart, setCurrentTimeDragStart] = useState(0);
  const [currentTimeOffset, setCurrentTimeOffset] = useState(0);
  const [currentTimeOffsetDelta, setCurrentTimeOffsetDelta] = useState(0);
  const [timelineMatchSelectedTracklets, setTimelineMatchSelectedTracklets] =
    useState<SelectedTrackletsResults>();
  const [disableScrubberDrag, setDisableScrubberDrag] = useState(false);
  const [play, setPlay] = useState(false);
  const [dragOverNewTimeline, setDragOverNewTimeline] = useState(false);
  const [draggingTracklet, setDraggingTracklet] = useState<{
    tracklet?: Tracklet;
    currentPosition?: number;
    inTimeline?: boolean;
  }>();
  const [dragoverTrackletIndex, setDragoverTrackletIndex] =
    useState<number>(-1);

  const seekToAfterFileSet = useRef({ trackletId: '', seekTime: 0 });

  const matchSelectedTrackletsCount =
    matchSelectedTracklets.results?.length ?? 0;
  const videoLengthSeconds = timeline.reduce(
    (a, track) => a + (track.stopTimeMs - track.startTimeMs) / 1000,
    0
  );

  const pxPerSecond = (widthOfTimelinePx / secondsInView) * scaleFactor;
  const timeOffsetPx = timeOffset * pxPerSecond;
  const timeOffsetDeltaPx = timeOffsetDelta * pxPerSecond;

  const currentTimeOffsetPx = currentTimeOffset * pxPerSecond;
  const currentTimeOffsetDeltaPx = currentTimeOffsetDelta * pxPerSecond;

  const timeOffsetPxWithDelta = timeOffsetPx + timeOffsetDeltaPx;

  const currentTimeOffsetPxWithDelta =
    currentTimeOffsetPx + currentTimeOffsetDeltaPx;
  const matchSelectedTrackletsLoading =
    matchSelectedTracklets?.apiStatus === 'loading';

  const currentFile = userSelectedTrackletFile.file;
  const fileLoading = userSelectedTrackletFile.apiStatus === 'loading';

  const noFile = !currentFile && !fileLoading;
  const dragImg = new Image(0, 0);
  dragImg.src =
    'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

  const streams = currentFile?.streams;
  const frameRate = currentFile?.frameRate ?? 30;
  const isFileAndBoundingBoxesLoaded =
    userSelectedTracklet?.fileId === currentFile?.id &&
    userSelectedTracklet?.trackletId === boundingBoxes?.[0]?.trackletId;
  const thumbnailAssets = currentFile?.thumbnailAssets;

  // Autoplay logic
  const autoplayStatusRef = useRef<
    '' | 'Seeking' | 'Seeked' | 'Playing' | 'Done'
  >('');
  const autoplayStopRef = useRef(0);
  const previousPlayRef = useRef<'' | 'Play' | 'Pause'>('');

  const setDefaultAutoplay = () => {
    autoplayStatusRef.current = '';
    autoplayStopRef.current = 0;
  };

  const handleTrackletClick = (tracklet: Tracklet, inTimeline: boolean) => {
    if (inTimeline) {
      selectedTimelineTrackletRef.current = tracklet;
      playingTrackletRef.current = tracklet;
      seekToAfterDragRef.current = undefined;
      setSelectedTimelineTracklet(tracklet);
      dispatch(setUserSelectedTracklet({ tracklet: undefined }));
    } else {
      dispatch(setUserSelectedTracklet({ tracklet }));
      setSelectedTimelineTracklet(undefined);
    }
  };

  const handleSetTimeline = (timeline: Tracklet[]) => {
    dispatch(setTimeline({ timeline }));
    dispatch(setTimelineProject({ timeline }));
  };

  const getFileAndSeekTo = (tracklet: Tracklet, time: number) => {
    if (
      tracklet.trackletId ===
      userSelectedTrackletFile?.selectedTracklet?.trackletId
    ) {
      const seekTime = Math.ceil((time / 1000) * frameRate) / frameRate;
      playerRef.current?.seek(seekTime);
    }
    dispatch(setUserSelectedTracklet({ tracklet }));
    setSelectedTimelineTracklet(undefined);
    seekToAfterFileSet.current = {
      trackletId: tracklet.trackletId,
      seekTime: time,
    };
  };

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setWidthOfTimelinePx(entry.contentRect.width);
      }
    });

    if (timelineScrubberContainerRef.current) {
      setWidthOfTimelinePx(
        timelineScrubberContainerRef.current.getBoundingClientRect().width
      );
      resizeObserver.observe(timelineScrubberContainerRef.current);
    }
  }, [timelineScrubberContainerRef]);

  useEffect(() => {
    if (orgId) {
      const missingTracklets =
        [
          ...(timelineMatchSelectedTracklets?.results ?? []),
          ...timeline,
        ].reduce<Array<{ trackletId: string; orgId: string; fileId: string }>>(
          (arr, { trackletId, fileId }) => {
            const thumbnailInCache = thumbnails && trackletId in thumbnails;
            const isExpired =
              thumbnailInCache &&
              new Date(thumbnails[trackletId].expiresDateTime) <= new Date();

            if (!thumbnailInCache || isExpired) {
              arr.push({ trackletId, orgId, fileId });
            }
            return arr;
          },
          []
        ) ?? [];

      if (missingTracklets.length) {
        dispatch(getThumbnails(missingTracklets));
      }
    }
  }, [orgId, timeline, timelineMatchSelectedTracklets]);

  const [hideClipToolbarRight, setHideClipToolbarRight] = useState<boolean[]>(
    []
  );
  const [hideClipToolbarLeft, setHideClipToolbarLeft] = useState<boolean[]>([]);

  const setDragoverTrackletIndexThrottled = useMemo(
    () =>
      throttle(setDragoverTrackletIndex, 100, {
        leading: false,
        trailing: true,
      }),
    [dispatch]
  );

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      const newHideClipToolbarRight = [...hideClipToolbarRight];
      const newHideClipToolbarLeft = [...hideClipToolbarLeft];

      for (const entry of entries) {
        const index = timelineClipRefs.current.findIndex(
          (ref) => ref === entry.target
        );
        newHideClipToolbarRight[index] = entry.contentRect.width < 125;
        newHideClipToolbarLeft[index] = entry.contentRect.width < 62.5;
      }

      setHideClipToolbarRight(newHideClipToolbarRight);
      setHideClipToolbarLeft(newHideClipToolbarLeft);
    });

    if (timelineClipRefs.current) {
      const newHideClipToolbarRight = [...hideClipToolbarRight];
      const newHideClipToolbarLeft = [...hideClipToolbarLeft];

      timelineClipRefs.current.forEach((ref, i) => {
        if (ref) {
          newHideClipToolbarRight[i] = ref.getBoundingClientRect().width < 125;
          newHideClipToolbarLeft[i] = ref.getBoundingClientRect().width < 62.5;

          resizeObserver.observe(ref);
        }
      });
      setHideClipToolbarRight(newHideClipToolbarRight);
      setHideClipToolbarLeft(newHideClipToolbarLeft);
    }
  }, [timelineClipRefs, timeline]);

  useEffect(() => {
    if (userSelectedTracklet) {
      dispatch(getFile({ tracklet: userSelectedTracklet }));
    } else if (selectedTimelineTracklet) {
      dispatch(getFile({ tracklet: selectedTimelineTracklet }));
    }
  }, [userSelectedTracklet, selectedTimelineTracklet, dispatch]);

  useEffect(() => {
    if (
      userSelectedTracklet?.fileId === currentFile?.id &&
      userSelectedTracklet
    ) {
      dispatch(
        getBoundingBoxes({
          trackletId: userSelectedTracklet?.trackletId,
        })
      );
    } else if (
      selectedTimelineTracklet?.fileId === currentFile?.id &&
      selectedTimelineTracklet
    ) {
      dispatch(
        getBoundingBoxes({
          trackletId: selectedTimelineTracklet?.trackletId,
        })
      );
    }
  }, [dispatch, userSelectedTracklet, selectedTimelineTracklet, currentFile]);

  useEffect(() => {
    if (isFileAndBoundingBoxesLoaded) {
      playerRef.current?.subscribeToStateChange((state) => {
        // 1.) Track when seeking
        if (autoplayStatusRef.current === 'Seeking' && !state.seeking) {
          autoplayStatusRef.current = 'Seeked';
        }
        // 2.) Track when seeked
        else if (autoplayStatusRef.current === 'Seeked' && !state.paused) {
          autoplayStatusRef.current = 'Playing';
          playerRef.current?.play();
          if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        } else if (autoplayStatusRef.current === 'Seeked' && state.paused) {
          if (
            previousPlayRef.current === 'Pause' ||
            previousPlayRef.current === ''
          ) {
            playerRef.current?.pause();
          } else if (previousPlayRef.current === 'Play') {
            playerRef.current?.play();
          }
        }
        // 3.) Track while playing
        else if (autoplayStatusRef.current === 'Playing' && !state.paused) {
          previousPlayRef.current = 'Play';
          const alignedStopTimeMs = frameAlignStopTimeMs(
            autoplayStopRef.current,
            frameRate
          );
          const frameAlignCurrentTimeMs = frameAlignStartTimeMs(
            state.currentTime * 1000,
            frameRate
          );
          if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
            autoplayStatusRef.current = 'Done';
            playerRef.current?.pause();
            previousPlayRef.current = 'Pause';
          }
        } else if (autoplayStatusRef.current === 'Playing' && state.paused) {
          previousPlayRef.current = 'Pause';
          playerRef.current?.pause();
        }
        // 4.) Track when done playing
        else if (autoplayStatusRef.current === 'Done') {
          setDefaultAutoplay();
        }
      });
    }
  }, [isFileAndBoundingBoxesLoaded, frameRate]);

  useEffect(() => {
    currentFileRef.current = currentFile;
    timelineRef.current = timeline;
    selectedTimelineTrackletRef.current = selectedTimelineTracklet;
    scaleFactorRef.current = scaleFactor;
  }, [selectedTimelineTracklet, timeline, scaleFactor, currentFile]);

  const reachedEndOfTrackletWhilePlaying = useRef(false);

  useEffect(() => {
    if (
      userSelectedTrackletFile?.selectedTracklet &&
      boundingBoxes?.length &&
      isFileAndBoundingBoxesLoaded
    ) {
      let startTimeMs = Number.MAX_SAFE_INTEGER;
      let stopTimeMs = 0;
      for (const boundingBox of boundingBoxes) {
        if (boundingBox.startTimeMs < startTimeMs) {
          startTimeMs = boundingBox.startTimeMs;
        }
        if (boundingBox.stopTimeMs > stopTimeMs) {
          stopTimeMs = boundingBox.stopTimeMs;
        }
      }
      autoplayStopRef.current = stopTimeMs;
      autoplayStatusRef.current = 'Seeking';
      const seekTime = Math.ceil((startTimeMs / 1000) * frameRate) / frameRate;
      playerRef.current?.seek(seekTime);
    }
  }, [
    boundingBoxes,
    frameRate,
    userSelectedTrackletFile,
    isFileAndBoundingBoxesLoaded,
  ]);

  useEffect(() => {
    if (playerRef.current) {
      playerRef.current?.subscribeToStateChange((state) => {
        setIsPlaying(!state.paused);

        setDisableScrubberDrag(!(state.paused || state.autoPaused));

        if (
          state.paused &&
          reachedEndOfTrackletWhilePlaying.current &&
          timelineRef.current &&
          selectedTimelineTrackletRef.current
        ) {
          const { trackletId } = selectedTimelineTrackletRef.current;
          const indexInTimeline = timelineRef.current.findIndex(
            (t) => t.trackletId === trackletId
          );
          if (indexInTimeline < timelineRef.current.length - 1) {
            selectedTimelineTrackletRef.current =
              timelineRef.current[indexInTimeline + 1];
            setSelectedTimelineTracklet(
              timelineRef.current[indexInTimeline + 1]
            );
            autoPlayNextRef.current = true;
            reachedEndOfTrackletWhilePlaying.current = false;
          }
        }

        if (
          (state.ended ||
            (state.buffered && !state.paused && !state.seeking)) &&
          selectedTimelineTrackletRef.current &&
          timelineRef.current
        ) {
          const { startTimeMs, stopTimeMs, trackletId } =
            selectedTimelineTrackletRef.current;
          const indexInTimeline = timelineRef.current.findIndex(
            (t) => t.trackletId === trackletId
          );
          const frameAlignCurrentTimeMs = frameAlignStartTimeMs(
            state.currentTime * 1000,
            frameRate
          );
          const alignedStopTimeMs = frameAlignStopTimeMs(stopTimeMs, frameRate);

          const trackletStartOffsetInTimelineMs = timelineRef.current.reduce(
            (res, cv, i) => {
              if (i < indexInTimeline) {
                res += cv.stopTimeMs - cv.startTimeMs;
              }
              return res;
            },
            0
          );

          if (
            startTimeMs !== undefined &&
            trackletStartOffsetInTimelineMs !== undefined
          ) {
            const alignedStartTimeMs = frameAlignStopTimeMs(
              startTimeMs,
              frameRate
            );
            const timelineScrubberOffsetSec =
              (trackletStartOffsetInTimelineMs +
                frameAlignCurrentTimeMs -
                alignedStartTimeMs) /
              1000;
            const trackletLengthSec = (stopTimeMs - startTimeMs) / 1000;
            const endOfTimelineTracklet =
              trackletStartOffsetInTimelineMs / 1000 + trackletLengthSec;
            const timelineScrubberOffsetOutsideBounds =
              timelineScrubberOffsetSec >= endOfTimelineTracklet;
            setCurrentTimeOffset(
              timelineScrubberOffsetOutsideBounds
                ? endOfTimelineTracklet
                : timelineScrubberOffsetSec
            );
          }

          if (frameAlignCurrentTimeMs >= alignedStopTimeMs) {
            autoPlayNextRef.current = false;

            seekToAfterDragRef.current = undefined;
            playerRef.current?.pause();
            reachedEndOfTrackletWhilePlaying.current = true;
          }
        }
      });
    }
  }, [playerRef.current, selectedTimelineTracklet, timeline]);

  useEffect(() => {
    if (selectedTimelineTracklet && !seekToAfterDragRef.current) {
      const indexInTimeline = timeline.findIndex(
        (t) => t.trackletId === selectedTimelineTracklet.trackletId
      );

      const trackletStartOffsetInTimelineMs = timeline.reduce((res, cv, i) => {
        if (i < indexInTimeline) {
          res += cv.stopTimeMs - cv.startTimeMs;
        }
        return res;
      }, 0);
      setCurrentTimeOffset(trackletStartOffsetInTimelineMs / 1000);
    }
  }, [selectedTimelineTracklet, timeline]);

  useEffect(() => {
    if (
      userSelectedTracklet?.fileId === currentFile?.id &&
      userSelectedTracklet?.startTimeMs
    ) {
      if (seekToAfterFileSet.current.trackletId !== '') {
        const seekTime =
          Math.ceil((seekToAfterFileSet.current.seekTime / 1000) * frameRate) /
          frameRate;
        playerRef.current?.seek(seekTime);
      } else {
        playerRef.current?.seek(userSelectedTracklet.startTimeMs / 1000);
      }
    } else if (
      selectedTimelineTracklet?.fileId === currentFile?.id &&
      selectedTimelineTracklet
    ) {
      if (selectedTimelineTracklet.startTimeMs >= 0) {
        const seekTime =
          seekToAfterDragRef.current ||
          selectedTimelineTracklet.startTimeMs / 1000;
        playerRef.current?.seek(seekTime);
      }
    }
  }, [
    currentFile,
    userSelectedTracklet,
    selectedTimelineTracklet,
    seekToAfterDragRef.current,
  ]);

  useEffect(() => {
    if (matchGroup.apiStatus === 'idle') {
      const timelineProject = matchGroup?.data?.timelineProject;
      if (
        (matchSelectedTracklets?.results?.length ?? 0) > 0 &&
        timelineProject?.groups?.[0]
      ) {
        const savedTracklets = timelineProject.groups
          .map((g) => g.tracklets)
          .flat();
        const results =
          matchSelectedTracklets?.results?.filter(
            (st) =>
              !savedTracklets.find((tt) => tt.trackletId === st.trackletId)
          ) ?? [];

        setTimelineMatchSelectedTracklets({
          ...matchSelectedTracklets,
          results,
        });
        dispatch(
          setTimeline({
            timeline: savedTracklets.map((t) => ({
              ...t,
              thumbnailUrls:
                thumbnails?.[t.trackletId]?.thumbnailUrls ?? t.thumbnailUrls,
            })),
          })
        );
      } else {
        setTimelineMatchSelectedTracklets(matchSelectedTracklets);
      }
    }
  }, [matchSelectedTrackletsCount, matchGroup, thumbnails]);

  useEffect(() => {
    if (timelineMatchSelectedTracklets) {
      const newTimelineMatchSelectedTracklets = clone(
        timelineMatchSelectedTracklets
      );
      newTimelineMatchSelectedTracklets.results =
        matchSelectedTracklets?.results?.filter(
          (st) => !timeline.find((tt) => tt.trackletId === st.trackletId)
        );
      setTimelineMatchSelectedTracklets(newTimelineMatchSelectedTracklets);
    }
  }, [timeline]);

  useEffect(() => {
    dispatch(
      saveTimeline({ matchGroupId: matchGroup.data?.id, noMessage: true })
    );

    if (timeline.length > 0) {
      const t = timeline.reduce(
        (a, track) => a + (track.stopTimeMs - track.startTimeMs) / 1000,
        0
      );
      setSecondsInView(t);
      setTimeOffset(0);
      setTimeOffsetDelta(0);
    }
  }, [timeline]);

  useEffect(() => {
    if (widthOfTimelinePx === 0 || secondsInView === 0) {
      return;
    }
    let scaleFactorTemp = scaleFactor;
    let pps = (widthOfTimelinePx / secondsInView) * scaleFactorTemp;
    if (pps < 15) {
      while (pps < 20) {
        pps = (widthOfTimelinePx / secondsInView) * scaleFactorTemp;
        scaleFactorTemp = scaleFactorTemp + 0.5;
      }
    }

    if (pps > 50) {
      while (pps > 30 && scaleFactorTemp > 1) {
        pps = (widthOfTimelinePx / secondsInView) * scaleFactorTemp;
        scaleFactorTemp = scaleFactorTemp - 0.5 < 1 ? 1 : scaleFactorTemp - 0.5;
      }
    }

    setScaleFactor(scaleFactorTemp);
  }, [secondsInView, widthOfTimelinePx]);

  useEffect(() => {
    if (secondsInView === videoLengthSeconds) {
      return;
    }

    const lastClipIndex =
      timelineClipRefs.current.length > 0
        ? timelineClipRefs.current.length - 1
        : 0;
    const firstClipLeft =
      timelineClipRefs.current[0]?.getBoundingClientRect().left;
    const lastClipRight =
      timelineClipRefs.current[lastClipIndex]?.getBoundingClientRect().right;
    const leftBoundary =
      timelineScrubberContainerRef.current?.getBoundingClientRect().left;
    const rightBoundary =
      timelineScrubberContainerRef.current?.getBoundingClientRect().right;

    if (!leftBoundary || !rightBoundary) {
      return;
    }

    if (firstClipLeft > leftBoundary && timeOffset !== 0) {
      setTimeOffset(0);
    }

    if (lastClipRight < rightBoundary) {
      const distanceToRightBoundary = rightBoundary - lastClipRight;
      const timeOffsetDelta = distanceToRightBoundary / pxPerSecond;
      if (timeOffsetDelta + timeOffset !== timeOffset) {
        setTimeOffset(timeOffsetDelta + timeOffset);
      }
    }
  }, [timeOffset, secondsInView]);

  const calculatePositionAndSeek = (currentTimeOffsetParam: number) => {
    let lengthOfAllPrevious = 0;
    let timeOffsetInTracklet = 0;
    let scrubbingOverTracklet: Tracklet | undefined;
    let scrubbingOverTrackletIndex;
    timelineRef.current?.forEach((tracklet, index) => {
      const lengthOfCurrent =
        (tracklet.stopTimeMs - tracklet.startTimeMs) / 1000;

      if (
        currentTimeOffsetParam >= lengthOfAllPrevious &&
        currentTimeOffsetParam < lengthOfAllPrevious + lengthOfCurrent &&
        scrubbingOverTracklet === undefined
      ) {
        scrubbingOverTrackletIndex = index;
        timeOffsetInTracklet = currentTimeOffsetParam - lengthOfAllPrevious;
        scrubbingOverTracklet = tracklet;
      }
      lengthOfAllPrevious += lengthOfCurrent;
    });

    if (
      scrubbingOverTrackletIndex !== undefined &&
      timelineRef.current &&
      scrubbingOverTracklet !== undefined
    ) {
      setSelectedTimelineTracklet(
        timelineRef.current[scrubbingOverTrackletIndex]
      );
      seekToAfterDragRef.current =
        scrubbingOverTracklet.startTimeMs / 1000 + timeOffsetInTracklet;
    }
  };

  return (
    <div className="timeline-editor" data-testid="timeline-editor">
      <div className="timeline-editor__top">
        <div className="timeline-editor__video_and_attributes">
          <div className="timeline-editor__video">
            {!fileLoading && streams ? (
              <MediaPlayer
                ref={playerRef}
                uri=""
                streams={streams}
                frameRate={frameRate}
                boundingBoxes={boundingBoxes}
                maxHeight={350}
                thumbnailAssets={thumbnailAssets}
              />
            ) : null}
          </div>
          <div className="timeline-editor__tracklet-detail">
            {noFile && (
              <div className="timeline-editor__tracklet-detail-no-file">
                {I18nTranslate.TranslateMessage('selectTrackletToViewDetails')}.
              </div>
            )}
            {fileLoading ? (
              <>
                <Skeleton
                  className="timeline-editor__tracklet-detail-loading"
                  variant="rectangular"
                  height={148}
                />
              </>
            ) : (
              <>
                {
                  <>
                    <Button
                      onClick={() => setPlay(!play)}
                      style={{ display: 'none' }}
                    >
                      Play
                    </Button>
                    <div className="timeline-editor__tracklet-detail-video-information">
                      <div className="timeline-editor__tracklet-detail-video-information-title">
                        Video Information
                      </div>
                      <div className="timeline-editor__tracklet-detail-video-information-content">
                        <div className="timeline-editor__tracklet-detail-video-information-tracklets-added">
                          <div>{timeline.length}</div>
                          <div>Tracklets Added</div>
                        </div>
                        <div className="timeline-editor__tracklet-detail-video-information-file-size">
                          <div>{`??? ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`}</div>
                          <div>
                            {I18nTranslate.TranslateMessage('fileSize')}
                          </div>
                        </div>
                        <div className="timeline-editor__tracklet-detail-video-information-video-length">
                          <div data-testid="TimelineEditor-VideoLength">
                            {millisToTimeFormatted(videoLengthSeconds * 1000)}
                          </div>
                          <div>Video Length</div>
                        </div>
                      </div>
                    </div>
                  </>
                }
              </>
            )}
          </div>
        </div>
        <div className="timeline-editor__detail">
          <div className="timeline-editor__tabbed-detections-main-container-label">
            {I18nTranslate.TranslateMessage('tracklets')}
          </div>
          <div
            className="timeline-editor__tabbed-detections-main-container"
            data-testid="tabbed-detections"
          >
            <div className="timeline-editor__tabbed-detections-tab-panels main__scrollbar">
              <div
                className="timeline-editor__matched-detection-tab-container"
                data-testid="matched-detection-tab"
              >
                <div className="main__tracklet_thumbnails-tracklets">
                  {!matchSelectedTrackletsLoading &&
                  timelineMatchSelectedTracklets &&
                  (timelineMatchSelectedTracklets?.results?.length ?? 0) > 0 ? (
                    timelineMatchSelectedTracklets?.results?.map(
                      (tracklet, index) => {
                        const thumbnailUrl =
                          thumbnails?.[tracklet.trackletId]?.thumbnailUrls
                            ?.best;
                        const thumbnailIsExpired =
                          new Date(
                            thumbnails?.[tracklet.trackletId]?.expiresDateTime
                          ) <= new Date();

                        return thumbnailUrl && !thumbnailIsExpired ? (
                          <TrackletComp
                            selected={
                              userSelectedTracklet?.trackletId ===
                              tracklet.trackletId
                            }
                            playing={isPlaying}
                            isInMatchGroup={true}
                            onTrackletStartTimeClick={(time) =>
                              getFileAndSeekTo(tracklet, time)
                            }
                            onTrackletStopTimeClick={(time) =>
                              getFileAndSeekTo(tracklet, time)
                            }
                            handleTrackletClick={() =>
                              handleTrackletClick(tracklet, false)
                            }
                            thumbnailUrl={thumbnailUrl}
                            thumbnailScale={thumbnailScale}
                            tracklet={tracklet}
                            index={index}
                            onDrag={(e) => {
                              e.stopPropagation();
                              setDraggingTracklet({
                                tracklet,
                                inTimeline: false,
                              });
                            }}
                          />
                        ) : (
                          <TrackletLoading
                            thumbnailScale={thumbnailScale}
                            index={index}
                          />
                        );
                      }
                    )
                  ) : (
                    <div
                      className="timeline-editor__no-tracklets-found"
                      data-testid="timeline-editor-no-tracklets-found"
                      style={{
                        display: matchSelectedTrackletsLoading
                          ? 'none'
                          : undefined,
                      }}
                    >
                      {intl.formatMessage({ id: 'addFirstTracklet', defaultMessage: 'Add your first detections by selecting them in the Potential Search Results.' })}
                    </div>
                  )}
                  {matchSelectedTrackletsLoading &&
                    range(100).map((i) => (
                      <Skeleton
                        className="timeline-editor__matched-detection-tab-tracklet skeleton"
                        data-testid={`matched-detection-tab-skeleton-${i}`}
                        key={`matched-detection-tab-tracklet-${i}`}
                        variant="rectangular"
                        style={{
                          width: `${(113 * thumbnailScale) / 100}px`,
                          height: `${(139 * thumbnailScale) / 100}px`,
                        }}
                      />
                    ))}
                </div>
              </div>
            </div>
            <div className="timeline-editor__tabbed-detections-footer">
              <ThumbnailScaler
                scale={thumbnailScale}
                setScale={setThumbnailScale}
                loading={matchSelectedTrackletsLoading}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="timeline-editor__timeline">
        <div
          className="timeline-editor__timeline-current-time-container"
          draggable={!disableScrubberDrag}
          onDragStart={(e) => {
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            e.dataTransfer.setDragImage(dragImg, 0, 0);
            setCurrentTimeDragStart(e.clientX);
          }}
          onDragEnd={(e) => {
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            setCurrentTimeOffset(currentTimeOffset + currentTimeOffsetDelta);
            setCurrentTimeOffsetDelta(0);
            calculatePositionAndSeek(
              currentTimeOffset + currentTimeOffsetDelta
            );
          }}
          onDrag={(e) => {
            playerRef.current?.pause();
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            if (e.clientX > 0) {
              const moveDeltaPx = e.clientX - currentTimeDragStart;
              setCurrentTimeOffsetDelta(moveDeltaPx / pxPerSecond);
            }
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
          }}
          style={{
            transform: `translate(${
              currentTimeOffsetPxWithDelta / scaleFactor +
              timeOffsetPxWithDelta -
              2
            }px, 0)`,
            display: draggingTracklet?.tracklet ? 'none' : 'initial',
          }}
        >
          <div className="timeline-editor__timeline-current-time" />
        </div>

        <div
          className="timeline-editor__timeline-scrubber"
          data-testid="timeline-scrubber"
          ref={timelineScrubberContainerRef}
          draggable
          onDragStart={(e) => {
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            e.dataTransfer.setDragImage(dragImg, 0, 0);
            setTimelineDragStart(e.clientX);
          }}
          onDragEnd={(e) => {
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            setTimeOffset(timeOffset + timeOffsetDelta);
            setTimeOffsetDelta(0);
          }}
          onDrag={(e) => {
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
            const lastClipIndex =
              timelineClipRefs.current.length > 0
                ? timelineClipRefs.current.length - 1
                : 0;
            const firstClipLeft =
              timelineClipRefs.current[0]?.getBoundingClientRect().left;
            const lastClipRight =
              timelineClipRefs.current[lastClipIndex]?.getBoundingClientRect()
                .right;

            if (e.clientX > 0) {
              const moveDeltaPx = e.clientX - timelineDragStart;
              const leftBoundary = e.currentTarget.getBoundingClientRect().left;
              const rightBoundary =
                e.currentTarget.getBoundingClientRect().right;
              const isOverflowLeftAfterDelta =
                firstClipLeft + moveDeltaPx / pxPerSecond < leftBoundary;
              const isOverflowRightAfterDelta =
                lastClipRight + moveDeltaPx / pxPerSecond > rightBoundary;

              if (isOverflowLeftAfterDelta && isOverflowRightAfterDelta) {
                setTimeOffsetDelta(moveDeltaPx / pxPerSecond);
              }
            }
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.dataTransfer.dropEffect = 'move';
          }}
        >
          {range((videoLengthSeconds + 1) / scaleFactor).map((si) => {
            const timestamp = Duration.fromObject({
              seconds: si * scaleFactor,
            }).toFormat('hh:mm:ss');
            const shouldNotRender =
              pxPerSecond * si + timeOffsetPxWithDelta < -100 ||
              pxPerSecond * si + timeOffsetPxWithDelta >
                window.innerWidth + 100;

            if (shouldNotRender) {
              return <Fragment key={`TimelineScrubberEmptyTick-${si}`} />;
            }
            return (
              <Fragment key={`TimelineScrubberTickGroup-${si}`}>
                {(si % 5 === 0 || secondsInView <= 20) && (
                  <div
                    key={`ScrubberTimestamp-${si}`}
                    className="timeline-editor__timeline-scrubber-timestamp"
                    style={{
                      left: pxPerSecond * si,
                      height: si % 5 === 0 ? 15 : undefined,
                      transform: `translate(calc(${timeOffsetPxWithDelta}px - 50%), -60px)`,
                    }}
                  >
                    {timestamp}
                  </div>
                )}
                <div
                  className="timeline-editor__timeline-scrubber-tick"
                  key={`TimelineEditorTick-${si}`}
                  style={{
                    left: pxPerSecond * si,
                    height: si % 5 === 0 ? 15 : undefined,
                    transform: `translate(calc(${timeOffsetPxWithDelta}px), -40px)`,
                  }}
                />
              </Fragment>
            );
          })}
        </div>
        <div className="timeline-editor__timeline-container">
          <div className="timeline-editor__timeline-container-left-border" />
          <div
            className="timeline-editor__timeline-clips"
            data-testid="timeline-clips"
            ref={clipsContainerRef}
          >
            {timeline?.length > 0 &&
              timeline.map((timelineTracklet, i, tlRef) => {
                const trackletLengthSec =
                  (timelineTracklet.stopTimeMs - timelineTracklet.startTimeMs) /
                  1000;
                const trackletLengthPx =
                  (pxPerSecond * trackletLengthSec) / scaleFactor;
                const isDragoverEnd = dragoverTrackletIndex === timeline.length;

                const widthsOfPreviousTrackletsInTimeline = tlRef.reduce(
                  (acc, cv, ci) => {
                    if (ci >= i) {
                      return acc;
                    }
                    const tlSec = (cv.stopTimeMs - cv.startTimeMs) / 1000;
                    const tlPx = (pxPerSecond * tlSec) / scaleFactor;
                    return acc + tlPx;
                  },
                  0
                );

                const widthsOfTrackletsInTimeline = tlRef.reduce((acc, cv) => {
                  const tlSec = (cv.stopTimeMs - cv.startTimeMs) / 1000;
                  const tlPx = (pxPerSecond * tlSec) / scaleFactor;
                  return acc + tlPx;
                }, 0);

                return (
                  <Fragment key={`TimelineEditorClips-${i}`}>
                    <div
                      className="timeline-editor__timeline-clips-reorder"
                      style={{
                        left: isDragoverEnd
                          ? widthsOfTrackletsInTimeline + 20 - 4
                          : widthsOfPreviousTrackletsInTimeline + 20 - 4,
                        transform: `translate(${timeOffsetPxWithDelta}px, 0)`,
                        display:
                          draggingTracklet?.tracklet &&
                          (dragoverTrackletIndex === i || isDragoverEnd)
                            ? 'initial'
                            : 'none',
                      }}
                    >
                      <div className="material-symbols-outlined">
                        place_item
                      </div>
                    </div>
                    <div
                      ref={(el) =>
                        (timelineClipRefs.current[i] = el as HTMLDivElement)
                      }
                      key={`TimelineEditor-Clip-${i}`}
                      data-testid={`TimelineEditor-Clip-${i}-${timelineTracklet.trackletId}`}
                      className={cn(
                        'timeline-editor__timeline-clips-clip has-tracklets',
                        {
                          'is-last': i === timeline.length - 1,
                          selected:
                            selectedTimelineTracklet?.trackletId ===
                            timelineTracklet.trackletId,
                          'next-selected':
                            timeline.findIndex(
                              (t) =>
                                t.trackletId ===
                                selectedTimelineTracklet?.trackletId
                            ) -
                              1 ===
                            i,
                        }
                      )}
                      id={timelineTracklet.trackletId}
                      style={{
                        left: widthsOfPreviousTrackletsInTimeline + 20,
                        width: trackletLengthPx,
                        transform: `translate(${timeOffsetPxWithDelta}px, 0)`,
                        backgroundImage: `url(${timelineTracklet.thumbnailUrls.best})`,
                        backgroundRepeat: 'repeat-x',
                        backgroundPosition: 'center center',
                        backgroundSize: 'contain',
                      }}
                      draggable
                      onClick={() => {
                        handleTrackletClick(timelineTracklet, true);
                      }}
                      onDragEnter={(e) => {
                        e.preventDefault();
                        const dragoverTrackletId = e.currentTarget.id;
                        setDragoverTrackletIndexThrottled(
                          timeline.findIndex(
                            (t) => t.trackletId === dragoverTrackletId
                          )
                        );
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        setDragoverTrackletIndexThrottled(-1);
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        const halfWidthOfClipPx =
                          e.currentTarget.getBoundingClientRect().width / 2;
                        const xFromLeftOfClipPx =
                          (e.clientX ?? 0) -
                          e.currentTarget.getBoundingClientRect().left;
                        const dragoverTrackletId = e.currentTarget.id;
                        const clipBeginning =
                          e.currentTarget.getBoundingClientRect().left;
                        const clipEnd =
                          e.currentTarget.getBoundingClientRect().right;
                        const clipBeginningOffScreen =
                          clipBeginning <
                          (clipsContainerRef?.current?.getBoundingClientRect()
                            .left ?? 0);
                        const clipEndOffScreen =
                          clipEnd >
                          (clipsContainerRef?.current?.getBoundingClientRect()
                            .right ?? 10000);

                        if (
                          xFromLeftOfClipPx < halfWidthOfClipPx &&
                          !clipBeginningOffScreen
                        ) {
                          setDragoverTrackletIndexThrottled(
                            timeline.findIndex(
                              (t) => t.trackletId === dragoverTrackletId
                            )
                          );
                        }
                        if (
                          xFromLeftOfClipPx >= halfWidthOfClipPx &&
                          !clipEndOffScreen
                        ) {
                          const indexOfDragoverClip = timeline.findIndex(
                            (t) => t.trackletId === dragoverTrackletId
                          );
                          setDragoverTrackletIndexThrottled(
                            indexOfDragoverClip + 1
                          );
                        }
                      }}
                      onDragStart={(e) => {
                        e.dataTransfer.setDragImage(
                          dragImages.current[i],
                          0,
                          0
                        );
                      }}
                      onDragEnd={() => {
                        if (
                          draggingTracklet?.tracklet &&
                          dragoverTrackletIndex === -1 &&
                          draggingTracklet.inTimeline
                        ) {
                          const newTimeline = clone(timeline);
                          const draggingTrackletIndex = newTimeline.findIndex(
                            (t) =>
                              t.trackletId ===
                              draggingTracklet?.tracklet?.trackletId
                          );
                          newTimeline.splice(draggingTrackletIndex, 1);
                          handleSetTimeline(newTimeline);
                        }
                        setDraggingTracklet({ tracklet: undefined });
                      }}
                      onDrop={(e) => {
                        e.preventDefault();

                        if (
                          draggingTracklet?.tracklet &&
                          dragoverTrackletIndex === -1
                        ) {
                          if (!draggingTracklet.inTimeline) {
                            const newTimeline = [
                              ...timeline,
                              { ...draggingTracklet.tracklet },
                            ];
                            handleSetTimeline(newTimeline);
                            setDraggingTracklet({ tracklet: undefined });
                            return;
                          }
                        }

                        if (
                          draggingTracklet?.tracklet &&
                          dragoverTrackletIndex >= 0
                        ) {
                          const newTimeline = clone(timeline);
                          const deletionTrackletId =
                            draggingTracklet.tracklet?.trackletId;

                          newTimeline.splice(dragoverTrackletIndex, 0, {
                            ...draggingTracklet.tracklet,
                          });

                          const deletionTrackletIndex = newTimeline.findIndex(
                            (t, ni) =>
                              dragoverTrackletIndex !== ni &&
                              deletionTrackletId === t.trackletId
                          );
                          if (deletionTrackletIndex > -1) {
                            newTimeline.splice(deletionTrackletIndex, 1);
                          }

                          handleSetTimeline(newTimeline);
                          setDraggingTracklet({ tracklet: undefined });
                          setDragoverTrackletIndexThrottled(-1);
                          return;
                        }
                      }}
                      onDrag={(e) => {
                        setDraggingTracklet({
                          tracklet: timelineTracklet,
                          inTimeline: true,
                          currentPosition: i,
                        });

                        if (
                          draggingTracklet?.tracklet &&
                          draggingTracklet?.inTimeline &&
                          clipsContainerRef.current
                        ) {
                          const clipsContainerLeft =
                            clipsContainerRef.current.getBoundingClientRect()
                              .left;
                          const clipsContainerRight =
                            clipsContainerRef.current.getBoundingClientRect()
                              .right;
                          const clipsContainerWidth =
                            clipsContainerRef.current.getBoundingClientRect()
                              .width;
                          const dragAreaLeft =
                            clipsContainerLeft + clipsContainerWidth * 0.1;
                          const dragAreaRight =
                            clipsContainerRight - clipsContainerWidth * 0.1;
                          const dragPositionWithinContainer =
                            e.clientX - clipsContainerLeft;

                          if (
                            dragPositionWithinContainer > clipsContainerLeft &&
                            dragPositionWithinContainer < dragAreaLeft
                          ) {
                            const dragScale =
                              (dragAreaLeft - dragPositionWithinContainer) /
                              (dragAreaLeft - clipsContainerLeft);
                            const newTimeOffsetDelta =
                              timeOffsetDelta + dragScale / 16;
                            const outOfBounds =
                              (newTimeOffsetDelta + timeOffset) * pxPerSecond >
                              clipsContainerLeft;

                            if (!outOfBounds) {
                              setTimeOffsetDelta(newTimeOffsetDelta);
                            }
                          }
                          if (
                            dragPositionWithinContainer < clipsContainerRight &&
                            dragPositionWithinContainer > dragAreaRight
                          ) {
                            const dragScale =
                              (dragPositionWithinContainer - dragAreaRight) /
                              (clipsContainerRight - dragAreaRight);
                            const newTimeOffsetDelta =
                              timeOffsetDelta - dragScale / 16;
                            const lastClipRight =
                              timelineClipRefs.current[
                                timelineClipRefs.current.length - 1
                              ]?.getBoundingClientRect().right;
                            const outOfBounds =
                              lastClipRight < clipsContainerRight - 20;

                            if (!outOfBounds) {
                              setTimeOffsetDelta(newTimeOffsetDelta);
                            }
                          }
                        }
                      }}
                    >
                      <div
                        className="timeline-editor__timeline-clips-clip-top"
                        style={{
                          width: trackletLengthPx,
                          display: hideClipToolbarLeft[i] ? 'none' : 'flex',
                        }}
                      >
                        <div
                          className="timeline-editor__timeline-clips-clip-title"
                          style={{
                            display: hideClipToolbarLeft[i] ? 'none' : 'flex',
                          }}
                        >
                          {`Tracklet ${i + 1}`}
                        </div>
                        <div
                          className="timeline-editor__timeline-clips-clip-right"
                          style={{
                            display: hideClipToolbarRight[i] ? 'none' : 'flex',
                          }}
                        >
                          <div className="timeline-editor__timeline-clips-clip-length">
                            {trackletLengthSec < 1
                              ? `${Duration.fromObject({
                                  milliseconds: trackletLengthSec * 1000,
                                }).toFormat('SSS')}ms`
                              : Duration.fromObject({
                                  seconds: trackletLengthSec,
                                }).toFormat('mm:ss')}
                          </div>
                          <div
                            className="timeline-editor__timeline-clips-clip-delete material-icons"
                            onClick={() => {
                              const newTimeline = [...timeline];
                              newTimeline.splice(i, 1);
                              handleSetTimeline(newTimeline);
                            }}
                          >
                            close
                          </div>
                        </div>
                      </div>
                      <div className="timeline-editor__timeline-clips-clip-img-container">
                        {/* {range(Math.ceil(trackletLengthPx / 30)).map((j) => <img key={`ClipImg-${j}-${i}`} className="timeline-editor__timeline-clips-clip-img" src={timelineTracklet.thumbnailUrls.best} />)}  */}
                      </div>
                    </div>
                  </Fragment>
                );
              })}
            {(!timeline || timeline?.length === 0) &&
              range(10).map((i) => (
                <div
                  key={`TimelineEditor-Clip-${i}`}
                  data-testid={`TimelineEditorEmpty-Clip-${i}`}
                  className={cn('timeline-editor__timeline-clips-clip', {
                    'first-tracklet': dragOverNewTimeline,
                  })}
                  style={{
                    left: i * 240 + (i + 1) * 20,
                  }}
                  onDragEnter={() => {
                    setDragOverNewTimeline(true);
                  }}
                  onDragLeave={() => {
                    setDragOverNewTimeline(false);
                  }}
                  onDragOver={(e) => {
                    e.preventDefault();
                  }}
                  onDrop={() => {
                    if (draggingTracklet?.tracklet) {
                      const newTimeline = [draggingTracklet.tracklet];
                      handleSetTimeline(newTimeline);
                      setDraggingTracklet(undefined);
                    }
                  }}
                >
                  {i === 0 ? (
                    !dragOverNewTimeline ? (
                      <div className="timeline-editor__timeline-clips-clip-plus">
                        +
                      </div>
                    ) : (
                      'Drag a detection here to start'
                    )
                  ) : (
                    ''
                  )}
                </div>
              ))}

            <div className="timeline-editor__timeline-seconds-in-view-scaler-container">
              <div className="timeline-editor__timeline-seconds-in-view-scaler-start-handle" />
              <div className="material-icons"> remove </div>
              <Slider
                className="timeline-editor__timeline-seconds-in-view-scaler"
                disabled={timeline.length === 0}
                min={1}
                max={videoLengthSeconds * 1000}
                defaultValue={DEFAULT_SECONDS_IN_VIEW * 1000}
                valueLabelDisplay="auto"
                value={secondsInView * 1000}
                onChange={(_, v) =>
                  setSecondsInView(
                    Array.isArray(v) ? DEFAULT_SECONDS_IN_VIEW : v / 1000
                  )
                }
                valueLabelFormat={(value) => (
                  <div>
                    {Math.round((value / (videoLengthSeconds * 1000)) * 100)}%
                  </div>
                )}
              />
              <div className="material-icons">add</div>
            </div>
          </div>
          <div className="timeline-editor__timeline-container-right-border" />
        </div>
      </div>
    </div>
  );
};

export default TimelineEditor;
