jest.mock('axios', () => ({ request: jest.fn() }));
import axios from 'axios';
import HttpClient from '../httpClient';

const env = {};

describe('HttpClient', () => {
  let http: HttpClient;
  beforeEach(() => {
    http = new HttpClient(env);
  });

  describe('buildUrl', () => {
    it('builds a url based on href', () => {
      const relativeUrl = '/my-uri';
      const actual = http.buildUrl(relativeUrl);

      expect(actual).toEqual('http://localhost/api/v1/my-uri');
    });

    it('inserts a slash when the relativeUrl does not contain one', () => {
      const relativeUrl = 'my-uri';
      const actual = http.buildUrl(relativeUrl);

      expect(actual).toEqual('http://localhost/api/v1/my-uri');
    });
  });

  describe('buildHeaders', () => {
    it('includes ad-hoc headers', () => {
      const headers = http.buildHeaders({ 'x-cool-header': 'cool header' });

      expect(headers['x-cool-header']).toEqual('cool header');
    });
  });

  describe('requests', () => {
    beforeEach(() => {
      http.makeRequest = jest.fn();
    });

    it('makes a get request', () => {
      http.get()('/my-uri', {});
      expect(http.makeRequest).toHaveBeenCalledWith({
        config: {},
        relativeUrl: '/my-uri',
        headers: {},
        method: 'get',
      });
    });

    it('makes a post request', () => {
      const body = { a: '123' };

      http.post()('/my-uri', body, {});
      expect(http.makeRequest).toHaveBeenCalledWith({
        relativeUrl: '/my-uri',
        headers: {},
        method: 'post',
        body,
      });
    });

    it('makes a patch request', () => {
      const body = { a: '123' };

      http.patch()('/my-uri', body, {});
      expect(http.makeRequest).toHaveBeenCalledWith({
        relativeUrl: '/my-uri',
        headers: {},
        method: 'patch',
        body,
      });
    });

    it('makes a delete request', () => {
      const body = { a: '123' };

      http.delete()('/my-uri', body, {});
      expect(http.makeRequest).toHaveBeenCalledWith({
        relativeUrl: '/my-uri',
        headers: {},
        method: 'delete',
        body,
      });
    });
  });

  describe('direct requests', () => {
    it('makes a raw request', () => {
      const params = {
        url: 'http://my-url.com',
        headers: { 'x-cool-header': 'neato' },
        data: { a: '123' },
        method: 'post',
      };

      http.raw(params);
      expect(axios.request).toHaveBeenCalledWith(
        expect.objectContaining(params)
      );
    });

    it('makes a raw request with default params', () => {
      const params = {
        url: 'http://my-url.com',
      };

      http.raw(params);
      expect(axios.request).toHaveBeenCalledWith(
        expect.objectContaining({ ...params, data: {}, headers: {}, method: 'GET' }));
    });

    it('wraps a request with the baseUrl and prebuilt headers', () => {
      const params = {
        relativeUrl: '/my-url',
        headers: { 'x-cool-header': 'neato' },
        data: { a: '123' },
        method: 'post',
      };

      const expectedParams = {
        url: 'http://localhost/api/v1/my-url',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          ...params.headers,
        },
        data: undefined,
        maxRedirects: 0,
        // TODO: Unclear to me how this would have ever been true
        // withCredentials: true,
        withCredentials: false,
        method: params.method,
      };

      http.makeRequest(params);
      expect(axios.request).toHaveBeenCalledWith(expectedParams);
    });
  });
});
