import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes(queries.getFileTemporalData)) {
        return Promise.resolve({
          temporalDataObject: {
            id: 'fileId',
            details: {
              veritoneFile: {
                fileName: 'fileName',
                duration: 100,
                fileType: 'fileType',
                fileSize: 1000,
              },
              createdDateTime: 'createdDateTime',
            },
          },
        });
      }
    }
  ),
}));

describe('get file', () => {
  it('get file', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/file/an-file-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.getFileTemporalData,
      {
        tdoId: 'an-file-id',
      }
    );
  });

  it('cannot find file w/o fileId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .get('/api/v1/file')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
