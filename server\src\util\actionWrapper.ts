// Useful code for debugging
const DEBUG_LOGS = true;
const DEBUG_TIMES = false;

// function actionWrapper<T extends (ctx: any, ...args: any[]) => any>(
//   actionFn: T
// ): T {
//   return function (context: any, ...args: any[]) {
//     const start = Date.now();
//     if (DEBUG_LOGS && !DEBUG_TIMES) {
//       console.log(actionFn.name);
//     }
//     const result = actionFn(context, ...args);
//     const end = Date.now();
//     if (DEBUG_TIMES) {
//       console.log(`${actionFn.name} took ${end - start}ms`);
//     }
//     return result;
//   } as unknown as T;
// }

// Wrapper function - use of any is not ideal, but necessary for this example
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function actionWrapper<T extends (...args: any[]) => any>(actionFn: T): T {
  return function (...args: Parameters<T>): ReturnType<T> {
    const start = Date.now();
    if (DEBUG_LOGS && !DEBUG_TIMES) {
      console.log(actionFn.name);
    }
    const result = actionFn(...args);
    const end = Date.now();
    if (DEBUG_TIMES) {
      console.log(`${actionFn.name} took ${end - start}ms`);
    }
    return result;
  } as T;
}

export default actionWrapper;
