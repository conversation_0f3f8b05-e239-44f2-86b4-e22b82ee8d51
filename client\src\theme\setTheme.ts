export const setDarkMode = (isDarkMode: boolean) => {
  // Map colors
  if (isDarkMode) {
    document.querySelector('body')?.classList.add('dark');
    document.querySelector('body')?.classList.remove('light');
  } else {
    document.querySelector('body')?.classList.add('light');
    document.querySelector('body')?.classList.remove('dark');
  }
};

export const detectThemeChange = () => {
  if (window.matchMedia('(prefers-color-scheme: dark)').addEventListener) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => setDarkMode(e.matches));
  } else if (window.matchMedia('(prefers-color-scheme: dark)').addListener) {
    window.matchMedia('(prefers-color-scheme: dark)').addListener(e => setDarkMode(e.matches));
  }
};

export const initTheme = () => {
  // TODO:  any reason for this?
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const theme = localStorage.getItem('theme');
  setDarkMode(false);

  // Uncomment for theme detection
  // if (theme === 'Dark') {
  //   setDarkMode(true);
  // } else if (theme === 'Light') {
  //   setDarkMode(false);
  // } else {
  //   const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  //   setDarkMode(isDark);
  //   detectThemeChange();
  // }
};
