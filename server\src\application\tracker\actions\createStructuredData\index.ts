import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';
import * as ResTypes from '../../../../../../types/responses';
import env from '../../../../env';

const createStructuredData = async <
  ReqPayload,
  Data extends Partial<
    ResTypes.CreateEventPayloadResponse &
      responses.getMe &
      responses.createFolder
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          ResTypes.CreateEventPayloadResponse &
            responses.getMe &
            responses.createFolder &
            responses.createStructuredData
        >
    >
  | undefined
> => {
  const { cache, data, req, log } = context;
  const headers = { Authorization: req.headers.authorization };

  const {
    name,
    userId,
    firstName,
    lastName,
    eventStartDate,
    eventEndDate,
    createFolder,
    currentTime,
  } = data;
  const description = data.description ?? '';

  if (
    !name ||
    !userId ||
    !eventStartDate ||
    !eventEndDate ||
    !createFolder ||
    !currentTime
  ) {
    throw new ActionError(
      `Missing required data: ${[!name ? 'name' : '', !userId ? 'userId' : '', !eventStartDate ? 'eventStartDate' : '', !eventEndDate ? 'eventEndDate' : '', !createFolder ? 'createFolder' : '', !currentTime ? 'currentTime' : ''].filter(Boolean).join(', ')}`
    );
  }
  const schemaId = cache.get('eventsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    const { createStructuredData } = await callGQL<
      responses.createStructuredData,
      ReqPayload,
      Data
    >(context, headers, queries.createStructuredData, {
      id: '',
      schemaId,
      data: {
        id: createFolder.id,
        name,
        description,
        createdBy: userId,
        createdByName: `${firstName} ${lastName}`,
        eventStartDate,
        eventEndDate,
        tags: [],
        trackerEngineId: env.trackerEngineId,
        createdDateTime: currentTime,
        modifiedDateTime: currentTime,
      },
    });

    if (createStructuredData) {
      const new_data = Object.assign({}, data, { createStructuredData });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createStructuredData;
