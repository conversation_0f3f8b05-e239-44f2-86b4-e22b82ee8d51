import { GetSessionTokenCommand, STSClient } from '@aws-sdk/client-sts';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import env from '../../../../env';
import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import NodeCache from 'node-cache';
import ActionValidationError from '@common/errors/ActionValidationError';

const EXPIRE_IN_SECS = 3600 * 6;

const CACHE_SEC = 3600;
const s3WritableCredentialCacheKey = 's3WritableCredentialCacheKey';
const s3bReadOnlyCredentialCacheKey = 's3bReadOnlyCredentialCacheKey';

export async function generateS3WritableCredential(cache?: NodeCache) {
  const s3 = env.s3;
  if (!s3) {
    throw new ActionValidationError('S3 config not found');
  }
  let credential: {
    accessKeyId: string;
    secretAccessKey: string;
    sessionToken: string;
  } = JSON.parse(cache?.get(s3WritableCredentialCacheKey) ?? '{}');

  // Writable STS token will be passed to engine. The expiration is configured
  // in datacenter config.
  if (!credential.sessionToken) {
    credential = await generateS3TempCredential({
      bucketName: s3.bucket,
      accessKey:
        process?.env?.AWS_S3_ACCESS_KEY?.replace(/(\r\n|\n|\r)/gm, '') ||
        s3.accessKey,
      secretKey:
        process?.env?.AWS_S3_SECRET_KEY?.replace(/(\r\n|\n|\r)/gm, '') ||
        s3.secretKey,
      region: s3.region,
      expireSecs: s3.expireSecs,
      writable: true,
    });
    cache?.set(
      s3WritableCredentialCacheKey,
      JSON.stringify(credential),
      CACHE_SEC
    );
  }
  return {
    storage: 's3',
    bucket: s3.bucket,
    region: s3.region,
    credential,
  };
}

export async function generateS3ReadOnlyCredential(cache?: NodeCache) {
  const s3 = env.s3;
  if (!s3) {
    throw new ActionValidationError('S3 config not found');
  }
  let credential: {
    accessKeyId: string;
    secretAccessKey: string;
    sessionToken: string;
  } = JSON.parse(cache?.get(s3bReadOnlyCredentialCacheKey) ?? '{}');

  // Readonly STS token is used for signed url for frontend, so it needs to set to
  // a value greater that expiration of signed url.
  // No value is passed, so the default value EXPIRE_IN_SECS + CACHE_SEC is used.
  if (!credential.sessionToken) {
    credential = await generateS3TempCredential({
      bucketName: s3.bucket,
      accessKey:
        process?.env?.AWS_S3_ACCESS_KEY?.replace(/(\r\n|\n|\r)/gm, '') ||
        s3.accessKey,
      secretKey:
        process?.env?.AWS_S3_SECRET_KEY?.replace(/(\r\n|\n|\r)/gm, '') ||
        s3.secretKey,
      region: s3.region,
    });

    cache?.set(
      s3bReadOnlyCredentialCacheKey,
      JSON.stringify(credential),
      CACHE_SEC
    );
  }
  return {
    storage: 's3',
    bucket: s3.bucket,
    region: s3.region,
    credential,
  };
}

export async function generateS3TempCredential({
  bucketName: _bucketName,
  accessKey,
  secretKey,
  region,
  expireSecs = EXPIRE_IN_SECS + CACHE_SEC, // default expire time is url expire time + cache time. AWS Max value is 36 hours.
  writable: _writable = false,
}: {
  bucketName: string;
  accessKey?: string;
  secretKey?: string;
  region: string;
  expireSecs?: number;
  writable?: boolean;
}) {
  // accessKey && secretKey not set, use defaultProvider
  let cred;
  if (accessKey && secretKey) {
    console.info(
      `generateS3TempCredential: use accessKey or secretAccessKey for credential`
    );
    cred = {
      accessKeyId: accessKey,
      secretAccessKey: secretKey,
    };
  } else {
    console.info(
      `generateS3TempCredential: no accessKey or secretAccessKey found, defaultProvider is used`
    );
    cred = defaultProvider();
  }
  const stsClient = new STSClient({
    credentials: cred,
    region,
  });
  const resp = await stsClient.send(
    new GetSessionTokenCommand({
      DurationSeconds: expireSecs,
    })
  );
  const credentials = resp.Credentials;
  if (
    !credentials?.AccessKeyId ||
    !credentials?.SecretAccessKey ||
    !credentials?.SessionToken
  ) {
    throw new ActionValidationError('Credentials not created');
  }
  return {
    accessKeyId: credentials.AccessKeyId,
    secretAccessKey: credentials.SecretAccessKey,
    sessionToken: credentials.SessionToken,
  };
}

export async function signedUrl({
  bucketName,
  key,
  accessKey,
  secretKey,
  region,
  sessionToken,
  expireSecs = EXPIRE_IN_SECS,
  writable = false,
}: {
  bucketName: string;
  key: string;
  accessKey: string;
  secretKey: string;
  region: string;
  sessionToken?: string;
  expireSecs?: number;
  writable?: boolean;
}) {
  const s3Client = new S3Client({
    region,
    credentials: {
      accessKeyId: accessKey,
      secretAccessKey: secretKey,
      sessionToken,
    },
  });
  let command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });
  if (writable) {
    command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
  }
  const url = await getSignedUrl(s3Client, command, { expiresIn: expireSecs });
  return url;
}
