.event {
  position: relative;
  height: 100%;
  min-height: 620px;
  background-color: var(--event-page-background);

  .event__search-heading {
    padding: 0 30px;

    .event__search-heading-title {
      @include size-4;
    }

    .event__search-heading-items-returned {
      @include size-3;

      margin-top: 10px;
      color: var(--text-secondary);
    }
  }

  .event__main-content {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    height: 100%;
  }

  .event__tables {
    width: 50%;
    display: flex;
    flex-direction: column;
  }

  .MuiTabs-root {
    margin-left: 30px;
    margin-top: 22px;
  }

  .MuiTab-root {
    min-height: 0;
  }

  .event__tab-container {
    margin-top: 20px;
    padding: 0 0 0 30px;
    overflow: auto;
    height: 100%;

    .event__pending-upload-files {
      max-height: 185px;
      overflow: hidden auto;   
      margin-bottom: 25px;
    }
  }

  .event__match-groups-table {
    border-top: none;
  }

  .divider {
    margin-top: 0;
    border-top: var(--divider) solid 1px;
    margin-bottom: 0;
    background-color: var(--divider);
    min-height: 2px;
  }

  .event__heading-container {
    .event__heading-tags {
      width: 50%;

      .MuiInputBase-root {
        border-radius: 6px;

        &.MuiOutlinedInput-root {
          display: flex;
          gap: 5px;
          padding: 5px;
        }

        .MuiButtonBase-root {
          color: black;
          height: 25px;
          margin: 0;

          .MuiChip-labelMedium {
            @include size-1;
          }

          .MuiButtonBase-root {
            font-size: 18px;
          }
        }

        .MuiAutocomplete-input {
          padding: 0;

          &:first-child {
            margin-left: 8px;
          }
        }
      }
    }

    .event__heading-name-date {
      display: block;
      padding: 0 30px;

      .event__tables-date-and-created-by-container {
        display: flex;
        align-items: center;
        gap: 14px;
        margin-top: 12px;

        .event__heading-date {
          line-height: 24px;
          color: var(--text-primary);

          @include size-2;
        }

        .event__heading-created-by {
          line-height: 24px;
          color: var(--text-tertiary);

          @include size-2-thin;
        }

        .event__heading-created-by-name {
          line-height: 24px;
          margin-left: 4px;
          color: var(--text-primary);

          @include size-2-bold;
        }

        .event__tables-tags-and-actions {
          display: flex;
          align-items: flex-start;
          padding-left: 0;
          margin-top: 0;
          gap: 14px;

          .event__tables-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .event__tables-tag {
              color: var(--icon-selector);

              @include size-1-bold;
            }
          }
        }
      }

      .event__heading-name {
        margin-right: 10px;

        @include size-4;

        font-weight: 600;

        .event__tables-name-textfield {
          width: 200px;
        }
      }
    }

    .event__tables-desc-container {
      display: flex;
      align-items: center;
      padding-left: 30px;
      gap: 8px;
      margin-top: 12px;

      .event__tables-desc {
        line-height: 22px;

        @include size-3-thin;
      }

      .event__tables-desc-textfield {
        width: 414px;
      }

      .material-icons {
        font-size: 14px;
        margin-bottom: 3px;
        color: var(--icon-edit);
        cursor: pointer;
      }
    }

    .event__tables-tags-and-actions {
      display: flex;
      align-items: flex-start;
      padding-left: 30px;
      margin-top: 16px;

      .event__tables-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .event__tables-tag {
          color: var(--icon-selector);

          @include size-1-bold;
        }
      }
    }

    .event__tables-actions {
      display: flex;
      align-items: center;
      gap: 4px;

      .event-icon {
        font-size: 28px;
        cursor: pointer;

        .svg-icon {
          fill: var(--icon-selector);
        }

        &:hover {
          .svg-icon {
            fill: var(--text-tertiary);
          }
        }
      }
    }
  }

  .event__tab-container-no-results {
    position: relative;
    margin-top: 170px;
  }

  .event__file-view {
    &.disabled {
      opacity: 0.5;
    }
  }

  .event__detail {
    width: 50%;
    height: 100%;
    min-width: 580px;
    overflow: auto;

    .event__detail-content {
      display: flex;
      flex-direction: column;
    }

    .event__detail-container {
      flex-shrink: 1;
      overflow: auto;
      border: solid 1px var(--divider);
      border-radius: 8px;
      margin-right: 30px;
      margin-bottom: 50px;

      &.hide {
        display: none;
      }

      .event__detail-preview-container {
        position: relative;

        .event__detail-preview {
          height: 100%;
        }

        .event__detail-preview-overlay {
          position: absolute;
          top: 40%;
          left: 0;
          height: 60%;
          width: 100%;
          background: linear-gradient(to bottom, transparent, #000000bd);
        }

        .material-icons {
          user-select: none;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 80px;
          z-index: 10;
          background-color: transparent;
          opacity: 0.8;
        }
      }

      .event__detail-info {
        position: relative;
        padding: 20px;

        .event__detail-file-name-container {
          display: flex;
          align-items: center;
          gap: 8px;

          .event__detail-file-name-textfield {
            padding: 0;
          }
  
          .event__detail-file-name {
            line-height: 22px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
  
          .material-icons {
            font-size: 14px;
            margin-bottom: 3px;
            color: var(--icon-edit);
            cursor: pointer;
          }
        }
      }

      .event__detail-container-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        height: calc(100% - 30px);
        color: var(--disabled);
        margin: 14px 0;

        @include size-1;
      }

      .event__detail-info-text {
        @include size-1;

        line-height: 16px;
        margin-top: 20px;
        display: flex;

        span:first-child {
          color: var(--text-tertiary);
          width: 22%;
        }
      }

      .event__detail-files-match-group-container {
        background-color: var(--row-active);
        padding: 18px 20px;
        margin-top: 11px;
      }

      .event__detail-actions {
        display: flex;
        justify-content: space-evenly;
        border-top: solid 1px var(--divider);
        padding: 10px 0;

        span {
          color: var(--text-primary);
          text-transform: none;

          @include size-2;
        }

        img {
          width: 18px;
          margin-right: 10px;
        }
      }
    }
  }

  .MuiButtonBase-root {
    &.MuiAccordionSummary-root {
      &.Mui-expanded {
        background-color: var(--row-active);
      }
    }
  }

  .MuiAccordionDetails-root {
    .table {
      border: none;
    }
  }
}
