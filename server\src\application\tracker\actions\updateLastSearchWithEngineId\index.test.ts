import updateLastSearchWithEngineId from './index';
import { Context } from '../../../types';
import { MatchGroup } from '../../../../../../types/tracker';
import { createRequest, createResponse } from 'node-mocks-http';
import consoleLogger from '../../../../logger';
import NodeCache from 'node-cache';
import GQLApi from '../../../../util/api/graphQL';

let cxt: Context<object, Partial<Pick<MatchGroup, 'searches'>>>;

jest.mock('../../../../env', () => ({
  trackerEngineId: 'test-engine-id',
}));

describe('updateLastSearchWithEngineId', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        searches: [],
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('should add engineId to the last search if it does not have one', async () => {
    cxt.data = {
      searches: [
        {
          attributes: {
            person: [
              {
                key: 'Accessory',
                label: 'AccessoryBagAny',
                value: 'BagAny',
              },
              {
                key: 'Accessory',
                label: 'AccessoryHandbag',
                value: 'Handbag',
              },
            ],
          },
          id: '',
          searchName: '',
        },
        {
          attributes: {
            person: [
              {
                key: 'Accessory',
                label: 'AccessoryBagAny',
                value: 'BagAny',
              },
            ],
          },
          id: '',
          searchName: '',
        },
      ],
    };

    const result = await updateLastSearchWithEngineId(cxt);

    expect(result.data.searches?.[1]?.engineId).toBe('test-engine-id');
  });

  it('should not modify the last search if it already has an engineId', async () => {
    cxt.data = {
      searches: [
        {
          attributes: {
            person: [
              {
                key: 'Accessory',
                label: 'AccessoryBagAny',
                value: 'BagAny',
              },
            ],
          },
          engineId: 'existing-engine-id',
          id: '',
          searchName: '',
        },
      ],
    };

    const result = await updateLastSearchWithEngineId(cxt);

    expect(result.data.searches?.[0]?.engineId).toBe('existing-engine-id');
  });

  it('should not modify the last search if it does not have attributes', async () => {
    cxt.data = {
      searches: [
        {
          referenceTrackletId: 'referenceTrackletId',
          id: '',
          searchName: '',
        },
      ],
    };

    const result = await updateLastSearchWithEngineId(cxt);

    expect(result.data.searches?.[0]?.engineId).toBeUndefined();
  });
});
