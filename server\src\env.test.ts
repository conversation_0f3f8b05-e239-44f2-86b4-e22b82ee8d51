import path from 'path';
import { loadEnvironment } from './env';
const fs = require('fs');

describe('Env', () => {
  it('defaults to gpu cluster id', () => {
    const config = {
      ...require('../apiConfig.json'),
      gpuClusterId: 'my-gpu-cluster-id',
      cpuClusterId: 'my-cpu-cluster-id',
      defaultClusterId: 'my-default-cluster-id',
    };
    jest
      .spyOn(fs, 'readFileSync')
      .mockImplementationOnce(() => JSON.stringify(config));

    const env = loadEnvironment();

    expect(env.gpuClusterId).toBe('my-gpu-cluster-id');
  });

  it('defaults to cpu cluster id', () => {
    const config = {
      ...require('../apiConfig.json'),
      gpuClusterId: 'my-gpu-cluster-id',
      cpuClusterId: 'my-cpu-cluster-id',
      defaultClusterId: 'my-default-cluster-id',
    };
    jest
      .spyOn(fs, 'readFileSync')
      .mockImplementationOnce(() => JSON.stringify(config));

    const env = loadEnvironment();

    expect(env.cpuClusterId).toBe('my-cpu-cluster-id');
  });

  it('cpu fallback is default cluster id', () => {
    const config = {
      ...require('../apiConfig.json'),
      defaultClusterId: 'my-default-cluster-id',
    };
    delete config.cpuClusterId;
    delete config.gpuClusterId;
    jest
      .spyOn(fs, 'readFileSync')
      .mockImplementationOnce(() => JSON.stringify(config));

    const env = loadEnvironment();

    expect(env.gpuClusterId).toBe('my-default-cluster-id');
  });

  it('gpu fallback is default cluster id', () => {
    const config = {
      ...require('../apiConfig.json'),
      defaultClusterId: 'my-default-cluster-id',
    };
    delete config.cpuClusterId;
    delete config.gpuClusterId;
    jest
      .spyOn(fs, 'readFileSync')
      .mockImplementationOnce(() => JSON.stringify(config));

    const env = loadEnvironment();

    expect(env.gpuClusterId).toBe('my-default-cluster-id');
  });
});
