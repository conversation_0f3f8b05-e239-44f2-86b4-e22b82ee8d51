import joi from 'joi';

const trackletSchema = joi.object().keys({
  trackletId: joi.string(),
  fileId: joi.string(),
  fileName: joi.string(),
  startTimeMs: joi.number(),
  stopTimeMs: joi.number(),
  attributes: joi.object().keys({
    accessories: joi.array().items(joi.string()).optional(),
    body: joi.array().items(joi.string()).optional(),
    face: joi.array().items(joi.string()).optional(),
    footwear: joi.array().items(joi.string()).optional(),
    gender: joi.array().items(joi.string()).optional(),
    hair: joi.array().items(joi.string()).optional(),
    upper: joi.array().items(joi.string()).optional(),
    lower: joi.array().items(joi.string()).optional(),
  }),
  thumbnailUrls: joi
    .object()
    .keys({
      best: joi.string().optional(),
    })
    .optional(),
  type: joi.string(),
});

const timelineProjectSchema = joi.object().keys({
  groups: joi.array().items(
    joi
      .object()
      .keys({
        id: joi.string(),
        name: joi.string(),
        tracklets: joi.array().items(trackletSchema),
      })
      .options({ presence: 'required' })
  ),
  modifiedDateTime: joi.string(),
  modifiedUserId: joi.string(),
});

const generatedTimeLineSchema = joi.object().keys({
  id: joi.string(),
  timeline: timelineProjectSchema,
  name: joi.string(),
  createdDateTime: joi.string(),
  createdUserId: joi.string(),
  tdoId: joi.string(),
  resolution: joi.string(),
  outputFormat: joi.string(),
  videoLengthMs: joi.number(),
  videoSizeBytes: joi.number(),
});

const schemas = {
  post: {
    body: {
      ingest: joi.object().keys({
        fileType: joi.string().required(),
        getUrl: joi.string().required(),
      }),
      event: joi.object().keys({
        name: joi.string().required(),
        description: joi.string().allow(''),
        eventStartDate: joi.string().required(),
        eventEndDate: joi.string().required(),
      }),
      file: joi.object().keys({
        eventId: joi.string().required(),
        fileName: joi.string().required(),
        fileType: joi.string().required(),
      }),
      matchGroup: joi.object().keys({
        name: joi.string().required(),
        eventId: joi.string().required(),
        timelineProject: timelineProjectSchema,
        generatedTimelines: joi.array().items(generatedTimeLineSchema),
      }),
      tag: joi.object().keys({
        eventId: joi.string().required(),
        tag: joi.string().required(),
      }),
      thumbnails: joi.object().keys({
        tracklets: joi
          .array()
          .items(
            joi.object().keys({
              trackletId: joi.string().required(),
              orgId: joi.number().required(),
              fileId: joi.number().required(),
            })
          )
          .required(),
      }),
    },
  },
  patch: {
    body: {
      event: joi.object().keys({
        name: joi.string(),
        description: joi.string().allow(''),
        tags: joi.array().items(joi.string()),
        eventStartDate: joi.string(),
        eventEndDate: joi.string(),
      }),
      file: joi.object().keys({
        name: joi.string(),
      }),
      matchGroup: joi.object().keys({
        name: joi.string(),
        searches: joi.array().items(
          joi
            .object()
            .keys({
              id: joi.string().required(),
              referenceTrackletId: joi.string(),
              searchName: joi.string().required(),
              attributes: joi.object().keys({
                person: joi.array().items(
                  joi.object().keys({
                    label: joi.string(),
                    value: joi.string(),
                    key: joi.string(),
                  })
                ),
                vehicle: joi.array().items(
                  joi.object().keys({
                    label: joi.string(),
                    value: joi.string(),
                    key: joi.string(),
                  })
                ),
              }),
              engineId: joi.string(),
            })
            .xor('referenceTrackletId', 'attributes')
        ),
        selectedTracklets: joi.array().items(joi.string()),
        timelineProject: timelineProjectSchema,
        generatedTimelines: joi.array().items(generatedTimeLineSchema),
        generatedTimelineName: joi.string(),
      }),
    },
  },
  delete: {
    query: {
      timeline: joi.object().keys({
        generatedTimelineId: joi.string().required(),
      }),
      _events: joi.object().keys({
        code: joi.string(),
      }),
    },
  },
  get: {
    query: {
      events: joi.object().keys({
        pageSize: joi.number(),
        currentPage: joi.number(),
        event: joi.string(),
        tag: joi.string(),
        sortBy: joi.string(),
        sortDirection: joi.string(),
      }),
      files: joi.object().keys({
        pageSize: joi.number(),
        currentPage: joi.number(),
        file: joi.string(),
        sortBy: joi.string(),
        sortDirection: joi.string(),
        eventId: joi.string(),
      }),
      matchGroups: joi.object().keys({
        pageSize: joi.number(),
        currentPage: joi.number(),
        sortDirection: joi.string(),
        sortType: joi.string(),
        eventId: joi.string(),
      }),
      matchGroupSearch: joi.object().keys({
        pageSize: joi.number(),
        currentPage: joi.number(),
        fileIds: joi.array().items(joi.string()),
        type: joi.string(),
        threshold: joi.string(),
      }),
      fileResults: joi.object().keys({
        pageSize: joi.number(),
        currentPage: joi.number(),
        fileId: joi.string(),
        eventId: joi.string(),
        type: joi.string(),
        attributes: joi.object().pattern(
          joi.string(), // Keys
          joi.array().items(joi.string()) // Values
        ),
      }),
      boundingBoxes: joi
        .object()
        .keys({
          file: joi.string(),
          trackletId: joi.string(),
          fileId: joi.string(),
          type: joi.string(),
          startTimeMs: joi.number(),
          stopTimeMs: joi.number(),
        })
        .or('trackletId', 'fileId'),
    },
  },
};

export { schemas };
