import '@testing-library/jest-dom';
require('jest-fetch-mock').enableMocks();

Object.defineProperty(window, 'config', {
  writable: true,
  value: { nodeEnv: 'test' }
});

jest.mock('@veritone/glc-redux', () => ({
  modules: {
    user: {
      fetchUser: jest.fn(() => ({})),
      //   fetchEnabledApps,
      FETCH_USER: 'FETCH_USER',
      FETCH_USER_SUCCESS: 'FETCH_USER_SUCCESS',
      FETCH_USER_FAILURE: 'FETCH_USER_FAILURE',
      FETCH_USER_APPLICATIONS: 'FETCH_USER_APPLICATIONS',
      FETCH_USER_APPLICATIONS_SUCCESS: 'FETCH_USER_APPLICATIONS_SUCCESS',
      FETCH_USER_APPLICATIONS_FAILURE: 'FETCH_USER_APPLICATIONS_FAILURE',
      LOGOUT: 'LOGOUT',
      LOGOUT_FAILURE: 'LOGOUT_FAILURE',
      LOGOUT_SUCCESS: 'LOGOUT_SUCCESS',
      selectUser: jest.fn(),
      // eslint-disable-next-line lodash/prefer-constant
      userIsAuthenticated: () => true
    },
    auth: {
      authRootSaga: jest.fn(),
      setOAuthToken: jest.fn((params) => params),
      OAUTH_GRANT_FLOW_SUCCESS: 'OAUTH_GRANT_FLOW_SUCCESS',
    },
    config: {
      getConfig: jest.fn(() => ({})),
    },
  },
  helpers: {
    createReducer: jest.fn(),
  },
}));

export class MockFile {
  constructor({ name = 'mock.jpg', size = 1024, type = 'image/jpg' }) {
    const blob = new Blob(['a'.repeat(size)], { type });

    return new File([blob], name);
  }
}

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

jest.setTimeout(20000);
