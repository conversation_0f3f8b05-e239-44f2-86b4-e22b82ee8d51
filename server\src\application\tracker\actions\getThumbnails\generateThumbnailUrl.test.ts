import { generateThumbnailUrl } from './generateThumbnailUrl';
describe('trackletTimeSearch', () => {
  // This is a driver programe to help troubleshooting buildThumbnailUrl with real data.
  // It is disabled on purpose. To enable it, remove the 'x' before 'it'.
  // Provide orgId, tdoId, and referenceId for troubleshooting. (the s3 info is in apiConfig.json)
  xit('driver programe to create thumbnail url', async () => {
    const orgId = '46571';
    const tdoId = '3370037746';
    const trackletId = '5341c0cf-a5f7-4fa5-a5d3-20baa5064f7a';
    const type = 'best';
    const got = await generateThumbnailUrl({ orgId, tdoId, trackletId, type });
    console.log('====>', got);
    expect(got).not.toBeNull();
  }, 10000);
});
