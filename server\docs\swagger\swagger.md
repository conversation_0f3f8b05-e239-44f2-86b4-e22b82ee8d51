# API Documentation

This project uses **openapi-types** with **swagger-ui-express** to serve API documentation at the `/api-docs` endpoint, available only in the development environment.

## Installation

Install the required packages:

```bash
yarn add openapi-types swagger-ui-express
```

## Set up

Custom options for title and css:

```bash
const customOptions = {
    customSiteTitle: 'Track API',
    customCss: `.topbar { display: none ; };`,
};
```

Set up swagger document:

```bash
const buildUrl = (url: string): OpenAPIV3.ServerObject => ({ url: `${url}` });
const getUrls = () => {
    const urls: OpenAPIV3.ServerObject[] = [];
    if (env?.nodeEnv === 'development') {
        urls.push(buildUrl('https://local.veritone.com:3002'));
    } else if(env?.apiRoot) {
        urls.push(buildUrl(env.apiRoot.replace('api', 'track')));
    } else {
        urls.push(buildUrl('https://track.stage.us-1.veritone.com'));
    }
    return urls;
};

export const swaggerDocument: OpenAPIV3.Document = {
    openapi: '3.0.1',
    info: {
        version: '1.0.0',
        title: 'Track APIs',
        description: 'Providing backend apis to work in Track application.',
    },
    servers: getUrls(),
    security: [
        {
        bearerAuth: [],
        },
    ],
    paths: {
        // check app health
        '/api/health': {
            get: {
                summary: 'Health Check',
                responses: {
                    200: { $ref: '#/components/responses/healthCheck' },
                },
            },
        },
    },
},
```

To enable the Swagger documentation, the following route is conditionally mounted on the development environment:

```bash
if (env?.nodeEnv === 'development') {
    app.use(
        '/api-docs',
        swaggerUi.serve,
        swaggerUi.setup(swaggerDocument, customOptions)
    );
}
```

## Usage

Run local server:

```bash
yarn start:dev
```

Open api docs in browser:

[https://local.veritone.com:3002/api-docs](https://local.veritone.com:3002/api-docs)