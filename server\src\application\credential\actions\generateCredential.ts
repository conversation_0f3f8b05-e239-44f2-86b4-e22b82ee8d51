import { UnauthorizedError } from '@common/errors';
import { Context } from '../../types';
import { getCredential } from './util/credential';
import env from '../../../env';
import GQLApi from '@util/api/graphQL';

export interface getCredentialType {
  credentialResult: {
    storage: string;
    storageUrl?: string;
    bucket?: string;
    region?: string;
    credential: {
      accessKeyId?: string;
      secretAccessKey?: string;
      sessionToken?: string;
      sasToken?: string;
    };
  };
}

export async function generateCredential<ReqPayload, Data>(
  context: Context<ReqPayload, Data> & { gql: GQLApi }
): Promise<Context<ReqPayload, Data & getCredentialType>> {
  const { req, log, gql, cache } = context;

  const bearerHeader = req.get('Authorization');
  if (!bearerHeader) {
    log.error('No Authorization header');
    throw new UnauthorizedError();
  }

  const bearer = bearerHeader.split(' ');
  const bearerToken = bearer[1];
  if (!bearerToken) {
    log.error('no bearer token in Authorization header');
    throw new UnauthorizedError();
  }

  gql.setToken(bearerToken);
  const result = await getCredential({
    gql,
    token: bearerToken,
    log,
    cloud: env.cloud,
    cache,
    noCache: true,
  });
  const new_data = Object.assign({}, context.data, {
    credentialResult: result,
  });
  const new_context = Object.assign({}, context, { data: new_data });
  return new_context;
}
