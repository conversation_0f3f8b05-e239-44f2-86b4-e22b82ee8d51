import ThumbnailScaler from './ThumbnailScaler';
import { render, screen } from '@testing-library/react';
import { I18nProvider, LOCALES } from '@i18n';

describe('ThumbnailScaler', () => {
  it('renders without crashing', () => {
    const props = {
      scale: 100,
      setScale: jest.fn(),
      loading: false,
    };

    render(
      <I18nProvider locale={LOCALES.ENGLISH}>
        <ThumbnailScaler {...props} />
      </I18nProvider>
    );
    expect(screen.getByTestId('thumbnail-scaler')).toBeInTheDocument();
  });
});
