import { useEffect, useState } from 'react';
import { useAppDispatch } from '@store/hooks';
import {
  selectAttributes,
  setAttributes,
  setSelectedAttributes,
  selectSelectedAttributes,
  selectNumberOfSourceBoundingBoxes,
} from '@store/modules/file/slice';
import {
  Button,
  FormControl,
  Select,
  InputLabel,
  MenuItem,
  Checkbox,
  FormGroup,
  FormControlLabel,
  SelectChangeEvent,
  OutlinedInput,
  Chip,
} from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { NestedMenuItem } from 'mui-nested-menu';
import './FindAndFilterMatches.scss';
import FindMatchesPopover from './FindMatchesPopover/FindMatchesPopover';
import { useSelector } from 'react-redux';
import {
  getMatchGroup,
  selectMatchGroup,
  updateSelectedTracklets,
} from '@store/modules/searchResults/slice';
import { AlertLevel, createSnackNotification } from '@components/common';
import { FileFilterState } from '@store/modules/searchResults/types';
import { Tracklet, Attributes } from '@shared-types/tracker';
import { toCapitalized } from '@utility/convert';
import { uniq } from 'lodash';
import { TrackletType } from '@store/modules/file/types';
import { getAttributeValue } from '@utility/getAttributeValue';
import { I18nTranslate } from '@i18n';
import { useIntl } from 'react-intl';
import { AttributesWithCategories, transformAttributesIntoCategories } from '@utility/transformAttributesIntoCategories';

interface Props {
  isFileViewer?: boolean;
  filesFilterLoading?: boolean;
  attributeFilter?: boolean;
  trackletType?: TrackletType;
  filesFilter?: FileFilterState;
  clearAll?: boolean;
  selectedTracklets?: Tracklet[];
  handleFileFilterChange?: (value: string | string[]) => void;
  onChangeAttributes?: (value?: Attributes) => void;
  onClearAttributes?: () => void;
}

const FindAndFilterMatches = ({
  isFileViewer,
  filesFilterLoading,
  attributeFilter,
  trackletType,
  filesFilter,
  clearAll,
  selectedTracklets,
  handleFileFilterChange,
  onChangeAttributes,
  onClearAttributes,
}: Props) => {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const attributes = useSelector(selectAttributes);
  const [anchorElFindMatches, setAnchorElFindMatches] =
    useState<null | HTMLElement>(null);
  const matchGroup = useSelector(selectMatchGroup);
  const { fileNames = [], selectedFileNames = [] } = filesFilter ?? {};
  const selectedAttributes = useSelector(selectSelectedAttributes);
  const selectedAttributesByType = selectedAttributes?.[trackletType ?? 'person'];
  const numberOfSourceBoundingBoxes = useSelector(selectNumberOfSourceBoundingBoxes);

  const renderNestedMenuItems = (attributes: AttributesWithCategories, parentKey = '') => Object.keys(attributes).map((attributeKey, index) => {
    const fullKey = parentKey ? `${parentKey}${attributeKey}` : attributeKey;
    const attributeValue = attributes[attributeKey];

    if (Array.isArray(attributeValue)) {
      return (
        <NestedMenuItem
          sx={{ width: 200 }}
          key={`${attributeKey}-${index}`}
          // eslint-disable-next-line formatjs/enforce-default-message
          label={intl.formatMessage({ id: attributeKey })}
          parentMenuOpen={open}
          rightIcon={<ArrowRightIcon />}
        >
          {attributeValue.map((attributeRecord, index) => (
            <MenuItem key={`${attributeRecord.key}-${index}`} data-value={attributeRecord.value}>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Checkbox
                      sx={{ height: 20 }}
                      checked={
                        selectedAttributes?.[trackletType ?? 'person'][fullKey]?.[
                          attributeRecord.value
                        ] || false
                      }
                      onChange={handleAttributeChange(
                        fullKey,
                        attributeRecord.value
                      )}
                    />
                  }
                  label={toCapitalized(getAttributeValue(attributeRecord.value))}
                />
              </FormGroup>
            </MenuItem>
          ))}
        </NestedMenuItem>
      );
    } else if (typeof attributeValue === 'object' && attributeValue !== null) {
      return (
        <NestedMenuItem
          sx={{ width: 200 }}
          key={`${attributeKey}-${index}`}
          // eslint-disable-next-line formatjs/enforce-default-message
          label={intl.formatMessage({ id: attributeKey })}
          parentMenuOpen={open}
          rightIcon={<ArrowRightIcon />}
        >
          {renderNestedMenuItems(attributeValue, fullKey)}
        </NestedMenuItem>
      );
    } else {
      return null;
    }
});

  const attributesWithCategories = transformAttributesIntoCategories(attributes?.[trackletType ?? 'person']);

  useEffect(() => {
    dispatch(setAttributes());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const clearAttributes = () => {
    dispatch(setSelectedAttributes({
      person: {},
      vehicle: {},
    }));
    onClearAttributes?.();
  };

  const handleAttributeChange =
    (attribute: keyof Attributes, attributeOption: string) =>
      (event: React.ChangeEvent<HTMLInputElement>) => {
        const newAttributes = {
          ...selectedAttributes,
          [trackletType ?? 'person']: {
            ...selectedAttributes[trackletType ?? 'person'],
            [attribute]: {
              ...selectedAttributes[trackletType ?? 'person'][attribute],
              [attributeOption]: event.target.checked,
            },
          },
        };
        dispatch(setSelectedAttributes(newAttributes));
        onChangeAttributes?.();
      };

  const openFindMatchesPopover = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElFindMatches(event.currentTarget);
  };

  const closeFindMatchesPopover = () => {
    setAnchorElFindMatches(null);
  };

  const isFindMatchesPopoverOpen = Boolean(anchorElFindMatches);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.SyntheticEvent<Element, Event>) =>
    setAnchorEl(event.currentTarget as HTMLElement);
  const handleClose = () => setAnchorEl(null);

  const onFileFilterChange = (event: SelectChangeEvent<typeof fileNames>) => {
    const {
      target: { value },
    } = event;

    handleFileFilterChange?.(value);
  };

  const addToSelectedTracklet = () => {
    if (
      selectedTracklets &&
      selectedTracklets.length > 0 &&
      matchGroup.data?.id
    ) {
      dispatch(
        updateSelectedTracklets({
          matchGroupId: matchGroup.data.id,
          selectedTracklets,
        })
      );
      dispatch(getMatchGroup({ matchGroupId: matchGroup.data.id }));
    }
  };

  const selectedAttributeCount = Object.values(selectedAttributesByType).reduce(
    (total, attribute) =>
      total + (attribute ? Object.values(attribute).filter(Boolean).length : 0),
    0
  );

  return (
    <div
      className="file-and-filter-matches__detail"
      data-testid="file-and-filter-matches"
    >
      {clearAll && (selectedAttributeCount > 0 || selectedFileNames.length > 0) && (
        <Button
          className="file-and-filter-matches__detail-clearall-button"
          variant="text"
          onClick={isFileViewer ? clearAttributes : () => handleFileFilterChange?.([])}
          disabled={filesFilterLoading}
        >
          Clear All
        </Button>
      )}

      {filesFilter && (
        <FormControl
          sx={{ width: 230 }}
          className="file-and-filter-matches__files"
          size="small"
        >
          {((!filesFilterLoading && (!fileNames || fileNames.length === 0))) ? (
              <InputLabel>
                {I18nTranslate.TranslateMessage('noFilesAvailable')}
              </InputLabel>
            ) : (
              <InputLabel>
                {I18nTranslate.TranslateMessage('fileName')}
              </InputLabel>
            )}
          <Select
            multiple
            value={selectedFileNames}
            onChange={onFileFilterChange}
            input={<OutlinedInput label="Name" />}
            data-testid="file-and-filter-matches-filter-select"
            renderValue={(selected) => selected.join(', ')}
            disabled={filesFilterLoading || (!fileNames || fileNames.length === 0)}
          >
            {uniq(fileNames).map((file, i) => (
              <MenuItem key={file} value={file} data-testid={`file-and-filter-matches-filter-select-item-${i}`}>
                <Checkbox size="small" checked={selectedFileNames.includes(file)} data-testid={`file-and-filter-matches-filter-select-item-checkbox-${i}`} />
                {file}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
      {attributeFilter && (
        <div className="file-and-filter-matches__detail-dropdown">
          <FormControl fullWidth onClick={() => {
              if (numberOfSourceBoundingBoxes !== 0) {
                createSnackNotification(
                  AlertLevel.Info,
                  'Info',
                  'Attribute filtering is disabled during region search'
                );
              }
            }}>
            <InputLabel id="file-and-filter-dropdown">
              {I18nTranslate.TranslateMessage('filter')}
            </InputLabel>
            <Select
              disabled={numberOfSourceBoundingBoxes !== 0}
              size="small"
              labelId="file-and-filter-dropdown"
              label={intl.formatMessage({ id: 'filter', defaultMessage: 'Filter' })}
              open={open}
              value={'Attributes'}
              renderValue={() => (
                <>
                  {I18nTranslate.TranslateMessage('attributes')}
                  <Chip
                    label={selectedAttributeCount}
                    size="small"
                    sx={{
                      borderRadius: '0px',
                      marginLeft: '6px',
                      visibility:
                        selectedAttributeCount > 0 ? 'visible' : 'hidden',
                    }}
                  />
                </>
              )}
              onOpen={handleClick}
              onClose={handleClose}
              MenuProps={{
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
                transformOrigin: {
                  vertical: 'top',
                  horizontal: 'center',
                },
              }}
              sx={{
                height: 40,
              }}
              data-testid="file-and-filter-matches-attributes-select"
            >
              <MenuItem value={'Attributes'} sx={{ display: 'none' }}>
                {I18nTranslate.TranslateMessage('attributes')}
              </MenuItem>
              {renderNestedMenuItems(attributesWithCategories)}
            </Select>
          </FormControl>
        </div>
      )}
      <div
        onClick={() => {
          if (!((selectedTracklets ?? []).length > 0)) {
            createSnackNotification(
              AlertLevel.Info,
              'Info',
              `Select 1 or more people or vehicles to add to ${matchGroup?.data?.name}`
            );
          }
        }}
      >
        {!isFileViewer && (
          <Button
            className="file-and-filter-matches__add-to-button"
            data-testid="file-and-filter-matches__add-to-button"
            variant="outlined"
            onClick={addToSelectedTracklet}
            disabled={
              !((selectedTracklets ?? []).length > 0 && matchGroup.data?.name)
            }
          >
            {intl.formatMessage({ id: 'markAsVerified', defaultMessage: 'Mark as Verified' })}
          </Button>
        )}
      </div>
      <div
        onClick={() => {
          if (!((selectedTracklets ?? []).length === 1)) {
            createSnackNotification(
              AlertLevel.Info,
              'Info',
              'Select a single person or vehicle to find matches.'
            );
          }
        }}
      >
        <Button
          className="file-and-filter-matches__detail-find-button"
          data-testid="file-and-filter-matches__detail-upload-button"
          variant="outlined"
          onClick={openFindMatchesPopover}
          disabled={!((selectedTracklets ?? []).length === 1)}
        >
          {intl.formatMessage({ id: 'findMatches', defaultMessage: 'Find Matches' })}
        </Button>
      </div>
      <FindMatchesPopover
        open={isFindMatchesPopoverOpen}
        onClose={closeFindMatchesPopover}
        anchorEl={anchorElFindMatches}
      />
    </div>
  );
};

export default FindAndFilterMatches;
