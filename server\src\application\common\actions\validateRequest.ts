import { Context } from '../../types';
import { ValidationError } from '@common/errors';

const validateRequest = async <ReqPayload, Data>(
  context: Context<ReqPayload, Data>,
  allowUnknown = true
): Promise<Context<ReqPayload, Data>> => {
  const { validations } = context;
  try {
    if (validations) {
      await Promise.all(
        validations.map((validation) =>
          validation.schema.validateAsync(validation.validationData, {
            allowUnknown,
          })
        )
      );
    }
  } catch (e) {
    console.trace();
    throw new ValidationError(e.message);
  }

  return context;
};

export default validateRequest;
