import cn from 'classnames';
import './GeneratedTimelineRow.scss';
import { RowProps } from '@components/common/Table/Table';
import { GeneratedTimelineWithMatchGroup } from '@shared-types/tracker';
import {constant, get} from 'lodash';
import { DateTime } from 'luxon';
import { bytesToMb, toLocalTime } from '@utility/convert';
import { Chip } from '@mui/material';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import {AlertLevel, createSnackNotification} from "@components/common";

export interface GeneratedTimelineRowAdditionalProps {
  isPendingDeletion: (id: string) => boolean;
}

const GeneratedTimelineRow = ({
  colData,
  rowData,
  rowIndex,
  onRowClick,
  selectedId,
  additionalProps,
}: RowProps<GeneratedTimelineWithMatchGroup, GeneratedTimelineRowAdditionalProps>) => {
  const intl = useIntl();
  const { id } = rowData;
  const { isPendingDeletion } = additionalProps ?? {
    isPendingDeletion: constant(false),
  };

  const renderCell = ({ dataKey }: { dataKey: string }) => {
    switch (dataKey) {
      case 'name':
        return (
          <div role="cell">
            {rowData[dataKey]}
          </div>
        );
      case 'status': {
        const status = `${rowData[dataKey]}`;
        return (
          <div role="cell" className="GTimeline-row__cell-status">
            <Chip
              className={cn({
                'pending-deletion': isPendingDeletion(id),
                [`${rowData[dataKey]}`]: !isPendingDeletion(id),
                status
              })}
              // eslint-disable-next-line formatjs/enforce-default-message
              label={intl.formatMessage({ id: isPendingDeletion(id) ? 'pendingDeletion' : status }).toUpperCase()}
              variant="outlined"
            />
          </div>
        );
      }
      case 'videoSizeBytes':
        return (
          <div role="cell" data-testid={`timeline-row-video-size-test-id-${rowData.id}`}>
            {rowData.status === 'complete' ?
                `${bytesToMb(get(rowData, dataKey, 0))} ${intl.formatMessage({ id: 'Mb', defaultMessage: 'Mb' })}`
                : '--'}
          </div>
        );
      case 'matchGroup':
        return (
          <div role="cell" className={"GTimeline-row__cell-match-group"}>
            {get(rowData, [dataKey, 'name'], '')}
          </div>
        );
      case 'createdDateTime':
        if (DateTime.fromISO(get(rowData, dataKey, '')).isValid) {
          return (
            <div role="cell">
              {I18nTranslate.TranslateDate(toLocalTime(get(rowData, dataKey, '')))}
            </div>
          );
        } else {
          return (
            <div role="cell">
              {get(rowData, dataKey, '')}
            </div>
          );
        }
    }
  };

  const timelineRowOnClick = () => {
    if (isPendingDeletion(id)) {
      createSnackNotification(
          AlertLevel.Warning,
          'Warning',
          'This file is pending deletion and cannot be selected.'
      );
    } else {
      onRowClick?.(rowData);
    }
  };

  return (
    <div
      role="row"
      className={cn('GTimeline-row', { selected: selectedId === id })}
    >
      <div
        data-testid={"generated-timeline-row-" + id}
        className={cn('GTimeline-row__row')}
        onClick={timelineRowOnClick}
      >
        {colData.map(({ grow, dataKey, width, minWidth }, index) => (
          <div
            className="GTimeline-row__cell"
            data-testid={`GTimeline-row-cell-${rowIndex}-${id}-${index}`}
            key={`GTimelineRowCell-${id}-${rowIndex}-${index}-${dataKey}`}
            style={{
              flexGrow: grow,
              maxWidth: width,
              minWidth,
            }}
          >
            {renderCell({ dataKey })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default GeneratedTimelineRow;
