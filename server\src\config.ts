import createHealthCheckApp from './application/health-check';
import createTrackerApp from './application/tracker';
import createLogger from './logger';
import createCache from './cache';
import createRedisCache from './redis';
import createGetCredentialApp from './application/credential';
import createGQLApiWithoutToken from './gql';
import { Env } from './env';
import RedisWrapper from './redisWrapper';

export interface Config {
  healthCheckApp: ReturnType<typeof createHealthCheckApp>;
  trackerApp: ReturnType<typeof createTrackerApp>;
  getCredentialApp: ReturnType<typeof createGetCredentialApp>;
  log: ReturnType<typeof createLogger>;
}

export type CreateConfig = ReturnType<typeof createConfig>;

async function createConfig({ env }: { env: Env }) {
  const log = createLogger(env);
  const cache = createCache();
  const redisCache = await createRedisCache({ env, log });
  const redisWrapper = redisCache
    ? new RedisWrapper(redisCache, log)
    : undefined;
  const gql = createGQLApiWithoutToken();

  log.info('Initializing config', { serviceName: env.serviceName });

  const healthCheckApp = createHealthCheckApp({ log, gql, cache });
  const trackerApp = createTrackerApp({
    log,
    cache,
    redis: redisCache,
    redisWrapper,
  });
  const getCredentialApp = createGetCredentialApp({ log, gql, cache });

  return {
    healthCheckApp,
    trackerApp,
    log,
    getCredentialApp,
  };
}

export default createConfig;
