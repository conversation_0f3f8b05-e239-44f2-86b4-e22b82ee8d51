import { Context, RequestHeader } from '../../../types';
import { responses } from '../../graphQL';
import * as ResTypes from '../../../../../../types/responses';
import { Variables } from 'graphql-request';
import updateFile from '../updateFile';
import { createRequest, createResponse } from 'node-mocks-http';
import consoleLogger from '../../../../logger';
import GQLApi from '../../../../util/api/graphQL';
import NodeCache from 'node-cache';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

let cxt: Context<
  object,
  responses.getFile & ResTypes.UpdateFilePayloadResponse
>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) => Promise.resolve({})
  ),
}));

describe('Update File Name', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    cxt = {
      data: {
        name: 'file_name_edited.mp4',
        currentTime: '2024-10-22T05:07:35.995Z',
        file: {
          id: 'fileId',
          createdByName: '',
          fileName: 'file_name.mp4',
          status: 'processed',
          length: 1,
          uploadDate: '2024-10-21T08:25:46.895Z',
          location: '',
          fileType: 'video/mp4',
          fileSize: 1,
          eventId: '',
          eventName: '',
          thumbnailUrl: '',
          primaryAsset: {
            signedUri: '',
          },
          streams: [],
          frameRate: 30,
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        params: {
          fileId: 'fileId',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
  });

  it('Updates a file name with adequate data', async () => {
    (callGQL as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        temporalDataObject: {
          id: 'fileId',
          name: 'file_name.mp4',
          jobs: { records: [] },
          details: {
            name: 'file_name.mp4',
            addToIndex: true,
            tags: [],
            veritoneFile: {
              fileName: '1.mp4',
              fileType: 'video/mp4',
              filename: '1.mp4',
            },
          },
          description: null,
          createdDateTime: '2024-10-21T08:25:46.895Z',
          modifiedDateTime: '2024-10-22T05:11:02.152Z',
          createdBy: 'd8285ec1-eb46-416c-bac1-68c4dd95214d',
          primaryAsset: {
            signedUri: '',
          },
          streams: [],
          assets: {},
          thumbnailUrl: '',
        },
      })
    );
    const response = await updateFile(cxt);
    expect(callGQL).toHaveBeenCalledTimes(2);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        tdoId: 'fileId',
      }
    );

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        tdoId: 'fileId',
        name: 'file_name_edited.mp4',
        details: {
          addToIndex: true,
          name: 'file_name_edited.mp4',
          tags: [],
          veritoneFile: {
            fileName: 'file_name_edited.mp4',
            fileType: 'video/mp4',
            filename: 'file_name_edited.mp4',
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is no name provided', async () => {
    cxt.data.name = undefined;
    expect(async () => await updateFile(cxt)).rejects.toThrowError(
      'Missing file name'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Throws an error if there is no folder provided', async () => {
    // @ts-expect-error Does it make sense to test this? Can it be called with
    cxt.data.file = undefined;
    expect(async () => await updateFile(cxt)).rejects.toThrowError(
      'Missing file'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });
});
