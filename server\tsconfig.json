{
  "compilerOptions": {
    "module": "commonjs",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "strictNullChecks": true,
    "target": "es6",
    "noImplicitAny": true,
    "moduleResolution": "node",
    "sourceMap": true,
    "typeRoots": ["./node_modules/@types"],
    "outDir": "dist",
    "baseUrl": ".",
    "paths": {
      "@util/*": ["src/util/*"],
      "@common/*": ["src/application/common/*"],
      "@tracker/*": ["src/application/tracker/*"],
      "@application/*": ["src/application/*"],
      "@tracker/redis": ["src/redis.ts"],
    }
  },
  "include": ["src/**/*", "./apiConfig.json", "../assets/*.json"],
  // "exclude": ["node_modules/*", "**/*.test.ts"],
  "exclude": ["node_modules/*"],
  "ts-node": { "files": true }
}
