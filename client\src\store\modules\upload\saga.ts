import { takeEvery, put } from 'redux-saga/effects';
import { selectFilesToUpload, startUpload, uploadFile } from './slice';
import { FileToUpload } from './types';
import { selectState } from '@store/store';

function* watchStartNextUpload() {
  const files: FileToUpload[] = yield selectState<FileToUpload[]>(selectFilesToUpload);
  const filesReady = files.filter(u =>
    !u.complete && (u.status === 'idle' || u.status === 'failure') // && !u.error
  );

  if (filesReady.length > 0) {
    yield put(uploadFile({ fileIndex: files.findIndex(f => filesReady[0] === f) }));
  }
}

export function* watchUploadSagas() {
  yield takeEvery(startUpload, watchStartNextUpload);
  yield takeEvery(uploadFile.fulfilled, watchStartNextUpload);
}

const uploadSagas = watchUploadSagas;

export default uploadSagas;
