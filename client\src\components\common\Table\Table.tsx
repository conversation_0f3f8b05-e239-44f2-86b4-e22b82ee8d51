import React, { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isValidElement } from 'react';
import cn from 'classnames';
import { constant, isArray, isObject, range } from 'lodash';
import { FormControl, Select, MenuItem, Skeleton } from '@mui/material';
import { Column, ASCENDING_SORT, NO_SORT, DESCENDING_SORT } from '@components/common';
import { keypressHelpers, dataHash } from '@utility/index';
import './Table.scss';
import { I18nTranslate } from '@i18n';

const { onEnter } = keypressHelpers;

interface Pagination {
  totalPages: number;
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

interface ColumnProps {
  grow: number;
  dataKey: string;
  minWidth: number;
  width?: number;
};

const isColumnElement = (
  element: React.ReactNode
): element is React.ReactElement<ColumnProps> => (
  isValidElement<ColumnProps>(element) && 'dataKey' in element.props
);

const castToNumber = (value: string | number, fallback = 10): number => {
  const num = typeof value === "number" ? value : Number(value);
  return Number.isFinite(num) ? num : fallback;
}

export interface RowProps<RowData extends { id: string | number }, AdditionalRowProps = object> {
  colData: ColumnProps[];
  rowData: RowData;
  rowIndex: number;
  selectedIndex?: number;
  selectedId?: string;
  onRowClick?: (rowData: RowData, rowIndex?: number) => void;
  onDoubleRowClick?: (rowData: RowData, rowIndex?: number) => void;
  onAuxClick?: (rowData: RowData, rowIndex?: number) => void;
  rowProps?: Record<string, ((rowData: RowData) => boolean | MouseEventHandler<HTMLElement>)>;
  additionalProps?: AdditionalRowProps;
}

export interface TableProps<RowData extends { id: string | number }, AdditionalRowProps> {
  setSort?: ({ column, direction }: { column?: string; direction: string }) => void;
  dataQuery?: ({ page, limit }: { page: number; limit: number }) => DispatchPromise;
  onDataQuery?: ({ page, limit }: { page: number; limit: number }) => void;
  pagination: Pagination;
  setPagination?: ({ pageSize, currentPage }: { pageSize: number; currentPage: number }) => void;
  children?: React.ReactNode | React.ReactNode[];
  RowComponent?: React.ComponentType<RowProps<RowData, AdditionalRowProps>>;
  rowData?: RowData[];
  emptyMessage?: string;
  loading?: boolean;
  disableOnlineFeatures?: boolean;
  // colRefs?: HTMLDivElement[];
  onRowClick?: (rowData: RowData, rowIndex?: number) => void;
  onDoubleRowClick?: (rowData: RowData, rowIndex?: number) => void;
  onAuxClick?: (rowData: RowData, rowIndex?: number) => void;
  selectedIndex?: number;
  selectedId?: string;
  queryOnMount?: boolean;
  disablePagination?: boolean;
  additionalProps?: AdditionalRowProps;
  className?: string;
  emptyComponent?: React.ReactNode;
}

class Table<RowData extends { id: string | number }, AdditionalRowProps = object> extends Component<TableProps<RowData, AdditionalRowProps>> {
  static defaultProps = {
    // eslint-disable-next-line react/default-props-match-prop-types
    pagination: { pageSize: 0, currentPage: 0, totalPages: 0, totalCount: 0 }
  };

  constructor(props: TableProps<RowData, AdditionalRowProps>) {
    super(props);
    if (isArray(props.children) && props.children.length > 0) {
      this.colRefs = props.children.map(constant(null));
    }
    this.firstLoad = !props.queryOnMount;
  }

  state = {
    activeColumnSortOrder: 0,
    activeColumnSortIndex: 0,
    colData: this.setColData(),
    loading: this.props.queryOnMount && !this.firstLoad || this.props.loading,
  };

  componentDidMount() {
    const { dataQuery, onDataQuery, pagination, queryOnMount } = this.props;
    window.addEventListener('resize', this.getColWidths);
    this.getColWidths();

    if (queryOnMount) {
      this.queryPromise = dataQuery?.({ page: pagination.currentPage, limit: pagination.pageSize })?.finally(() => {
        this.firstLoad = true;
        this.setState({ loading: false });
      }) as DispatchPromise;
      onDataQuery?.({ page: pagination.currentPage, limit: pagination.pageSize });
    }

    this.observer = new MutationObserver(this.getColWidths);
    document.querySelectorAll('.table').forEach((el) => this.observer?.observe(el, { attributes: true, childList: true, subtree: true }));
  }

  componentDidUpdate(prevProps: Readonly<TableProps<RowData, AdditionalRowProps>>): void {
    if (prevProps.loading !== this.props.loading) {
      this.getColWidths();
      this.setState({ loading: this.props.loading });
    }
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.getColWidths);
    // this.queryPromise?.abort();
    this.firstLoad = false;
  }

  colRefs: (HTMLDivElement | null)[] = [];

  tableRef?: HTMLDivElement;

  queryPromise?: DispatchPromise;

  observer?: MutationObserver;

  firstLoad?: boolean = false;

  setColData() {
    const { children } = this.props;
    if (isArray(children) && children.length > 0) {
      return Children.toArray(children).filter(isColumnElement).map(
        ({ props }) => ({
          minWidth: props.minWidth,
          grow: props.grow,
          dataKey: props.dataKey,
        })
      );
    }
    return [];
  }

  getColWidths = () => {
    const { colData } = this.state;
    this.setState({ colData: colData.map((d, i) => ({ ...d, width: this.colRefs?.[i]?.getBoundingClientRect().width ?? 10000 })) });
  };

  onColumnClick = (columnIndex: number) => () => {
    const { activeColumnSortIndex, activeColumnSortOrder, colData } = this.state;
    const { setSort } = this.props;

    const getNextSortState = (sortOrder: number) => {
      switch (sortOrder) {
        case DESCENDING_SORT:
          return ASCENDING_SORT;
        case ASCENDING_SORT:
          return NO_SORT;
        case NO_SORT:
          return DESCENDING_SORT;
      }
    };
    this.setState(() => {
      if (activeColumnSortIndex === columnIndex) {
        const nextSortOrder = getNextSortState(activeColumnSortOrder);
        const direction = nextSortOrder === ASCENDING_SORT ? 'asc' : nextSortOrder === DESCENDING_SORT ? 'desc' : '';
        setSort?.({ 
          column: direction ? colData[activeColumnSortIndex].dataKey : undefined, 
          direction 
        });
        return { activeColumnSortOrder: nextSortOrder };
      }
      setSort?.({ column: colData[columnIndex].dataKey, direction: 'desc' });
      return { activeColumnSortOrder: DESCENDING_SORT, activeColumnSortIndex: columnIndex };
    });
  };

  setPageSize = (pageSize: number) => {
    const { dataQuery, onDataQuery, setPagination } = this.props;
    if (setPagination) {
      setPagination({ pageSize, currentPage: 1 });
    }
    this.queryPromise = dataQuery?.({ page: 1, limit: pageSize });
    onDataQuery?.({ page: 1, limit: pageSize });
  };

  nextPage = () => {
    const { dataQuery, onDataQuery, pagination, setPagination } = this.props;
    const { pageSize, currentPage, totalPages } = pagination;

    if (currentPage < totalPages) {
      this.queryPromise = dataQuery?.({ page: currentPage + 1, limit: pageSize });
      setPagination?.({ pageSize, currentPage: currentPage + 1 });
      onDataQuery?.({ page: currentPage + 1, limit: pageSize });
    }
  };

  prevPage = () => {
    const { dataQuery, onDataQuery, setPagination, pagination } = this.props;
    const { pageSize, currentPage } = pagination ?? { pageSize: 10, currentPage: 1 };

    if (currentPage && currentPage > 1) {
      setPagination?.({ pageSize, currentPage: currentPage - 1 });
      this.queryPromise = dataQuery?.({ page: currentPage - 1, limit: pageSize });
      onDataQuery?.({ page: currentPage - 1, limit: pageSize });
    }
  };

  setColRef = (ref: HTMLDivElement, index: number) => {
    if (ref) {
      if (!this.colRefs) {
        this.colRefs = [];
      }
      this.colRefs[index] = ref;
    }
  };

  render() {
    const {
      children,
      emptyComponent,
      className,
      RowComponent,
      // queryOnMount,
      rowData,
      pagination,
      emptyMessage,
      onRowClick,
      onDoubleRowClick,
      onAuxClick,
      selectedIndex,
      disablePagination,
      additionalProps,
      selectedId,
    } = this.props;
    const { activeColumnSortOrder, activeColumnSortIndex, colData, loading } = this.state;

    // pagination is optional if it is not provided set default values
    let paginationData = pagination;
    if (!pagination) {
      paginationData = {
        totalCount: 0,
        pageSize: 0,
        currentPage: 0,
        totalPages: 0
      };
    }

    const { totalCount, pageSize, currentPage, totalPages } = paginationData;
    const currentPageIndex = currentPage - 1;

    return (
      <div className={cn('table', className)} data-testid="table" role="table" key={`Table-${dataHash({ emptyMessage })}`}>
        {rowData?.length !== 0 && <div className="table__column-header" role="columnheader">
          {React.Children.toArray(children).map((child, index) =>
            isObject(child) && 'type' in child && child.type === Column ? React.cloneElement(child, {
              onClick: this.onColumnClick(index),
              sortOrder: activeColumnSortIndex === index ? activeColumnSortOrder : NO_SORT,
              setColRef: (r: HTMLDivElement) => this.setColRef(r, index),
              loading,
              index
            }) : null)}
        </div>}
        {loading && <div className="table__loading">
          {range(pageSize || 10).map((i) =>
            <Skeleton
              key={`Table-Loading-${i}`}
              variant="rectangular"
              width="100%"
              height={40}
            />)}
        </div>}
        {!loading && rowData?.length === 0 && <div className={cn("table__content-empty", { 'has-component': Boolean(emptyComponent) })}>
          {emptyComponent ?? emptyMessage}
        </div>}
        {!loading && rowData && rowData.length > 0 && <div className="table__content">
          {rowData.map((data, rowIndex) => RowComponent ?
            <RowComponent
              key={`Table-Row-${rowIndex}-${data.id}`}
              onRowClick={onRowClick}
              onDoubleRowClick={onDoubleRowClick}
              onAuxClick={onAuxClick}
              colData={colData}
              rowData={data}
              selectedId={selectedId}
              rowIndex={rowIndex}
              selectedIndex={selectedIndex}
              additionalProps={additionalProps}
            />
            :
            <div data-testid="table-text-row" key={`Table-Row-${dataHash(data)}`}>
              {JSON.stringify(data)}
            </div>
          )}
        </div>}
        {!disablePagination && rowData && rowData.length !== 0 && <div className="table__pagination">
          {loading &&
            <Skeleton
              className="table__pagination-page-size"
              variant="rectangular"
              width={135}
              height={22}
            />}
          {!loading && <>
            <div className="table__pagination-page-size-text">
              {I18nTranslate.TranslateMessage('resultsPerPage')}
            </div>
            <FormControl className="table__pagination-page-size" variant="standard" size="small">
              <Select
                className="table__pagination-page-size-select"
                value={pageSize === 0 ? 10 : pageSize}
                onChange={({ target: { value } }) => this.setPageSize(castToNumber(value))}
                data-testid="table-pagination-page-size"
                disableUnderline
              >
                <MenuItem value={10} data-testid="table-pagination-page-size-menu-item-10">
                  10
                </MenuItem>
                <MenuItem value={50} data-testid="table-pagination-page-size-menu-item-50">
                  50
                </MenuItem>
                <MenuItem value={100} data-testid="table-pagination-page-size-menu-item-100">
                  100
                </MenuItem>
              </Select>
            </FormControl>
          </>}
          {loading &&
            <Skeleton
              className="table__pagination-page-selector"
              variant="rectangular"
              width={135}
              height={22}
            />}
          {!loading && <div className="table__pagination-page-selector">
            <div className="table__pagination-page-selector-text" data-testid="table-pagination-page-selector-text">
              {`${totalCount === 0 ? 0 : currentPageIndex * pageSize + 1}-${(currentPageIndex + 1) * pageSize > totalCount ? totalCount : (currentPageIndex + 1) * pageSize} `}
              {` of ${totalCount}`}
            </div>
            <div
              tabIndex={0}
              role="button"
              className={cn('table__pagination-page-selector-back material-icons', { enabled: currentPageIndex > 0 })}
              aria-label="Previous page"
              data-testid="table-pagination-page-selector-back"
              onClick={this.prevPage}
              onKeyUp={onEnter(this.prevPage)}
            >
              chevron_left
            </div>
            <div
              tabIndex={0}
              role="button"
              aria-label="Next page"
              className={cn('table__pagination-page-selector-next material-icons', { enabled: currentPage < totalPages })}
              data-testid="table-pagination-page-selector-next"
              onClick={this.nextPage}
              onKeyUp={onEnter(this.nextPage)}
            >
              chevron_right
            </div>
          </div>}
        </div>}
      </div>
    );
  }
}

export default Table;
