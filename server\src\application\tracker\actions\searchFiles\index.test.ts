import NodeCache from 'node-cache';
import searchFiles from '../searchFiles';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { responses } from '@tracker/graphQL';

let cxt: Context<object, responses.getFolder>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      _query: string,
      _variables?: Variables
    ) =>
      Promise.resolve({
        searchMedia: {
          jsondata: {
            totalResults: {
              value: 0,
              relation: 'eq',
            },
            results: [],
            timestamp: 1234,
            from: 0,
            limit: 50,
            to: 50,
            skipDedupe: true,
            searchToken: 'SEARCH_TOKEN',
          },
        },
      })
  ),
}));

jest.mock('../getFolder', () => ({
  __esModule: true,
  default: async () => ({
    data: {
      folder: {
        id: '1234-5678-ABCD-EFGH',
        treeObjectId: '1234-5678-ABCD-EFGH',
      },
    },
  }),
}));

describe('Search Files', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cxt = {
      data: {
        folder: {
          contentTemplates: [
            {
              id: 'validId',
              sdo: {
                id: 'validId',
                schemaId: 'validSchemaId',
                data: {
                  eventEndDate: '2024-04-10T17:02:10Z',
                  createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
                  createdByName: 'Test User',
                  name: 'Event Test 1',
                  description: 'New description',
                  createdDateTime: '2024-04-11T00:10:13.876Z',
                  modifiedDateTime: '2024-04-11T00:10:24.681Z',
                  id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
                  eventStartDate: '2024-04-10T17:02:10Z',
                  tags: ['Tag 1', 'Tag 2'],
                  matchGroupsCount: 14,
                  filesCount: 13,
                },
              },
            },
          ],
          id: '',
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
        query: {
          pageSize: 50,
          currentPage: 1,
          query: 'query',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
    };
  });

  it('Query w/ empty value searches all files', async () => {
    cxt.req.query.file = '';

    const response = await searchFiles(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          type: 'file',
          select: ['veritone-file'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
            ],
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/ fileName', async () => {
    cxt.req.query.file = 'test_file.mp4';

    const response = await searchFiles(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          type: 'file',
          select: ['veritone-file'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'veritone-file.filename',
                    operator: 'query_string',
                    value: `*test_file.mp4*`,
                  },
                ],
              },
            ],
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/ eventId and fileName', async () => {
    cxt.req.query.file = 'test_file.mp4';
    cxt.req.query.eventId = '1234-5678-ABCD-EFGH';
    cxt.data.folder = {
      id: '',
      name: '',
      description: '',
      createdDateTime: '',
      modifiedDateTime: '',
      contentTemplates: [
        {
          id: 'validId',
          sdo: {
            id: 'validId',
            schemaId: 'validSchemaId',
            data: {
              eventEndDate: '2024-04-10T17:02:10Z',
              createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
              createdByName: 'Test User',
              name: 'Event Test 1',
              description: 'New description',
              createdDateTime: '2024-04-11T00:10:13.876Z',
              modifiedDateTime: '2024-04-11T00:10:24.681Z',
              id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
              eventStartDate: '2024-04-10T17:02:10Z',
              tags: ['Tag 1', 'Tag 2'],
              matchGroupsCount: 14,
              filesCount: 13,
            },
          },
        },
      ],
      treeObjectId: '1234-5678-ABCD-EFGH',
      parent: {
        organization: {
          id: 'orgId',
        },
      },
    };

    const response = await searchFiles(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          type: 'file',
          select: ['veritone-file'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
              {
                operator: 'and',
                conditions: [
                  {
                    field: 'veritone-file.filename',
                    operator: 'query_string',
                    value: `*test_file.mp4*`,
                  },
                ],
              },
              {
                field: 'parentTreeObjectIds',
                operator: 'terms',
                values: ['1234-5678-ABCD-EFGH'],
              },
            ],
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Query w/ eventId and no fileName defined', async () => {
    cxt.req.query.file = undefined;
    cxt.req.query.eventId = '1234-5678-ABCD-EFGH';
    cxt.data.folder = {
      id: '',
      name: '',
      description: '',
      createdDateTime: '',
      modifiedDateTime: '',
      contentTemplates: [
        {
          id: 'validId',
          sdo: {
            id: 'validId',
            schemaId: 'validSchemaId',
            data: {
              eventEndDate: '2024-04-10T17:02:10Z',
              createdBy: 'c38b16a7-f623-4dd4-9847-0f135bef9dc5',
              createdByName: 'Test User',
              name: 'Event Test 1',
              description: 'New description',
              createdDateTime: '2024-04-11T00:10:13.876Z',
              modifiedDateTime: '2024-04-11T00:10:24.681Z',
              id: '947db3be-91ec-4e4b-a00f-6ad2ae06e25d',
              eventStartDate: '2024-04-10T17:02:10Z',
              tags: ['Tag 1', 'Tag 2'],
              matchGroupsCount: 14,
              filesCount: 13,
            },
          },
        },
      ],
      treeObjectId: '1234-5678-ABCD-EFGH',
      parent: {
        organization: {
          id: 'orgId',
        },
      },
    };

    const response = await searchFiles(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        search: {
          index: ['mine'],
          type: 'file',
          select: ['veritone-file'],
          limit: 50,
          offset: 0,
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_object',
                field: 'tags',
                not: false,
                query: {
                  operator: 'term',
                  field: 'tags.value',
                  value: 'veritone_track',
                  dotNotation: true,
                },
              },
              {
                field: 'parentTreeObjectIds',
                operator: 'terms',
                values: ['1234-5678-ABCD-EFGH'],
              },
            ],
          },
        },
      }
    );
    expect(response).not.toBeNull();
  });

  const sortingOptions = [
    {
      sortBy: 'veritone-file.filename',
      sortDirection: 'asc',
    },
    {
      sortBy: 'veritone-file.filename',
      sortDirection: 'desc',
    },
    {
      sortBy: 'recordingId',
      sortDirection: 'asc',
    },
    {
      sortBy: 'recordingId',
      sortDirection: 'desc',
    },
    {
      sortBy: 'createdTime',
      sortDirection: 'asc',
    },
    {
      sortBy: 'createdTime',
      sortDirection: 'desc',
    },
  ];

  for (const sort of sortingOptions) {
    it(`SearchFiles query are sorted by ${sort.sortBy} and ${sort.sortDirection} parameters`, async () => {
      cxt.req.query.file = '';
      cxt.req.query.sortBy = sort.sortBy;
      cxt.req.query.sortDirection = sort.sortDirection;
      await searchFiles(cxt);

      expect(callGQL).toHaveBeenCalledTimes(1);
      expect(callGQL).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        {
          search: {
            index: ['mine'],
            type: 'file',
            select: ['veritone-file'],
            limit: 50,
            offset: 0,
            sort: [{ field: sort.sortBy, order: sort.sortDirection }],
            query: {
              operator: 'and',
              conditions: [
                {
                  operator: 'query_object',
                  field: 'tags',
                  not: false,
                  query: {
                    operator: 'term',
                    field: 'tags.value',
                    value: 'veritone_track',
                    dotNotation: true,
                  },
                },
              ],
            },
          },
        }
      );
    });
  }
});
