import supertest from 'supertest';
import createExpressApp from '@application/express';
import createConfig from '../../../config';
import env from '../../../env';
import { Context, RequestHeader } from '@application/types';
import { Variables } from 'graphql-request';
import { queries, responses } from '../../tracker/graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { has } from 'lodash';

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      variables?: Variables
    ) => {
      if (query.includes('validateToken')) {
        return Promise.resolve({
          validateToken: {
            token: 'validToken',
          },
        });
      }
      if (query.includes('fetchLatestSchema')) {
        return Promise.resolve({
          dataRegistry: {
            publishedSchema: {
              id: 'schemaId',
            },
          },
        });
      }
      if (query.includes('getFolder')) {
        return Promise.resolve({
          folder: {
            id: 'folderId',
            contentTemplates: [
              {
                id: 'contentTemplateId',
                sdo: { id: 'sdoId', schemaId: 'schemaId', data: {} },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        !has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              results: [],
            },
          },
        });
      }
      if (
        query.includes('searchMedia') &&
        variables &&
        has(variables.search, 'aggregate')
      ) {
        return Promise.resolve({
          searchMedia: {
            jsondata: {
              aggregations: {
                recordingId: {
                  doc_count_error_upper_bound: 0,
                  sum_other_doc_count: 0,
                  buckets: [],
                },
              },
            },
          },
        });
      }
      if (query.includes('createStructuredData')) {
        return Promise.resolve({
          createStructuredData: {
            id: 'id',
          },
        });
      }
    }
  ),
}));

jest.mock('../../tracker/actions/searchFiles', () =>
  jest.fn(async (context: Context<object, responses.searchFiles>) => ({
    ...context,
    data: {
      ...context.data,
      searchFiles: {
        searchResults: [
          {
            id: 'id',
            fileName: 'name',
            status: 'status',
            createdBy: 'createdBy',
            length: 50,
            uploadDate: 'uploadDate',
            location: 'location',
            fileType: 'filetype',
            fileSize: 50,
            thumbnailUrl: 'thumbnailUrl',
            primaryAsset: {
              signedUri: 'signedUri',
            },
            streams: [{ uri: 'uri', protocol: 'protocol' }],
            frameRate: 24,
          },
        ],
        pageSize: 50,
        currentPage: 1,
        totalPages: 10,
        totalCount: 500,
      },
      filesCount: 500,
    },
  }))
);

jest.mock('../../tracker/actions/getMatchGroups', () =>
  jest.fn(async (context: Context<object, responses.getMatchGroups>) => ({
    ...context,
    data: {
      ...context.data,
      matchGroups: {
        pagination: {
          pageSize: 50,
          currentPage: 1,
          totalPages: 10,
          totalCount: 500,
        },
        sort: {
          field: 'field',
          direction: 'direction',
        },
        eventId: 'eventId',
        searchResults: [
          {
            id: 'id',
            name: 'name',
            eventId: 'eventId',
            searches: [],
            selectedTracklets: [],
            modifiedDateTime: 'modifiedDateTime',
            timelineProject: {
              groups: [],
            },
            generatedTimelines: [],
          },
        ],
      },
    },
  }))
);

describe('delete event', () => {
  it('delete event', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/event/an-event-id')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(200);

    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteFolderContentTemplate,
      {
        contentTemplateId: 'contentTemplateId',
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteStructuredData,
      {
        sdoId: 'sdoId',
        schemaId: 'schemaId',
      }
    );
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      queries.deleteFolder,
      {
        folderId: 'folderId',
      }
    );
  });

  it('cannot delete event w/o eventId', async () => {
    const config = await createConfig({ env });
    const expressApp = await createExpressApp({ config });

    const resp = await supertest(expressApp)
      .delete('/api/v1/event')
      .set('Authorization', 'Bearer validToken')
      .send()
      .expect(404);
  });
});
